..  SPDX-License-Identifier: BSD-3-Clause
    Copyright 2019 The DPDK contributors

DPDK Vulnerability Management Process
=====================================

Scope
-----

Only the main repositories (dpdk and dpdk-stable) of the core project
are in the scope of this security process (including experimental APIs).
If a stable branch is declared unmaintained (end of life),
no fix will be applied.

All vulnerabilities are bugs, but not every bug is a vulnerability.
Vulnerabilities compromise one or more of:

* Confidentiality (personal or corporate confidential data).
* Integrity (trustworthiness and correctness).
* Availability (uptime and service).

If in doubt, please consider the vulnerability as security sensitive.
At worst, the response will be to report the bug through the usual channels.


Finding
-------

There is no pro-active security engineering effort at the moment.

Please report any security issue you find in DPDK as described below.


Report
------

Do not use Bugzilla (unsecured).
Instead, send GPG-encrypted emails
to `<EMAIL> <https://core.dpdk.org/security#contact>`_.
Anyone can post to this list.
In order to reduce the disclosure of a vulnerability in the early stages,
membership of this list is intentionally limited to a `small number of people
<https://mails.dpdk.org/roster/security>`_.

It is additionally encouraged to GPG-sign one-on-one conversations
as part of the security process.

As it is with any bug, the more information provided,
the easier it will be to diagnose and fix.
If you already have a fix, please include it with your report,
as that can speed up the process considerably.

In the report, please note how you would like to be credited
for discovering the issue
and the details of any embargo you would like to impose.

If the vulnerability is not public yet,
no patch or information should be disclosed publicly.
If a fix is already published,
the reporting process must be followed anyway, as described below.


Confirmation
------------

Upon reception of the report, a security team member should reply
to the reporter acknowledging that the report has been received.

The DPDK security team reviews the security vulnerability reported.
Area experts not members of the security team may be involved in the process.
In case the reported issue is not qualified as a security vulnerability,
the security team will request the submitter to report it
using the usual channel (Bugzilla).
If qualified, the security team will assess which DPDK version are affected.
A bugzilla ID (allocated in a `reserved pool
<https://bugs.dpdk.org/buglist.cgi?f1=bug_group&o1=equals&v1=security>`_)
is assigned to the vulnerability, and kept empty until public disclosure.

The security team calculates the severity score with
`CVSS calculator <https://www.first.org/cvss/calculator/3.0>`_
based on inputs from the reporter and its own assessment of the vulnerability,
and agrees on the score with the reporter.

An embargo may be put in place depending on the severity of the vulnerability.
If an embargo is decided, its duration should be suggested by the security team
and negotiated with the reporter.
Embargo duration between vulnerability confirmation and public disclosure
should be between **one and ten weeks**.
If an embargo is not required, the vulnerability may be fixed
using the standard patch process, once a CVE number has been assigned.

The confirmation mail should be sent within **3 business days**.

Following information must be included in the mail:

* Confirmation
* CVSS severity and score
* Embargo duration
* Reporter credit
* Bug ID (empty and restricted for future reference)

CVE Request
-----------

The security team develops a security advisory document.
The security team may, at its discretion,
include the reporter (via "CC") in developing the security advisory document,
but in any case should accept feedback
from the reporter before finalizing the document.
When the document is final, the security team needs to
request a CVE identifier from a CNA.

The CVE request should be sent
to `<EMAIL> <mailto:<EMAIL>>`_
using GPG encrypted email
(see `contact details <https://access.redhat.com/security/team/contact>`_).


CVE Request Template with Embargo
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

::

  A vulnerability was discovered in the DPDK project.
  In order to ensure full traceability, we need a CVE number assigned
  that we can attach to private and public notifications.
  Please treat the following information as confidential during the embargo
  until further public disclosure.

  [PRODUCT]:
  [VERSION]:
  [PROBLEMTYPE]:
  [SEVERITY]:
  [REFERENCES]: { bug_url }
  [DESCRIPTION]:

  Thanks
  { DPDK_security_team_member }, on behalf of the DPDK security team


CVE Request Template without Embargo
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

::

  A vulnerability was discovered in the DPDK project.
  In order to ensure full traceability, we need a CVE number assigned
  that we can attach to private and public notifications.

  [PRODUCT]:
  [VERSION]:
  [PROBLEMTYPE]:
  [SEVERITY]:
  [REFERENCES]: { bug_url }
  [DESCRIPTION]:

  Thanks
  { DPDK_security_team_member }, on behalf of the DPDK security team


Fix Development and Review
--------------------------

If the fix is already published, this step is skipped,
and the pre-release disclosure is replaced with the private disclosure,
as described below. It must not be considered as the standard process.

This step may be started in parallel with CVE creation.
The patches fixing the vulnerability are developed and reviewed
by the security team and
by elected area experts that agree to maintain confidentiality.

The CVE id and the bug id must be referenced in the patch if there is no
embargo, or if there is an embargo, but it will be lifted when the release
including the patch is published. If the embargo is going to be lifted after the
release, then the CVE and bug ids must be omitted from the commit message.

Backports to the identified affected versions are done once the fix is ready.


Pre-Release Disclosure
----------------------

When the fix is ready, the security advisory and patches are sent
to downstream stakeholders
(`<EMAIL> <mailto:<EMAIL>>`_),
specifying the date and time of the end of the embargo.
The communicated public disclosure date should be **less than one week**

Downstream stakeholders are expected not to deploy or disclose patches
until the embargo is passed, otherwise they will be removed from the list.

Downstream stakeholders (in `security-prerelease list
<https://mails.dpdk.org/roster/security-prerelease>`_), are:

* Operating system vendors known to package DPDK
* Major DPDK users, considered trustworthy by the technical board, who
  have made the request to `<EMAIL> <mailto:<EMAIL>>`_

The `OSS security private mailing list mailto:<EMAIL>>` will
also be contacted one week before the end of the embargo, as indicated by `the
OSS-security process <https://oss-security.openwall.org/wiki/mailing-lists/distros>`
and using the PGP key listed on the same page, describing the details of the
vulnerability and sharing the patch[es]. Distributions and major vendors follow
this private mailing list, and it functions as a single point of contact for
embargoed advance notices for open source projects.

The security advisory will be based on below template,
and will be sent signed with a security team's member GPG key.


Pre-Release Mail Template
~~~~~~~~~~~~~~~~~~~~~~~~~

::

  This is an advance warning of a vulnerability discovered in DPDK,
  to give you, as downstream stakeholders, a chance to coordinate
  the release of fixes and reduce the vulnerability window.
  Please treat the following information as confidential until
  the proposed public disclosure date.

  { impact_description }

  Proposed patches are attached.
  Unless a flaw is discovered in them, these patches will be merged
  to { branches } on the public disclosure date.

  CVE: { cve_id }
  Severity: { severity }
  CVSS scores: { cvss_scores }

  Proposed public disclosure date/time: { disclosure_date } at 15:00 UTC.
  Please do not make the issue public (or release public patches)
  before this coordinated embargo date.

If the issue is leaked during the embargo, the same procedure is followed
with only a few days delay between the pre-release and the public disclosure.


Private Disclosure
------------------

If a vulnerability is unintentionally already fixed in the public repository,
a security advisory is sent to downstream stakeholders
(`<EMAIL> <mailto:<EMAIL>>`_),
giving few days to prepare for updating before the public disclosure.


Private Disclosure Mail Template
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

::

  This is a warning of a vulnerability discovered in DPDK,
  to give you, as downstream stakeholders, a chance to coordinate
  the deployment of fixes before a CVE is public.

  Please treat the following information as confidential until
  the proposed public disclosure date.

  { impact_description }

  Commits: { commit_ids with branch number }

  CVE: { cve_id }
  Severity: { severity }
  CVSS scores: { cvss_scores }

  Proposed public disclosure date/time: { disclosure_date }.
  Please do not make the vulnerability information public
  before this coordinated embargo date.


Public Disclosure
-----------------

On embargo expiration, following tasks will be done simultaneously:

* The assigned bug is filled by a member of the security team,
  with all relevant information, and it is made public.
* The patches are pushed to the appropriate branches.
* For long and short term stable branches fixed,
  new versions should be released.

Releases on Monday to Wednesday are preferred, so that system administrators
do not have to deal with security updates over the weekend.

The security advisory is posted
to `<EMAIL> <mailto:<EMAIL>>`_ and to `the public OSS-security
mailing list <mailto:<EMAIL>>` as soon as the patches
are pushed to the appropriate branches.

Patches are then sent to `<EMAIL> <mailto:<EMAIL>>`_
and `<EMAIL> <mailto:<EMAIL>>`_ accordingly.


Release Mail Template
~~~~~~~~~~~~~~~~~~~~~

::

  A vulnerability was fixed in DPDK.
  Some downstream stakeholders were warned in advance
  in order to coordinate the release of fixes
  and reduce the vulnerability window.

  { impact_description }

  Commits: { commit_ids with branch number }

  CVE: { cve_id }
  Bugzilla: { bug_url }
  Severity: { severity }
  CVSS scores: { cvss_scores }


References
----------

* `A minimal security response process
  <https://access.redhat.com/blogs/766093/posts/1975833>`_
* `fd.io Vulnerability Management
  <https://wiki.fd.io/view/TSC:Vulnerability_Management>`_
* `Open Daylight Vulnerability Management
  <https://wiki.opendaylight.org/view/Security:Vulnerability_Management>`_
* `CVE Assignment Information Format
  <https://cve.mitre.org/cve/list_rules_and_guidance/cve_assignment_information_format.html>`_
