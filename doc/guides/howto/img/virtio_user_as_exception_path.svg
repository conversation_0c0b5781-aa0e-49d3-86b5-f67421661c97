<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by Microsoft Visio, SVG Export virtio_user_as_exceptional_pathvsdx.svg Page-1 -->
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:ev="http://www.w3.org/2001/xml-events"
		xmlns:v="http://schemas.microsoft.com/visio/2003/SVGExtensions/" width="5.77778in" height="3.88851in"
		viewBox="0 0 416 279.973" xml:space="preserve" color-interpolation-filters="sRGB" class="st13">
	<v:documentProperties v:langID="1033" v:viewMarkup="false"/>

	<style type="text/css">
	<![CDATA[
		.st1 {visibility:visible}
		.st2 {fill:#5b9bd5;fill-opacity:0.22;filter:url(#filter_2);stroke:#5b9bd5;stroke-opacity:0.22}
		.st3 {fill:#ffffff;stroke:#c7c8c8;stroke-width:0.25}
		.st4 {stroke:#000000;stroke-dasharray:7,5;stroke-linecap:round;stroke-linejoin:round;stroke-width:1}
		.st5 {fill:#000000;font-family:Calibri;font-size:0.833336em}
		.st6 {fill:none;stroke:none;stroke-width:0.25}
		.st7 {fill:#000000;font-family:Calibri;font-size:1.00001em;font-style:italic}
		.st8 {fill:#70ad47;stroke:#c7c8c8;stroke-width:0.25}
		.st9 {stroke:#5b9bd5;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.75}
		.st10 {marker-end:url(#mrkr4-68);marker-start:url(#mrkr4-66);stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-width:1}
		.st11 {fill:#000000;fill-opacity:1;stroke:#000000;stroke-opacity:1;stroke-width:0.28409090909091}
		.st12 {fill:#d8d8d8;stroke:#c7c8c8;stroke-width:0.25}
		.st13 {fill:none;fill-rule:evenodd;font-size:12px;overflow:visible;stroke-linecap:square;stroke-miterlimit:3}
	]]>
	</style>

	<defs id="Markers">
		<g id="lend4">
			<path d="M 2 1 L 0 0 L 2 -1 L 2 1 " style="stroke:none"/>
		</g>
		<marker id="mrkr4-66" class="st11" v:arrowType="4" v:arrowSize="2" v:setback="6.68" refX="6.68" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend4" transform="scale(3.52) "/>
		</marker>
		<marker id="mrkr4-68" class="st11" v:arrowType="4" v:arrowSize="2" v:setback="7.04" refX="-7.04" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend4" transform="scale(-3.52,-3.52) "/>
		</marker>
	</defs>
	<defs id="Filters">
		<filter id="filter_2">
			<feGaussianBlur stdDeviation="2"/>
		</filter>
	</defs>
	<g v:mID="0" v:index="1" v:groupContext="foregroundPage">
		<title>Page-1</title>
		<v:pageProperties v:drawingScale="1" v:pageScale="1" v:drawingUnits="0" v:shadowOffsetX="9" v:shadowOffsetY="-9"/>
		<v:layer v:name="Connector" v:index="0"/>
		<g id="shape23-1" v:mID="23" v:groupContext="shape" transform="translate(195.804,-74.9728)">
			<title>Rectangle.23</title>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<g id="shadow23-2" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="113.473" width="216.6" height="166.5" class="st2"/>
			</g>
			<rect x="0" y="113.473" width="216.6" height="166.5" class="st3"/>
		</g>
		<g id="shape42-6" v:mID="42" v:groupContext="shape" v:layerMember="0" transform="translate(146.904,-277.473)">
			<title>Dynamic connector.42</title>
			<path d="M-9 279.97 L-9 540.97" class="st4"/>
		</g>
		<g id="shape45-9" v:mID="45" v:groupContext="shape" transform="translate(2.9044,-142.292)">
			<title>Rectangle.45</title>
			<desc>tap</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="34.05" cy="264.132" width="68.1" height="31.6807"/>
			<g id="shadow45-10" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="248.292" width="68.1" height="31.6807" class="st2"/>
			</g>
			<rect x="0" y="248.292" width="68.1" height="31.6807" class="st3"/>
			<text x="27.35" y="267.13" class="st5" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>tap</text>		</g>
		<g id="shape46-15" v:mID="46" v:groupContext="shape" transform="translate(2.9044,-43.2921)">
			<title>Rectangle.46</title>
			<desc>vhost ko</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="34.05" cy="264.132" width="68.1" height="31.6807"/>
			<g id="shadow46-16" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="248.292" width="68.1" height="31.6807" class="st2"/>
			</g>
			<rect x="0" y="248.292" width="68.1" height="31.6807" class="st3"/>
			<text x="16.86" y="267.13" class="st5" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>vhost ko  </text>		</g>
		<g id="shape47-21" v:mID="47" v:groupContext="shape" transform="translate(18.9544,-257.223)">
			<title>Sheet.47</title>
			<desc>Kernel space</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="44.025" cy="268.723" width="88.05" height="22.5"/>
			<rect x="0" y="257.473" width="88.05" height="22.5" class="st6"/>
			<text x="13.44" y="272.32" class="st7" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Kernel space</text>		</g>
		<g id="shape48-24" v:mID="48" v:groupContext="shape" transform="translate(148.854,-257.223)">
			<title>Sheet.48</title>
			<desc>User space</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="44.025" cy="268.723" width="88.05" height="22.5"/>
			<rect x="0" y="257.473" width="88.05" height="22.5" class="st6"/>
			<text x="17.7" y="272.32" class="st7" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>User space</text>		</g>
		<g id="shape49-27" v:mID="49" v:groupContext="shape" transform="translate(218.904,-182.792)">
			<title>Rectangle.49</title>
			<desc>ETHDEV</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="83.25" cy="264.132" width="166.5" height="31.6807"/>
			<g id="shadow49-28" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="248.292" width="166.5" height="31.6807" class="st2"/>
			</g>
			<rect x="0" y="248.292" width="166.5" height="31.6807" class="st3"/>
			<text x="66.9" y="267.13" class="st5" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>ETHDEV</text>		</g>
		<g id="shape50-33" v:mID="50" v:groupContext="shape" transform="translate(218.904,-142.292)">
			<title>Rectangle.50</title>
			<desc>virtio PMD</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="38.7911" cy="264.132" width="77.59" height="31.6807"/>
			<g id="shadow50-34" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="248.292" width="77.5823" height="31.6807" class="st2"/>
			</g>
			<rect x="0" y="248.292" width="77.5823" height="31.6807" class="st3"/>
			<text x="17.12" y="267.13" class="st5" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>virtio PMD</text>		</g>
		<g id="shape51-39" v:mID="51" v:groupContext="shape" transform="translate(308.904,-142.292)">
			<title>Rectangle.51</title>
			<desc>other PMDs</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="38.7911" cy="264.132" width="77.59" height="31.6807"/>
			<g id="shadow51-40" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="248.292" width="77.5823" height="31.6807" class="st2"/>
			</g>
			<rect x="0" y="248.292" width="77.5823" height="31.6807" class="st3"/>
			<text x="14.6" y="267.13" class="st5" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>other PMDs</text>		</g>
		<g id="shape52-45" v:mID="52" v:groupContext="shape" transform="translate(218.904,-86.3131)">
			<title>Rectangle.52</title>
			<desc>virtio-user</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="38.7911" cy="256.393" width="77.59" height="47.1597"/>
			<g id="shadow52-46" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="232.813" width="77.5823" height="47.1597" class="st2"/>
			</g>
			<rect x="0" y="232.813" width="77.5823" height="47.1597" class="st8"/>
			<text x="17.84" y="247.39" class="st5" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>virtio-user<v:newlineChar/><v:newlineChar/></text>		</g>
		<g id="shape53-51" v:mID="53" v:groupContext="shape" transform="translate(223.404,-90.1829)">
			<title>Rectangle.53</title>
			<desc>vhost adapter</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="34.05" cy="268.183" width="68.1" height="23.5798"/>
			<g id="shadow53-52" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="256.393" width="68.1" height="23.5798" class="st2"/>
			</g>
			<rect x="0" y="256.393" width="68.1" height="23.5798" class="st3"/>
			<text x="5.82" y="271.18" class="st5" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>vhost adapter</text>		</g>
		<g id="shape54-57" v:mID="54" v:groupContext="shape" v:layerMember="0" transform="translate(71.0044,-59.1325)">
			<title>Dynamic connector</title>
			<path d="M0 279.97 L63.9 279.97 A3 3 0 1 1 69.9 279.97 L186.69 279.97 L186.69 252.79" class="st9"/>
		</g>
		<g id="shape55-60" v:mID="55" v:groupContext="shape" v:layerMember="0" transform="translate(71.0044,-149.132)">
			<title>Dynamic connector.55</title>
			<path d="M6.68 270.97 L7.04 270.97 L63.9 270.97 A3 3 0 0 1 69.9 270.97 L140.86 270.97" class="st10"/>
		</g>
		<g id="shape56-69" v:mID="56" v:groupContext="shape" transform="translate(308.904,-5.2228)">
			<title>Rectangle.38</title>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<g id="shadow56-70" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="257.473" width="77.5823" height="22.5" rx="11.25" ry="11.25" class="st2"/>
			</g>
			<rect x="0" y="257.473" width="77.5823" height="22.5" rx="11.25" ry="11.25" class="st12"/>
		</g>
		<g id="shape57-74" v:mID="57" v:groupContext="shape" transform="translate(318.204,-5.2228)">
			<title>Sheet.57</title>
			<desc>NIC</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="28.125" cy="268.723" width="56.25" height="22.5"/>
			<rect x="0" y="257.473" width="56.25" height="22.5" class="st6"/>
			<text x="19.61" y="272.32" class="st7" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>NIC</text>		</g>
		<g id="shape58-77" v:mID="58" v:groupContext="shape" v:layerMember="0" transform="translate(356.696,-142.292)">
			<title>Dynamic connector.41</title>
			<path d="M-9 286.65 L-9 287.01 L-9 387.5" class="st10"/>
		</g>
	</g>
</svg>
