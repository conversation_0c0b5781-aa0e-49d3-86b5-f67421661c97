<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by Microsoft Visio, SVG Export user_models_for_running_dpdk_in_containers.svg Page-1 -->
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:ev="http://www.w3.org/2001/xml-events"
		xmlns:v="http://schemas.microsoft.com/visio/2003/SVGExtensions/" width="10.6194in" height="4.55593in"
		viewBox="0 0 764.596 328.027" xml:space="preserve" color-interpolation-filters="sRGB" class="st19">
	<v:documentProperties v:langID="1033" v:viewMarkup="false"/>

	<style type="text/css">
	<![CDATA[
		.st1 {visibility:visible}
		.st2 {fill:#5b9bd5;fill-opacity:0.22;filter:url(#filter_2);stroke:#5b9bd5;stroke-opacity:0.22}
		.st3 {fill:#ffffff;stroke:#c7c8c8;stroke-width:0.25}
		.st4 {fill:#000000;font-family:Calibri;font-size:0.833336em}
		.st5 {fill:none;filter:url(#filter_2);stroke:#5b9bd5;stroke-opacity:0.22}
		.st6 {fill:none;stroke:#c7c8c8;stroke-width:0.25}
		.st7 {fill:#d8d8d8;stroke:#c7c8c8;stroke-width:0.25}
		.st8 {fill:none;stroke:none;stroke-width:0.25}
		.st9 {fill:#000000;font-family:Calibri;font-size:1.00001em;font-style:italic}
		.st10 {fill:#ed7d31;stroke:#c7c8c8;stroke-width:0.25}
		.st11 {fill:#feffff;font-family:Calibri;font-size:0.833336em}
		.st12 {fill:#a5a5a5;stroke:#c7c8c8;stroke-width:0.25}
		.st13 {marker-end:url(#mrkr4-61);marker-start:url(#mrkr4-59);stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-width:1}
		.st14 {fill:#000000;fill-opacity:1;stroke:#000000;stroke-opacity:1;stroke-width:0.28409090909091}
		.st15 {font-size:1em}
		.st16 {fill:none;filter:url(#filter_2);stroke:#5b9bd5;stroke-dasharray:7,5;stroke-opacity:0.22}
		.st17 {fill:none;stroke:#ff0000;stroke-dasharray:7,5;stroke-width:1}
		.st18 {stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-width:1}
		.st19 {fill:none;fill-rule:evenodd;font-size:12px;overflow:visible;stroke-linecap:square;stroke-miterlimit:3}
	]]>
	</style>

	<defs id="Markers">
		<g id="lend4">
			<path d="M 2 1 L 0 0 L 2 -1 L 2 1 " style="stroke:none"/>
		</g>
		<marker id="mrkr4-59" class="st14" v:arrowType="4" v:arrowSize="2" v:setback="6.68" refX="6.68" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend4" transform="scale(3.52) "/>
		</marker>
		<marker id="mrkr4-61" class="st14" v:arrowType="4" v:arrowSize="2" v:setback="7.04" refX="-7.04" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend4" transform="scale(-3.52,-3.52) "/>
		</marker>
	</defs>
	<defs id="Filters">
		<filter id="filter_2">
			<feGaussianBlur stdDeviation="2"/>
		</filter>
	</defs>
	<g v:mID="0" v:index="1" v:groupContext="foregroundPage">
		<title>Page-1</title>
		<v:pageProperties v:drawingScale="1" v:pageScale="1" v:drawingUnits="0" v:shadowOffsetX="9" v:shadowOffsetY="-9"/>
		<v:layer v:name="Connector" v:index="0"/>
		<g id="shape1-1" v:mID="1" v:groupContext="shape" transform="translate(146.2,-258.819)">
			<title>Rectangle</title>
			<desc>Container</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="34.05" cy="300.937" width="68.1" height="54.1807"/>
			<g id="shadow1-2" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="273.847" width="68.1" height="54.1807" class="st2"/>
			</g>
			<rect x="0" y="273.847" width="68.1" height="54.1807" class="st3"/>
			<text x="14.04" y="291.94" class="st4" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>Container<v:newlineChar/><v:newlineChar/></text>		</g>
		<g id="shape3-7" v:mID="3" v:groupContext="shape" transform="translate(18.25,-169.971)">
			<title>Rectangle.3</title>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<g id="shadow3-8" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="270.699" width="306" height="57.3286" rx="13.5" ry="13.5" class="st5"/>
			</g>
			<rect x="0" y="270.699" width="306" height="57.3286" rx="13.5" ry="13.5" class="st6"/>
		</g>
		<g id="shape4-12" v:mID="4" v:groupContext="shape" transform="translate(18.25,-61)">
			<title>Rectangle.4</title>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<g id="shadow4-13" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="256.027" width="306" height="72" rx="13.5" ry="13.5" class="st2"/>
			</g>
			<rect x="0" y="256.027" width="306" height="72" rx="13.5" ry="13.5" class="st7"/>
		</g>
		<g id="shape5-17" v:mID="5" v:groupContext="shape" transform="translate(17.65,-202.75)">
			<title>Sheet.5</title>
			<desc>Host kernel</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="34.05" cy="316.777" width="68.1" height="22.5"/>
			<rect x="0" y="305.527" width="68.1" height="22.5" class="st8"/>
			<text x="6.55" y="320.38" class="st9" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>Host kernel</text>		</g>
		<g id="shape6-20" v:mID="6" v:groupContext="shape" transform="translate(0.25,-110.5)">
			<title>Sheet.6</title>
			<desc>NIC</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="34.05" cy="316.777" width="68.1" height="22.5"/>
			<rect x="0" y="305.527" width="68.1" height="22.5" class="st8"/>
			<text x="25.54" y="320.38" class="st9" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>NIC</text>		</g>
		<g id="shape7-23" v:mID="7" v:groupContext="shape" transform="translate(67.75,-99.3)">
			<title>Rectangle.7</title>
			<desc>PF</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="14.625" cy="316.777" width="29.26" height="22.5"/>
			<g id="shadow7-24" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="305.527" width="29.25" height="22.5" class="st2"/>
			</g>
			<rect x="0" y="305.527" width="29.25" height="22.5" class="st10"/>
			<text x="9.74" y="319.78" class="st11" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>PF</text>		</g>
		<g id="shape8-29" v:mID="8" v:groupContext="shape" transform="translate(165.625,-99.3)">
			<title>Rectangle.8</title>
			<desc>VF</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="14.625" cy="316.777" width="29.26" height="22.5"/>
			<g id="shadow8-30" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="305.527" width="29.25" height="22.5" class="st2"/>
			</g>
			<rect x="0" y="305.527" width="29.25" height="22.5" class="st10"/>
			<text x="9.49" y="319.78" class="st11" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>VF</text>		</g>
		<g id="shape10-35" v:mID="10" v:groupContext="shape" transform="translate(67.75,-70)">
			<title>Rectangle.10</title>
			<desc>Hardware virtual switch</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="112.5" cy="316.777" width="225" height="22.5"/>
			<g id="shadow10-36" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="305.527" width="225" height="22.5" class="st2"/>
			</g>
			<rect x="0" y="305.527" width="225" height="22.5" class="st12"/>
			<text x="64.07" y="319.78" class="st11" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>Hardware virtual switch</text>		</g>
		<g id="shape14-41" v:mID="14" v:groupContext="shape" transform="translate(238.15,-258.7)">
			<title>Rectangle.14</title>
			<desc>Container</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="34.05" cy="300.937" width="68.1" height="54.1807"/>
			<g id="shadow14-42" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="273.847" width="68.1" height="54.1807" class="st2"/>
			</g>
			<rect x="0" y="273.847" width="68.1" height="54.1807" class="st3"/>
			<text x="14.04" y="291.94" class="st4" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>Container<v:newlineChar/><v:newlineChar/></text>		</g>
		<g id="shape15-47" v:mID="15" v:groupContext="shape" transform="translate(257.575,-99.2)">
			<title>Rectangle.15</title>
			<desc>VF</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="14.625" cy="316.777" width="29.26" height="22.5"/>
			<g id="shadow15-48" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="305.527" width="29.25" height="22.5" class="st2"/>
			</g>
			<rect x="0" y="305.527" width="29.25" height="22.5" class="st10"/>
			<text x="9.49" y="319.78" class="st11" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>VF</text>		</g>
		<g id="shape16-53" v:mID="16" v:groupContext="shape" v:layerMember="0" transform="translate(263.2,-258.7)">
			<title>Dynamic connector.16</title>
			<path d="M9 334.71 L9 335.07 L9 457.99" class="st13"/>
		</g>
		<g id="shape18-62" v:mID="18" v:groupContext="shape" transform="translate(54.25,-180.25)">
			<title>Ellipse</title>
			<desc>PF driver</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="28.125" cy="316.777" width="49.22" height="19.6875"/>
			<g id="shadow18-63" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<path d="M0 316.78 A28.125 11.25 0 0 1 56.25 316.78 A28.125 11.25 0 1 1 0 316.78 Z" class="st2"/>
			</g>
			<path d="M0 316.78 A28.125 11.25 0 0 1 56.25 316.78 A28.125 11.25 0 1 1 0 316.78 Z" class="st10"/>
			<text x="10.11" y="319.78" class="st11" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>PF driver</text>		</g>
		<g id="shape19-68" v:mID="19" v:groupContext="shape" v:layerMember="0" transform="translate(73.375,-180.25)">
			<title>Dynamic connector.19</title>
			<path d="M9 334.71 L9 335.07 L9 379.44" class="st13"/>
		</g>
		<g id="shape20-75" v:mID="20" v:groupContext="shape" transform="translate(152.125,-263.44)">
			<title>Ellipse.20</title>
			<desc>DPDK</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="28.125" cy="316.777" width="49.22" height="19.6875"/>
			<g id="shadow20-76" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<path d="M0 316.78 A28.125 11.25 0 0 1 56.25 316.78 A28.125 11.25 0 1 1 0 316.78 Z" class="st2"/>
			</g>
			<path d="M0 316.78 A28.125 11.25 0 0 1 56.25 316.78 A28.125 11.25 0 1 1 0 316.78 Z" class="st10"/>
			<text x="16.79" y="319.78" class="st11" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>DPDK</text>		</g>
		<g id="shape21-81" v:mID="21" v:groupContext="shape" v:layerMember="0" transform="translate(171.25,-258.819)">
			<title>Dynamic connector.21</title>
			<path d="M9 334.71 L9 335.07 L9 458.01" class="st13"/>
		</g>
		<g id="shape22-88" v:mID="22" v:groupContext="shape" transform="translate(243.25,-263.44)">
			<title>Ellipse.22</title>
			<desc>DPDK</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="28.125" cy="316.777" width="49.22" height="19.6875"/>
			<g id="shadow22-89" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<path d="M0 316.78 A28.125 11.25 0 0 1 56.25 316.78 A28.125 11.25 0 1 1 0 316.78 Z" class="st2"/>
			</g>
			<path d="M0 316.78 A28.125 11.25 0 0 1 56.25 316.78 A28.125 11.25 0 1 1 0 316.78 Z" class="st10"/>
			<text x="16.79" y="319.78" class="st11" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>DPDK</text>		</g>
		<g id="shape23-94" v:mID="23" v:groupContext="shape" transform="translate(395.65,-254.5)">
			<title>Rectangle.23</title>
			<desc>Virtual Appliance</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="34.05" cy="300.937" width="68.1" height="54.1807"/>
			<g id="shadow23-95" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="273.847" width="68.1" height="54.1807" class="st2"/>
			</g>
			<rect x="0" y="273.847" width="68.1" height="54.1807" class="st3"/>
			<text x="20.48" y="297.94" class="st4" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>Virtual <tspan
						x="13.98" dy="1.2em" class="st15">Appliance</tspan></text>		</g>
		<g id="shape25-101" v:mID="25" v:groupContext="shape" transform="translate(476.65,-254.681)">
			<title>Rectangle.25</title>
			<desc>VM + Container</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="34.05" cy="300.937" width="68.1" height="54.1807"/>
			<g id="shadow25-102" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="273.847" width="68.1" height="54.1807" class="st2"/>
			</g>
			<rect x="0" y="273.847" width="68.1" height="54.1807" class="st3"/>
			<text x="23.32" y="297.94" class="st4" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>VM + <tspan x="14.04"
						dy="1.2em" class="st15">Container</tspan></text>		</g>
		<g id="shape27-108" v:mID="27" v:groupContext="shape" transform="translate(566.65,-254.681)">
			<title>Rectangle.27</title>
			<desc>Container</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="34.05" cy="300.937" width="68.1" height="54.1807"/>
			<g id="shadow27-109" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="273.847" width="68.1" height="54.1807" class="st2"/>
			</g>
			<rect x="0" y="273.847" width="68.1" height="54.1807" class="st3"/>
			<text x="14.04" y="291.94" class="st4" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>Container<v:newlineChar/><v:newlineChar/></text>		</g>
		<g id="shape28-114" v:mID="28" v:groupContext="shape" transform="translate(570.625,-261.431)">
			<title>Ellipse.28</title>
			<desc>DPDK</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="28.125" cy="316.777" width="49.22" height="19.6875"/>
			<g id="shadow28-115" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<path d="M0 316.78 A28.125 11.25 0 0 1 56.25 316.78 A28.125 11.25 0 1 1 0 316.78 Z" class="st2"/>
			</g>
			<path d="M0 316.78 A28.125 11.25 0 0 1 56.25 316.78 A28.125 11.25 0 1 1 0 316.78 Z" class="st10"/>
			<text x="16.79" y="319.78" class="st11" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>DPDK</text>		</g>
		<g id="shape29-120" v:mID="29" v:groupContext="shape" transform="translate(405.25,-110.5)">
			<title>Rectangle.29</title>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<g id="shadow29-121" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="270.699" width="346.5" height="57.3286" rx="13.5" ry="13.5" class="st5"/>
			</g>
			<rect x="0" y="270.699" width="346.5" height="57.3286" rx="13.5" ry="13.5" class="st6"/>
		</g>
		<g id="shape30-125" v:mID="30" v:groupContext="shape" transform="translate(405.25,-142)">
			<title>Sheet.30</title>
			<desc>Host kernel</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="34.05" cy="316.777" width="68.1" height="22.5"/>
			<rect x="0" y="305.527" width="68.1" height="22.5" class="st8"/>
			<text x="6.55" y="320.38" class="st9" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>Host kernel</text>		</g>
		<g id="shape31-128" v:mID="31" v:groupContext="shape" transform="translate(681.417,-205)">
			<title>Rectangle.31</title>
			<desc>vSwitch or vRouter</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="34.05" cy="276.277" width="68.1" height="103.5"/>
			<g id="shadow31-129" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="224.527" width="68.1" height="103.5" class="st2"/>
			</g>
			<rect x="0" y="224.527" width="68.1" height="103.5" class="st3"/>
			<text x="18.36" y="255.28" class="st4" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>vSwitch<v:newlineChar/><tspan
						x="29.67" dy="1.2em" class="st15">or<v:newlineChar/></tspan><tspan x="17.91" dy="1.2em" class="st15">vRouter</tspan><v:newlineChar/><v:newlineChar/></text>		</g>
		<g id="shape32-136" v:mID="32" v:groupContext="shape" transform="translate(687.342,-214)">
			<title>Ellipse.32</title>
			<desc>DPDK</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="28.125" cy="316.777" width="49.22" height="19.6875"/>
			<g id="shadow32-137" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<path d="M0 316.78 A28.125 11.25 0 0 1 56.25 316.78 A28.125 11.25 0 1 1 0 316.78 Z" class="st2"/>
			</g>
			<path d="M0 316.78 A28.125 11.25 0 0 1 56.25 316.78 A28.125 11.25 0 1 1 0 316.78 Z" class="st10"/>
			<text x="16.79" y="319.78" class="st11" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>DPDK</text>		</g>
		<g id="shape34-142" v:mID="34" v:groupContext="shape" v:layerMember="0" transform="translate(429.7,-254.5)">
			<title>Dynamic connector</title>
			<path d="M0 334.71 L0 335.07 L0 364.03 L244.68 364.03" class="st13"/>
		</g>
		<g id="shape35-149" v:mID="35" v:groupContext="shape" v:layerMember="0" transform="translate(510.7,-254.681)">
			<title>Dynamic connector.35</title>
			<path d="M0 334.71 L0 335.07 L0 355.21 L163.68 355.21" class="st13"/>
		</g>
		<g id="shape36-156" v:mID="36" v:groupContext="shape" v:layerMember="0" transform="translate(600.7,-254.681)">
			<title>Dynamic connector.36</title>
			<path d="M0 334.71 L0 335.07 L0 346.21 L73.68 346.21" class="st13"/>
		</g>
		<g id="shape37-163" v:mID="37" v:groupContext="shape" transform="translate(557.933,-182.5)">
			<title>Rectangle.37</title>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<g id="shadow37-164" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<path d="M0 328.03 L202.82 328.03 L202.82 184.03 L0 184.03 L0 328.03 Z" class="st16"/>
			</g>
			<path d="M0 328.03 L202.82 328.03 L202.82 184.03 L0 184.03 L0 328.03 Z" class="st17"/>
		</g>
		<g id="shape38-168" v:mID="38" v:groupContext="shape" transform="translate(676.9,-72.25)">
			<title>Rectangle.38</title>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<g id="shadow38-169" v:groupContext="shadow" v:shadowOffsetX="0.345598" v:shadowOffsetY="-1.97279" v:shadowType="1"
					transform="matrix(1,0,0,1,0.345598,1.97279)" class="st1">
				<rect x="0" y="305.527" width="74.85" height="22.5" rx="11.25" ry="11.25" class="st2"/>
			</g>
			<rect x="0" y="305.527" width="74.85" height="22.5" rx="11.25" ry="11.25" class="st7"/>
		</g>
		<g id="shape39-173" v:mID="39" v:groupContext="shape" transform="translate(686.2,-72.25)">
			<title>Sheet.39</title>
			<desc>NIC</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="28.125" cy="316.777" width="56.25" height="22.5"/>
			<rect x="0" y="305.527" width="56.25" height="22.5" class="st8"/>
			<text x="19.61" y="320.38" class="st9" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>NIC</text>		</g>
		<g id="shape41-176" v:mID="41" v:groupContext="shape" v:layerMember="0" transform="translate(723.896,-205)">
			<title>Dynamic connector.41</title>
			<path d="M-8.5 334.71 L-8.5 335.07 L-9.5 431.24" class="st13"/>
		</g>
		<g id="shape42-183" v:mID="42" v:groupContext="shape" v:layerMember="0" transform="translate(382.75,-317.5)">
			<title>Dynamic connector.42</title>
			<path d="M-9 328.03 L-9 589.03" class="st18"/>
		</g>
		<g id="shape43-186" v:mID="43" v:groupContext="shape" transform="translate(161.65,-0.25)">
			<title>Sheet.43</title>
			<desc>(1) Slicing</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="34.05" cy="316.777" width="68.1" height="22.5"/>
			<rect x="0" y="305.527" width="68.1" height="22.5" class="st8"/>
			<text x="10.5" y="320.38" class="st9" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>(1) Slicing</text>		</g>
		<g id="shape44-189" v:mID="44" v:groupContext="shape" transform="translate(553.75,-0.25)">
			<title>Sheet.44</title>
			<desc>(2) Aggregation</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="44.025" cy="316.777" width="88.05" height="22.5"/>
			<rect x="0" y="305.527" width="88.05" height="22.5" class="st8"/>
			<text x="5.7" y="320.38" class="st9" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>(2) Aggregation</text>		</g>
	</g>
</svg>
