<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by Microsoft Visio, SVG Export scheduler-fan.svg Page-1 -->
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:ev="http://www.w3.org/2001/xml-events"
		xmlns:v="http://schemas.microsoft.com/visio/2003/SVGExtensions/" width="6.81229in" height="3.40992in"
		viewBox="0 0 490.485 245.514" xml:space="preserve" color-interpolation-filters="sRGB" class="st10">
	<v:documentProperties v:langID="1033" v:metric="true" v:viewMarkup="false"/>

	<style type="text/css">
	<![CDATA[
		.st1 {visibility:visible}
		.st2 {fill:#fec000;fill-opacity:0.25;filter:url(#filter_2);stroke:#fec000;stroke-opacity:0.25}
		.st3 {fill:#cc3399;stroke:#ff8c00;stroke-width:3}
		.st4 {fill:#ffffff;font-family:Calibri;font-size:1.33333em}
		.st5 {fill:#ff9900;stroke:#ff8c00;stroke-width:3}
		.st6 {fill:#ffffff;font-family:Calibri;font-size:1.33333em;font-weight:bold}
		.st7 {fill:#ffc000;stroke:#ffffff;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.5}
		.st8 {marker-end:url(#mrkr4-40);stroke:#ff0000;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.5}
		.st9 {fill:#ff0000;fill-opacity:1;stroke:#ff0000;stroke-opacity:1;stroke-width:0.37313432835821}
		.st10 {fill:none;fill-rule:evenodd;font-size:12px;overflow:visible;stroke-linecap:square;stroke-miterlimit:3}
	]]>
	</style>

	<defs id="Markers">
		<g id="lend4">
			<path d="M 2 1 L 0 0 L 2 -1 L 2 1 " style="stroke:none"/>
		</g>
		<marker id="mrkr4-40" class="st9" v:arrowType="4" v:arrowSize="2" v:setback="5.36" refX="-5.36" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend4" transform="scale(-2.68,-2.68) "/>
		</marker>
	</defs>
	<defs id="Filters">
		<filter id="filter_2">
			<feGaussianBlur stdDeviation="2"/>
		</filter>
	</defs>
	<g v:mID="0" v:index="1" v:groupContext="foregroundPage">
		<title>Page-1</title>
		<v:pageProperties v:drawingScale="0.0393701" v:pageScale="0.0393701" v:drawingUnits="24" v:shadowOffsetX="8.50394"
				v:shadowOffsetY="-8.50394"/>
		<v:layer v:name="Connector" v:index="0"/>
		<g id="shape31-1" v:mID="31" v:groupContext="shape" transform="translate(4.15435,-179.702)">
			<title>Rounded Rectangle.55</title>
			<desc>User Application</desc>
			<v:userDefs>
				<v:ud v:nameU="CTypeTopLeftSnip" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="CTypeTopRightSnip" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="CTypeBotLeftSnip" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="CTypeBotRightSnip" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="CornerLockHoriz" v:prompt="" v:val="VT0(1):5"/>
				<v:ud v:nameU="CornerLockVert" v:prompt="" v:val="VT0(1):5"/>
				<v:ud v:nameU="CornerLockDiag" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="TopLeftOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
				<v:ud v:nameU="TopRightOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
				<v:ud v:nameU="BotLeftOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
				<v:ud v:nameU="BotRightOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
				<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
				<v:ud v:nameU="TopLeftOffset" v:prompt="" v:val="VT0(0.15348434426561):1"/>
				<v:ud v:nameU="TopRightOffset" v:prompt="" v:val="VT0(0.15348434426561):1"/>
				<v:ud v:nameU="BotLeftOffset" v:prompt="" v:val="VT0(0.15348434426561):1"/>
				<v:ud v:nameU="BotRightOffset" v:prompt="" v:val="VT0(0.15348434426561):1"/>
				<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
			<v:textRect cx="240.743" cy="214.108" width="481.49" height="62.8119"/>
			<g id="shadow31-2" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
					transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
				<path d="M11.05 245.51 L470.43 245.51 A11.0507 11.0507 -180 0 0 481.49 234.46 L481.49 193.75 A11.0507 11.0507 -180
							 0 0 470.43 182.7 L11.05 182.7 A11.0507 11.0507 -180 0 0 -0 193.75 L0 234.46 A11.0507 11.0507 -180 0
							 0 11.05 245.51 Z" class="st2"/>
			</g>
			<path d="M11.05 245.51 L470.43 245.51 A11.0507 11.0507 -180 0 0 481.49 234.46 L481.49 193.75 A11.0507 11.0507 -180 0
						 0 470.43 182.7 L11.05 182.7 A11.0507 11.0507 -180 0 0 -0 193.75 L0 234.46 A11.0507 11.0507 -180 0 0 11.05
						 245.51 Z" class="st3"/>
			<text x="187.04" y="218.91" class="st4" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>User Application</text>		</g>
		<g id="shape135-7" v:mID="135" v:groupContext="shape" transform="translate(4.15435,-6.4728)">
			<title>Rounded Rectangle.135</title>
			<desc>Cryptodev</desc>
			<v:userDefs>
				<v:ud v:nameU="CTypeTopLeftSnip" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="CTypeTopRightSnip" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="CTypeBotLeftSnip" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="CTypeBotRightSnip" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="CornerLockHoriz" v:prompt="" v:val="VT0(1):5"/>
				<v:ud v:nameU="CornerLockVert" v:prompt="" v:val="VT0(1):5"/>
				<v:ud v:nameU="CornerLockDiag" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="TopLeftOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
				<v:ud v:nameU="TopRightOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
				<v:ud v:nameU="BotLeftOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
				<v:ud v:nameU="BotRightOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
				<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
				<v:ud v:nameU="TopLeftOffset" v:prompt="" v:val="VT0(0.045922865409173):1"/>
				<v:ud v:nameU="TopRightOffset" v:prompt="" v:val="VT0(0.045922865409173):1"/>
				<v:ud v:nameU="BotLeftOffset" v:prompt="" v:val="VT0(0.045922865409173):1"/>
				<v:ud v:nameU="BotRightOffset" v:prompt="" v:val="VT0(0.045922865409173):1"/>
				<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
			<v:textRect cx="72.0307" cy="230.549" width="144.07" height="29.9308"/>
			<g id="shadow135-8" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
					transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
				<path d="M3.31 245.51 L140.76 245.51 A3.30639 3.30639 -180 0 0 144.06 242.21 L144.06 218.89 A3.30639 3.30639 -180
							 0 0 140.76 215.58 L3.31 215.58 A3.30639 3.30639 -180 0 0 0 218.89 L0 242.21 A3.30639 3.30639 -180 0
							 0 3.31 245.51 Z" class="st2"/>
			</g>
			<path d="M3.31 245.51 L140.76 245.51 A3.30639 3.30639 -180 0 0 144.06 242.21 L144.06 218.89 A3.30639 3.30639 -180 0 0
						 140.76 215.58 L3.31 215.58 A3.30639 3.30639 -180 0 0 0 218.89 L0 242.21 A3.30639 3.30639 -180 0 0 3.31 245.51
						 Z" class="st5"/>
			<text x="38.46" y="235.35" class="st4" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Cryptodev</text>		</g>
		<g id="shape136-13" v:mID="136" v:groupContext="shape" transform="translate(172.866,-6.4728)">
			<title>Rounded Rectangle.136</title>
			<desc>Cryptodev</desc>
			<v:userDefs>
				<v:ud v:nameU="CTypeTopLeftSnip" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="CTypeTopRightSnip" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="CTypeBotLeftSnip" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="CTypeBotRightSnip" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="CornerLockHoriz" v:prompt="" v:val="VT0(1):5"/>
				<v:ud v:nameU="CornerLockVert" v:prompt="" v:val="VT0(1):5"/>
				<v:ud v:nameU="CornerLockDiag" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="TopLeftOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
				<v:ud v:nameU="TopRightOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
				<v:ud v:nameU="BotLeftOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
				<v:ud v:nameU="BotRightOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
				<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
				<v:ud v:nameU="TopLeftOffset" v:prompt="" v:val="VT0(0.045922865409173):1"/>
				<v:ud v:nameU="TopRightOffset" v:prompt="" v:val="VT0(0.045922865409173):1"/>
				<v:ud v:nameU="BotLeftOffset" v:prompt="" v:val="VT0(0.045922865409173):1"/>
				<v:ud v:nameU="BotRightOffset" v:prompt="" v:val="VT0(0.045922865409173):1"/>
				<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
			<v:textRect cx="72.0307" cy="230.549" width="144.07" height="29.9308"/>
			<g id="shadow136-14" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
					transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
				<path d="M3.31 245.51 L140.76 245.51 A3.30639 3.30639 -180 0 0 144.06 242.21 L144.06 218.89 A3.30639 3.30639 -180
							 0 0 140.76 215.58 L3.31 215.58 A3.30639 3.30639 -180 0 0 0 218.89 L0 242.21 A3.30639 3.30639 -180 0
							 0 3.31 245.51 Z" class="st2"/>
			</g>
			<path d="M3.31 245.51 L140.76 245.51 A3.30639 3.30639 -180 0 0 144.06 242.21 L144.06 218.89 A3.30639 3.30639 -180 0 0
						 140.76 215.58 L3.31 215.58 A3.30639 3.30639 -180 0 0 0 218.89 L0 242.21 A3.30639 3.30639 -180 0 0 3.31 245.51
						 Z" class="st5"/>
			<text x="38.46" y="235.35" class="st4" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Cryptodev</text>		</g>
		<g id="shape137-19" v:mID="137" v:groupContext="shape" transform="translate(341.578,-6.4728)">
			<title>Rounded Rectangle.137</title>
			<desc>Cryptodev</desc>
			<v:userDefs>
				<v:ud v:nameU="CTypeTopLeftSnip" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="CTypeTopRightSnip" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="CTypeBotLeftSnip" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="CTypeBotRightSnip" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="CornerLockHoriz" v:prompt="" v:val="VT0(1):5"/>
				<v:ud v:nameU="CornerLockVert" v:prompt="" v:val="VT0(1):5"/>
				<v:ud v:nameU="CornerLockDiag" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="TopLeftOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
				<v:ud v:nameU="TopRightOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
				<v:ud v:nameU="BotLeftOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
				<v:ud v:nameU="BotRightOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
				<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
				<v:ud v:nameU="TopLeftOffset" v:prompt="" v:val="VT0(0.045922865409173):1"/>
				<v:ud v:nameU="TopRightOffset" v:prompt="" v:val="VT0(0.045922865409173):1"/>
				<v:ud v:nameU="BotLeftOffset" v:prompt="" v:val="VT0(0.045922865409173):1"/>
				<v:ud v:nameU="BotRightOffset" v:prompt="" v:val="VT0(0.045922865409173):1"/>
				<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
			<v:textRect cx="72.0307" cy="230.549" width="144.07" height="29.9308"/>
			<g id="shadow137-20" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
					transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
				<path d="M3.31 245.51 L140.76 245.51 A3.30639 3.30639 -180 0 0 144.06 242.21 L144.06 218.89 A3.30639 3.30639 -180
							 0 0 140.76 215.58 L3.31 215.58 A3.30639 3.30639 -180 0 0 0 218.89 L0 242.21 A3.30639 3.30639 -180 0
							 0 3.31 245.51 Z" class="st2"/>
			</g>
			<path d="M3.31 245.51 L140.76 245.51 A3.30639 3.30639 -180 0 0 144.06 242.21 L144.06 218.89 A3.30639 3.30639 -180 0 0
						 140.76 215.58 L3.31 215.58 A3.30639 3.30639 -180 0 0 0 218.89 L0 242.21 A3.30639 3.30639 -180 0 0 3.31 245.51
						 Z" class="st5"/>
			<text x="38.46" y="235.35" class="st4" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Cryptodev</text>		</g>
		<g id="group139-25" transform="translate(4.15435,-66.8734)" v:mID="139" v:groupContext="group">
			<title>Sheet.139</title>
			<g id="shape33-26" v:mID="33" v:groupContext="shape">
				<title>Rounded Rectangle.40</title>
				<desc>Cryptodev Scheduler</desc>
				<v:userDefs>
					<v:ud v:nameU="CTypeTopLeftSnip" v:prompt="" v:val="VT0(0):5"/>
					<v:ud v:nameU="CTypeTopRightSnip" v:prompt="" v:val="VT0(0):5"/>
					<v:ud v:nameU="CTypeBotLeftSnip" v:prompt="" v:val="VT0(0):5"/>
					<v:ud v:nameU="CTypeBotRightSnip" v:prompt="" v:val="VT0(0):5"/>
					<v:ud v:nameU="CornerLockHoriz" v:prompt="" v:val="VT0(1):5"/>
					<v:ud v:nameU="CornerLockVert" v:prompt="" v:val="VT0(1):5"/>
					<v:ud v:nameU="CornerLockDiag" v:prompt="" v:val="VT0(0):5"/>
					<v:ud v:nameU="TopLeftOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
					<v:ud v:nameU="TopRightOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
					<v:ud v:nameU="BotLeftOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
					<v:ud v:nameU="BotRightOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
					<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					<v:ud v:nameU="TopLeftOffset" v:prompt="" v:val="VT0(0.15348434426561):1"/>
					<v:ud v:nameU="TopRightOffset" v:prompt="" v:val="VT0(0.15348434426561):1"/>
					<v:ud v:nameU="BotLeftOffset" v:prompt="" v:val="VT0(0.15348434426561):1"/>
					<v:ud v:nameU="BotRightOffset" v:prompt="" v:val="VT0(0.15348434426561):1"/>
					<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197" v:verticalAlign="0"/>
				<v:textRect cx="240.743" cy="204.056" width="481.49" height="82.916"/>
				<g id="shadow33-27" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
					<path d="M11.05 245.51 L470.43 245.51 A11.0507 11.0507 -180 0 0 481.49 234.46 L481.49 173.65 A11.0507 11.0507
								 -180 0 0 470.43 162.6 L11.05 162.6 A11.0507 11.0507 -180 0 0 0 173.65 L0 234.46 A11.0507 11.0507
								 -180 0 0 11.05 245.51 Z" class="st2"/>
				</g>
				<path d="M11.05 245.51 L470.43 245.51 A11.0507 11.0507 -180 0 0 481.49 234.46 L481.49 173.65 A11.0507 11.0507 -180
							 0 0 470.43 162.6 L11.05 162.6 A11.0507 11.0507 -180 0 0 0 173.65 L0 234.46 A11.0507 11.0507 -180 0 0
							 11.05 245.51 Z" class="st5"/>
				<text x="171.72" y="181" class="st6" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Cryptodev Scheduler</text>			</g>
			<g id="shape138-32" v:mID="138" v:groupContext="shape" transform="translate(24.6009,-12.5889)">
				<title>Rounded Rectangle.138</title>
				<desc>Crypto Op Distribution Mechanism</desc>
				<v:userDefs>
					<v:ud v:nameU="CTypeTopLeftSnip" v:prompt="" v:val="VT0(0):5"/>
					<v:ud v:nameU="CTypeTopRightSnip" v:prompt="" v:val="VT0(0):5"/>
					<v:ud v:nameU="CTypeBotLeftSnip" v:prompt="" v:val="VT0(0):5"/>
					<v:ud v:nameU="CTypeBotRightSnip" v:prompt="" v:val="VT0(0):5"/>
					<v:ud v:nameU="CornerLockHoriz" v:prompt="" v:val="VT0(1):5"/>
					<v:ud v:nameU="CornerLockVert" v:prompt="" v:val="VT0(1):5"/>
					<v:ud v:nameU="CornerLockDiag" v:prompt="" v:val="VT0(0):5"/>
					<v:ud v:nameU="TopLeftOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
					<v:ud v:nameU="TopRightOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
					<v:ud v:nameU="BotLeftOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
					<v:ud v:nameU="BotRightOffset" v:prompt="" v:val="VT0(0.15748031496063):24"/>
					<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					<v:ud v:nameU="TopLeftOffset" v:prompt="" v:val="VT0(0.13780016666367):1"/>
					<v:ud v:nameU="TopRightOffset" v:prompt="" v:val="VT0(0.13780016666367):1"/>
					<v:ud v:nameU="BotLeftOffset" v:prompt="" v:val="VT0(0.13780016666367):1"/>
					<v:ud v:nameU="BotRightOffset" v:prompt="" v:val="VT0(0.13780016666367):1"/>
					<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
				<v:textRect cx="216.142" cy="230.549" width="432.29" height="29.9308"/>
				<path d="M9.92 245.51 L422.36 245.51 A9.92145 9.92145 -180 0 0 432.28 235.59 L432.28 225.51 A9.92145 9.92145 -180
							 0 0 422.36 215.58 L9.92 215.58 A9.92145 9.92145 -180 0 0 0 225.51 L0 235.59 A9.92145 9.92145 -180 0
							 0 9.92 245.51 Z" class="st7"/>
				<text x="103.11" y="235.35" class="st4" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Crypto Op Distribution Mechanism</text>			</g>
		</g>
		<g id="shape140-35" v:mID="140" v:groupContext="shape" v:layerMember="0" transform="translate(234.378,-149.789)">
			<title>Dynamic connector.229</title>
			<path d="M7.09 245.51 L7.09 223.64" class="st8"/>
		</g>
		<g id="shape141-41" v:mID="141" v:groupContext="shape" v:layerMember="0" transform="translate(248.551,-179.702)">
			<title>Dynamic connector.141</title>
			<path d="M7.09 245.51 L7.09 267.39" class="st8"/>
		</g>
		<g id="shape142-46" v:mID="142" v:groupContext="shape" v:layerMember="0" transform="translate(71.3856,-35.6203)">
			<title>Dynamic connector.142</title>
			<path d="M7.09 245.51 L7.09 223.64" class="st8"/>
		</g>
		<g id="shape143-51" v:mID="143" v:groupContext="shape" v:layerMember="0" transform="translate(85.5588,-65.5333)">
			<title>Dynamic connector.143</title>
			<path d="M7.09 245.51 L7.09 267.39" class="st8"/>
		</g>
		<g id="shape144-56" v:mID="144" v:groupContext="shape" v:layerMember="0" transform="translate(234.378,-35.6203)">
			<title>Dynamic connector.144</title>
			<path d="M7.09 245.51 L7.09 223.64" class="st8"/>
		</g>
		<g id="shape145-61" v:mID="145" v:groupContext="shape" v:layerMember="0" transform="translate(248.551,-65.5333)">
			<title>Dynamic connector.145</title>
			<path d="M7.09 245.51 L7.09 267.39" class="st8"/>
		</g>
		<g id="shape146-66" v:mID="146" v:groupContext="shape" v:layerMember="0" transform="translate(397.37,-34.837)">
			<title>Dynamic connector.146</title>
			<path d="M7.09 245.51 L7.09 223.64" class="st8"/>
		</g>
		<g id="shape147-71" v:mID="147" v:groupContext="shape" v:layerMember="0" transform="translate(411.543,-64.75)">
			<title>Dynamic connector.147</title>
			<path d="M7.09 245.51 L7.09 267.39" class="st8"/>
		</g>
	</g>
</svg>
