;
; Supported features of the 'ionic' crypto driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Symmetric crypto       = Y
HW Accelerated         = Y
In Place SGL           = Y
OOP SGL In LB  Out     = Y
OOP SGL In SGL Out     = Y
OOP LB  In LB  Out     = Y

;
; Supported crypto algorithms of 'ionic' crypto driver.
;
[Cipher]

;
; Supported authentication algorithms of 'ionic' crypto driver.
;
[Auth]

;
; Supported AEAD algorithms of 'ionic' crypto driver.
;
[AEAD]
AES GCM (128)     = Y
AES GCM (256)     = Y

;
; Supported Asymmetric algorithms of the 'ionic' crypto driver.
;
[Asymmetric]

;
; Supported Operating systems of the 'ionic' crypto driver.
;
[OS]
Linux = Y
