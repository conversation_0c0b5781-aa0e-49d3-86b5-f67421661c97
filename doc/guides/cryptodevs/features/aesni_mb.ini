;
; Supported features of the 'aesni_mb' crypto driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Symmetric crypto       = Y
Sym operation chaining = Y
Protocol offload       = Y
CPU SSE                = Y
CPU AVX                = Y
CPU AVX2               = Y
CPU AVX512             = Y
CPU AESNI              = Y
In Place SGL           = Y
OOP SGL In SGL Out     = Y
OOP SGL In LB  Out     = Y
OOP LB  In SGL Out     = Y
OOP LB  In LB  Out     = Y
CPU crypto             = Y
Symmetric sessionless  = Y
Non-Byte aligned data  = Y
Digest encrypted       = Y

;
; Supported crypto algorithms of the 'aesni_mb' crypto driver.
;
[Cipher]
AES CBC (128)  = Y
AES CBC (192)  = Y
AES CBC (256)  = Y
AES CTR (128)  = Y
AES CTR (192)  = Y
AES CTR (256)  = Y
AES DOCSIS BPI = Y
DES CBC        = Y
3DES CBC       = Y
DES DOCSIS BPI = Y
AES ECB (128)  = Y
AES ECB (192)  = Y
AES ECB (256)  = Y
ZUC EEA3       = Y
SNOW3G UEA2    = Y
KASUMI F8      = Y
SM4 CBC        = Y
SM4 ECB        = Y
SM4 CTR        = Y

;
; Supported authentication algorithms of the 'aesni_mb' crypto driver.
;
[Auth]
MD5 HMAC     = Y
SHA1         = Y
SHA1 HMAC    = Y
SHA224       = Y
SHA224 HMAC  = Y
SHA256       = Y
SHA256 HMAC  = Y
SHA384       = Y
SHA384 HMAC  = Y
SHA512       = Y
SHA512 HMAC  = Y
AES XCBC MAC = Y
AES CMAC (128)  = Y
AES GMAC     = Y
ZUC EIA3     = Y
SNOW3G UIA2  = Y
KASUMI F9    = Y
SM3          = Y
SM3 HMAC     = Y

;
; Supported AEAD algorithms of the 'aesni_mb' crypto driver.
;
[AEAD]
AES CCM (128)     = Y
AES CCM (256)     = Y
AES GCM (128)     = Y
AES GCM (192)     = Y
AES GCM (256)     = Y
CHACHA20-POLY1305 = Y
SM4 GCM           = Y

;
; Supported Asymmetric algorithms of the 'aesni_mb' crypto driver.
;
[Asymmetric]

;
; Supported Operating systems of the 'aesni_mb' crypto driver.
;
[OS]
Linux = Y
FreeBSD = Y
