;
; Supported features of the 'qat' crypto driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Symmetric crypto       = Y
Sym operation chaining = Y
HW Accelerated         = Y
Protocol offload       = Y
In Place SGL           = Y
OOP SGL In SGL Out     = Y
OOP SGL In LB  Out     = Y
OOP LB  In SGL Out     = Y
OOP LB  In LB  Out     = Y
Digest encrypted       = Y
Asymmetric sessionless = Y
RSA PRIV OP KEY EXP    = Y
RSA PRIV OP KEY QT     = Y
Sym raw data path API  = Y

;
; Supported crypto algorithms of the 'qat' crypto driver.
;
[Cipher]
NULL           = Y
AES CBC (128)  = Y
AES CBC (192)  = Y
AES CBC (256)  = Y
AES CTR (128)  = Y
AES CTR (192)  = Y
AES CTR (256)  = Y
AES XTS (128)  = Y
AES XTS (256)  = Y
3DES CBC       = Y
3DES CTR       = Y
DES CBC        = Y
SNOW3G UEA2    = Y
KASUMI F8      = Y
AES DOCSIS BPI = Y
DES DOCSIS BPI = Y
ZUC EEA3       = Y
SM4 ECB        = Y
SM4 CBC        = Y
SM4 CTR        = Y
;
; Supported authentication algorithms of the 'qat' crypto driver.
;
[Auth]
NULL         = Y
MD5 HMAC     = Y
SHA1         = Y
SHA1 HMAC    = Y
SHA224       = Y
SHA224 HMAC  = Y
SHA256       = Y
SHA256 HMAC  = Y
SHA384       = Y
SHA384 HMAC  = Y
SHA512       = Y
SHA512 HMAC  = Y
SHA3_224     = Y
SHA3_256     = Y
SHA3_384     = Y
SHA3_512     = Y
AES GMAC     = Y
SNOW3G UIA2  = Y
KASUMI F9    = Y
AES XCBC MAC = Y
ZUC EIA3     = Y
AES CMAC (128) = Y
SM3          = Y
SM3 HMAC     = Y

;
; Supported AEAD algorithms of the 'qat' crypto driver.
;
[AEAD]
AES GCM (128)     = Y
AES GCM (192)     = Y
AES GCM (256)     = Y
AES CCM (128)     = Y
AES CCM (192)     = Y
AES CCM (256)     = Y
CHACHA20-POLY1305 = Y

;
; Supported Asymmetric algorithms of the 'qat' crypto driver.
;
[Asymmetric]
Modular Exponentiation  = Y
Modular Inversion	= Y
RSA			= Y
ECDSA                   = Y
ECPM                    = Y
ECDH                    = Y
SM2                     = Y

;
; Supported Operating systems of the 'qat' crypto driver.
;
[OS]
Linux = Y
FreeBSD = Y
