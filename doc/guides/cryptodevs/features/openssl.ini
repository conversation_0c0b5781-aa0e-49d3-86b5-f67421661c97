;
; Supported features of the 'openssl' crypto driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Symmetric crypto       = Y
Sym operation chaining = Y
In Place SGL           = Y
OOP SGL In LB  Out     = Y
OOP LB  In LB  Out     = Y
Asymmetric crypto      = Y
RSA PRIV OP KEY EXP    = Y
RSA PRIV OP KEY QT     = Y
Symmetric sessionless  = Y

;
; Supported crypto algorithms of the 'openssl' crypto driver.
;
[Cipher]
AES CBC (128)  = Y
AES CBC (192)  = Y
AES CBC (256)  = Y
AES CTR (128)  = Y
AES CTR (192)  = Y
AES CTR (256)  = Y
3DES CBC       = Y
3DES CTR       = Y
DES DOCSIS BPI = Y
;
; Supported authentication algorithms of the 'openssl' crypto driver.
;
[Auth]
MD5          = Y
MD5 HMAC     = Y
SHA1         = Y
SHA1 HMAC    = Y
SHA224       = Y
SHA224 HMAC  = Y
SHA256       = Y
SHA256 HMAC  = Y
SHA384       = Y
SHA384 HMAC  = Y
SHA512       = Y
SHA512 HMAC  = Y
AES GMAC     = Y

;
; Supported AEAD algorithms of the 'openssl' crypto driver.
;
[AEAD]
AES GCM (128) = Y
AES GCM (192) = Y
AES GCM (256) = Y
AES CCM (128) = Y
AES CCM (192) = Y
AES CCM (256) = Y

;
; Supported Asymmetric algorithms of the 'openssl' crypto driver.
;
[Asymmetric]
RSA = Y
DSA = Y
Modular Exponentiation = Y
Modular Inversion = Y
Diffie-hellman = Y
SM2 = Y
EdDSA = Y

;
; Supported Operating systems of the 'openssl' crypto driver.
;
[OS]
Linux = Y
FreeBSD = Y
