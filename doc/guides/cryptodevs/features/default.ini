;
; Features of a default crypto driver.
;
; This file defines the features that are valid for inclusion in
; the other driver files and also the order that they appear in
; the features table in the documentation.
;
[Features]
Symmetric crypto       =
Asymmetric crypto      =
Sym operation chaining =
HW Accelerated         =
Protocol offload       =
CPU SSE                =
CPU AVX                =
CPU AVX2               =
CPU AVX512             =
CPU AESNI              =
CPU NEON               =
CPU ARM CE             =
In Place SGL           =
OOP SGL In SGL Out     =
OOP SGL In LB  Out     =
OOP LB  In SGL Out     =
OOP LB  In LB  Out     =
RSA PRIV OP KEY EXP    =
RSA PRIV OP KEY QT     =
Digest encrypted       =
Asymmetric sessionless =
CPU crypto             =
Symmetric sessionless  =
Non-Byte aligned data  =
Sym raw data path API  =
Cipher multiple data units =
Cipher wrapped key     =
Inner checksum         =
Rx inject              =

;
; Supported crypto algorithms of a default crypto driver.
;
[Cipher]
NULL           =
AES CBC (128)  =
AES CBC (192)  =
AES CBC (256)  =
AES ECB (128)  =
AES ECB (192)  =
AES ECB (256)  =
AES CTR (128)  =
AES CTR (192)  =
AES CTR (256)  =
AES XTS (128)  =
AES XTS (192)  =
AES XTS (256)  =
AES DOCSIS BPI =
3DES CBC       =
3DES CTR       =
3DES ECB       =
DES CBC        =
DES DOCSIS BPI =
SNOW3G UEA2    =
KASUMI F8      =
ZUC EEA3       =
SM4 ECB        =
SM4 CBC        =
SM4 CTR        =
SM4 CFB        =
SM4 OFB        =
SM4 XTS        =

;
; Supported authentication algorithms of a default crypto driver.
;
[Auth]
NULL         =
MD5          =
MD5 HMAC     =
SHA1         =
SHA1 HMAC    =
SHA224       =
SHA224 HMAC  =
SHA256       =
SHA256 HMAC  =
SHA384       =
SHA384 HMAC  =
SHA512       =
SHA512 HMAC  =
AES XCBC MAC =
AES GMAC     =
SNOW3G UIA2  =
KASUMI F9    =
ZUC EIA3     =
AES CMAC (128)  =
AES CMAC (192)  =
AES CMAC (256)  =
SHA3_224        =
SHA3_224 HMAC   =
SHA3_256        =
SHA3_256 HMAC   =
SHA3_384        =
SHA3_384 HMAC   =
SHA3_512        =
SHA3_512 HMAC   =
SM3             =
SM3 HMAC        =
SHAKE_128       =
SHAKE_256       =

;
; Supported AEAD algorithms of a default crypto driver.
;
[AEAD]
AES GCM (128)     =
AES GCM (192)     =
AES GCM (256)     =
AES CCM (128)     =
AES CCM (192)     =
AES CCM (256)     =
CHACHA20-POLY1305 =
SM4 GCM           =

;
; Supported Asymmetric algorithms of a default crypto driver.
;
[Asymmetric]
RSA                     =
DSA                     =
Modular Exponentiation  =
Modular Inversion       =
Diffie-hellman          =
ECDSA                   =
ECPM                    =
ECDH                    =
SM2                     =
EdDSA                   =

;
; Supported Operating systems of a default crypto driver.
;
[OS]
Linux   =
FreeBSD =
Windows =
