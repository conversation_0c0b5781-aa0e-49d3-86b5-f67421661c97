;
; Supported features of the 'zuc' crypto driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Symmetric crypto       = Y
Sym operation chaining = Y
Symmetric sessionless  = Y
Non-Byte aligned data  = Y
OOP LB  In LB  Out     = Y

;
; Supported crypto algorithms of the 'zuc' crypto driver.
;
[Cipher]
ZUC EEA3 = Y
;
; Supported authentication algorithms of the 'zuc' crypto driver.
;
[Auth]
ZUC EIA3 = Y

;
; Supported AEAD algorithms of the 'zuc' crypto driver.
;
[AEAD]

;
; Supported Asymmetric algorithms of the 'zuc' crypto driver.
;
[Asymmetric]

;
; Supported Operating systems of the 'zuc' crypto driver.
;
[OS]
Linux = Y
FreeBSD = Y
