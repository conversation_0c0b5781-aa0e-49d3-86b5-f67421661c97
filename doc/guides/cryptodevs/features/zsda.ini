;
; Supported features of the 'zsda' crypto driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Symmetric crypto       = Y
HW Accelerated         = Y
In Place SGL           = Y
OOP SGL In SGL Out     = Y
OOP SGL In LB  Out     = Y
OOP LB  In SGL Out     = Y
OOP LB  In LB  Out     = Y

;
; Supported crypto algorithms of the 'zsda' crypto driver.
;
[Cipher]
AES XTS (128)  = Y
AES XTS (256)  = Y
SM4 XTS        = Y

;
; Supported authentication algorithms of the 'zsda' crypto driver.
;
[Auth]
SHA1         = Y
SHA224       = Y
SHA256       = Y
SHA384       = Y
SHA512       = Y
SM3          = Y


;
; Supported AEAD algorithms of the 'zsda' crypto driver.
;
[AEAD]


;
; Supported Asymmetric algorithms of the 'zsda' crypto driver.
;
[Asymmetric]


;
; Supported Operating systems of the 'zsda' crypto driver.
;
[OS]
Linux = Y
