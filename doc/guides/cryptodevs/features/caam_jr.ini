;
; Supported features of the 'caam_jr' crypto driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Symmetric crypto       = Y
Sym operation chaining = Y
HW Accelerated         = Y
Protocol offload       = Y
In Place SGL           = Y
OOP SGL In SGL Out     = Y
OOP SGL In LB  Out     = Y
OOP LB  In SGL Out     = Y
OOP LB  In LB  Out     = Y

;
; Supported crypto algorithms of the 'dpaa2_sec' crypto driver.
;
[Cipher]
AES CBC (128) = Y
AES CBC (192) = Y
AES CBC (256) = Y
AES CTR (128) = Y
AES CTR (192) = Y
AES CTR (256) = Y
3DES CBC      = Y

;
; Supported authentication algorithms of the 'dpaa2_sec' crypto driver.
;
[Auth]
MD5 HMAC     = Y
SHA1 HMAC    = Y
SHA224 HMAC  = Y
SHA256 HMAC  = Y
SHA384 HMAC  = Y
SHA512 HMAC  = Y

;
; Supported AEAD algorithms of the 'dpaa2_sec' crypto driver.
;
[AEAD]
AES GCM (128) = Y
AES GCM (192) = Y
AES GCM (256) = Y

;
; Supported Asymmetric algorithms of the 'dpaa2_sec' crypto driver.
;
[Asymmetric]

;
; Supported Operating systems of the 'dpaa2_sec' crypto driver.
;
[OS]
Linux = Y
