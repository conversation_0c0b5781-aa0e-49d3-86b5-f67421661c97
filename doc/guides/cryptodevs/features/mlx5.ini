;
; Features of a mlx5 crypto driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Symmetric crypto       = Y
HW Accelerated         = Y
In Place SGL           = Y
OOP SGL In SGL Out     = Y
OOP SGL In LB  Out     = Y
OOP LB  In SGL Out     = Y
OOP LB  In LB  Out     = Y
Cipher multiple data units = Y
Cipher wrapped key     = Y

;
; Supported crypto algorithms of a mlx5 crypto driver.
;
[Cipher]
AES XTS (128)  = Y
AES XTS (256)  = Y

;
; Supported authentication algorithms of a mlx5 crypto driver.
;
[Auth]

;
; Supported AEAD algorithms of a mlx5 crypto driver.
;
[AEAD]
AES GCM (128)  = Y
AES GCM (256)  = Y

;
; Supported Asymmetric algorithms of a mlx5 crypto driver.
;
[Asymmetric]

;
; Supported Operating systems of the 'mlx5' crypto driver.
;
[OS]
Linux = Y
Windows = Y
