;
; Supported features of the 'cn9k' crypto driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Symmetric crypto       = Y
Asymmetric crypto      = Y
Sym operation chaining = Y
HW Accelerated         = Y
Protocol offload       = Y
In Place SGL           = Y
OOP SGL In LB  Out     = Y
OOP SGL In SGL Out     = Y
OOP LB  In LB  Out     = Y
Symmetric sessionless  = Y
RSA PRIV OP KEY EXP    = Y
RSA PRIV OP KEY QT     = Y
Digest encrypted       = Y

;
; Supported crypto algorithms of 'cn9k' crypto driver.
;
[Cipher]
NULL           = Y
3DES CBC       = Y
3DES ECB       = Y
AES CBC (128)  = Y
AES CBC (192)  = Y
AES CBC (256)  = Y
AES CTR (128)  = Y
AES CTR (192)  = Y
AES CTR (256)  = Y
AES XTS (128)  = Y
AES XTS (256)  = Y
DES CBC        = Y
KASUMI F8      = Y
SNOW3G UEA2    = Y
ZUC EEA3       = Y
AES DOCSIS BPI = Y
DES DOCSIS BPI = Y

;
; Supported authentication algorithms of 'cn9k' crypto driver.
;
[Auth]
NULL            = Y
AES GMAC        = Y
KASUMI F9       = Y
MD5             = Y
MD5 HMAC        = Y
SHA1            = Y
SHA1 HMAC       = Y
SHA224          = Y
SHA224 HMAC     = Y
SHA256          = Y
SHA256 HMAC     = Y
SHA384          = Y
SHA384 HMAC     = Y
SHA512          = Y
SHA512 HMAC     = Y
SNOW3G UIA2     = Y
ZUC EIA3        = Y
AES CMAC (128)  = Y
AES CMAC (192)  = Y
AES CMAC (256)  = Y
SHA3_224        = Y
SHA3_224 HMAC   = Y
SHA3_256        = Y
SHA3_256 HMAC   = Y
SHA3_384        = Y
SHA3_384 HMAC   = Y
SHA3_512        = Y
SHA3_512 HMAC   = Y
SHAKE_128       = Y
SHAKE_256       = Y

;
; Supported AEAD algorithms of 'cn9k' crypto driver.
;
[AEAD]
AES GCM (128)     = Y
AES GCM (192)     = Y
AES GCM (256)     = Y
AES CCM (128)     = Y
AES CCM (192)     = Y
AES CCM (256)     = Y
CHACHA20-POLY1305 = Y

;
; Supported Asymmetric algorithms of the 'cn9k' crypto driver.
;
[Asymmetric]
RSA                     = Y
DSA                     =
Modular Exponentiation  = Y
Modular Inversion       =
Diffie-hellman          =
ECDH                    = Y
ECDSA                   = Y
ECPM                    = Y

;
; Supported Operating systems of the 'cn9k' crypto driver.
;
[OS]
Linux = Y
