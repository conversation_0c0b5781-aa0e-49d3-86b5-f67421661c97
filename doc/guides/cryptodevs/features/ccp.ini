;
; Supported features of the 'ccp' crypto poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Symmetric crypto       = Y
Sym operation chaining = Y
HW Accelerated         = Y
Symmetric sessionless  = Y

;
; Supported crypto algorithms of the 'ccp' crypto driver.
;
[Cipher]
AES CBC (128)  = Y
AES CBC (192)  = Y
AES CBC (256)  = Y
AES ECB (128)  = Y
AES ECB (192)  = Y
AES ECB (256)  = Y
AES CTR (128)  = Y
AES CTR (192)  = Y
AES CTR (256)  = Y
3DES CBC       = Y

;
; Supported authentication algorithms of the 'ccp' crypto driver.
;
[Auth]
MD5 HMAC        = Y
SHA1            = Y
SHA1 HMAC       = Y
SHA224	        = Y
SHA224 HMAC     = Y
SHA256          = Y
SHA256 HMAC     = Y
SHA384          = Y
SHA384 HMAC     = Y
SHA512          = Y
SHA512 HMAC     = Y
AES CMAC (128)  = Y
AES CMAC (192)  = Y
AES CMAC (256)  = Y
SHA3_224        = Y
SHA3_224 HMAC   = Y
SHA3_256        = Y
SHA3_256 HMAC   = Y
SHA3_384        = Y
SHA3_384 HMAC   = Y
SHA3_512        = Y
SHA3_512 HMAC   = Y

;
; Supported AEAD algorithms of the 'ccp' crypto driver.
;
[AEAD]
AES GCM (128) = Y
AES GCM (192) = Y
AES GCM (256) = Y

;
; Supported Asymmetric algorithms of the 'ccp' crypto driver.
;
[Asymmetric]

;
; Supported Operating systems of the 'ccp' crypto driver.
;
[OS]
Linux = Y
