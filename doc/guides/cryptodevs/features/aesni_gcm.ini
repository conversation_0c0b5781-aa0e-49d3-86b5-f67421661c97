;
; Supported features of the 'aesni_gcm' crypto driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Symmetric crypto       = Y
Sym operation chaining = Y
CPU AESNI              = Y
CPU SSE                = Y
CPU AVX                = Y
CPU AVX2               = Y
CPU AVX512             = Y
In Place SGL           = Y
OOP SGL In LB  Out     = Y
OOP LB  In LB  Out     = Y
CPU crypto             = Y
Symmetric sessionless  = Y
;
; Supported crypto algorithms of the 'aesni_gcm' crypto driver.
;
[Cipher]

;
; Supported authentication algorithms of the 'aesni_gcm' crypto driver.
;
[Auth]
AES GMAC = Y

;
; Supported AEAD algorithms of the 'aesni_gcm' crypto driver.
;
[AEAD]
AES GCM (128) = Y
AES GCM (192) = Y
AES GCM (256) = Y

;
; Supported Asymmetric algorithms of the 'aesni_gcm' crypto driver.
;
[Asymmetric]

;
; Supported Operating systems of the 'aesni_gcm' crypto driver.
;
[OS]
Linux = Y
FreeBSD = Y
