;
; Supported features of the 'octeontx' crypto driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Symmetric crypto       = Y
Asymmetric crypto      = Y
Sym operation chaining = Y
HW Accelerated         = Y
In Place SGL           = Y
OOP SGL In LB  Out     = Y
OOP SGL In SGL Out     = Y
OOP LB  In LB  Out     = Y
RSA PRIV OP KEY QT     = Y
Digest encrypted       = Y
Symmetric sessionless  = Y

;
; Supported crypto algorithms of 'octeontx' crypto driver.
;
[Cipher]
NULL           = Y
3DES CBC       = Y
3DES ECB       = Y
AES CBC (128)  = Y
AES CBC (192)  = Y
AES CBC (256)  = Y
AES CTR (128)  = Y
AES CTR (192)  = Y
AES CTR (256)  = Y
AES XTS (128)  = Y
AES XTS (256)  = Y
DES CBC        = Y
KASUMI F8      = Y
SNOW3G UEA2    = Y
ZUC EEA3       = Y

;
; Supported authentication algorithms of 'octeontx' crypto driver.
;
[Auth]
NULL         = Y
AES GMAC     = Y
KASUMI F9    = Y
MD5          = Y
MD5 HMAC     = Y
SHA1         = Y
SHA1 HMAC    = Y
SHA224       = Y
SHA224 HMAC  = Y
SHA256       = Y
SHA256 HMAC  = Y
SHA384       = Y
SHA384 HMAC  = Y
SHA512       = Y
SHA512 HMAC  = Y
SNOW3G UIA2  = Y
ZUC EIA3     = Y

;
; Supported AEAD algorithms of 'octeontx' crypto driver.
;
[AEAD]
AES GCM (128) = Y
AES GCM (192) = Y
AES GCM (256) = Y

;
; Supported Asymmetric algorithms of the 'octeontx' crypto driver.
;
[Asymmetric]
RSA                     = Y
DSA                     =
Modular Exponentiation  = Y
Modular Inversion       =
Diffie-hellman          =
ECDSA                   = Y
ECPM                    = Y

;
; Supported Operating systems of the 'octeontx' crypto driver.
;
[OS]
Linux = Y
FreeBSD = Y
