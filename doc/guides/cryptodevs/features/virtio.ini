; Supported features of the 'virtio' crypto driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Symmetric crypto       = Y
Asymmetric crypto      = Y
Sym operation chaining = Y
OOP LB  In LB  Out     = Y
RSA PRIV OP KEY EXP    = Y
RSA PRIV OP KEY QT     = Y

;
; Supported crypto algorithms of the 'virtio' crypto driver.
;
[Cipher]
AES CBC (128)  = Y
AES CBC (192)  = Y
AES CBC (256)  = Y

;
; Supported authentication algorithms of the 'virtio' crypto driver.
;
[Auth]
SHA1 HMAC      = Y

;
; Supported AEAD algorithms of the 'virtio' crypto driver.
;
[AEAD]

;
; Supported Asymmetric algorithms of the 'virtio' crypto driver.
;
[Asymmetric]
RSA                     = Y

;
; Supported Operating systems of the 'virtio' crypto driver.
;
[OS]
Linux = Y
FreeBSD = Y
