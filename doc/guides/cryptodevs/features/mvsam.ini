; Supported features of the 'mvsam' crypto driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Symmetric crypto       = Y
Sym operation chaining = Y
HW Accelerated         = Y
Protocol offload       = Y
OOP SGL In LB  Out     = Y
OOP LB  In LB  Out     = Y

;
; Supported crypto algorithms of a default crypto driver.
;
[Cipher]
NULL           = Y
AES CBC (128)  = Y
AES CBC (192)  = Y
AES CBC (256)  = Y
AES CTR (128)  = Y
AES CTR (192)  = Y
AES CTR (256)  = Y
AES ECB (128)  = Y
AES ECB (192)  = Y
AES ECB (256)  = Y
3DES CBC       = Y
3DES CTR       = Y

;
; Supported authentication algorithms of a default crypto driver.
;
[Auth]
NULL         = Y
MD5          = Y
MD5 HMAC     = Y
SHA1         = Y
SHA1 HMAC    = Y
SHA224       = Y
SHA224 HMAC  = Y
SHA256       = Y
SHA256 HMAC  = Y
SHA384       = Y
SHA384 HMAC  = Y
SHA512       = Y
SHA512 HMAC  = Y
AES GMAC     = Y

;
; Supported AEAD algorithms of a default crypto driver.
;
[AEAD]
AES GCM (128) = Y
AES GCM (192) = Y
AES GCM (256) = Y

;
; Supported Asymmetric algorithms of the 'mvsam' crypto driver.
;
[Asymmetric]

;
; Supported Operating systems of the 'mvsam' crypto driver.
;
[OS]
Linux = Y
FreeBSD = Y
