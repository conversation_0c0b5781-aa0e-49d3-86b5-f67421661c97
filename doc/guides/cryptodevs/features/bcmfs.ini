;
; Supported features of the 'bcmfs' crypto driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Symmetric crypto       = Y
Sym operation chaining = Y
HW Accelerated         = Y
Protocol offload       = Y
OOP LB  In LB  Out     = Y

;
; Supported crypto algorithms of the 'bcmfs' crypto driver.
;
[Cipher]
AES CBC (128)  = Y
AES CBC (192)  = Y
AES CBC (256)  = Y
AES CTR (128)  = Y
AES CTR (192)  = Y
AES CTR (256)  = Y
AES XTS (128)  = Y
AES XTS (256)  = Y
3DES CBC       = Y
DES CBC        = Y
;
; Supported authentication algorithms of the 'bcmfs' crypto driver.
;
[Auth]
MD5 HMAC     = Y
SHA1         = Y
SHA1 HMAC    = Y
SHA224       = Y
SHA224 HMAC  = Y
SHA256       = Y
SHA256 HMAC  = Y
SHA384       = Y
SHA384 HMAC  = Y
SHA512       = Y
SHA512 HMAC  = Y
AES GMAC     = Y
AES CMAC (128) = Y
AES XCBC MAC = Y

;
; Supported AEAD algorithms of the 'bcmfs' crypto driver.
;
[AEAD]
AES GCM (128)     = Y
AES GCM (192)     = Y
AES GCM (256)     = Y
AES CCM (128)     = Y
AES CCM (192)     = Y
AES CCM (256)     = Y

;
; Supported Asymmetric algorithms of the 'bcmfs' crypto driver.
;
[Asymmetric]

;
; Supported Operating systems of the 'bcmfs' crypto driver.
;
[OS]
Linux = Y
FreeBSD = Y
