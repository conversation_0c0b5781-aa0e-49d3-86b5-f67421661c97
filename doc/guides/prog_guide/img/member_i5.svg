<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">
<!-- Generated by Microsoft Visio 11.0, SVG Export, v1.0 memship_i5.svg Page-1 -->
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:ev="http://www.w3.org/2001/xml-events"
		xmlns:v="http://schemas.microsoft.com/visio/2003/SVGExtensions/" width="5.30481in" height="1.96146in"
		viewBox="0 0 381.946 141.225" xml:space="preserve" color-interpolation-filters="sRGB" class="st15">
	<v:documentProperties v:langID="1033" v:viewMarkup="false"/>

	<style type="text/css">
	<![CDATA[
		.st1 {visibility:visible}
		.st2 {fill:#5b9bd5;fill-opacity:0.22;stroke:#5b9bd5;stroke-opacity:0.22;stroke-width:0.25}
		.st3 {fill:#5b9bd5;stroke:#c8c8c8;stroke-width:0.25}
		.st4 {fill:none;stroke:none;stroke-width:0.25}
		.st5 {fill:#ffffff;font-family:Calibri;font-size:1.16666em;font-weight:bold}
		.st6 {marker-end:url(#mrkr5-14);stroke:#5b9bd5;stroke-linecap:round;stroke-linejoin:round;stroke-width:1}
		.st7 {fill:#5b9bd5;fill-opacity:1;stroke:#5b9bd5;stroke-opacity:1;stroke-width:0.28409090909091}
		.st8 {fill:#5b9bd5;font-family:Calibri;font-size:0.833336em}
		.st9 {fill:#5b9bd5;font-family:Calibri;font-size:0.75em}
		.st10 {stroke:#5b9bd5;stroke-linecap:round;stroke-linejoin:round;stroke-width:1}
		.st11 {marker-end:url(#mrkr5-63);stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.75}
		.st12 {fill:#000000;fill-opacity:1;stroke:#000000;stroke-opacity:1;stroke-width:0.22935779816514}
		.st13 {fill:#000000;font-family:Calibri;font-size:1.00001em}
		.st14 {font-size:1em}
		.st15 {fill:none;fill-rule:evenodd;font-size:12px;overflow:visible;stroke-linecap:square;stroke-miterlimit:3}
	]]>
	</style>

	<defs id="Markers">
		<g id="lend5">
			<path d="M 2 1 L 0 0 L 1.98117 -0.993387 C 1.67173 -0.364515 1.67301 0.372641 1.98465 1.00043 " style="stroke:none"/>
		</g>
		<marker id="mrkr5-14" class="st7" v:arrowType="5" v:arrowSize="2" v:setback="6.16" refX="-6.16" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend5" transform="scale(-3.52,-3.52) "/>
		</marker>
		<marker id="mrkr5-63" class="st12" v:arrowType="5" v:arrowSize="2" v:setback="7.15" refX="-7.15" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend5" transform="scale(-4.36,-4.36) "/>
		</marker>
	</defs>
	<g v:mID="0" v:index="1" v:groupContext="foregroundPage">
		<title>Page-1</title>
		<v:pageProperties v:drawingScale="1" v:pageScale="1" v:drawingUnits="0" v:shadowOffsetX="9" v:shadowOffsetY="-9"/>
		<g id="group1-1" transform="translate(191.995,-19.4751)" v:mID="1" v:groupContext="group">
			<title>Sheet.1</title>
			<g id="shape2-2" v:mID="2" v:groupContext="shape" transform="translate(146.944,42.2251) rotate(90)">
				<title>Triangle</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow2-3" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,1.9728,-0.3456)" class="st1">
					<path d="M99 141.23 L49.5 58.62 L0 141.23 L99 141.23 Z" class="st2"/>
				</g>
				<path d="M99 141.23 L49.5 58.62 L0 141.23 L99 141.23 Z" class="st3"/>
			</g>
			<g id="shape3-6" v:mID="3" v:groupContext="shape" transform="translate(0,-34.65)">
				<title>Sheet.3</title>
				<desc>vBF</desc>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="38.1251" cy="126.375" width="76.26" height="29.7"/>
				<rect x="0" y="111.525" width="76.2502" height="29.7" class="st4"/>
				<text x="27.68" y="130.58" class="st5" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>vBF  </text>			</g>
		</g>
		<g id="shape4-9" v:mID="4" v:groupContext="shape" transform="translate(126.724,-100.475)">
			<title>Sheet.4</title>
			<v:userDefs>
				<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
			</v:userDefs>
			<path d="M0 141.23 L64.83 141.23" class="st6"/>
		</g>
		<g id="shape5-15" v:mID="5" v:groupContext="shape" transform="translate(103.5,-101.775)">
			<title>Sheet.5</title>
			<desc>Flow Key</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="39.9122" cy="135.863" width="79.83" height="10.7251"/>
			<rect x="0" y="130.5" width="79.8244" height="10.7251" class="st4"/>
			<text x="21.78" y="138.86" class="st8" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Flow Key</text>		</g>
		<g id="shape6-18" v:mID="6" v:groupContext="shape" transform="translate(221.726,-56.2468) rotate(-24.5123)">
			<title>Sheet.6</title>
			<v:userDefs>
				<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
			</v:userDefs>
			<path d="M0 141.23 L65.42 141.23" class="st6"/>
		</g>
		<g id="shape7-23" v:mID="7" v:groupContext="shape" transform="translate(280.318,-68.9751)">
			<title>Sheet.7</title>
			<v:userDefs>
				<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
			</v:userDefs>
			<path d="M0 141.23 L64.83 141.23" class="st6"/>
		</g>
		<g id="shape8-28" v:mID="8" v:groupContext="shape" transform="translate(338.125,-56.6022) rotate(24.1625)">
			<title>Sheet.8</title>
			<v:userDefs>
				<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
			</v:userDefs>
			<path d="M0 141.23 L70.8 141.23" class="st6"/>
		</g>
		<g id="shape9-33" v:mID="9" v:groupContext="shape" transform="translate(197.714,217.975) rotate(180)">
			<title>Sheet.9</title>
			<v:userDefs>
				<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
			</v:userDefs>
			<path d="M0 141.23 L51.03 141.23" class="st6"/>
		</g>
		<g id="shape10-38" v:mID="10" v:groupContext="shape" transform="translate(18,-67.5)">
			<title>Sheet.10</title>
			<desc>New Flow =&#62; New Assignment</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="80.4201" cy="134.475" width="160.85" height="13.5"/>
			<rect x="0" y="127.725" width="160.84" height="13.5" class="st4"/>
			<text x="25.11" y="137.18" class="st9" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>New Flow =&#62; New Assignment</text>		</g>
		<g id="shape11-41" v:mID="11" v:groupContext="shape" transform="translate(198.032,253.975) rotate(180)">
			<title>Sheet.11</title>
			<v:userDefs>
				<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
			</v:userDefs>
			<path d="M0 141.23 L51.03 141.23" class="st6"/>
		</g>
		<g id="shape12-46" v:mID="12" v:groupContext="shape" transform="translate(0,-31.5)">
			<title>Sheet.12</title>
			<desc>Old Flow =&#62; forward to specific thread</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="81" cy="136.725" width="162.01" height="9"/>
			<rect x="0" y="132.225" width="162" height="9" class="st4"/>
			<text x="11.04" y="139.43" class="st9" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Old Flow =&#62; forward to specific thread</text>		</g>
		<g id="shape13-49" v:mID="13" v:groupContext="shape" transform="translate(494.552,22.75) rotate(90)">
			<title>Sheet.13</title>
			<v:userDefs>
				<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
			</v:userDefs>
			<path d="M0 134.49 C3.18 142.89 7.57 142.28 11.25 138.99 C15.79 134.93 19.26 126.78 27 134.49" class="st10"/>
		</g>
		<g id="shape14-52" v:mID="14" v:groupContext="shape" transform="translate(494.552,58.75) rotate(90)">
			<title>Sheet.14</title>
			<v:userDefs>
				<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
			</v:userDefs>
			<path d="M0 134.49 C3.18 142.89 7.57 142.28 11.25 138.99 C15.79 134.93 19.26 126.78 27 134.49" class="st10"/>
		</g>
		<g id="shape15-55" v:mID="15" v:groupContext="shape" transform="translate(494.552,94.75) rotate(90)">
			<title>Sheet.15</title>
			<v:userDefs>
				<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
			</v:userDefs>
			<path d="M0 134.49 C3.18 142.89 7.57 142.28 11.25 138.99 C15.79 134.93 19.26 126.78 27 134.49" class="st10"/>
		</g>
		<g id="shape17-58" v:mID="17" v:groupContext="shape" transform="translate(348.769,-25.0593) rotate(44.5185)">
			<title>Sheet.17</title>
			<path d="M-0 141.23 A35.1884 19.2595 167.75 0 1 42.43 138.27 L42.74 138.46" class="st11"/>
		</g>
		<g id="shape18-64" v:mID="18" v:groupContext="shape" transform="translate(222.188,-5.40005)">
			<title>Sheet.18</title>
			<desc>A BF corresponding to each worker thread</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="59.0625" cy="127.725" width="118.13" height="27"/>
			<rect x="0" y="114.225" width="118.125" height="27" class="st4"/>
			<text x="5.14" y="124.13" class="st13" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>A BF corresponding to <tspan
						x="11.19" dy="1.2em" class="st14">each worker thread</tspan></text>		</g>
	</g>
</svg>
