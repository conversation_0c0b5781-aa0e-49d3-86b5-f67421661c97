<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">
<!-- Generated by Microsoft Visio 11.0, SVG Export, v1.0 memship_i1.svg Page-1 -->
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:ev="http://www.w3.org/2001/xml-events"
		xmlns:v="http://schemas.microsoft.com/visio/2003/SVGExtensions/" width="7.18709in" height="4.75757in"
		viewBox="0 0 517.471 342.545" xml:space="preserve" color-interpolation-filters="sRGB" class="st61">
	<v:documentProperties v:langID="1033" v:viewMarkup="false">
		<v:userDefs>
			<v:ud v:nameU="msvSubprocessMaster" v:prompt="" v:val="VT4(Rectangle)"/>
			<v:ud v:nameU="msvNoAutoConnect" v:val="VT0(1):26"/>
		</v:userDefs>
	</v:documentProperties>

	<style type="text/css">
	<![CDATA[
		.st1 {visibility:visible}
		.st2 {fill:none;stroke:#5b9bd5;stroke-opacity:0.22;stroke-width:3}
		.st3 {fill:#5b9bd5;font-family:Calibri;font-size:0.666664em;opacity:0.219608}
		.st4 {font-size:1em}
		.st5 {fill:none;stroke:#41719c;stroke-width:3}
		.st6 {fill:#5b9bd5;font-family:Calibri;font-size:0.666664em}
		.st7 {fill:#5b9bd5;font-family:Calibri;font-size:0.75em;opacity:0.219608}
		.st8 {fill:#5b9bd5;font-family:Calibri;font-size:0.75em}
		.st9 {fill:#5b9bd5;fill-opacity:0.22;stroke:#5b9bd5;stroke-opacity:0.22;stroke-width:0.25}
		.st10 {fill:#5b9bd5;stroke:#c8c8c8;stroke-width:0.25}
		.st11 {fill:none;stroke:none;stroke-width:0.25}
		.st12 {fill:#ffffff;font-family:Calibri;font-size:0.499992em;font-weight:bold}
		.st13 {fill:#ffffff;font-family:Calibri;font-size:0.75em;font-weight:bold}
		.st14 {marker-end:url(#mrkr5-63);stroke:#5b9bd5;stroke-linecap:round;stroke-linejoin:round;stroke-width:1}
		.st15 {fill:#5b9bd5;fill-opacity:1;stroke:#5b9bd5;stroke-opacity:1;stroke-width:0.28409090909091}
		.st16 {fill:#5b9bd5;font-family:Calibri;font-size:0.499992em;font-weight:bold}
		.st17 {stroke:#5b9bd5;stroke-linecap:round;stroke-linejoin:round;stroke-width:1}
		.st18 {fill:#feffff;font-family:Calibri;font-size:0.499992em}
		.st19 {fill:#deebf6;stroke:#c8c8c8;stroke-width:0.25}
		.st20 {fill:#000000;font-family:Calibri;font-size:0.499992em}
		.st21 {marker-end:url(#mrkr5-178);stroke:#ff0000;stroke-linecap:round;stroke-linejoin:round;stroke-width:1}
		.st22 {fill:#ff0000;fill-opacity:1;stroke:#ff0000;stroke-opacity:1;stroke-width:0.28409090909091}
		.st23 {fill:#ff0000;font-family:Calibri;font-size:0.666664em}
		.st24 {fill:#5b9bd5;fill-opacity:0.22}
		.st25 {stroke:#5b9bd5;stroke-opacity:0.22;stroke-width:0.25}
		.st26 {fill:#ffffff}
		.st27 {stroke:#0070c0;stroke-width:0.25}
		.st28 {fill:#5b9bd5;stroke:#0070c0;stroke-width:0.25}
		.st29 {fill:#5b9bd5;stroke:#ffffff;stroke-width:0.25}
		.st30 {fill:#5b9bd5}
		.st31 {stroke:#c8c8c8;stroke-width:0.25}
		.st32 {fill:#acccea;stroke:#c8c8c8;stroke-width:0.25}
		.st33 {fill:#5b9bd5;fill-opacity:0.22;stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st34 {fill:#000000;fill-opacity:0;stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st35 {fill:url(#grad30-309);stroke:none;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.75}
		.st36 {fill:url(#grad25-313);stroke:none;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.75}
		.st37 {fill:url(#grad35-317);stroke:#308dda;stroke-linecap:butt;stroke-width:0.130208}
		.st38 {fill:url(#grad36-325);stroke:none;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.75}
		.st39 {fill:url(#grad40-335);stroke:#000000;stroke-linecap:butt;stroke-width:0.130208}
		.st40 {fill:url(#grad39-342);stroke:none;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.75}
		.st41 {fill:url(#grad40-355);stroke:none;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.75}
		.st42 {fill:none}
		.st43 {stroke:#308dda;stroke-linecap:butt;stroke-width:0.130208}
		.st44 {stroke:#ffffff;stroke-linecap:butt;stroke-width:0.130208}
		.st45 {fill:url(#grad30-383);stroke:none;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.75}
		.st46 {fill:url(#grad36-396);stroke:none;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.75}
		.st47 {fill:none;stroke:#c8c8c8;stroke-width:0.75}
		.st48 {fill:#9a9a9a;stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.0833333}
		.st49 {fill:url(#grad40-415);stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.0833333}
		.st50 {fill:url(#grad40-419);stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.0833333}
		.st51 {stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.25}
		.st52 {fill:url(#grad35-430);stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.0833333}
		.st53 {stroke:#c8c8c8;stroke-width:0.75}
		.st54 {stroke:#4f88bb;stroke-width:0.75}
		.st55 {fill:#feffff;font-family:Calibri;font-size:0.416656em}
		.st56 {fill:#5b9bd5;fill-opacity:0.25;stroke:#5b9bd5;stroke-opacity:0.25;stroke-width:0.75}
		.st57 {fill:#4f88bb;stroke:#41719c;stroke-width:0.75}
		.st58 {fill:none;stroke:#5b9bd5;stroke-opacity:0.22;stroke-width:2.25}
		.st59 {fill:none;stroke:#0070c0;stroke-width:2.25}
		.st60 {fill:#595959;font-family:Arial;font-size:0.666664em}
		.st61 {fill:none;fill-rule:evenodd;font-size:12px;overflow:visible;stroke-linecap:square;stroke-miterlimit:3}
	]]>
	</style>

	<defs id="Patterns_And_Gradients">
		<linearGradient id="grad30-309" v:fillPattern="30" v:foreground="#97c2e6" v:background="#4274a2" x1="0" y1="1" x2="0"
				y2="0">
			<stop offset="0" style="stop-color:#97c2e6;stop-opacity:1"/>
			<stop offset="1" style="stop-color:#4274a2;stop-opacity:1"/>
		</linearGradient>
		<linearGradient id="grad25-313" v:fillPattern="25" v:foreground="#5491d3" v:background="#246ba6" x1="0" y1="0" x2="1"
				y2="0">
			<stop offset="0" style="stop-color:#5491d3;stop-opacity:1"/>
			<stop offset="1" style="stop-color:#246ba6;stop-opacity:1"/>
		</linearGradient>
		<pattern id="grad35-317" v:fillPattern="35" v:foreground="#569bd3" v:background="#aed0ec" x="0" y="0" width="1" height="1"
				patternContentUnits="objectBoundingBox">
			<path d="M 0.5 0.5 L 0 0 L 0 1 z" style="fill:url(#grad27-318)"/>
			<path d="M 0.5 0.5 L 1 0 L 1 1 z" style="fill:url(#grad25-319)"/>
			<path d="M 0.5 0.5 L 0 0 L 1 0 z" style="fill:url(#grad30-320)"/>
			<path d="M 0.5 0.5 L 0 1 L 1 1 z" style="fill:url(#grad28-321)"/>
		</pattern>
		<linearGradient id="grad27-318" v:fillPattern="35" v:foreground="#569bd3" v:background="#aed0ec" x1="1" y1="0" x2="0"
				y2="0">
			<stop offset="0" style="stop-color:#569bd3;stop-opacity:1"/>
			<stop offset="1" style="stop-color:#aed0ec;stop-opacity:1"/>
		</linearGradient>
		<linearGradient id="grad25-319" v:fillPattern="35" v:foreground="#569bd3" v:background="#aed0ec" x1="0" y1="0" x2="1"
				y2="0">
			<stop offset="0" style="stop-color:#569bd3;stop-opacity:1"/>
			<stop offset="1" style="stop-color:#aed0ec;stop-opacity:1"/>
		</linearGradient>
		<linearGradient id="grad30-320" v:fillPattern="35" v:foreground="#569bd3" v:background="#aed0ec" x1="0" y1="1" x2="0"
				y2="0">
			<stop offset="0" style="stop-color:#569bd3;stop-opacity:1"/>
			<stop offset="1" style="stop-color:#aed0ec;stop-opacity:1"/>
		</linearGradient>
		<linearGradient id="grad28-321" v:fillPattern="35" v:foreground="#569bd3" v:background="#aed0ec" x1="0" y1="0" x2="0"
				y2="1">
			<stop offset="0" style="stop-color:#569bd3;stop-opacity:1"/>
			<stop offset="1" style="stop-color:#aed0ec;stop-opacity:1"/>
		</linearGradient>
		<radialGradient id="grad36-325" v:fillPattern="36" v:foreground="#c0dff1" v:background="#246ba6" cx="0" cy="0" r="1">
			<stop offset="0" style="stop-color:#c0dff1;stop-opacity:1"/>
			<stop offset="1" style="stop-color:#246ba6;stop-opacity:1"/>
		</radialGradient>
		<radialGradient id="grad40-335" v:fillPattern="40" v:foreground="#c8e5c8" v:background="#19bf19" cx="0.5" cy="0.5" r="0.5">
			<stop offset="0" style="stop-color:#c8e5c8;stop-opacity:1"/>
			<stop offset="1" style="stop-color:#19bf19;stop-opacity:1"/>
		</radialGradient>
		<radialGradient id="grad39-342" v:fillPattern="39" v:foreground="#5599d7" v:background="#b9daf2" cx="1" cy="1" r="1">
			<stop offset="0" style="stop-color:#5599d7;stop-opacity:1"/>
			<stop offset="1" style="stop-color:#b9daf2;stop-opacity:1"/>
		</radialGradient>
		<radialGradient id="grad40-355" v:fillPattern="40" v:foreground="#5599d7" v:background="#214383" cx="0.5" cy="0.5" r="0.5">
			<stop offset="0" style="stop-color:#5599d7;stop-opacity:1"/>
			<stop offset="1" style="stop-color:#214383;stop-opacity:1"/>
		</radialGradient>
		<linearGradient id="grad30-383" v:fillPattern="30" v:foreground="#97c2e6" v:background="#6ba4dc" x1="0" y1="1" x2="0"
				y2="0">
			<stop offset="0" style="stop-color:#97c2e6;stop-opacity:1"/>
			<stop offset="1" style="stop-color:#6ba4dc;stop-opacity:1"/>
		</linearGradient>
		<radialGradient id="grad36-396" v:fillPattern="36" v:foreground="#89bee9" v:background="#b9daf2" cx="0" cy="0" r="1">
			<stop offset="0" style="stop-color:#89bee9;stop-opacity:1"/>
			<stop offset="1" style="stop-color:#b9daf2;stop-opacity:1"/>
		</radialGradient>
		<radialGradient id="grad40-415" v:fillPattern="40" v:foreground="#000000" v:background="#ffffff" cx="0.5" cy="0.5" r="0.5">
			<stop offset="0" style="stop-color:#000000;stop-opacity:1"/>
			<stop offset="1" style="stop-color:#ffffff;stop-opacity:1"/>
		</radialGradient>
		<radialGradient id="grad40-419" v:fillPattern="40" v:foreground="#ffffff" v:background="#9a9a9a" cx="0.5" cy="0.5" r="0.5">
			<stop offset="0" style="stop-color:#ffffff;stop-opacity:1"/>
			<stop offset="1" style="stop-color:#9a9a9a;stop-opacity:1"/>
		</radialGradient>
		<pattern id="grad35-430" v:fillPattern="35" v:foreground="#ffffff" v:background="#ffcc00" x="0" y="0" width="1" height="1"
				patternContentUnits="objectBoundingBox">
			<path d="M 0.5 0.5 L 0 0 L 0 1 z" style="fill:url(#grad27-431)"/>
			<path d="M 0.5 0.5 L 1 0 L 1 1 z" style="fill:url(#grad25-432)"/>
			<path d="M 0.5 0.5 L 0 0 L 1 0 z" style="fill:url(#grad30-433)"/>
			<path d="M 0.5 0.5 L 0 1 L 1 1 z" style="fill:url(#grad28-434)"/>
		</pattern>
		<linearGradient id="grad27-431" v:fillPattern="35" v:foreground="#ffffff" v:background="#ffcc00" x1="1" y1="0" x2="0"
				y2="0">
			<stop offset="0" style="stop-color:#ffffff;stop-opacity:1"/>
			<stop offset="1" style="stop-color:#ffcc00;stop-opacity:1"/>
		</linearGradient>
		<linearGradient id="grad25-432" v:fillPattern="35" v:foreground="#ffffff" v:background="#ffcc00" x1="0" y1="0" x2="1"
				y2="0">
			<stop offset="0" style="stop-color:#ffffff;stop-opacity:1"/>
			<stop offset="1" style="stop-color:#ffcc00;stop-opacity:1"/>
		</linearGradient>
		<linearGradient id="grad30-433" v:fillPattern="35" v:foreground="#ffffff" v:background="#ffcc00" x1="0" y1="1" x2="0"
				y2="0">
			<stop offset="0" style="stop-color:#ffffff;stop-opacity:1"/>
			<stop offset="1" style="stop-color:#ffcc00;stop-opacity:1"/>
		</linearGradient>
		<linearGradient id="grad28-434" v:fillPattern="35" v:foreground="#ffffff" v:background="#ffcc00" x1="0" y1="0" x2="0"
				y2="1">
			<stop offset="0" style="stop-color:#ffffff;stop-opacity:1"/>
			<stop offset="1" style="stop-color:#ffcc00;stop-opacity:1"/>
		</linearGradient>
	</defs>
	<defs id="Markers">
		<g id="lend5">
			<path d="M 2 1 L 0 0 L 1.98117 -0.993387 C 1.67173 -0.364515 1.67301 0.372641 1.98465 1.00043 " style="stroke:none"/>
		</g>
		<marker id="mrkr5-63" class="st15" v:arrowType="5" v:arrowSize="2" v:setback="6.16" refX="-6.16" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend5" transform="scale(-3.52,-3.52) "/>
		</marker>
		<marker id="mrkr5-178" class="st22" v:arrowType="5" v:arrowSize="2" v:setback="5.8" refX="-5.8" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend5" transform="scale(-3.52,-3.52) "/>
		</marker>
	</defs>
	<g v:mID="0" v:index="1" v:groupContext="foregroundPage">
		<v:userDefs>
			<v:ud v:nameU="msvThemeOrder" v:val="VT0(0):26"/>
		</v:userDefs>
		<title>Page-1</title>
		<v:pageProperties v:drawingScale="1" v:pageScale="1" v:drawingUnits="0" v:shadowOffsetX="9" v:shadowOffsetY="-9"/>
		<v:layer v:name="Flowchart" v:index="0"/>
		<g id="group165-1" transform="translate(21.7794,-24.0978)" v:mID="165" v:groupContext="group">
			<title>Sheet.165</title>
			<g id="group1-2" transform="translate(308.647,-25.7109)" v:mID="1" v:groupContext="group">
				<title>Sheet.1</title>
				<g id="shape2-3" v:mID="2" v:groupContext="shape" transform="translate(11.5732,-58.1913)">
					<title>Circle</title>
					<desc>List 1 matching Criteria 1</desc>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<v:textBlock v:margins="rect(4,4,4,4)"/>
					<v:textRect cx="27.2233" cy="315.322" width="47.65" height="40.835"/>
					<g id="shadow2-4" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
							transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
						<path d="M0 315.32 A27.2233 27.2233 0 1 1 54.45 315.32 A27.2233 27.2233 0 1 1 0 315.32 Z" class="st2"/>
						<text x="18.79" y="308.12" class="st3" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>List 1 <tspan
									x="12.08" dy="1.2em" class="st4">matching </tspan><tspan x="12.29" dy="1.2em" class="st4">Criteria </tspan>1</text>					</g>
					<path d="M0 315.32 A27.2233 27.2233 0 1 1 54.45 315.32 A27.2233 27.2233 0 1 1 0 315.32 Z" class="st5"/>
					<text x="18.79" y="308.12" class="st6" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>List 1 <tspan
								x="12.08" dy="1.2em" class="st4">matching </tspan><tspan x="12.29" dy="1.2em" class="st4">Criteria </tspan>1</text>				</g>
				<g id="shape3-13" v:mID="3" v:groupContext="shape" transform="translate(58.9839,-58.9839)">
					<title>Circle.23</title>
					<desc>List 2</desc>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<v:textBlock v:margins="rect(4,4,4,4)"/>
					<v:textRect cx="27.2233" cy="315.322" width="47.65" height="40.835"/>
					<g id="shadow3-14" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
							transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
						<path d="M0 315.32 A27.2233 27.2233 0 1 1 54.45 315.32 A27.2233 27.2233 0 1 1 0 315.32 Z" class="st2"/>
						<text x="17.73" y="318.02" class="st7" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>List 2</text>					</g>
					<path d="M0 315.32 A27.2233 27.2233 0 1 1 54.45 315.32 A27.2233 27.2233 0 1 1 0 315.32 Z" class="st5"/>
					<text x="17.73" y="318.02" class="st8" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>List 2</text>				</g>
				<g id="shape4-19" v:mID="4" v:groupContext="shape">
					<title>Circle.24</title>
					<desc>List 1 matching Criteria 1</desc>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<v:textBlock v:margins="rect(4,4,4,4)"/>
					<v:textRect cx="27.2233" cy="315.322" width="47.65" height="40.835"/>
					<g id="shadow4-20" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
							transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
						<path d="M0 315.32 A27.2233 27.2233 0 1 1 54.45 315.32 A27.2233 27.2233 0 1 1 0 315.32 Z" class="st2"/>
						<text x="18.79" y="308.12" class="st3" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>List 1 <tspan
									x="12.08" dy="1.2em" class="st4">matching </tspan><tspan x="12.29" dy="1.2em" class="st4">Criteria </tspan>1</text>					</g>
					<path d="M0 315.32 A27.2233 27.2233 0 1 1 54.45 315.32 A27.2233 27.2233 0 1 1 0 315.32 Z" class="st5"/>
					<text x="18.79" y="308.12" class="st6" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>List 1 <tspan
								x="12.08" dy="1.2em" class="st4">matching </tspan><tspan x="12.29" dy="1.2em" class="st4">Criteria </tspan>1</text>				</g>
				<g id="group5-29" transform="translate(50.7413,-4.53722)" v:mID="5" v:groupContext="group">
					<title>Sheet.5</title>
					<g id="shape6-30" v:mID="6" v:groupContext="shape" transform="translate(344.2,300.5) rotate(90)">
						<title>Triangle</title>
						<v:userDefs>
							<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
						</v:userDefs>
						<g id="shadow6-31" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,1.9728,-0.3456)" class="st1">
							<path d="M42.04 342.55 L21.02 318.64 L0 342.55 L42.04 342.55 Z" class="st9"/>
						</g>
						<path d="M42.04 342.55 L21.02 318.64 L0 342.55 L42.04 342.55 Z" class="st10"/>
					</g>
					<g id="shape7-34" v:mID="7" v:groupContext="shape" transform="translate(-0.884982,-14.7157)">
						<title>Sheet.7</title>
						<desc>setsum</desc>
						<v:textBlock v:margins="rect(4,4,4,4)"/>
						<v:textRect cx="12.9268" cy="336.238" width="25.86" height="12.6135"/>
						<rect x="0" y="329.932" width="25.8535" height="12.6135" class="st11"/>
						<text x="6.37" y="334.44" class="st12" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>setsu<tspan
									x="10.49" dy="1.2em" class="st4">m</tspan></text>					</g>
				</g>
				<g id="shape8-38" v:mID="8" v:groupContext="shape" transform="translate(72.5955,0)">
					<title>Circle.29</title>
					<desc>List 2 matching Criteria 2</desc>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<v:textBlock v:margins="rect(4,4,4,4)"/>
					<v:textRect cx="27.2233" cy="315.322" width="47.65" height="40.835"/>
					<g id="shadow8-39" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
							transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
						<path d="M0 315.32 A27.2233 27.2233 0 1 1 54.45 315.32 A27.2233 27.2233 0 1 1 0 315.32 Z" class="st2"/>
						<text x="18.79" y="308.12" class="st3" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>List 2 <tspan
									x="12.08" dy="1.2em" class="st4">matching </tspan><tspan x="12.29" dy="1.2em" class="st4">Criteria </tspan>2</text>					</g>
					<path d="M0 315.32 A27.2233 27.2233 0 1 1 54.45 315.32 A27.2233 27.2233 0 1 1 0 315.32 Z" class="st5"/>
					<text x="18.79" y="308.12" class="st6" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>List 2 <tspan
								x="12.08" dy="1.2em" class="st4">matching </tspan><tspan x="12.29" dy="1.2em" class="st4">Criteria </tspan>2</text>				</g>
			</g>
			<g id="group9-48" transform="translate(31.6515,-49.9094)" v:mID="9" v:groupContext="group">
				<title>Sheet.9</title>
				<g id="group10-49" transform="translate(99.5691,0)" v:mID="10" v:groupContext="group">
					<title>Sheet.10</title>
					<g id="shape11-50" v:mID="11" v:groupContext="shape" transform="translate(346.175,275.999) rotate(90)">
						<title>Triangle</title>
						<v:userDefs>
							<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
						</v:userDefs>
						<g id="shadow11-51" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,1.9728,-0.3456)" class="st1">
							<path d="M66.55 342.55 L33.27 290.12 L0 342.55 L66.55 342.55 Z" class="st9"/>
						</g>
						<path d="M66.55 342.55 L33.27 290.12 L0 342.55 L66.55 342.55 Z" class="st10"/>
					</g>
					<g id="shape12-54" v:mID="12" v:groupContext="shape" transform="translate(355.063,285.074) rotate(90)">
						<title>Sheet.12</title>
						<desc>Set Summary</desc>
						<v:textBlock v:margins="rect(4,4,4,4)"/>
						<v:textRect cx="24.1985" cy="332.563" width="48.4" height="19.9638"/>
						<rect x="0" y="322.581" width="48.397" height="19.9638" class="st11"/>
						<text x="18.25" y="329.86" class="st13" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Set <tspan
									x="6.38" dy="1.2em" class="st4">Summary</tspan></text>					</g>
				</g>
				<g id="shape13-58" v:mID="13" v:groupContext="shape" transform="translate(57.5835,-54.4467)">
					<title>Sheet.13</title>
					<v:userDefs>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<path d="M0 342.55 L38.9 342.55" class="st14"/>
				</g>
				<g id="shape14-64" v:mID="14" v:groupContext="shape" transform="translate(20.2363,-51.8439)">
					<title>Sheet.14</title>
					<desc>Flow Key</desc>
					<v:textBlock v:margins="rect(4,4,4,4)"/>
					<v:textRect cx="25.3328" cy="333.471" width="50.67" height="18.1489"/>
					<rect x="0" y="324.396" width="50.6656" height="18.1489" class="st11"/>
					<text x="14.12" y="335.27" class="st16" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Flow Key</text>				</g>
				<g id="shape15-67" v:mID="15" v:groupContext="shape" transform="translate(5.02911,1.60865) rotate(-26.0815)">
					<title>Sheet.15</title>
					<v:userDefs>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<path d="M0 342.55 L39.25 342.55" class="st14"/>
				</g>
				<g id="shape16-72" v:mID="16" v:groupContext="shape" transform="translate(155.629,-33.273)">
					<title>Sheet.16</title>
					<v:userDefs>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<path d="M0 342.55 L38.34 342.55" class="st14"/>
				</g>
				<g id="shape17-77" v:mID="17" v:groupContext="shape" transform="translate(304.141,0.595416) rotate(25.6934)">
					<title>Sheet.17</title>
					<v:userDefs>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<path d="M0 342.55 L42.68 342.55" class="st14"/>
				</g>
				<g id="shape18-82" v:mID="18" v:groupContext="shape" transform="translate(102.642,654.842) rotate(180)">
					<title>Sheet.18</title>
					<v:userDefs>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<path d="M0 342.55 L30.14 342.55" class="st14"/>
				</g>
				<g id="shape19-87" v:mID="19" v:groupContext="shape" transform="translate(-15.1809,-33.9928)">
					<title>Sheet.19</title>
					<desc>New Flow =&#62; New Assignment</desc>
					<v:textBlock v:margins="rect(4,4,4,4)"/>
					<v:textRect cx="42.75" cy="338.045" width="85.5" height="9"/>
					<rect x="0" y="333.545" width="85.5" height="9" class="st11"/>
					<text x="5.06" y="339.85" class="st16" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>New Flow =&#62; New Assignment</text>				</g>
				<g id="shape20-90" v:mID="20" v:groupContext="shape" transform="translate(102.844,679.041) rotate(180)">
					<title>Sheet.20</title>
					<v:userDefs>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<path d="M0 342.55 L30.14 342.55" class="st14"/>
				</g>
				<g id="shape21-95" v:mID="21" v:groupContext="shape" transform="translate(-35.4309,-11.4928)">
					<title>Sheet.21</title>
					<desc>Old Flow =&#62; forward to specific thread</desc>
					<v:textBlock v:margins="rect(4,4,4,4)"/>
					<v:textRect cx="54" cy="337.971" width="108" height="9.14889"/>
					<rect x="0" y="333.396" width="108" height="9.14889" class="st11"/>
					<text x="6.36" y="339.77" class="st16" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Old Flow =&#62; forward to specific thread</text>				</g>
				<g id="shape22-98" v:mID="22" v:groupContext="shape" transform="translate(541.496,275.999) rotate(90)">
					<title>Sheet.22</title>
					<v:userDefs>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<path d="M0 335.81 C2.14 344.21 5.09 343.6 7.56 340.31 C10.62 336.25 12.94 328.1 18.15 335.81" class="st17"/>
				</g>
				<g id="shape23-101" v:mID="23" v:groupContext="shape" transform="translate(541.496,300.198) rotate(90)">
					<title>Sheet.23</title>
					<v:userDefs>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<path d="M0 335.81 C2.14 344.21 5.09 343.6 7.56 340.31 C10.62 336.25 12.94 328.1 18.15 335.81" class="st17"/>
				</g>
				<g id="shape24-104" v:mID="24" v:groupContext="shape" transform="translate(541.496,324.396) rotate(90)">
					<title>Sheet.24</title>
					<v:userDefs>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<path d="M0 335.81 C2.14 344.21 5.09 343.6 7.56 340.31 C10.62 336.25 12.94 328.1 18.15 335.81" class="st17"/>
				</g>
			</g>
			<g id="group25-107" transform="translate(285.961,-178.628)" v:mID="25" v:groupContext="group">
				<title>Sheet.25</title>
				<g id="shape26-108" v:mID="26" v:groupContext="shape" transform="translate(51.2583,-51.2583)">
					<title>Circle</title>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
					</v:userDefs>
					<g id="shadow26-109" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
							v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
						<path d="M0 333.23 A9.3197 9.3197 0 0 1 18.64 333.23 A9.3197 9.3197 0 0 1 0 333.23 Z" class="st9"/>
					</g>
					<path d="M0 333.23 A9.3197 9.3197 0 0 1 18.64 333.23 A9.3197 9.3197 0 0 1 0 333.23 Z" class="st10"/>
				</g>
				<g id="shape27-112" v:mID="27" v:groupContext="shape" transform="translate(107.177,-55.9182)">
					<title>Circle.156</title>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
					</v:userDefs>
					<g id="shadow27-113" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
							v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
						<path d="M0 333.23 A9.3197 9.3197 0 0 1 18.64 333.23 A9.3197 9.3197 0 0 1 0 333.23 Z" class="st9"/>
					</g>
					<path d="M0 333.23 A9.3197 9.3197 0 0 1 18.64 333.23 A9.3197 9.3197 0 0 1 0 333.23 Z" class="st10"/>
				</g>
				<g id="shape28-116" v:mID="28" v:groupContext="shape" transform="translate(79.2174,-83.8773)">
					<title>Circle.157</title>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
					</v:userDefs>
					<g id="shadow28-117" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
							v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
						<path d="M0 333.23 A9.3197 9.3197 0 0 1 18.64 333.23 A9.3197 9.3197 0 0 1 0 333.23 Z" class="st9"/>
					</g>
					<path d="M0 333.23 A9.3197 9.3197 0 0 1 18.64 333.23 A9.3197 9.3197 0 0 1 0 333.23 Z" class="st10"/>
				</g>
				<g id="shape29-120" v:mID="29" v:groupContext="shape" transform="translate(153.775,-51.2583)">
					<title>Circle.158</title>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
					</v:userDefs>
					<g id="shadow29-121" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
							v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
						<path d="M0 333.23 A9.3197 9.3197 0 0 1 18.64 333.23 A9.3197 9.3197 0 0 1 0 333.23 Z" class="st9"/>
					</g>
					<path d="M0 333.23 A9.3197 9.3197 0 0 1 18.64 333.23 A9.3197 9.3197 0 0 1 0 333.23 Z" class="st10"/>
				</g>
				<g id="shape30-124" v:mID="30" v:groupContext="shape" transform="translate(93.197,-18.6394)">
					<title>Circle.159</title>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
					</v:userDefs>
					<g id="shadow30-125" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
							v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
						<path d="M0 333.23 A9.3197 9.3197 0 0 1 18.64 333.23 A9.3197 9.3197 0 0 1 0 333.23 Z" class="st9"/>
					</g>
					<path d="M0 333.23 A9.3197 9.3197 0 0 1 18.64 333.23 A9.3197 9.3197 0 0 1 0 333.23 Z" class="st10"/>
				</g>
				<g id="shape31-128" v:mID="31" v:groupContext="shape" transform="translate(27.4102,-57.9329) rotate(-7.12502)">
					<title>Sheet.31</title>
					<v:userDefs>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<path d="M0 342.55 L31.41 342.55" class="st14"/>
				</g>
				<g id="shape32-133" v:mID="32" v:groupContext="shape" transform="translate(182.13,-60.5772) rotate(9.46232)">
					<title>Sheet.32</title>
					<v:userDefs>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<path d="M0 342.55 L22.18 342.55" class="st14"/>
				</g>
				<g id="shape33-138" v:mID="33" v:groupContext="shape" transform="translate(47.8843,595.237) rotate(-160.346)">
					<title>Sheet.33</title>
					<v:userDefs>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<path d="M0 342.55 L63.11 342.55" class="st14"/>
				</g>
				<g id="shape34-143" v:mID="34" v:groupContext="shape" transform="translate(292.945,525.785) rotate(141.977)">
					<title>Sheet.34</title>
					<v:userDefs>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<path d="M0 342.55 L20.97 342.55" class="st14"/>
				</g>
				<g id="shape35-148" v:mID="35" v:groupContext="shape" transform="translate(-95.8971,591.793) rotate(-145.945)">
					<title>Sheet.35</title>
					<v:userDefs>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<path d="M0 342.55 L28.55 342.55" class="st14"/>
				</g>
				<g id="shape36-153" v:mID="36" v:groupContext="shape" transform="translate(37.2788,2.27374E-013)">
					<title>Rectangle.167</title>
					<desc>SUM</desc>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
					</v:userDefs>
					<v:textBlock v:margins="rect(4,4,4,4)"/>
					<v:textRect cx="10.8652" cy="335.555" width="21.74" height="13.9795"/>
					<g id="shadow36-154" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
							v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
						<rect x="0" y="328.566" width="21.7305" height="13.9795" class="st9"/>
					</g>
					<rect x="0" y="328.566" width="21.7305" height="13.9795" class="st10"/>
					<text x="5" y="337.36" class="st18" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>SUM</text>				</g>
				<g id="shape37-158" v:mID="37" v:groupContext="shape" transform="translate(55.9182,2.27374E-013)">
					<title>Rectangle.168</title>
					<desc>Packet</desc>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<v:textBlock v:margins="rect(4,4,4,4)"/>
					<v:textRect cx="23.2992" cy="335.555" width="46.6" height="13.9795"/>
					<g id="shadow37-159" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
							v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
						<rect x="0" y="328.566" width="46.5985" height="13.9795" class="st9"/>
					</g>
					<rect x="0" y="328.566" width="46.5985" height="13.9795" class="st19"/>
					<text x="15.18" y="337.36" class="st20" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Packet</text>				</g>
				<g id="shape38-163" v:mID="38" v:groupContext="shape" transform="translate(-1.65867E-013,-32.6189)">
					<title>Rectangle.169</title>
					<desc>SUM</desc>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
					</v:userDefs>
					<v:textBlock v:margins="rect(4,4,4,4)"/>
					<v:textRect cx="10.3796" cy="335.555" width="20.76" height="13.9795"/>
					<g id="shadow38-164" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
							v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
						<rect x="0" y="328.566" width="20.7593" height="13.9795" class="st9"/>
					</g>
					<rect x="0" y="328.566" width="20.7593" height="13.9795" class="st10"/>
					<text x="4.51" y="337.36" class="st18" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>SUM</text>				</g>
				<g id="shape39-168" v:mID="39" v:groupContext="shape" transform="translate(18.6394,-32.6189)">
					<title>Rectangle.170</title>
					<desc>Packet</desc>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<v:textBlock v:margins="rect(4,4,4,4)"/>
					<v:textRect cx="23.2992" cy="335.555" width="46.6" height="13.9795"/>
					<g id="shadow39-169" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
							v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
						<rect x="0" y="328.566" width="46.5985" height="13.9795" class="st9"/>
					</g>
					<rect x="0" y="328.566" width="46.5985" height="13.9795" class="st19"/>
					<text x="15.18" y="337.36" class="st20" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Packet</text>				</g>
				<g id="shape40-173" v:mID="40" v:groupContext="shape" transform="translate(197.019,626.053) rotate(161.565)">
					<title>Sheet.40</title>
					<path d="M0 328.31 A55.7483 27.2427 -124.2 0 0 42.37 334.19 L42.47 333.85" class="st21"/>
				</g>
				<g id="shape41-179" v:mID="41" v:groupContext="shape" transform="translate(154.607,584.177) rotate(161.121)">
					<title>Sheet.41</title>
					<v:userDefs>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<path d="M0 319.39 A80.5593 29.9756 -101.99 0 0 41.7 325.37 L41.79 325.02" class="st21"/>
				</g>
				<g id="shape42-184" v:mID="42" v:groupContext="shape" transform="translate(3.02481,-66.7025)">
					<title>Sheet.42</title>
					<desc>Encode ID</desc>
					<v:textBlock v:margins="rect(4,4,4,4)"/>
					<v:textRect cx="19.4569" cy="335.555" width="38.92" height="13.9795"/>
					<rect x="0" y="328.566" width="38.9138" height="13.9795" class="st11"/>
					<text x="7.51" y="333.16" class="st23" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Encode <tspan
								x="15.99" dy="1.2em" class="st4">ID</tspan></text>				</g>
			</g>
			<g id="group43-188" transform="translate(12.0993,-165.858)" v:mID="43" v:groupContext="group">
				<title>Sheet.43</title>
				<g id="group44-189" transform="translate(7.21495,-75.757)" v:mID="44" v:groupContext="group" v:layerMember="0">
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
						<v:ud v:nameU="ConnGap" v:prompt="" v:val="VT0(0.083333333333333):0"/>
					</v:userDefs>
					<title>User</title>
					<g id="shape45-190" v:mID="45" v:groupContext="shape" v:layerMember="0"
							transform="translate(13.3353,-1.13687E-013)">
						<title>Sheet.45</title>
						<g id="shadow45-191" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<path d="M20.77 325.42 A2.63551 2.63601 -180 1 0 15.5 325.42 A2.63551 2.63601 -180 1 0 20.77 325.42 ZM20.96
										 328.77 L15.25 328.77 C14.84 328.77 14.46 328.91 14.16 329.14 C13.95 329.31 13.78 329.52
										 13.66 329.76 C13.54 330 13.47 330.27 13.47 330.55 L13.47 337.32 L15.03 337.32 L15.03 342.55
										 L21.12 342.55 L21.12 337.32 L22.74 337.32 L22.74 330.55 C22.74 330.14 22.6 329.76 22.36
										 329.46 C22.2 329.25 21.99 329.08 21.75 328.96 C21.51 328.84 21.24 328.77 20.96 328.77 Z"
									class="st24"/>
							<path d="M20.77 325.42 A2.63551 2.63601 -180 1 0 15.5 325.42 A2.63551 2.63601 -180 1 0 20.77 325.42"
									class="st25"/>
							<path d="M20.96 328.77 L15.25 328.77 C14.84 328.77 14.46 328.91 14.16 329.14 C13.95 329.31 13.78 329.52
										 13.66 329.76 C13.54 330 13.47 330.27 13.47 330.55 L13.47 337.32 L15.03 337.32 L15.03 342.55
										 L21.12 342.55 L21.12 337.32 L22.74 337.32 L22.74 330.55 C22.74 330.14 22.6 329.76 22.36
										 329.46 C22.2 329.25 21.99 329.08 21.75 328.96 C21.51 328.84 21.24 328.77 20.96 328.77"
									class="st25"/>
							<path d="M18.1 342.55 L18.1 338.37" class="st25"/>
							<path d="M15.03 337.32 L15.03 333.71" class="st25"/>
							<path d="M21.12 337.32 L21.12 333.71" class="st25"/>
							<path d="M0 337.32 L13.47 337.32" class="st25"/>
						</g>
						<path d="M20.77 325.42 A2.63551 2.63601 -180 1 0 15.5 325.42 A2.63551 2.63601 -180 1 0 20.77 325.42 ZM20.96
									 328.77 L15.25 328.77 C14.84 328.77 14.46 328.91 14.16 329.14 C13.95 329.31 13.78 329.52 13.66
									 329.76 C13.54 330 13.47 330.27 13.47 330.55 L13.47 337.32 L15.03 337.32 L15.03 342.55 L21.12
									 342.55 L21.12 337.32 L22.74 337.32 L22.74 330.55 C22.74 330.14 22.6 329.76 22.36 329.46 C22.2
									 329.25 21.99 329.08 21.75 328.96 C21.51 328.84 21.24 328.77 20.96 328.77 Z" class="st26"/>
						<path d="M20.77 325.42 A2.63551 2.63601 -180 1 0 15.5 325.42 A2.63551 2.63601 -180 1 0 20.77 325.42"
								class="st27"/>
						<path d="M20.96 328.77 L15.25 328.77 C14.84 328.77 14.46 328.91 14.16 329.14 C13.95 329.31 13.78 329.52 13.66
									 329.76 C13.54 330 13.47 330.27 13.47 330.55 L13.47 337.32 L15.03 337.32 L15.03 342.55 L21.12
									 342.55 L21.12 337.32 L22.74 337.32 L22.74 330.55 C22.74 330.14 22.6 329.76 22.36 329.46 C22.2
									 329.25 21.99 329.08 21.75 328.96 C21.51 328.84 21.24 328.77 20.96 328.77" class="st27"/>
						<path d="M18.1 342.55 L18.1 338.37" class="st27"/>
						<path d="M15.03 337.32 L15.03 333.71" class="st27"/>
						<path d="M21.12 337.32 L21.12 333.71" class="st27"/>
						<path d="M0 337.32 L13.47 337.32" class="st27"/>
					</g>
					<g id="shape46-206" v:mID="46" v:groupContext="shape" v:layerMember="0" transform="translate(0,-8.39743)">
						<title>Sheet.46</title>
						<g id="shadow46-207" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<path d="M21.09 325.52 C21.09 325.13 20.96 324.79 20.74 324.51 C20.59 324.32 20.4 324.16 20.19 324.06
										 C19.97 323.95 19.72 323.89 19.46 323.89 L3.55 323.89 C3.16 323.89 2.82 324.02 2.54 324.23
										 C2.35 324.38 2.19 324.57 2.09 324.79 C1.98 325.01 1.92 325.25 1.92 325.52 L1.92 336.04 L21.09
										 336.04 L21.09 325.52 ZM21.18 337.33 L1.77 337.33 L0 340.51 L0 342.55 L23.06 342.55 L23.06
										 340.51 L21.18 337.33 Z" class="st9"/>
						</g>
						<path d="M21.09 325.52 C21.09 325.13 20.96 324.79 20.74 324.51 C20.59 324.32 20.4 324.16 20.19 324.06 C19.97
									 323.95 19.72 323.89 19.46 323.89 L3.55 323.89 C3.16 323.89 2.82 324.02 2.54 324.23 C2.35 324.38
									 2.19 324.57 2.09 324.79 C1.98 325.01 1.92 325.25 1.92 325.52 L1.92 336.04 L21.09 336.04 L21.09
									 325.52 ZM21.18 337.33 L1.77 337.33 L0 340.51 L0 342.55 L23.06 342.55 L23.06 340.51 L21.18 337.33
									 Z" class="st28"/>
					</g>
					<g id="shape47-210" v:mID="47" v:groupContext="shape" v:layerMember="0" transform="translate(3.19243,-16.175)">
						<title>Sheet.47</title>
						<v:userDefs>
							<v:ud v:nameU="SurroundingRegionColor" v:prompt="" v:val="VT5(#5b9bd5)"/>
						</v:userDefs>
						<path d="M16.62 342.55 L16.62 333.29 C16.62 333.23 16.61 333.18 16.58 333.13 C16.55 333.07 16.5 333.02 16.44
									 332.98 C16.39 332.95 16.33 332.94 16.27 332.94 L0.35 332.94 C0.29 332.94 0.24 332.95 0.19 332.98
									 C0.13 333.01 0.08 333.07 0.04 333.12 C0.02 333.17 0 333.23 0 333.29 L0 342.55 L16.62 342.55
									 Z" class="st29"/>
					</g>
					<g id="shape48-212" v:mID="48" v:groupContext="shape" v:layerMember="0" transform="translate(1.97942,-10.81)">
						<title>Sheet.48</title>
						<v:userDefs>
							<v:ud v:nameU="SurroundingRegionColor" v:prompt="" v:val="VT5(#5b9bd5)"/>
						</v:userDefs>
						<path d="M0.96 340.83 L0 342.55 L19.06 342.55 L18.1 340.83 L0.96 340.83 Z" class="st26"/>
					</g>
				</g>
				<g id="group49-215" transform="translate(7.21495,-47.1858)" v:mID="49" v:groupContext="group" v:layerMember="0">
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
						<v:ud v:nameU="ConnGap" v:prompt="" v:val="VT0(0.083333333333333):0"/>
					</v:userDefs>
					<title>User.7</title>
					<g id="shape50-216" v:mID="50" v:groupContext="shape" v:layerMember="0"
							transform="translate(13.3353,-1.13687E-013)">
						<title>Sheet.50</title>
						<g id="shadow50-217" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<path d="M20.77 325.42 A2.63551 2.63601 -180 1 0 15.5 325.42 A2.63551 2.63601 -180 1 0 20.77 325.42 ZM20.96
										 328.77 L15.25 328.77 C14.84 328.77 14.46 328.91 14.16 329.14 C13.95 329.31 13.78 329.52
										 13.66 329.76 C13.54 330 13.47 330.27 13.47 330.55 L13.47 337.32 L15.03 337.32 L15.03 342.55
										 L21.12 342.55 L21.12 337.32 L22.74 337.32 L22.74 330.55 C22.74 330.14 22.6 329.76 22.36
										 329.46 C22.2 329.25 21.99 329.08 21.75 328.96 C21.51 328.84 21.24 328.77 20.96 328.77 Z"
									class="st24"/>
							<path d="M20.77 325.42 A2.63551 2.63601 -180 1 0 15.5 325.42 A2.63551 2.63601 -180 1 0 20.77 325.42"
									class="st25"/>
							<path d="M20.96 328.77 L15.25 328.77 C14.84 328.77 14.46 328.91 14.16 329.14 C13.95 329.31 13.78 329.52
										 13.66 329.76 C13.54 330 13.47 330.27 13.47 330.55 L13.47 337.32 L15.03 337.32 L15.03 342.55
										 L21.12 342.55 L21.12 337.32 L22.74 337.32 L22.74 330.55 C22.74 330.14 22.6 329.76 22.36
										 329.46 C22.2 329.25 21.99 329.08 21.75 328.96 C21.51 328.84 21.24 328.77 20.96 328.77"
									class="st25"/>
							<path d="M18.1 342.55 L18.1 338.37" class="st25"/>
							<path d="M15.03 337.32 L15.03 333.71" class="st25"/>
							<path d="M21.12 337.32 L21.12 333.71" class="st25"/>
							<path d="M0 337.32 L13.47 337.32" class="st25"/>
						</g>
						<path d="M20.77 325.42 A2.63551 2.63601 -180 1 0 15.5 325.42 A2.63551 2.63601 -180 1 0 20.77 325.42 ZM20.96
									 328.77 L15.25 328.77 C14.84 328.77 14.46 328.91 14.16 329.14 C13.95 329.31 13.78 329.52 13.66
									 329.76 C13.54 330 13.47 330.27 13.47 330.55 L13.47 337.32 L15.03 337.32 L15.03 342.55 L21.12
									 342.55 L21.12 337.32 L22.74 337.32 L22.74 330.55 C22.74 330.14 22.6 329.76 22.36 329.46 C22.2
									 329.25 21.99 329.08 21.75 328.96 C21.51 328.84 21.24 328.77 20.96 328.77 Z" class="st26"/>
						<path d="M20.77 325.42 A2.63551 2.63601 -180 1 0 15.5 325.42 A2.63551 2.63601 -180 1 0 20.77 325.42"
								class="st27"/>
						<path d="M20.96 328.77 L15.25 328.77 C14.84 328.77 14.46 328.91 14.16 329.14 C13.95 329.31 13.78 329.52 13.66
									 329.76 C13.54 330 13.47 330.27 13.47 330.55 L13.47 337.32 L15.03 337.32 L15.03 342.55 L21.12
									 342.55 L21.12 337.32 L22.74 337.32 L22.74 330.55 C22.74 330.14 22.6 329.76 22.36 329.46 C22.2
									 329.25 21.99 329.08 21.75 328.96 C21.51 328.84 21.24 328.77 20.96 328.77" class="st27"/>
						<path d="M18.1 342.55 L18.1 338.37" class="st27"/>
						<path d="M15.03 337.32 L15.03 333.71" class="st27"/>
						<path d="M21.12 337.32 L21.12 333.71" class="st27"/>
						<path d="M0 337.32 L13.47 337.32" class="st27"/>
					</g>
					<g id="shape51-232" v:mID="51" v:groupContext="shape" v:layerMember="0" transform="translate(0,-8.39743)">
						<title>Sheet.51</title>
						<g id="shadow51-233" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<path d="M21.09 325.52 C21.09 325.13 20.96 324.79 20.74 324.51 C20.59 324.32 20.4 324.16 20.19 324.06
										 C19.97 323.95 19.72 323.89 19.46 323.89 L3.55 323.89 C3.16 323.89 2.82 324.02 2.54 324.23
										 C2.35 324.38 2.19 324.57 2.09 324.79 C1.98 325.01 1.92 325.25 1.92 325.52 L1.92 336.04 L21.09
										 336.04 L21.09 325.52 ZM21.18 337.33 L1.77 337.33 L0 340.51 L0 342.55 L23.06 342.55 L23.06
										 340.51 L21.18 337.33 Z" class="st9"/>
						</g>
						<path d="M21.09 325.52 C21.09 325.13 20.96 324.79 20.74 324.51 C20.59 324.32 20.4 324.16 20.19 324.06 C19.97
									 323.95 19.72 323.89 19.46 323.89 L3.55 323.89 C3.16 323.89 2.82 324.02 2.54 324.23 C2.35 324.38
									 2.19 324.57 2.09 324.79 C1.98 325.01 1.92 325.25 1.92 325.52 L1.92 336.04 L21.09 336.04 L21.09
									 325.52 ZM21.18 337.33 L1.77 337.33 L0 340.51 L0 342.55 L23.06 342.55 L23.06 340.51 L21.18 337.33
									 Z" class="st28"/>
					</g>
					<g id="shape52-236" v:mID="52" v:groupContext="shape" v:layerMember="0" transform="translate(3.19243,-16.175)">
						<title>Sheet.52</title>
						<v:userDefs>
							<v:ud v:nameU="SurroundingRegionColor" v:prompt="" v:val="VT5(#5b9bd5)"/>
						</v:userDefs>
						<path d="M16.62 342.55 L16.62 333.29 C16.62 333.23 16.61 333.18 16.58 333.13 C16.55 333.07 16.5 333.02 16.44
									 332.98 C16.39 332.95 16.33 332.94 16.27 332.94 L0.35 332.94 C0.29 332.94 0.24 332.95 0.19 332.98
									 C0.13 333.01 0.08 333.07 0.04 333.12 C0.02 333.17 0 333.23 0 333.29 L0 342.55 L16.62 342.55
									 Z" class="st29"/>
					</g>
					<g id="shape53-238" v:mID="53" v:groupContext="shape" v:layerMember="0" transform="translate(1.97942,-10.81)">
						<title>Sheet.53</title>
						<v:userDefs>
							<v:ud v:nameU="SurroundingRegionColor" v:prompt="" v:val="VT5(#5b9bd5)"/>
						</v:userDefs>
						<path d="M0.96 340.83 L0 342.55 L19.06 342.55 L18.1 340.83 L0.96 340.83 Z" class="st26"/>
					</g>
				</g>
				<g id="group54-241" transform="translate(7.21495,-18.6146)" v:mID="54" v:groupContext="group" v:layerMember="0">
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
						<v:ud v:nameU="ConnGap" v:prompt="" v:val="VT0(0.083333333333333):0"/>
					</v:userDefs>
					<title>User.12</title>
					<g id="shape55-242" v:mID="55" v:groupContext="shape" v:layerMember="0"
							transform="translate(13.3353,-1.13687E-013)">
						<title>Sheet.55</title>
						<g id="shadow55-243" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<path d="M20.77 325.42 A2.63551 2.63601 -180 1 0 15.5 325.42 A2.63551 2.63601 -180 1 0 20.77 325.42 ZM20.96
										 328.77 L15.25 328.77 C14.84 328.77 14.46 328.91 14.16 329.14 C13.95 329.31 13.78 329.52
										 13.66 329.76 C13.54 330 13.47 330.27 13.47 330.55 L13.47 337.32 L15.03 337.32 L15.03 342.55
										 L21.12 342.55 L21.12 337.32 L22.74 337.32 L22.74 330.55 C22.74 330.14 22.6 329.76 22.36
										 329.46 C22.2 329.25 21.99 329.08 21.75 328.96 C21.51 328.84 21.24 328.77 20.96 328.77 Z"
									class="st24"/>
							<path d="M20.77 325.42 A2.63551 2.63601 -180 1 0 15.5 325.42 A2.63551 2.63601 -180 1 0 20.77 325.42"
									class="st25"/>
							<path d="M20.96 328.77 L15.25 328.77 C14.84 328.77 14.46 328.91 14.16 329.14 C13.95 329.31 13.78 329.52
										 13.66 329.76 C13.54 330 13.47 330.27 13.47 330.55 L13.47 337.32 L15.03 337.32 L15.03 342.55
										 L21.12 342.55 L21.12 337.32 L22.74 337.32 L22.74 330.55 C22.74 330.14 22.6 329.76 22.36
										 329.46 C22.2 329.25 21.99 329.08 21.75 328.96 C21.51 328.84 21.24 328.77 20.96 328.77"
									class="st25"/>
							<path d="M18.1 342.55 L18.1 338.37" class="st25"/>
							<path d="M15.03 337.32 L15.03 333.71" class="st25"/>
							<path d="M21.12 337.32 L21.12 333.71" class="st25"/>
							<path d="M0 337.32 L13.47 337.32" class="st25"/>
						</g>
						<path d="M20.77 325.42 A2.63551 2.63601 -180 1 0 15.5 325.42 A2.63551 2.63601 -180 1 0 20.77 325.42 ZM20.96
									 328.77 L15.25 328.77 C14.84 328.77 14.46 328.91 14.16 329.14 C13.95 329.31 13.78 329.52 13.66
									 329.76 C13.54 330 13.47 330.27 13.47 330.55 L13.47 337.32 L15.03 337.32 L15.03 342.55 L21.12
									 342.55 L21.12 337.32 L22.74 337.32 L22.74 330.55 C22.74 330.14 22.6 329.76 22.36 329.46 C22.2
									 329.25 21.99 329.08 21.75 328.96 C21.51 328.84 21.24 328.77 20.96 328.77 Z" class="st26"/>
						<path d="M20.77 325.42 A2.63551 2.63601 -180 1 0 15.5 325.42 A2.63551 2.63601 -180 1 0 20.77 325.42"
								class="st27"/>
						<path d="M20.96 328.77 L15.25 328.77 C14.84 328.77 14.46 328.91 14.16 329.14 C13.95 329.31 13.78 329.52 13.66
									 329.76 C13.54 330 13.47 330.27 13.47 330.55 L13.47 337.32 L15.03 337.32 L15.03 342.55 L21.12
									 342.55 L21.12 337.32 L22.74 337.32 L22.74 330.55 C22.74 330.14 22.6 329.76 22.36 329.46 C22.2
									 329.25 21.99 329.08 21.75 328.96 C21.51 328.84 21.24 328.77 20.96 328.77" class="st27"/>
						<path d="M18.1 342.55 L18.1 338.37" class="st27"/>
						<path d="M15.03 337.32 L15.03 333.71" class="st27"/>
						<path d="M21.12 337.32 L21.12 333.71" class="st27"/>
						<path d="M0 337.32 L13.47 337.32" class="st27"/>
					</g>
					<g id="shape56-258" v:mID="56" v:groupContext="shape" v:layerMember="0" transform="translate(0,-8.39743)">
						<title>Sheet.56</title>
						<g id="shadow56-259" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<path d="M21.09 325.52 C21.09 325.13 20.96 324.79 20.74 324.51 C20.59 324.32 20.4 324.16 20.19 324.06
										 C19.97 323.95 19.72 323.89 19.46 323.89 L3.55 323.89 C3.16 323.89 2.82 324.02 2.54 324.23
										 C2.35 324.38 2.19 324.57 2.09 324.79 C1.98 325.01 1.92 325.25 1.92 325.52 L1.92 336.04 L21.09
										 336.04 L21.09 325.52 ZM21.18 337.33 L1.77 337.33 L0 340.51 L0 342.55 L23.06 342.55 L23.06
										 340.51 L21.18 337.33 Z" class="st9"/>
						</g>
						<path d="M21.09 325.52 C21.09 325.13 20.96 324.79 20.74 324.51 C20.59 324.32 20.4 324.16 20.19 324.06 C19.97
									 323.95 19.72 323.89 19.46 323.89 L3.55 323.89 C3.16 323.89 2.82 324.02 2.54 324.23 C2.35 324.38
									 2.19 324.57 2.09 324.79 C1.98 325.01 1.92 325.25 1.92 325.52 L1.92 336.04 L21.09 336.04 L21.09
									 325.52 ZM21.18 337.33 L1.77 337.33 L0 340.51 L0 342.55 L23.06 342.55 L23.06 340.51 L21.18 337.33
									 Z" class="st28"/>
					</g>
					<g id="shape57-262" v:mID="57" v:groupContext="shape" v:layerMember="0" transform="translate(3.19243,-16.175)">
						<title>Sheet.57</title>
						<v:userDefs>
							<v:ud v:nameU="SurroundingRegionColor" v:prompt="" v:val="VT5(#5b9bd5)"/>
						</v:userDefs>
						<path d="M16.62 342.55 L16.62 333.29 C16.62 333.23 16.61 333.18 16.58 333.13 C16.55 333.07 16.5 333.02 16.44
									 332.98 C16.39 332.95 16.33 332.94 16.27 332.94 L0.35 332.94 C0.29 332.94 0.24 332.95 0.19 332.98
									 C0.13 333.01 0.08 333.07 0.04 333.12 C0.02 333.17 0 333.23 0 333.29 L0 342.55 L16.62 342.55
									 Z" class="st29"/>
					</g>
					<g id="shape58-264" v:mID="58" v:groupContext="shape" v:layerMember="0" transform="translate(1.97942,-10.81)">
						<title>Sheet.58</title>
						<v:userDefs>
							<v:ud v:nameU="SurroundingRegionColor" v:prompt="" v:val="VT5(#5b9bd5)"/>
						</v:userDefs>
						<path d="M0.96 340.83 L0 342.55 L19.06 342.55 L18.1 340.83 L0.96 340.83 Z" class="st26"/>
					</g>
				</g>
				<g id="group59-267" transform="translate(171.161,-45.6707)" v:mID="59" v:groupContext="group" v:layerMember="0">
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
						<v:ud v:nameU="ConnGap" v:prompt="" v:val="VT0(0.083333333333333):0"/>
					</v:userDefs>
					<title>Data Center</title>
					<g id="shape60-268" v:mID="60" v:groupContext="shape" v:layerMember="0">
						<title>Sheet.60</title>
						<g id="shadow60-269" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<ellipse cx="37.8785" cy="331.299" rx="37.8785" ry="11.246" class="st9"/>
						</g>
						<ellipse cx="37.8785" cy="331.299" rx="37.8785" ry="11.246" class="st10"/>
					</g>
					<g id="shape61-272" v:mID="61" v:groupContext="shape" v:layerMember="0" transform="translate(6.86487,-7.30475)">
						<title>Sheet.61</title>
						<g id="shadow61-273" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<path d="M54.1 311.79 L43.28 311.79 L43.28 342.55 L62.03 342.55 L62.03 311.79 L54.1 311.79 ZM43.28 332.44
										 L43.28 311.79 L51.21 311.79 L51.21 301.69 L32.33 301.69 L32.33 311.79 L40.39 311.79 L40.39
										 332.44 L43.28 332.44 ZM40.39 301.69 L40.39 293.03 L21.64 293.03 L21.64 301.69 L29.57 301.69
										 L29.57 311.79 L32.46 311.79 L32.46 301.69 L40.39 301.69 ZM32.46 311.79 L21.64 311.79 L21.64
										 342.55 L40.39 342.55 L40.39 311.79 L32.46 311.79 ZM10.82 311.79 L0 311.79 L0 342.55 L18.75
										 342.55 L18.75 311.79 L10.82 311.79 ZM21.64 311.79 L29.57 311.79 L29.57 301.69 L10.82 301.69
										 L10.82 311.79 L18.75 311.79 L18.75 332.44 L21.64 332.44 L21.64 311.79 Z" class="st9"/>
						</g>
						<path d="M54.1 311.79 L43.28 311.79 L43.28 342.55 L62.03 342.55 L62.03 311.79 L54.1 311.79 ZM43.28 332.44
									 L43.28 311.79 L51.21 311.79 L51.21 301.69 L32.33 301.69 L32.33 311.79 L40.39 311.79 L40.39 332.44
									 L43.28 332.44 ZM40.39 301.69 L40.39 293.03 L21.64 293.03 L21.64 301.69 L29.57 301.69 L29.57
									 311.79 L32.46 311.79 L32.46 301.69 L40.39 301.69 ZM32.46 311.79 L21.64 311.79 L21.64 342.55
									 L40.39 342.55 L40.39 311.79 L32.46 311.79 ZM10.82 311.79 L0 311.79 L0 342.55 L18.75 342.55 L18.75
									 311.79 L10.82 311.79 ZM21.64 311.79 L29.57 311.79 L29.57 301.69 L10.82 301.69 L10.82 311.79
									 L18.75 311.79 L18.75 332.44 L21.64 332.44 L21.64 311.79 Z" class="st10"/>
					</g>
					<g id="shape62-276" v:mID="62" v:groupContext="shape" v:layerMember="0" transform="translate(20.0835,-20.5174)">
						<title>Sheet.62</title>
						<g id="shadow62-277" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<path d="M45.36 341.36 A1.13296 1.18615 -180 1 0 43.09 341.36 A1.13296 1.18615 -180 1 0 45.36 341.36
										 ZM23.46 341.36 A1.13296 1.18615 -180 1 0 21.2 341.36 A1.13296 1.18615 -180 1 0 23.46 341.36
										 ZM2.27 341.36 A1.13296 1.18615 -180 1 0 0 341.36 A1.13296 1.18615 -180 1 0 2.27 341.36 Z"
									class="st24"/>
						</g>
						<path d="M45.36 341.36 A1.13296 1.18615 -180 1 0 43.09 341.36 A1.13296 1.18615 -180 1 0 45.36 341.36 ZM23.46
									 341.36 A1.13296 1.18615 -180 1 0 21.2 341.36 A1.13296 1.18615 -180 1 0 23.46 341.36 ZM2.27 341.36
									 A1.13296 1.18615 -180 1 0 0 341.36 A1.13296 1.18615 -180 1 0 2.27 341.36 Z" class="st30"/>
					</g>
					<g id="shape63-282" v:mID="63" v:groupContext="shape" v:layerMember="0" transform="translate(14.2717,-12.5134)">
						<title>Sheet.63</title>
						<v:userDefs>
							<v:ud v:nameU="SurroundingRegionColor" v:prompt="" v:val="VT5(#5b9bd5)"/>
						</v:userDefs>
						<g id="shadow63-283" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<path d="M43.09 342.55 L51.17 342.55 L51.17 341.74 L43.09 341.74 L43.09 342.55 ZM43.09 340.12 L51.17
										 340.12 L51.17 339.32 L43.09 339.32 L43.09 340.12 ZM43.09 337.69 L51.17 337.69 L51.17 336.89
										 L43.09 336.89 L43.09 337.69 ZM21.2 342.55 L29.27 342.55 L29.27 341.74 L21.2 341.74 L21.2
										 342.55 ZM21.2 340.12 L29.27 340.12 L29.27 339.32 L21.2 339.32 L21.2 340.12 ZM21.2 337.69
										 L29.27 337.69 L29.27 336.89 L21.2 336.89 L21.2 337.69 ZM-0 342.55 L8.08 342.55 L8.08 341.74
										 L-0 341.74 L-0 342.55 ZM-0 340.12 L8.08 340.12 L8.08 339.32 L-0 339.32 L-0 340.12 ZM-0 337.69
										 L8.08 337.69 L8.08 336.89 L-0 336.89 L-0 337.69 Z" class="st24"/>
						</g>
						<path d="M43.09 342.55 L51.17 342.55 L51.17 341.74 L43.09 341.74 L43.09 342.55 ZM43.09 340.12 L51.17 340.12
									 L51.17 339.32 L43.09 339.32 L43.09 340.12 ZM43.09 337.69 L51.17 337.69 L51.17 336.89 L43.09
									 336.89 L43.09 337.69 ZM21.2 342.55 L29.27 342.55 L29.27 341.74 L21.2 341.74 L21.2 342.55 ZM21.2
									 340.12 L29.27 340.12 L29.27 339.32 L21.2 339.32 L21.2 340.12 ZM21.2 337.69 L29.27 337.69 L29.27
									 336.89 L21.2 336.89 L21.2 337.69 ZM-0 342.55 L8.08 342.55 L8.08 341.74 L-0 341.74 L-0 342.55
									 ZM-0 340.12 L8.08 340.12 L8.08 339.32 L-0 339.32 L-0 340.12 ZM-0 337.69 L8.08 337.69 L8.08 336.89
									 L-0 336.89 L-0 337.69 Z" class="st26"/>
					</g>
				</g>
				<g id="group64-288" transform="translate(59.5234,-47.1858)" v:mID="64" v:groupContext="group">
					<v:custProps>
						<v:cp v:nameU="AssetNumber" v:lbl="Asset Number" v:prompt="" v:type="0" v:format="" v:sortKey="Asset"
								v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
						<v:cp v:nameU="SerialNumber" v:lbl="Serial Number" v:prompt="" v:type="0" v:format="" v:sortKey="Asset"
								v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
						<v:cp v:nameU="Location" v:lbl="Location" v:prompt="" v:type="0" v:format="" v:sortKey="Asset"
								v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
						<v:cp v:nameU="Building" v:lbl="Building" v:prompt="" v:type="0" v:format="" v:sortKey="Asset"
								v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
						<v:cp v:nameU="Room" v:lbl="Room" v:prompt="" v:type="0" v:format="" v:sortKey="Asset" v:invis="false"
								v:ask="false" v:langID="1033" v:cal="0"/>
						<v:cp v:nameU="Manufacturer" v:lbl="Manufacturer" v:prompt="" v:type="0" v:format="" v:sortKey="Equipment"
								v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
						<v:cp v:nameU="ProductNumber" v:lbl="Product Number" v:prompt="" v:type="0" v:format=""
								v:sortKey="Equipment" v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
						<v:cp v:nameU="PartNumber" v:lbl="Part Number" v:prompt="" v:type="0" v:format="" v:sortKey="Equipment"
								v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
						<v:cp v:nameU="ProductDescription" v:lbl="Product Description" v:prompt="" v:type="0" v:format=""
								v:sortKey="Equipment" v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
						<v:cp v:nameU="NetworkName" v:lbl="Network Name" v:prompt="" v:type="0" v:format="" v:sortKey="Network"
								v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
						<v:cp v:nameU="IPAddress" v:lbl="IP Address" v:prompt="" v:type="0" v:format="" v:sortKey="Network"
								v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
						<v:cp v:nameU="SubnetMask" v:lbl="Subnet Mask" v:prompt="" v:type="0" v:format="" v:sortKey="Network"
								v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
						<v:cp v:nameU="AdminInterface" v:lbl="Administrative Interface" v:prompt="" v:type="0" v:format=""
								v:sortKey="Network" v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
						<v:cp v:nameU="NumberofPorts" v:lbl="Number of Ports" v:prompt="" v:type="0" v:format=""
								v:sortKey="Network" v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
						<v:cp v:nameU="CommunityString" v:lbl="Community String" v:prompt="" v:type="0" v:format=""
								v:sortKey="Network" v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
						<v:cp v:nameU="NetworkDescription" v:lbl="Network Description" v:prompt="" v:type="0" v:format=""
								v:sortKey="Network" v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
						<v:cp v:nameU="MACAddress" v:lbl="MAC Address" v:prompt="" v:type="0" v:format="" v:sortKey="Network"
								v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
						<v:cp v:nameU="ShapeClass" v:lbl="ShapeClass" v:prompt="" v:type="0" v:format="" v:sortKey=""
								v:invis="true" v:ask="false" v:langID="1033" v:cal="0" v:val="VT4(Equipment)"/>
						<v:cp v:nameU="ShapeType" v:lbl="ShapeType" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="true"
								v:ask="false" v:langID="1033" v:cal="0" v:val="VT4(Device)"/>
						<v:cp v:nameU="SubShapeType" v:lbl="SubShapeType" v:prompt="" v:type="0" v:format="" v:sortKey=""
								v:invis="true" v:ask="false" v:langID="1033" v:cal="0" v:val="VT4(Load balancer)"/>
					</v:custProps>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
						<v:ud v:nameU="ShapeClass" v:prompt="" v:val="VT0(5):26"/>
						<v:ud v:nameU="SolSH" v:prompt="" v:val="VT14({BF0433D9-CD73-4EB5-8390-8653BE590246}):41"/>
						<v:ud v:nameU="visLegendShape" v:prompt="" v:val="VT0(2):26"/>
					</v:userDefs>
					<title>Load balancer</title>
					<g id="shape65-289" v:mID="65" v:groupContext="shape" transform="translate(0,-1.653)">
						<title>Sheet.65</title>
						<g id="shadow65-290" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<path d="M12.18 329.62 L4.06 329.62 L0 332.02 L0 342.55 L16.23 342.55 L16.23 332.02 L12.18 329.62 Z"
									class="st24"/>
							<path d="M0 332.02 L16.23 332.02" class="st25"/>
							<path d="M12.18 329.62 L4.06 329.62 L0 332.02 L0 342.55 L16.23 342.55 L16.23 332.02 L12.18 329.62"
									class="st25"/>
						</g>
						<path d="M12.18 329.62 L4.06 329.62 L0 332.02 L0 342.55 L16.23 342.55 L16.23 332.02 L12.18 329.62 Z"
								class="st30"/>
						<path d="M0 332.02 L16.23 332.02" class="st31"/>
						<path d="M12.18 329.62 L4.06 329.62 L0 332.02 L0 342.55 L16.23 342.55 L16.23 332.02 L12.18 329.62"
								class="st31"/>
					</g>
					<g id="shape66-297" v:mID="66" v:groupContext="shape" transform="translate(1.81062,-2.91583)">
						<title>Sheet.66</title>
						<g id="shadow66-298" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<path d="M10.22 341.92 L9.29 342.12 L9.95 342.55 L11.2 342.23 L10.99 340.96 L10.33 340.52 L10.53 341.44
										 L8.34 340.01 L8.03 340.49 L10.22 341.92 ZM11.46 338.22 L8.84 338.22 L8.84 338.78 L11.45
										 338.78 L10.78 339.45 L11.57 339.45 L12.45 338.5 L11.57 337.55 L10.78 337.55 L11.46 338.22
										 ZM10.48 335.2 L8.29 336.64 L8.6 337.12 L10.79 335.68 L10.59 336.61 L11.25 336.17 L11.46
										 334.9 L10.21 334.58 L9.55 335.01 L10.48 335.2 ZM6.25 336.37 C5.11 336.37 4.19 337.29 4.19
										 338.43 C4.19 339.56 5.11 340.48 6.25 340.48 C7.38 340.48 8.31 339.56 8.31 338.43 C8.31 337.29
										 7.38 336.37 6.25 336.37 ZM6.25 337.02 C7.02 337.02 7.66 337.65 7.66 338.43 C7.66 339.2 7.02
										 339.83 6.25 339.83 C5.47 339.83 4.84 339.2 4.84 338.43 C4.84 337.65 5.47 337.02 6.25 337.02
										 ZM2.62 338.14 L0 338.14 L0 338.71 L2.62 338.71 L1.94 339.38 L2.74 339.38 L3.61 338.43 L2.73
										 337.47 L1.95 337.47 L2.62 338.14 Z" class="st9"/>
						</g>
						<path d="M10.22 341.92 L9.29 342.12 L9.95 342.55 L11.2 342.23 L10.99 340.96 L10.33 340.52 L10.53 341.44 L8.34
									 340.01 L8.03 340.49 L10.22 341.92 ZM11.46 338.22 L8.84 338.22 L8.84 338.78 L11.45 338.78 L10.78
									 339.45 L11.57 339.45 L12.45 338.5 L11.57 337.55 L10.78 337.55 L11.46 338.22 ZM10.48 335.2 L8.29
									 336.64 L8.6 337.12 L10.79 335.68 L10.59 336.61 L11.25 336.17 L11.46 334.9 L10.21 334.58 L9.55
									 335.01 L10.48 335.2 ZM6.25 336.37 C5.11 336.37 4.19 337.29 4.19 338.43 C4.19 339.56 5.11 340.48
									 6.25 340.48 C7.38 340.48 8.31 339.56 8.31 338.43 C8.31 337.29 7.38 336.37 6.25 336.37 ZM6.25
									 337.02 C7.02 337.02 7.66 337.65 7.66 338.43 C7.66 339.2 7.02 339.83 6.25 339.83 C5.47 339.83
									 4.84 339.2 4.84 338.43 C4.84 337.65 5.47 337.02 6.25 337.02 ZM2.62 338.14 L0 338.14 L0 338.71
									 L2.62 338.71 L1.94 339.38 L2.74 339.38 L3.61 338.43 L2.73 337.47 L1.95 337.47 L2.62 338.14 Z"
								class="st32"/>
					</g>
				</g>
				<g id="group67-301" transform="translate(104.617,-86.5795)" v:mID="67" v:groupContext="group">
					<v:userDefs>
						<v:ud v:nameU="SkinColor" v:prompt="" v:val="VT5(#da8c36)"/>
						<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					</v:userDefs>
					<title>Directory server</title>
					<g id="shape68-302" v:mID="68" v:groupContext="shape" transform="translate(0,-0.451005)">
						<title>Sheet.68</title>
						<g id="shadow68-303" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<path d="M0.47 329.86 L0.47 331.94 L1.46 332.57 L3.33 331.52 L15.43 338.57 L15.43 340.61 L16.42 341.24
										 L18.24 340.22 L22.24 342.55 L22.24 339.27 L36.07 331.28 L36.07 321.27 L19.64 311.85 L3.16
										 321.13 L3.16 321.5 L0 319.68 L0 329.58 L0.47 329.86 Z" class="st33"/>
						</g>
						<path d="M0.47 329.86 L0.47 331.94 L1.46 332.57 L3.33 331.52 L15.43 338.57 L15.43 340.61 L16.42 341.24 L18.24
									 340.22 L22.24 342.55 L22.24 339.27 L36.07 331.28 L36.07 321.27 L19.64 311.85 L3.16 321.13 L3.16
									 321.5 L0 319.68 L0 329.58 L0.47 329.86 Z" class="st34"/>
					</g>
					<g id="shape69-306" v:mID="69" v:groupContext="shape" transform="translate(3.1636,-11.8063)">
						<title>Sheet.69</title>
						<path d="M16.48 323.24 L32.91 332.66 L16.31 342.55 L0 333.26 L0 332.52 L16.48 323.24 Z" class="st35"/>
					</g>
					<g id="shape70-310" v:mID="70" v:groupContext="shape" transform="translate(19.06,-3.68954)">
						<title>Sheet.70</title>
						<path d="M17.01 324.55 L0 334.19 L3.18 342.55 L17.01 334.56 L17.01 324.55 Z" class="st36"/>
					</g>
					<g id="shape71-314" v:mID="71" v:groupContext="shape" transform="translate(0,-0.415652)">
						<title>Sheet.71</title>
						<path d="M22.24 342.55 L0 329.58 L0 319.68 L22.24 332.43 L22.24 342.55 Z" class="st37"/>
					</g>
					<g id="shape72-322" v:mID="72" v:groupContext="shape" transform="translate(0.82443,-19.8334)">
						<title>Sheet.72</title>
						<path d="M1.13 341.58 a0.653986 0.653986 -180 0 0 -0.73971 -0.492434 a0.656072 0.656072 -180 0 0 -0.253101
									 0.865731 a0.653066 0.653066 -180 0 0 0.740769 0.491375 a0.655459 0.655459 -180 0 0 0.252042
									 -0.864672 Z" class="st38"/>
					</g>
					<g id="shape73-326" v:mID="73" v:groupContext="shape" transform="translate(3.62283,-15.1638)">
						<title>Sheet.73</title>
						<path d="M3.22 339.78 A1.86495 1.86495 -180 0 0 1.11 338.38 A1.8709 1.8709 -180 0 0 0.38 340.85 A1.86532
									 1.86532 -180 0 0 2.5 342.25 A1.87264 1.87264 -180 0 0 3.22 339.78 Z" class="st38"/>
					</g>
					<g id="shape74-329" v:mID="74" v:groupContext="shape" transform="translate(3.62283,-10.4867)">
						<title>Sheet.74</title>
						<path d="M3.22 339.78 A1.86495 1.86495 -180 0 0 1.11 338.38 A1.8709 1.8709 -180 0 0 0.38 340.85 A1.86532
									 1.86532 -180 0 0 2.5 342.25 A1.87264 1.87264 -180 0 0 3.22 339.78 Z" class="st38"/>
					</g>
					<g id="shape75-332" v:mID="75" v:groupContext="shape" transform="translate(4.52404,-16.3668)">
						<title>Sheet.75</title>
						<path d="M1.61 341.16 a0.931952 0.931952 -180 0 0 -1.05741 -0.702645 a0.935408 0.935408 -180 0 0 -0.361118
									 1.23585 a0.932139 0.932139 -180 0 0 1.05794 0.702645 a0.935822 0.935822 -180 0 0 0.360589 -1.23585
									 Z" class="st39"/>
					</g>
					<g id="shape76-336" v:mID="76" v:groupContext="shape" transform="translate(4.52404,-11.6897)">
						<title>Sheet.76</title>
						<path d="M1.61 341.16 a0.931952 0.931952 -180 0 0 -1.05741 -0.702645 a0.935875 0.935875 -180 0 0 -0.361118
									 1.23585 a0.932139 0.932139 -180 0 0 1.05794 0.702645 a0.935822 0.935822 -180 0 0 0.360589 -1.23585
									 Z" class="st39"/>
					</g>
					<g id="shape77-339" v:mID="77" v:groupContext="shape" transform="translate(7.78787,-8.83469)">
						<title>Sheet.77</title>
						<path d="M0 341.57 L0.05 333.6 L1.83 334.57 L1.78 342.55 L0 341.57 Z" class="st40"/>
					</g>
					<g id="shape78-343" v:mID="78" v:groupContext="shape" transform="translate(10.204,-7.4008)">
						<title>Sheet.78</title>
						<path d="M0 341.57 L0.05 333.6 L1.83 334.57 L1.78 342.55 L0 341.57 Z" class="st40"/>
					</g>
					<g id="shape79-346" v:mID="79" v:groupContext="shape" transform="translate(12.6196,-5.96639)">
						<title>Sheet.79</title>
						<path d="M0 341.57 L0.05 333.6 L1.83 334.57 L1.78 342.55 L0 341.57 Z" class="st40"/>
					</g>
					<g id="shape80-349" v:mID="80" v:groupContext="shape" transform="translate(15.0357,-4.53251)">
						<title>Sheet.80</title>
						<path d="M0 341.57 L0.05 333.6 L1.83 334.57 L1.78 342.55 L0 341.57 Z" class="st40"/>
					</g>
					<g id="shape81-352" v:mID="81" v:groupContext="shape" transform="translate(8.24006,-10.0631)">
						<title>Sheet.81</title>
						<path d="M0.85 342.24 a0.388199 0.388199 0 0 1 -0.425188 0.308698 a0.638045 0.638045 0 0 1 -0.424658 -0.573447
									 L0 336.5 a0.387575 0.387575 0 0 1 0.424658 -0.308698 a0.637725 0.637725 0 0 1 0.425188 0.573447
									 L0.85 342.24 Z" class="st41"/>
					</g>
					<g id="shape82-356" v:mID="82" v:groupContext="shape" transform="translate(10.6556,-8.62924)">
						<title>Sheet.82</title>
						<path d="M0.85 342.24 a0.388199 0.388199 0 0 1 -0.425188 0.308698 a0.638045 0.638045 0 0 1 -0.424658 -0.573447
									 L0 336.5 a0.387575 0.387575 0 0 1 0.424658 -0.308698 a0.637725 0.637725 0 0 1 0.425188 0.573447
									 L0.85 342.24 Z" class="st41"/>
					</g>
					<g id="shape83-359" v:mID="83" v:groupContext="shape" transform="translate(13.0717,-7.19483)">
						<title>Sheet.83</title>
						<path d="M0.85 342.24 a0.388199 0.388199 0 0 1 -0.425188 0.308698 a0.638045 0.638045 0 0 1 -0.424658 -0.573447
									 L0 336.5 a0.387575 0.387575 0 0 1 0.424658 -0.308698 a0.637725 0.637725 0 0 1 0.425188 0.573447
									 L0.85 342.24 Z" class="st41"/>
					</g>
					<g id="shape84-362" v:mID="84" v:groupContext="shape" transform="translate(15.4873,-5.76095)">
						<title>Sheet.84</title>
						<path d="M0.85 342.24 a0.388502 0.388502 0 0 1 -0.425717 0.308698 a0.638367 0.638367 0 0 1 -0.424129 -0.573447
									 L0 336.5 a0.387272 0.387272 0 0 1 0.424129 -0.308698 a0.638235 0.638235 0 0 1 0.425717 0.573447
									 L0.85 342.24 Z" class="st41"/>
					</g>
					<g id="shape85-365" v:mID="85" v:groupContext="shape" transform="translate(7.78787,-9.81214)">
						<title>Sheet.85</title>
						<path d="M0 342.55 L0.05 334.57 L1.83 335.55 L0 342.55 Z" class="st42"/>
						<path d="M0 342.55 L0.05 334.57 L1.83 335.55" class="st43"/>
					</g>
					<g id="shape86-368" v:mID="86" v:groupContext="shape" transform="translate(10.204,-8.37826)">
						<title>Sheet.86</title>
						<path d="M0 342.55 L0.05 334.57 L1.83 335.55 L0 342.55 Z" class="st42"/>
						<path d="M0 342.55 L0.05 334.57 L1.83 335.55" class="st43"/>
					</g>
					<g id="shape87-371" v:mID="87" v:groupContext="shape" transform="translate(12.6196,-6.94385)">
						<title>Sheet.87</title>
						<path d="M0 342.55 L0.05 334.57 L1.83 335.55 L0 342.55 Z" class="st42"/>
						<path d="M0 342.55 L0.05 334.57 L1.83 335.55" class="st43"/>
					</g>
					<g id="shape88-374" v:mID="88" v:groupContext="shape" transform="translate(15.0357,-5.50996)">
						<title>Sheet.88</title>
						<path d="M0 342.55 L0.05 334.57 L1.83 335.55 L0 342.55 Z" class="st42"/>
						<path d="M0 342.55 L0.05 334.57 L1.83 335.55" class="st43"/>
					</g>
					<g id="shape89-377" v:mID="89" v:groupContext="shape" transform="translate(7.78787,-4.53251)">
						<title>Sheet.89</title>
						<path d="M9.08 334.57 L9.03 342.55 L7.25 341.57 L9.08 334.57 ZM6.66 333.14 L6.61 341.11 L4.83 340.13 L6.66
									 333.14 ZM4.25 331.7 L4.2 339.68 L2.42 338.7 L4.25 331.7 ZM1.83 330.27 L1.78 338.24 L0 337.27
									 L1.83 330.27 Z" class="st42"/>
						<path d="M9.08 334.57 L9.03 342.55 L7.25 341.57M6.66 333.14 L6.61 341.11 L4.83 340.13M4.25 331.7 L4.2 339.68
									 L2.42 338.7M1.83 330.27 L1.78 338.24 L0 337.27" class="st44"/>
					</g>
					<g id="shape90-380" v:mID="90" v:groupContext="shape" transform="translate(2.22125,-11.8454)">
						<title>Sheet.90</title>
						<path d="M0 341.85 L0.63 341.42 L1.42 341.78 L0.03 342.55 L0 341.85 Z" class="st45"/>
					</g>
					<g id="shape91-384" v:mID="91" v:groupContext="shape" transform="translate(17.1796,-3.17487)">
						<title>Sheet.91</title>
						<path d="M0 341.85 L0.63 341.42 L1.42 341.78 L0.03 342.55 L0 341.85 Z" class="st45"/>
					</g>
					<g id="shape92-387" v:mID="92" v:groupContext="shape" transform="translate(1.46036,-10.3893)">
						<title>Sheet.92</title>
						<path d="M2.12 341.35 L0 342.55 L0 333.54 L2.12 332.29 L2.12 333.41 L0.79 334.15 L0.79 341.09 L2.18 340.33
									 L2.12 341.35 Z" class="st36"/>
					</g>
					<g id="shape93-390" v:mID="93" v:groupContext="shape" transform="translate(16.4187,-1.71875)">
						<title>Sheet.93</title>
						<path d="M2.12 341.35 L0 342.55 L0 333.54 L2.12 332.29 L2.12 333.41 L0.79 334.15 L0.79 341.09 L2.18 340.33
									 L2.12 341.35 Z" class="st36"/>
					</g>
					<g id="shape94-393" v:mID="94" v:groupContext="shape" transform="translate(0.467548,-10.3893)">
						<title>Sheet.94</title>
						<path d="M0.99 333.54 L3.11 332.29 L2.12 331.66 L0 332.91 L0 341.92 L0.99 342.55 L0.99 333.54 Z"
								class="st46"/>
					</g>
					<g id="shape95-397" v:mID="95" v:groupContext="shape" transform="translate(15.4259,-1.71875)">
						<title>Sheet.95</title>
						<path d="M0.99 333.54 L3.11 332.29 L2.12 331.66 L0 332.91 L0 341.92 L0.99 342.55 L0.99 333.54 Z"
								class="st46"/>
					</g>
					<g id="shape96-400" v:mID="96" v:groupContext="shape" transform="translate(0.467548,-1.71928)">
						<title>Sheet.96</title>
						<path d="M17.34 339.96 L16.75 340.37 L16.75 334.15 L18.07 333.41 L18.07 332.29 L17.08 331.66 L14.96 332.91
									 L14.96 341.92 L15.95 342.55 L18.07 341.35 L18.14 340.33 L17.34 339.96 ZM2.38 331.29 L1.79 331.7
									 L1.79 325.48 L3.11 324.74 L3.11 323.62 L2.12 322.99 L0 324.24 L0 333.25 L0.99 333.87 L3.11 332.68
									 L3.18 331.66 L2.38 331.29 Z" class="st47"/>
					</g>
					<g id="shape97-402" v:mID="97" v:groupContext="shape" transform="translate(19.9526,-8.71396)">
						<title>Sheet.97</title>
						<path d="M1.13 341.58 a0.653986 0.653986 -180 0 0 -0.73971 -0.492434 a0.656072 0.656072 -180 0 0 -0.253101
									 0.865731 a0.653066 0.653066 -180 0 0 0.740769 0.491375 a0.655459 0.655459 -180 0 0 0.252042
									 -0.864672 Z" class="st38"/>
					</g>
					<g id="shape98-405" v:mID="98" v:groupContext="shape" transform="translate(19.9526,-2.35997)">
						<title>Sheet.98</title>
						<path d="M1.13 341.58 a0.653986 0.653986 -180 0 0 -0.73971 -0.492434 a0.656072 0.656072 -180 0 0 -0.253101
									 0.865731 a0.653066 0.653066 -180 0 0 0.740769 0.491375 a0.655459 0.655459 -180 0 0 0.252042
									 -0.864672 Z" class="st38"/>
					</g>
					<g id="shape99-408" v:mID="99" v:groupContext="shape" transform="translate(0,-0.415652)">
						<title>Sheet.99</title>
						<path d="M36.07 331.28 L36.07 321.27 L19.64 311.85 L3.16 321.13 L3.16 321.52 L0 319.68 L0 329.58 L0.47 329.86
									 L0.47 331.94 L1.46 332.57 L3.33 331.52 L15.43 338.57 L15.43 340.61 L16.42 341.24 L18.24 340.22
									 L22.24 342.55 L22.24 339.27 L36.07 331.28 Z" class="st47"/>
					</g>
					<g id="shape100-410" v:mID="100" v:groupContext="shape" transform="translate(27.8077,-2.86477)">
						<title>Sheet.100</title>
						<path d="M0.29 342.55 L6.62 338.89 A1.82805 1.82805 0 0 1 6.62 336.9 L0.29 340.55 A1.82805 1.82805 -180 0
									 0 0.29 342.55 Z" class="st48"/>
					</g>
					<g id="shape101-412" v:mID="101" v:groupContext="shape" transform="translate(23.5035,-4.85627)">
						<title>Sheet.101</title>
						<path d="M4.6 342.55 L10.92 338.89 L6.32 336.24 L0 339.89 L4.6 342.55 Z" class="st49"/>
					</g>
					<g id="shape102-416" v:mID="102" v:groupContext="shape" transform="translate(23.3588,-2.86477)">
						<title>Sheet.102</title>
						<path d="M0.14 339.89 L4.74 342.55 A1.82805 1.82805 0 0 1 4.74 340.55 L0.14 337.9 A3.49826 3.49826 -180 0
									 0 0.14 339.89 Z" class="st50"/>
					</g>
					<g id="shape103-420" v:mID="103" v:groupContext="shape" transform="translate(25.8933,-5.98478)">
						<title>Sheet.103</title>
						<path d="M2.87 342.55 L0 340.89" class="st51"/>
						<path d="M0.94 340.34 L3.82 342" class="st51"/>
						<path d="M1.88 339.8 L4.76 341.46" class="st51"/>
						<path d="M2.82 339.26 L5.7 340.92" class="st51"/>
						<path d="M3.76 338.71 L6.64 340.37" class="st51"/>
					</g>
					<g id="shape104-427" v:mID="104" v:groupContext="shape" transform="translate(23.5035,-7.51159)">
						<title>Sheet.104</title>
						<path d="M5.13 341.17 L11.45 337.52 A11.9345 11.9345 0 0 1 6.32 338.89 L0 342.55 A11.9345 11.9345 -180 0
									 0 5.13 341.17 Z" class="st52"/>
					</g>
					<g id="shape105-435" v:mID="105" v:groupContext="shape" transform="translate(30.2106,-4.74563)">
						<title>Sheet.105</title>
						<path d="M0.98 341.98 L0 342.55" class="st51"/>
						<path d="M1.26 341.48 L2.24 340.92" class="st51"/>
						<path d="M2.53 340.42 L3.51 339.86" class="st51"/>
					</g>
					<g id="shape106-440" v:mID="106" v:groupContext="shape" transform="translate(23.3588,-2.86477)">
						<title>Sheet.106</title>
						<path d="M0.14 339.89 L4.74 342.55 L11.07 338.89 A1.82805 1.82805 0 0 1 11.07 336.9 L7.85 335.04 L11.6 332.87
									 A11.9345 11.9345 0 0 1 6.47 334.25 L0.14 337.9 A3.49826 3.49826 -180 0 0 0.14 339.89"
								class="st53"/>
					</g>
				</g>
				<g id="group107-443" transform="translate(104.617,-33.8201)" v:mID="107" v:groupContext="group">
					<v:userDefs>
						<v:ud v:nameU="SkinColor" v:prompt="" v:val="VT5(#da8c36)"/>
						<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					</v:userDefs>
					<title>Directory server.104</title>
					<g id="shape108-444" v:mID="108" v:groupContext="shape" transform="translate(0,-0.451005)">
						<title>Sheet.108</title>
						<g id="shadow108-445" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<path d="M0.47 329.86 L0.47 331.94 L1.46 332.57 L3.33 331.52 L15.43 338.57 L15.43 340.61 L16.42 341.24
										 L18.24 340.22 L22.24 342.55 L22.24 339.27 L36.07 331.28 L36.07 321.27 L19.64 311.85 L3.16
										 321.13 L3.16 321.5 L0 319.68 L0 329.58 L0.47 329.86 Z" class="st33"/>
						</g>
						<path d="M0.47 329.86 L0.47 331.94 L1.46 332.57 L3.33 331.52 L15.43 338.57 L15.43 340.61 L16.42 341.24 L18.24
									 340.22 L22.24 342.55 L22.24 339.27 L36.07 331.28 L36.07 321.27 L19.64 311.85 L3.16 321.13 L3.16
									 321.5 L0 319.68 L0 329.58 L0.47 329.86 Z" class="st34"/>
					</g>
					<g id="shape109-448" v:mID="109" v:groupContext="shape" transform="translate(3.1636,-11.8063)">
						<title>Sheet.109</title>
						<path d="M16.48 323.24 L32.91 332.66 L16.31 342.55 L0 333.26 L0 332.52 L16.48 323.24 Z" class="st35"/>
					</g>
					<g id="shape110-451" v:mID="110" v:groupContext="shape" transform="translate(19.06,-3.68954)">
						<title>Sheet.110</title>
						<path d="M17.01 324.55 L0 334.19 L3.18 342.55 L17.01 334.56 L17.01 324.55 Z" class="st36"/>
					</g>
					<g id="shape111-454" v:mID="111" v:groupContext="shape" transform="translate(0,-0.415652)">
						<title>Sheet.111</title>
						<path d="M22.24 342.55 L0 329.58 L0 319.68 L22.24 332.43 L22.24 342.55 Z" class="st37"/>
					</g>
					<g id="shape112-457" v:mID="112" v:groupContext="shape" transform="translate(0.82443,-19.8334)">
						<title>Sheet.112</title>
						<path d="M1.13 341.58 a0.653986 0.653986 -180 0 0 -0.73971 -0.492434 a0.656072 0.656072 -180 0 0 -0.253101
									 0.865731 a0.653066 0.653066 -180 0 0 0.740769 0.491375 a0.655459 0.655459 -180 0 0 0.252042
									 -0.864672 Z" class="st38"/>
					</g>
					<g id="shape113-460" v:mID="113" v:groupContext="shape" transform="translate(3.62283,-15.1638)">
						<title>Sheet.113</title>
						<path d="M3.22 339.78 A1.86495 1.86495 -180 0 0 1.11 338.38 A1.8709 1.8709 -180 0 0 0.38 340.85 A1.86532
									 1.86532 -180 0 0 2.5 342.25 A1.87264 1.87264 -180 0 0 3.22 339.78 Z" class="st38"/>
					</g>
					<g id="shape114-463" v:mID="114" v:groupContext="shape" transform="translate(3.62283,-10.4867)">
						<title>Sheet.114</title>
						<path d="M3.22 339.78 A1.86495 1.86495 -180 0 0 1.11 338.38 A1.8709 1.8709 -180 0 0 0.38 340.85 A1.86532
									 1.86532 -180 0 0 2.5 342.25 A1.87264 1.87264 -180 0 0 3.22 339.78 Z" class="st38"/>
					</g>
					<g id="shape115-466" v:mID="115" v:groupContext="shape" transform="translate(4.52404,-16.3668)">
						<title>Sheet.115</title>
						<path d="M1.61 341.16 a0.931952 0.931952 -180 0 0 -1.05741 -0.702645 a0.935408 0.935408 -180 0 0 -0.361118
									 1.23585 a0.932139 0.932139 -180 0 0 1.05794 0.702645 a0.935822 0.935822 -180 0 0 0.360589 -1.23585
									 Z" class="st39"/>
					</g>
					<g id="shape116-469" v:mID="116" v:groupContext="shape" transform="translate(4.52404,-11.6897)">
						<title>Sheet.116</title>
						<path d="M1.61 341.16 a0.931952 0.931952 -180 0 0 -1.05741 -0.702645 a0.935875 0.935875 -180 0 0 -0.361118
									 1.23585 a0.932139 0.932139 -180 0 0 1.05794 0.702645 a0.935822 0.935822 -180 0 0 0.360589 -1.23585
									 Z" class="st39"/>
					</g>
					<g id="shape117-472" v:mID="117" v:groupContext="shape" transform="translate(7.78787,-8.83469)">
						<title>Sheet.117</title>
						<path d="M0 341.57 L0.05 333.6 L1.83 334.57 L1.78 342.55 L0 341.57 Z" class="st40"/>
					</g>
					<g id="shape118-475" v:mID="118" v:groupContext="shape" transform="translate(10.204,-7.4008)">
						<title>Sheet.118</title>
						<path d="M0 341.57 L0.05 333.6 L1.83 334.57 L1.78 342.55 L0 341.57 Z" class="st40"/>
					</g>
					<g id="shape119-478" v:mID="119" v:groupContext="shape" transform="translate(12.6196,-5.96639)">
						<title>Sheet.119</title>
						<path d="M0 341.57 L0.05 333.6 L1.83 334.57 L1.78 342.55 L0 341.57 Z" class="st40"/>
					</g>
					<g id="shape120-481" v:mID="120" v:groupContext="shape" transform="translate(15.0357,-4.53251)">
						<title>Sheet.120</title>
						<path d="M0 341.57 L0.05 333.6 L1.83 334.57 L1.78 342.55 L0 341.57 Z" class="st40"/>
					</g>
					<g id="shape121-484" v:mID="121" v:groupContext="shape" transform="translate(8.24006,-10.0631)">
						<title>Sheet.121</title>
						<path d="M0.85 342.24 a0.388199 0.388199 0 0 1 -0.425188 0.308698 a0.638045 0.638045 0 0 1 -0.424658 -0.573447
									 L0 336.5 a0.387575 0.387575 0 0 1 0.424658 -0.308698 a0.637725 0.637725 0 0 1 0.425188 0.573447
									 L0.85 342.24 Z" class="st41"/>
					</g>
					<g id="shape122-487" v:mID="122" v:groupContext="shape" transform="translate(10.6556,-8.62924)">
						<title>Sheet.122</title>
						<path d="M0.85 342.24 a0.388199 0.388199 0 0 1 -0.425188 0.308698 a0.638045 0.638045 0 0 1 -0.424658 -0.573447
									 L0 336.5 a0.387575 0.387575 0 0 1 0.424658 -0.308698 a0.637725 0.637725 0 0 1 0.425188 0.573447
									 L0.85 342.24 Z" class="st41"/>
					</g>
					<g id="shape123-490" v:mID="123" v:groupContext="shape" transform="translate(13.0717,-7.19483)">
						<title>Sheet.123</title>
						<path d="M0.85 342.24 a0.388199 0.388199 0 0 1 -0.425188 0.308698 a0.638045 0.638045 0 0 1 -0.424658 -0.573447
									 L0 336.5 a0.387575 0.387575 0 0 1 0.424658 -0.308698 a0.637725 0.637725 0 0 1 0.425188 0.573447
									 L0.85 342.24 Z" class="st41"/>
					</g>
					<g id="shape124-493" v:mID="124" v:groupContext="shape" transform="translate(15.4873,-5.76095)">
						<title>Sheet.124</title>
						<path d="M0.85 342.24 a0.388502 0.388502 0 0 1 -0.425717 0.308698 a0.638367 0.638367 0 0 1 -0.424129 -0.573447
									 L0 336.5 a0.387272 0.387272 0 0 1 0.424129 -0.308698 a0.638235 0.638235 0 0 1 0.425717 0.573447
									 L0.85 342.24 Z" class="st41"/>
					</g>
					<g id="shape125-496" v:mID="125" v:groupContext="shape" transform="translate(7.78787,-9.81214)">
						<title>Sheet.125</title>
						<path d="M0 342.55 L0.05 334.57 L1.83 335.55 L0 342.55 Z" class="st42"/>
						<path d="M0 342.55 L0.05 334.57 L1.83 335.55" class="st43"/>
					</g>
					<g id="shape126-499" v:mID="126" v:groupContext="shape" transform="translate(10.204,-8.37826)">
						<title>Sheet.126</title>
						<path d="M0 342.55 L0.05 334.57 L1.83 335.55 L0 342.55 Z" class="st42"/>
						<path d="M0 342.55 L0.05 334.57 L1.83 335.55" class="st43"/>
					</g>
					<g id="shape127-502" v:mID="127" v:groupContext="shape" transform="translate(12.6196,-6.94385)">
						<title>Sheet.127</title>
						<path d="M0 342.55 L0.05 334.57 L1.83 335.55 L0 342.55 Z" class="st42"/>
						<path d="M0 342.55 L0.05 334.57 L1.83 335.55" class="st43"/>
					</g>
					<g id="shape128-505" v:mID="128" v:groupContext="shape" transform="translate(15.0357,-5.50996)">
						<title>Sheet.128</title>
						<path d="M0 342.55 L0.05 334.57 L1.83 335.55 L0 342.55 Z" class="st42"/>
						<path d="M0 342.55 L0.05 334.57 L1.83 335.55" class="st43"/>
					</g>
					<g id="shape129-508" v:mID="129" v:groupContext="shape" transform="translate(7.78787,-4.53251)">
						<title>Sheet.129</title>
						<path d="M9.08 334.57 L9.03 342.55 L7.25 341.57 L9.08 334.57 ZM6.66 333.14 L6.61 341.11 L4.83 340.13 L6.66
									 333.14 ZM4.25 331.7 L4.2 339.68 L2.42 338.7 L4.25 331.7 ZM1.83 330.27 L1.78 338.24 L0 337.27
									 L1.83 330.27 Z" class="st42"/>
						<path d="M9.08 334.57 L9.03 342.55 L7.25 341.57M6.66 333.14 L6.61 341.11 L4.83 340.13M4.25 331.7 L4.2 339.68
									 L2.42 338.7M1.83 330.27 L1.78 338.24 L0 337.27" class="st44"/>
					</g>
					<g id="shape130-511" v:mID="130" v:groupContext="shape" transform="translate(2.22125,-11.8454)">
						<title>Sheet.130</title>
						<path d="M0 341.85 L0.63 341.42 L1.42 341.78 L0.03 342.55 L0 341.85 Z" class="st45"/>
					</g>
					<g id="shape131-514" v:mID="131" v:groupContext="shape" transform="translate(17.1796,-3.17487)">
						<title>Sheet.131</title>
						<path d="M0 341.85 L0.63 341.42 L1.42 341.78 L0.03 342.55 L0 341.85 Z" class="st45"/>
					</g>
					<g id="shape132-517" v:mID="132" v:groupContext="shape" transform="translate(1.46036,-10.3893)">
						<title>Sheet.132</title>
						<path d="M2.12 341.35 L0 342.55 L0 333.54 L2.12 332.29 L2.12 333.41 L0.79 334.15 L0.79 341.09 L2.18 340.33
									 L2.12 341.35 Z" class="st36"/>
					</g>
					<g id="shape133-520" v:mID="133" v:groupContext="shape" transform="translate(16.4187,-1.71875)">
						<title>Sheet.133</title>
						<path d="M2.12 341.35 L0 342.55 L0 333.54 L2.12 332.29 L2.12 333.41 L0.79 334.15 L0.79 341.09 L2.18 340.33
									 L2.12 341.35 Z" class="st36"/>
					</g>
					<g id="shape134-523" v:mID="134" v:groupContext="shape" transform="translate(0.467548,-10.3893)">
						<title>Sheet.134</title>
						<path d="M0.99 333.54 L3.11 332.29 L2.12 331.66 L0 332.91 L0 341.92 L0.99 342.55 L0.99 333.54 Z"
								class="st46"/>
					</g>
					<g id="shape135-526" v:mID="135" v:groupContext="shape" transform="translate(15.4259,-1.71875)">
						<title>Sheet.135</title>
						<path d="M0.99 333.54 L3.11 332.29 L2.12 331.66 L0 332.91 L0 341.92 L0.99 342.55 L0.99 333.54 Z"
								class="st46"/>
					</g>
					<g id="shape136-529" v:mID="136" v:groupContext="shape" transform="translate(0.467548,-1.71928)">
						<title>Sheet.136</title>
						<path d="M17.34 339.96 L16.75 340.37 L16.75 334.15 L18.07 333.41 L18.07 332.29 L17.08 331.66 L14.96 332.91
									 L14.96 341.92 L15.95 342.55 L18.07 341.35 L18.14 340.33 L17.34 339.96 ZM2.38 331.29 L1.79 331.7
									 L1.79 325.48 L3.11 324.74 L3.11 323.62 L2.12 322.99 L0 324.24 L0 333.25 L0.99 333.87 L3.11 332.68
									 L3.18 331.66 L2.38 331.29 Z" class="st47"/>
					</g>
					<g id="shape137-531" v:mID="137" v:groupContext="shape" transform="translate(19.9526,-8.71396)">
						<title>Sheet.137</title>
						<path d="M1.13 341.58 a0.653986 0.653986 -180 0 0 -0.73971 -0.492434 a0.656072 0.656072 -180 0 0 -0.253101
									 0.865731 a0.653066 0.653066 -180 0 0 0.740769 0.491375 a0.655459 0.655459 -180 0 0 0.252042
									 -0.864672 Z" class="st38"/>
					</g>
					<g id="shape138-534" v:mID="138" v:groupContext="shape" transform="translate(19.9526,-2.35997)">
						<title>Sheet.138</title>
						<path d="M1.13 341.58 a0.653986 0.653986 -180 0 0 -0.73971 -0.492434 a0.656072 0.656072 -180 0 0 -0.253101
									 0.865731 a0.653066 0.653066 -180 0 0 0.740769 0.491375 a0.655459 0.655459 -180 0 0 0.252042
									 -0.864672 Z" class="st38"/>
					</g>
					<g id="shape139-537" v:mID="139" v:groupContext="shape" transform="translate(0,-0.415652)">
						<title>Sheet.139</title>
						<path d="M36.07 331.28 L36.07 321.27 L19.64 311.85 L3.16 321.13 L3.16 321.52 L0 319.68 L0 329.58 L0.47 329.86
									 L0.47 331.94 L1.46 332.57 L3.33 331.52 L15.43 338.57 L15.43 340.61 L16.42 341.24 L18.24 340.22
									 L22.24 342.55 L22.24 339.27 L36.07 331.28 Z" class="st47"/>
					</g>
					<g id="shape140-539" v:mID="140" v:groupContext="shape" transform="translate(27.8077,-2.86477)">
						<title>Sheet.140</title>
						<path d="M0.29 342.55 L6.62 338.89 A1.82805 1.82805 0 0 1 6.62 336.9 L0.29 340.55 A1.82805 1.82805 -180 0
									 0 0.29 342.55 Z" class="st48"/>
					</g>
					<g id="shape141-541" v:mID="141" v:groupContext="shape" transform="translate(23.5035,-4.85627)">
						<title>Sheet.141</title>
						<path d="M4.6 342.55 L10.92 338.89 L6.32 336.24 L0 339.89 L4.6 342.55 Z" class="st49"/>
					</g>
					<g id="shape142-544" v:mID="142" v:groupContext="shape" transform="translate(23.3588,-2.86477)">
						<title>Sheet.142</title>
						<path d="M0.14 339.89 L4.74 342.55 A1.82805 1.82805 0 0 1 4.74 340.55 L0.14 337.9 A3.49826 3.49826 -180 0
									 0 0.14 339.89 Z" class="st50"/>
					</g>
					<g id="shape143-547" v:mID="143" v:groupContext="shape" transform="translate(25.8933,-5.98478)">
						<title>Sheet.143</title>
						<path d="M2.87 342.55 L0 340.89" class="st51"/>
						<path d="M0.94 340.34 L3.82 342" class="st51"/>
						<path d="M1.88 339.8 L4.76 341.46" class="st51"/>
						<path d="M2.82 339.26 L5.7 340.92" class="st51"/>
						<path d="M3.76 338.71 L6.64 340.37" class="st51"/>
					</g>
					<g id="shape144-554" v:mID="144" v:groupContext="shape" transform="translate(23.5035,-7.51159)">
						<title>Sheet.144</title>
						<path d="M5.13 341.17 L11.45 337.52 A11.9345 11.9345 0 0 1 6.32 338.89 L0 342.55 A11.9345 11.9345 -180 0
									 0 5.13 341.17 Z" class="st52"/>
					</g>
					<g id="shape145-557" v:mID="145" v:groupContext="shape" transform="translate(30.2106,-4.74563)">
						<title>Sheet.145</title>
						<path d="M0.98 341.98 L0 342.55" class="st51"/>
						<path d="M1.26 341.48 L2.24 340.92" class="st51"/>
						<path d="M2.53 340.42 L3.51 339.86" class="st51"/>
					</g>
					<g id="shape146-562" v:mID="146" v:groupContext="shape" transform="translate(23.3588,-2.86477)">
						<title>Sheet.146</title>
						<path d="M0.14 339.89 L4.74 342.55 L11.07 338.89 A1.82805 1.82805 0 0 1 11.07 336.9 L7.85 335.04 L11.6 332.87
									 A11.9345 11.9345 0 0 1 6.47 334.25 L0.14 337.9 A3.49826 3.49826 -180 0 0 0.14 339.89"
								class="st53"/>
					</g>
				</g>
				<g id="shape147-565" v:mID="147" v:groupContext="shape" transform="translate(427.321,214.49) rotate(90)">
					<title>Cloud</title>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					</v:userDefs>
					<path d="M5.37 311.54 A8.61618 10.0654 0 0 1 9.5 292.2 A17.4727 20.4114 0 0 1 34.86 275.89 A20.0634 23.4379 0
								 0 1 56.58 272.26 A12.5816 14.6977 0 0 1 75.21 271.05 A14.3244 16.7336 0 0 1 97.98 277.09 A10.2423
								 11.9646 0 0 1 106.25 294.02 A12.6864 14.8197 0 0 1 95.9 318.19 A16.0049 18.6962 0 0 1 73.14 330.27
								 A18.8712 22.0444 0 0 1 42.1 335.11 A23.9217 27.9441 0 0 1 15.2 330.27 A9.43759 11.0249 0 0 1 5.37
								 311.54 Z" class="st42"/>
					<path d="M5.37 311.54 A8.61618 10.0654 0 0 1 9.5 292.2 A17.4727 20.4114 0 0 1 34.86 275.89 A20.0634 23.4379 0
								 0 1 56.58 272.26 A12.5816 14.6977 0 0 1 75.21 271.05 A14.3244 16.7336 0 0 1 97.98 277.09 A10.2423
								 11.9646 0 0 1 106.25 294.02 A12.6864 14.8197 0 0 1 95.9 318.19 A16.0049 18.6962 0 0 1 73.14 330.27
								 A18.8712 22.0444 0 0 1 42.1 335.11 A23.9217 27.9441 0 0 1 15.2 330.27 A9.43759 11.0249 0 0 1 5.37
								 311.54" class="st54"/>
					<path d="M11.05 312.14 A8.59237 10.0375 0 0 1 5.37 311.54" class="st54"/>
					<path d="M40.54 332.09 A8.62978 10.0812 -180 0 0 42.1 335.11" class="st54"/>
					<path d="M73.92 326.65 A6.96633 8.13801 0 0 1 73.14 330.27" class="st54"/>
					<path d="M89.7 308.52 A7.30994 8.5394 0 0 1 95.9 318.19" class="st54"/>
					<path d="M103.15 297.64 A6.67364 7.79609 -180 0 0 106.25 294.02" class="st54"/>
					<path d="M37.96 278.3 A10.2914 12.0219 -180 0 0 34.86 275.89" class="st54"/>
				</g>
				<g id="shape148-574" v:mID="148" v:groupContext="shape" transform="translate(110.222,-64.9346)">
					<title>Triangle</title>
					<desc>setsum</desc>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
					</v:userDefs>
					<v:textBlock v:margins="rect(4,4,4,4)"/>
					<v:textRect cx="15.6995" cy="336.449" width="31.4" height="12.1933"/>
					<g id="shadow148-575" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
							v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
						<path d="M31.4 342.55 L14.67 324.26 L0 342.55 L31.4 342.55 Z" class="st9"/>
					</g>
					<path d="M31.4 342.55 L14.67 324.26 L0 342.55 L31.4 342.55 Z" class="st10"/>
					<text x="8.35" y="337.95" class="st55" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>setsum</text>				</g>
				<g id="shape149-579" v:mID="149" v:groupContext="shape" transform="translate(292.639,20.8827) rotate(45)">
					<title>Sheet.149</title>
					<v:userDefs>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<path d="M0 342.55 L14.71 342.55" class="st14"/>
				</g>
				<g id="shape150-584" v:mID="150" v:groupContext="shape" transform="translate(43.2897,-54.1122)">
					<title>Sheet.150</title>
					<v:userDefs>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<path d="M0 342.55 L10.07 342.55" class="st14"/>
				</g>
				<g id="shape151-589" v:mID="151" v:groupContext="shape" transform="translate(-112.261,8.34531) rotate(-28.1394)">
					<title>Sheet.151</title>
					<v:userDefs>
						<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
					</v:userDefs>
					<path d="M0 342.55 L18 342.55" class="st14"/>
				</g>
				<g id="shape152-594" v:mID="152" v:groupContext="shape">
					<title>Sheet.152</title>
					<desc>Clients</desc>
					<v:textBlock v:margins="rect(4,4,4,4)"/>
					<v:textRect cx="32.4673" cy="337.134" width="64.94" height="10.8224"/>
					<rect x="0" y="331.723" width="64.9346" height="10.8224" class="st11"/>
					<text x="21.5" y="339.53" class="st6" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Clients</text>				</g>
				<g id="shape153-597" v:mID="153" v:groupContext="shape" transform="translate(83.578,-9.58078)">
					<title>Sheet.153</title>
					<desc>Distributed Cache</desc>
					<v:textBlock v:margins="rect(4,4,4,4)"/>
					<v:textRect cx="42.0677" cy="337.134" width="84.14" height="10.8224"/>
					<rect x="0" y="331.723" width="84.1355" height="10.8224" class="st11"/>
					<text x="13.1" y="339.53" class="st6" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Distributed Cache</text>				</g>
				<g id="shape154-600" v:mID="154" v:groupContext="shape" transform="translate(181.983,-18.6146)">
					<title>Sheet.154</title>
					<desc>Web Servers</desc>
					<v:textBlock v:margins="rect(4,4,4,4)"/>
					<v:textRect cx="32.4673" cy="337.134" width="64.94" height="10.8224"/>
					<rect x="0" y="331.723" width="64.9346" height="10.8224" class="st11"/>
					<text x="11.93" y="339.53" class="st6" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Web Servers</text>				</g>
				<g id="shape155-603" v:mID="155" v:groupContext="shape" transform="translate(96.6068,630.978) rotate(180)">
					<title>Simple Arrow</title>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
						<v:ud v:nameU="ArrowType" v:prompt="" v:val="VT0(2):26"/>
					</v:userDefs>
					<g id="shadow155-604" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
							v:shadowType="1" transform="matrix(1,0,0,1,-0.3456,-1.9728)" class="st1">
						<path d="M0 342.55 L12 330.55 L12 336.55 L16.23 336.55 L16.23 342.55 L16.23 348.54 L12 348.54 L12 354.54
									 L0 342.55 Z" class="st56"/>
					</g>
					<path d="M0 342.55 L12 330.55 L12 336.55 L16.23 336.55 L16.23 342.55 L16.23 348.54 L12 348.54 L12 354.54 L0 342.55
								 Z" class="st57"/>
				</g>
				<g id="shape156-607" v:mID="156" v:groupContext="shape" transform="translate(173.159,625.567) rotate(180)">
					<title>Simple Arrow.153</title>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
						<v:ud v:nameU="ArrowType" v:prompt="" v:val="VT0(2):26"/>
					</v:userDefs>
					<g id="shadow156-608" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
							v:shadowType="1" transform="matrix(1,0,0,1,-0.3456,-1.9728)" class="st1">
						<path d="M0 342.55 L12 330.55 L12 336.55 L16.23 336.55 L16.23 342.55 L16.23 348.54 L12 348.54 L12 354.54
									 L0 342.55 Z" class="st56"/>
					</g>
					<path d="M0 342.55 L12 330.55 L12 336.55 L16.23 336.55 L16.23 342.55 L16.23 348.54 L12 348.54 L12 354.54 L0 342.55
								 Z" class="st57"/>
				</g>
			</g>
			<g id="shape157-611" v:mID="157" v:groupContext="shape" transform="translate(0,-149.475)">
				<title>Rectangle</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow157-612" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
					<rect x="0" y="193.823" width="271.116" height="148.722" class="st58"/>
				</g>
				<rect x="0" y="193.823" width="271.116" height="148.722" class="st59"/>
			</g>
			<g id="shape158-615" v:mID="158" v:groupContext="shape" transform="translate(271.116,-149.475)">
				<title>Rectangle.158</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow158-616" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
					<rect x="0" y="193.823" width="202.104" height="148.722" class="st58"/>
				</g>
				<rect x="0" y="193.823" width="202.104" height="148.722" class="st59"/>
			</g>
			<g id="shape159-619" v:mID="159" v:groupContext="shape">
				<title>Rectangle.159</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow159-620" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
					<rect x="0" y="193.823" width="271.116" height="148.722" class="st58"/>
				</g>
				<rect x="0" y="193.823" width="271.116" height="148.722" class="st59"/>
			</g>
			<g id="shape160-623" v:mID="160" v:groupContext="shape" transform="translate(271.116,0)">
				<title>Rectangle.160</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow160-624" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
					<rect x="0" y="193.823" width="202.104" height="148.722" class="st58"/>
				</g>
				<rect x="0" y="193.823" width="202.104" height="148.722" class="st59"/>
			</g>
			<g id="shape161-627" v:mID="161" v:groupContext="shape" transform="translate(83.578,-151.241)">
				<title>Sheet.161</title>
				<desc>(a) Distributed Web Cache</desc>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="54.3546" cy="333.806" width="108.71" height="17.4792"/>
				<g id="shadow161-628" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
					<rect x="0" y="325.066" width="108.709" height="17.4792" class="st9"/>
				</g>
				<rect x="0" y="325.066" width="108.709" height="17.4792" class="st10"/>
				<text x="4" y="336.81" class="st60" v:langID="1033"><v:paragraph v:spLine="-1.5" v:spBefore="8" v:spAfter="16"
							v:bulletSize="0.166667"/><v:tabList/>(a) Distributed Web Cache</text>			</g>
			<g id="shape162-632" v:mID="162" v:groupContext="shape" transform="translate(319.513,-151.241)">
				<title>Sheet.162</title>
				<desc>(b) Detecting Routing Loops</desc>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="54.3546" cy="333.806" width="108.71" height="17.4792"/>
				<g id="shadow162-633" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
					<rect x="0" y="325.066" width="108.709" height="17.4792" class="st9"/>
				</g>
				<rect x="0" y="325.066" width="108.709" height="17.4792" class="st10"/>
				<text x="4" y="336.81" class="st60" v:langID="1033"><v:paragraph v:spLine="-1.5" v:spBefore="8" v:spAfter="16"
							v:bulletSize="0.166667"/><v:tabList/>(b) Detecting Routing Loops</text>			</g>
			<g id="shape163-637" v:mID="163" v:groupContext="shape" transform="translate(77.5283,-3.35965)">
				<title>Sheet.163</title>
				<desc>(c) In-order Workload Scheduler</desc>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="63.5211" cy="333.806" width="127.05" height="17.4792"/>
				<g id="shadow163-638" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
					<rect x="0" y="325.066" width="127.042" height="17.4792" class="st9"/>
				</g>
				<rect x="0" y="325.066" width="127.042" height="17.4792" class="st10"/>
				<text x="4" y="336.81" class="st60" v:langID="1033"><v:paragraph v:spLine="-1.5" v:spBefore="8" v:spAfter="16"
							v:bulletSize="0.166667"/><v:tabList/>(c) In-order Workload Scheduler</text>			</g>
			<g id="shape164-642" v:mID="164" v:groupContext="shape" transform="translate(307.414,-3.35965)">
				<title>Sheet.164</title>
				<desc>(d) Database Semi-join Operations</desc>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="66.2253" cy="333.806" width="132.46" height="17.4792"/>
				<g id="shadow164-643" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
					<rect x="0" y="325.066" width="132.451" height="17.4792" class="st9"/>
				</g>
				<rect x="0" y="325.066" width="132.451" height="17.4792" class="st10"/>
				<text x="4" y="336.81" class="st60" v:langID="1033"><v:paragraph v:spLine="-1.5" v:spBefore="8" v:spAfter="16"
							v:bulletSize="0.166667"/><v:tabList/>(d) Database Semi-join Operations</text>			</g>
		</g>
	</g>
</svg>
