<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by Microsoft Visio, SVG Export efd_i12.svg Page-1 -->
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:ev="http://www.w3.org/2001/xml-events"
		xmlns:v="http://schemas.microsoft.com/visio/2003/SVGExtensions/" width="10.2783in" height="4.28958in"
		viewBox="0 0 740.039 308.85" xml:space="preserve" color-interpolation-filters="sRGB" class="st21">
	<v:documentProperties v:langID="1033" v:viewMarkup="false">
		<v:userDefs>
			<v:ud v:nameU="msvSubprocessMaster" v:prompt="" v:val="VT4(Rectangle)"/>
			<v:ud v:nameU="msvNoAutoConnect" v:val="VT0(1):26"/>
		</v:userDefs>
	</v:documentProperties>

	<style type="text/css">
	<![CDATA[
		.st1 {fill:#ffffff;stroke:none;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.75}
		.st2 {fill:none;stroke:#00aeef;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.03901}
		.st3 {stroke:none;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.75}
		.st4 {fill:#000000;font-family:Arial;font-size:0.998566em}
		.st5 {fill:#0071c5;stroke:none;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.75}
		.st6 {stroke:#00b050;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.03901}
		.st7 {stroke:#00aeef;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.03901}
		.st8 {fill:#000000;font-family:Arial;font-size:0.918686em;font-weight:bold}
		.st9 {fill:#00b050;font-size:1em}
		.st10 {fill:#c00000;font-family:Arial;font-size:0.828804em;font-weight:bold}
		.st11 {fill:#004280;stroke:#004280;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.00749637}
		.st12 {fill:#ffffff;font-family:Arial;font-size:1.16833em}
		.st13 {fill:#2e75b5;stroke:#5b9bd5;stroke-linecap:round;stroke-linejoin:round;stroke-width:1}
		.st14 {fill:#ffffff;font-family:Arial;font-size:1.16666em}
		.st15 {font-size:1em}
		.st16 {fill:none;stroke:none;stroke-width:0.25}
		.st17 {fill:#000000;font-family:Calibri;font-size:1.00001em}
		.st18 {marker-end:url(#mrkr5-121);stroke:#5b9bd5;stroke-dasharray:1.5,3;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.5}
		.st19 {fill:#5b9bd5;fill-opacity:1;stroke:#5b9bd5;stroke-opacity:1;stroke-width:0.37313432835821}
		.st20 {fill:#000000;font-family:Calibri;font-size:1.16666em}
		.st21 {fill:none;fill-rule:evenodd;font-size:12px;overflow:visible;stroke-linecap:square;stroke-miterlimit:3}
	]]>
	</style>

	<defs id="Markers">
		<g id="lend5">
			<path d="M 2 1 L 0 0 L 1.98117 -0.993387 C 1.67173 -0.364515 1.67301 0.372641 1.98465 1.00043 " style="stroke:none"/>
		</g>
		<marker id="mrkr5-121" class="st19" v:arrowType="5" v:arrowSize="2" v:setback="4.69" refX="-4.69" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend5" transform="scale(-2.68,-2.68) "/>
		</marker>
	</defs>
	<g v:mID="0" v:index="1" v:groupContext="foregroundPage">
		<v:userDefs>
			<v:ud v:nameU="msvThemeOrder" v:val="VT0(0):26"/>
		</v:userDefs>
		<title>Page-1</title>
		<v:pageProperties v:drawingScale="1" v:pageScale="1" v:drawingUnits="0" v:shadowOffsetX="9" v:shadowOffsetY="-9"/>
		<g id="shape5-1" v:mID="5" v:groupContext="shape" transform="translate(36.0674,-256.878)">
			<title>Sheet.5</title>
			<path d="M0 291.88 C0 290 1.52 288.48 3.41 288.48 L68.51 288.48 C70.4 288.48 71.91 290 71.91 291.88 L71.91 305.46 C71.91
						 307.33 70.4 308.85 68.51 308.85 L3.41 308.85 C1.52 308.85 0 307.33 0 305.46 L0 291.88 Z" class="st1"/>
		</g>
		<g id="shape6-3" v:mID="6" v:groupContext="shape" transform="translate(36.0674,-256.878)">
			<title>Sheet.6</title>
			<path d="M0 291.88 C0 290 1.52 288.48 3.41 288.48 L68.51 288.48 C70.4 288.48 71.91 290 71.91 291.88 L71.91 305.46 C71.91
						 307.33 70.4 308.85 68.51 308.85 L3.41 308.85 C1.52 308.85 0 307.33 0 305.46 L0 291.88 Z" class="st2"/>
		</g>
		<g id="shape7-5" v:mID="7" v:groupContext="shape" transform="translate(61.6502,-258.089)">
			<title>Sheet.7</title>
			<desc>Key</desc>
			<v:textBlock v:margins="rect(0,0,0,0)"/>
			<v:textRect cx="13.7891" cy="301.658" width="27.58" height="14.3829"/>
			<path d="M27.58 294.47 L0 294.47 L0 308.85 L27.58 308.85 L27.58 294.47" class="st3"/>
			<text x="3.46" y="305.25" class="st4" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Key</text>		</g>
		<g id="shape8-9" v:mID="8" v:groupContext="shape" transform="translate(51.9748,-236.328)">
			<title>Sheet.8</title>
			<path d="M0 298.54 L9.81 298.54 L9.81 288.24 L29.44 288.24 L29.44 298.54 L39.26 298.54 L19.63 308.85 L0 298.54 Z"
					class="st5"/>
		</g>
		<g id="shape9-11" v:mID="9" v:groupContext="shape" transform="translate(36.0674,-215.298)">
			<title>Sheet.9</title>
			<path d="M0 291.77 C0 289.89 1.54 288.36 3.42 288.36 L68.49 288.36 C70.38 288.36 71.91 289.89 71.91 291.77 L71.91 305.43
						 C71.91 307.32 70.38 308.85 68.49 308.85 L3.42 308.85 C1.54 308.85 0 307.32 0 305.43 L0 291.77 Z"
					class="st1"/>
		</g>
		<g id="shape10-13" v:mID="10" v:groupContext="shape" transform="translate(36.0674,-215.298)">
			<title>Sheet.10</title>
			<path d="M0 291.77 C0 289.89 1.54 288.36 3.42 288.36 L68.49 288.36 C70.38 288.36 71.91 289.89 71.91 291.77 L71.91 305.43
						 C71.91 307.32 70.38 308.85 68.49 308.85 L3.42 308.85 C1.54 308.85 0 307.32 0 305.43 L0 291.77 Z"
					class="st2"/>
		</g>
		<g id="shape11-15" v:mID="11" v:groupContext="shape" transform="translate(58.8889,-216.57)">
			<title>Sheet.11</title>
			<desc>hash</desc>
			<v:textBlock v:margins="rect(0,0,0,0)"/>
			<v:textRect cx="16.8573" cy="301.658" width="33.72" height="14.3829"/>
			<path d="M33.71 294.47 L0 294.47 L0 308.85 L33.71 308.85 L33.71 294.47" class="st3"/>
			<text x="3.86" y="305.25" class="st4" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>hash</text>		</g>
		<g id="shape12-19" v:mID="12" v:groupContext="shape" transform="translate(27.3033,-174.437)">
			<title>Sheet.12</title>
			<path d="M0 292.58 C0 290.78 1.46 289.32 3.26 289.32 L87.15 289.32 C88.95 289.32 90.4 290.78 90.4 292.58 L90.4 305.6
						 C90.4 307.4 88.95 308.85 87.15 308.85 L3.26 308.85 C1.46 308.85 0 307.4 0 305.6 L0 292.58 Z" class="st1"/>
		</g>
		<g id="shape13-21" v:mID="13" v:groupContext="shape" transform="translate(27.3033,-174.437)">
			<title>Sheet.13</title>
			<path d="M0 292.58 C0 290.78 1.46 289.32 3.26 289.32 L87.15 289.32 C88.95 289.32 90.4 290.78 90.4 292.58 L90.4 305.6
						 C90.4 307.4 88.95 308.85 87.15 308.85 L3.26 308.85 C1.46 308.85 0 307.4 0 305.6 L0 292.58 Z" class="st2"/>
		</g>
		<g id="shape14-23" v:mID="14" v:groupContext="shape" transform="translate(36.0515,-175.256)">
			<title>Sheet.14</title>
			<desc>0x0102ABCD</desc>
			<v:textBlock v:margins="rect(0,0,0,0)"/>
			<v:textRect cx="43.6644" cy="301.658" width="87.33" height="14.3829"/>
			<path d="M87.33 294.47 L0 294.47 L0 308.85 L87.33 308.85 L87.33 294.47" class="st3"/>
			<text x="7.36" y="305.25" class="st4" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>0x0102ABCD</text>		</g>
		<g id="shape15-27" v:mID="15" v:groupContext="shape" transform="translate(51.9748,-194.029)">
			<title>Sheet.15</title>
			<path d="M0 298.48 L9.81 298.48 L9.81 288.12 L29.44 288.12 L29.44 298.48 L39.26 298.48 L19.63 308.85 L0 298.48 Z"
					class="st5"/>
		</g>
		<g id="shape16-29" v:mID="16" v:groupContext="shape" transform="translate(48.9133,-159.818)">
			<title>Sheet.16</title>
			<path d="M26.41 296.87 C26.41 300.18 25.97 302.86 25.41 302.86 L14.21 302.86 C13.66 302.86 13.21 305.55 13.21 308.85
						 C13.21 305.55 12.76 302.86 12.21 302.86 L1.01 302.86 C0.45 302.86 0 300.18 0 296.87" class="st6"/>
		</g>
		<g id="shape17-32" v:mID="17" v:groupContext="shape" transform="translate(19.0195,-19.0195)">
			<title>Sheet.17</title>
			<path d="M0 196.93 L0 308.85 L145.15 308.85 L145.15 196.93 L0 196.93 L0 196.93 Z" class="st1"/>
		</g>
		<g id="shape18-34" v:mID="18" v:groupContext="shape" transform="translate(19.0195,-19.0195)">
			<title>Sheet.18</title>
			<path d="M0 196.93 L145.15 196.93 L145.15 308.85 L0 308.85 L0 196.93" class="st7"/>
		</g>
		<g id="shape19-37" v:mID="19" v:groupContext="shape" transform="translate(28.2638,-70.6655)">
			<title>Sheet.19</title>
			<path d="M0 280.69 C0 277.58 2.53 275.06 5.64 275.06 L124.14 275.06 C127.26 275.06 129.78 277.58 129.78 280.69 L129.78
						 303.22 C129.78 306.33 127.26 308.85 124.14 308.85 L5.64 308.85 C2.53 308.85 0 306.33 0 303.22 L0 280.69
						 Z" class="st1"/>
		</g>
		<g id="shape20-39" v:mID="20" v:groupContext="shape" transform="translate(28.2638,-70.6655)">
			<title>Sheet.20</title>
			<path d="M0 280.69 C0 277.58 2.53 275.06 5.64 275.06 L124.14 275.06 C127.26 275.06 129.78 277.58 129.78 280.69 L129.78
						 303.22 C129.78 306.33 127.26 308.85 124.14 308.85 L5.64 308.85 C2.53 308.85 0 306.33 0 303.22 L0 280.69
						 Z" class="st2"/>
		</g>
		<g id="shape21-41" v:mID="21" v:groupContext="shape" transform="translate(57.4514,-85.7513)">
			<title>Sheet.21</title>
			<desc>hash_index =</desc>
			<v:textBlock v:margins="rect(0,0,0,0)"/>
			<v:textRect cx="45.0133" cy="301.658" width="90.03" height="14.3829"/>
			<path d="M90.03 294.47 L0 294.47 L0 308.85 L90.03 308.85 L90.03 294.47" class="st3"/>
			<text x="9.2" y="305.25" class="st4" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>hash_index =  </text>		</g>
		<g id="shape22-45" v:mID="22" v:groupContext="shape" transform="translate(76.3001,-71.3719)">
			<title>Sheet.22</title>
			<desc>38123</desc>
			<v:textBlock v:margins="rect(0,0,0,0)"/>
			<v:textRect cx="21.0762" cy="301.658" width="42.16" height="14.3829"/>
			<path d="M42.15 294.47 L0 294.47 L0 308.85 L42.15 308.85 L42.15 294.47" class="st3"/>
			<text x="4.42" y="305.25" class="st4" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>38123</text>		</g>
		<g id="shape23-49" v:mID="23" v:groupContext="shape" transform="translate(28.2638,-27.048)">
			<title>Sheet.23</title>
			<path d="M0 280.69 C0 277.59 2.54 275.06 5.64 275.06 L124.14 275.06 C127.26 275.06 129.78 277.59 129.78 280.69 L129.78
						 303.22 C129.78 306.33 127.26 308.85 124.14 308.85 L5.64 308.85 C2.54 308.85 0 306.33 0 303.22 L0 280.69
						 Z" class="st1"/>
		</g>
		<g id="shape24-51" v:mID="24" v:groupContext="shape" transform="translate(28.2638,-27.048)">
			<title>Sheet.24</title>
			<path d="M0 280.69 C0 277.59 2.54 275.06 5.64 275.06 L124.14 275.06 C127.26 275.06 129.78 277.59 129.78 280.69 L129.78
						 303.22 C129.78 306.33 127.26 308.85 124.14 308.85 L5.64 308.85 C2.54 308.85 0 306.33 0 303.22 L0 280.69
						 Z" class="st2"/>
		</g>
		<g id="shape25-53" v:mID="25" v:groupContext="shape" transform="translate(54.0924,-41.564)">
			<title>Sheet.25</title>
			<desc>lookup_table =</desc>
			<v:textBlock v:margins="rect(0,0,0,0)"/>
			<v:textRect cx="46.931" cy="301.658" width="93.87" height="14.3829"/>
			<path d="M93.86 294.47 L0 294.47 L0 308.85 L93.86 308.85 L93.86 294.47" class="st3"/>
			<text x="7.79" y="305.25" class="st4" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>lookup_table =</text>		</g>
		<g id="shape26-57" v:mID="26" v:groupContext="shape" transform="translate(28.0195,-28.5506)">
			<title>Sheet.26</title>
			<desc>0110 1100 0101 1101</desc>
			<v:textBlock v:margins="rect(0,0,0,0)"/>
			<v:textRect cx="64.89" cy="302.233" width="129.79" height="13.2327"/>
			<path d="M129.78 295.62 L0 295.62 L0 308.85 L129.78 308.85 L129.78 295.62" class="st3"/>
			<text x="11.25" y="305.54" class="st8" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>0110 11<tspan
						class="st9">0</tspan>0 0101 1101</text>		</g>
		<g id="shape27-62" v:mID="27" v:groupContext="shape" transform="translate(26.2461,-113.863)">
			<title>Sheet.27</title>
			<desc>Group ID: 0x0102</desc>
			<v:textBlock v:margins="rect(0,0,0,0)"/>
			<v:textRect cx="48.6286" cy="302.881" width="97.26" height="11.9384"/>
			<path d="M97.26 296.91 L0 296.91 L0 308.85 L97.26 308.85 L97.26 296.91" class="st3"/>
			<text x="7.73" y="305.86" class="st10" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Group ID: 0x0102</text>		</g>
		<g id="shape28-66" v:mID="28" v:groupContext="shape" transform="translate(42.3703,-135.313)">
			<title>Sheet.28</title>
			<path d="M0 298.48 L9.84 298.48 L9.84 288.12 L29.53 288.12 L29.53 298.48 L39.38 298.48 L19.69 308.85 L0 298.48 Z"
					class="st5"/>
		</g>
		<g id="shape29-68" v:mID="29" v:groupContext="shape" transform="translate(117.645,-244.476)">
			<title>Sheet.29</title>
			<path d="M0 274.07 L22.75 274.07 L22.75 262.48 L45.5 285.66 L22.75 308.85 L22.75 297.26 L0 297.26 L0 274.07 Z"
					class="st5"/>
		</g>
		<g id="shape30-70" v:mID="30" v:groupContext="shape" transform="translate(169.209,-251.966)">
			<title>Sheet.30</title>
			<path d="M0 283.69 C0 280.91 2.27 278.65 5.04 278.65 L111.77 278.65 C114.56 278.65 116.81 280.91 116.81 283.69 L116.81
						 303.82 C116.81 306.6 114.56 308.85 111.77 308.85 L5.04 308.85 C2.27 308.85 0 306.6 0 303.82 L0 283.69 Z"
					class="st1"/>
		</g>
		<g id="shape31-72" v:mID="31" v:groupContext="shape" transform="translate(169.209,-251.966)">
			<title>Sheet.31</title>
			<path d="M0 283.69 C0 280.91 2.27 278.65 5.04 278.65 L111.77 278.65 C114.56 278.65 116.81 280.91 116.81 283.69 L116.81
						 303.82 C116.81 306.6 114.56 308.85 111.77 308.85 L5.04 308.85 C2.27 308.85 0 306.6 0 303.82 L0 283.69 Z"
					class="st2"/>
		</g>
		<g id="shape35-74" v:mID="35" v:groupContext="shape" transform="translate(291.966,-244.476)">
			<title>Sheet.35</title>
			<path d="M0 274.07 L22.69 274.07 L22.69 262.48 L45.38 285.66 L22.69 308.85 L22.69 297.26 L0 297.26 L0 274.07 Z"
					class="st5"/>
		</g>
		<g id="shape36-76" v:mID="36" v:groupContext="shape" transform="translate(343.17,-254.482)">
			<title>Sheet.36</title>
			<path d="M0 288.09 C0 285.8 1.88 283.93 4.17 283.93 L109.18 283.93 C111.47 283.93 113.33 285.8 113.33 288.09 L113.33
						 304.7 C113.33 306.99 111.47 308.85 109.18 308.85 L4.17 308.85 C1.88 308.85 0 306.99 0 304.7 L0 288.09 Z"
					class="st1"/>
		</g>
		<g id="shape37-78" v:mID="37" v:groupContext="shape" transform="translate(343.17,-254.482)">
			<title>Sheet.37</title>
			<path d="M0 288.09 C0 285.8 1.88 283.93 4.17 283.93 L109.18 283.93 C111.47 283.93 113.33 285.8 113.33 288.09 L113.33
						 304.7 C113.33 306.99 111.47 308.85 109.18 308.85 L4.17 308.85 C1.88 308.85 0 306.99 0 304.7 L0 288.09 Z"
					class="st2"/>
		</g>
		<g id="shape38-80" v:mID="38" v:groupContext="shape" transform="translate(368.337,-257.958)">
			<title>Sheet.38</title>
			<desc>Position = 6</desc>
			<v:textBlock v:margins="rect(0,0,0,0)"/>
			<v:textRect cx="38.1131" cy="301.658" width="76.23" height="14.3829"/>
			<path d="M76.23 294.47 L0 294.47 L0 308.85 L76.23 308.85 L76.23 294.47" class="st3"/>
			<text x="6.64" y="305.25" class="st4" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Position = 6</text>		</g>
		<g id="shape39-84" v:mID="39" v:groupContext="shape" transform="translate(158.044,-86.5202)">
			<title>Sheet.39</title>
			<path d="M0 308.85 L69.59 308.85 C70.16 308.85 70.62 308.39 70.62 307.83 L70.62 148.5 L68.57 148.5 L68.57 307.83 L69.59
						 306.81 L0 306.81 L0 308.85 ZM72.66 149.52 L69.59 143.4 L66.53 149.52 L72.66 149.52 Z" class="st11"/>
		</g>
		<g id="shape41-86" v:mID="41" v:groupContext="shape" transform="translate(335.112,-199.647)">
			<title>Sheet.41</title>
			<desc>Apply the equation</desc>
			<v:textBlock v:margins="rect(0,0,0,0)"/>
			<v:textRect cx="71.2648" cy="300.436" width="142.53" height="16.8275"/>
			<path d="M142.53 292.02 L0 292.02 L0 308.85 L142.53 308.85 L142.53 292.02" class="st3"/>
			<text x="13.19" y="304.64" class="st12" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Apply the equation  </text>		</g>
		<g id="shape42-90" v:mID="42" v:groupContext="shape" transform="translate(341.115,-182.871)">
			<title>Sheet.42</title>
			<desc>to retrieve the bit</desc>
			<v:textBlock v:margins="rect(0,0,0,0)"/>
			<v:textRect cx="64.5256" cy="300.436" width="129.06" height="16.8275"/>
			<path d="M129.05 292.02 L0 292.02 L0 308.85 L129.05 308.85 L129.05 292.02" class="st3"/>
			<text x="12.31" y="304.64" class="st12" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>to retrieve the bit  </text>		</g>
		<g id="shape43-94" v:mID="43" v:groupContext="shape" transform="translate(349.999,-166.095)">
			<title>Sheet.43</title>
			<desc>position in the</desc>
			<v:textBlock v:margins="rect(0,0,0,0)"/>
			<v:textRect cx="54.2285" cy="300.436" width="108.46" height="16.8275"/>
			<path d="M108.46 292.02 L0 292.02 L0 308.85 L108.46 308.85 L108.46 292.02" class="st3"/>
			<text x="10.97" y="304.64" class="st12" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>position in the  </text>		</g>
		<g id="shape44-98" v:mID="44" v:groupContext="shape" transform="translate(353.361,-149.319)">
			<title>Sheet.44</title>
			<desc>lookup_table</desc>
			<v:textBlock v:margins="rect(0,0,0,0)"/>
			<v:textRect cx="47.9619" cy="300.436" width="95.93" height="16.8275"/>
			<path d="M95.92 292.02 L0 292.02 L0 308.85 L95.92 308.85 L95.92 292.02" class="st3"/>
			<text x="8.21" y="304.64" class="st12" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>lookup_table</text>		</g>
		<g id="shape47-102" v:mID="47" v:groupContext="shape" transform="translate(115.17,255.2) rotate(-90)">
			<title>1-D word balloon</title>
			<desc>Retrieve the value “0&#39; from the specified location in the loo...</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				<v:ud v:nameU="Scale" v:val="VT0(1):26"/>
				<v:ud v:nameU="AntiScale" v:val="VT0(1):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="29.2016" cy="218.85" width="180" height="58.4032" transform="rotate(90)"/>
			<path d="M0 308.85 L58.4 308.85 L58.4 128.85 L0 128.85 L0 204.67 L-11.87 38.85 L-7.09 233.03 L0 233.03 L0 308.85 Z"
					class="st13"/>
			<text x="136.98" y="-41.8" transform="rotate(90)" class="st14" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Retrieve the value “0&#39; from <tspan
						x="134.41" dy="1.2em" class="st15">the specified location in the </tspan><tspan x="181.1" dy="1.2em"
						class="st15">lookup table</tspan></text>		</g>
		<g id="shape48-107" v:mID="48" v:groupContext="shape" transform="translate(169.209,-251.966)">
			<title>Sheet.48</title>
			<desc>F(Key, hash_index = 38123</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="54.2285" cy="295.35" width="108.46" height="27"/>
			<rect x="0" y="281.85" width="108.457" height="27" class="st16"/>
			<text x="5.86" y="291.75" class="st17" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>F(Key, hash_index = <tspan
						x="39.02" dy="1.2em" class="st15">38123</tspan></text>		</g>
		<g id="shape49-111" v:mID="49" v:groupContext="shape" transform="translate(553.962,99) rotate(90)">
			<title>1-D word balloon.49</title>
			<desc>Apply the equation to retrieve the bit position in the lookup...</desc>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				<v:ud v:nameU="Scale" v:val="VT0(1):26"/>
				<v:ud v:nameU="AntiScale" v:val="VT0(1):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="29.2016" cy="218.85" width="180" height="58.4032" transform="rotate(-90)"/>
			<path d="M0 308.85 L58.4 308.85 L58.4 128.85 L0 128.85 L0 204.67 L-51.13 299.85 L0 233.03 L0 308.85 Z" class="st13"/>
			<text x="-284.62" y="16.6" transform="rotate(-90)" class="st14" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Apply the equation to <tspan
						x="-296.67" dy="1.2em" class="st15">retrieve the bit position in </tspan><tspan x="-270.22" dy="1.2em"
						class="st15">the lookup</tspan>_table</text>		</g>
		<g id="shape50-116" v:mID="50" v:groupContext="shape" transform="translate(640.132,-104.709) rotate(44.1224)">
			<title>Sheet.50</title>
			<path d="M0 308.85 L54.13 308.85" class="st18"/>
		</g>
		<g id="shape51-122" v:mID="51" v:groupContext="shape" transform="translate(433.02,-122.267)">
			<title>Sheet.51</title>
			<desc>(Hash(key,seed1)+38123*hash(key,seed2))%16</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="144" cy="295.35" width="288" height="27"/>
			<rect x="0" y="281.85" width="288" height="27" class="st2"/>
			<text x="9.86" y="299.55" class="st20" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>(Hash(key,seed1)+38123*hash(key,seed2))%16</text>		</g>
	</g>
</svg>
