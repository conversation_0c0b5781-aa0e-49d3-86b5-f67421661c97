<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by Microsoft Visio, SVG Export memship_i2.svg Page-1 -->
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:ev="http://www.w3.org/2001/xml-events"
		xmlns:v="http://schemas.microsoft.com/visio/2003/SVGExtensions/" width="4.38194in" height="1.25694in"
		viewBox="0 0 315.5 90.5" xml:space="preserve" color-interpolation-filters="sRGB" class="st6">
	<v:documentProperties v:langID="1033" v:viewMarkup="false"/>

	<style type="text/css">
	<![CDATA[
		.st1 {fill:none;stroke:none;stroke-width:0.25}
		.st2 {fill:#5b9bd5;font-family:Calibri;font-size:1.16666em}
		.st3 {baseline-shift:32.4943%;font-size:0.649886em}
		.st4 {font-size:1em}
		.st5 {font-family:Cambria Math;font-size:1em}
		.st6 {fill:none;fill-rule:evenodd;font-size:12px;overflow:visible;stroke-linecap:square;stroke-miterlimit:3}
	]]>
	</style>

	<g v:mID="0" v:index="1" v:groupContext="foregroundPage">
		<title>Page-1</title>
		<v:pageProperties v:drawingScale="1" v:pageScale="1" v:drawingUnits="0" v:shadowOffsetX="9" v:shadowOffsetY="-9"/>
		<g id="shape3-1" v:mID="3" v:groupContext="shape" transform="translate(0.25,-0.25)">
			<title>Sheet.3</title>
			<desc>False Positive Probability = (1-(1-1/m)kn)k ≃ (1-ekn/m)k</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="157.5" cy="45.5" width="315" height="90"/>
			<rect x="0" y="0.5" width="315" height="90" class="st1"/>
			<text x="8.28" y="49.82" class="st2" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>False Positive Probability = (1-(1-1/m)<tspan
						dy="-0.234em" class="st3" v:baseFontSize="14">kn</tspan><tspan dy="0.152em" class="st4">)</tspan><tspan
						dy="-0.234em" class="st3" v:baseFontSize="14">k</tspan><tspan dy="0.152em" class="st4"> </tspan><tspan
						class="st5">≃</tspan> (1-e<tspan dy="-0.234em" class="st3" v:baseFontSize="14">kn</tspan><tspan class="st3"
						v:baseFontSize="14">/</tspan><tspan class="st3" v:baseFontSize="14">m</tspan><tspan dy="0.152em"
						class="st4">)</tspan><tspan dy="-0.234em" class="st3" v:baseFontSize="14">k</tspan></text>		</g>
	</g>
</svg>
