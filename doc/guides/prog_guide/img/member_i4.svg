<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">
<!-- Generated by Microsoft Visio 11.0, SVG Export, v1.0 memship_i4.svg Page-1 -->
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:ev="http://www.w3.org/2001/xml-events"
		xmlns:v="http://schemas.microsoft.com/visio/2003/SVGExtensions/" width="7.625in" height="3.125in" viewBox="0 0 549 225"
		xml:space="preserve" color-interpolation-filters="sRGB" class="st18">
	<v:documentProperties v:langID="1033" v:viewMarkup="false"/>

	<style type="text/css">
	<![CDATA[
		.st1 {fill:none;stroke:#ff0000;stroke-width:0.25}
		.st2 {fill:#5b9bd5;font-family:Calibri;font-size:1.00001em}
		.st3 {marker-end:url(#mrkr5-10);stroke:#ff0000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.25}
		.st4 {fill:#ff0000;fill-opacity:1;stroke:#ff0000;stroke-opacity:1;stroke-width:0.47169811320755}
		.st5 {visibility:visible}
		.st6 {fill:#5b9bd5;fill-opacity:0.22;stroke:#5b9bd5;stroke-opacity:0.22;stroke-width:0.25}
		.st7 {fill:#5b9bd5;stroke:#c8c8c8;stroke-width:0.25}
		.st8 {fill:none;stroke:none;stroke-width:0.25}
		.st9 {fill:#5b9bd5;font-family:Calibri;font-size:1.16666em}
		.st10 {stroke:#5b9bd5;stroke-dasharray:1.5,3;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.5}
		.st11 {fill:#5b9bd5;fill-opacity:0.25;stroke:#5b9bd5;stroke-opacity:0.25;stroke-width:0.75}
		.st12 {fill:#4f88bb;stroke:#41719c;stroke-width:0.75}
		.st13 {fill:#ffffff;font-family:Calibri;font-size:1.00001em;font-weight:bold}
		.st14 {fill:#000000;font-family:Calibri;font-size:1.00001em}
		.st15 {font-size:1em}
		.st16 {marker-end:url(#mrkr5-162);stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-width:1}
		.st17 {fill:#000000;fill-opacity:1;stroke:#000000;stroke-opacity:1;stroke-width:0.28409090909091}
		.st18 {fill:none;fill-rule:evenodd;font-size:12px;overflow:visible;stroke-linecap:square;stroke-miterlimit:3}
	]]>
	</style>

	<defs id="Markers">
		<g id="lend5">
			<path d="M 2 1 L 0 0 L 1.98117 -0.993387 C 1.67173 -0.364515 1.67301 0.372641 1.98465 1.00043 " style="stroke:none"/>
		</g>
		<marker id="mrkr5-10" class="st4" v:arrowType="5" v:arrowSize="2" v:setback="3.71" refX="-3.71" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend5" transform="scale(-2.12,-2.12) "/>
		</marker>
		<marker id="mrkr5-162" class="st17" v:arrowType="5" v:arrowSize="2" v:setback="5.8" refX="-5.8" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend5" transform="scale(-3.52,-3.52) "/>
		</marker>
	</defs>
	<g v:mID="0" v:index="1" v:groupContext="foregroundPage">
		<title>Page-1</title>
		<v:pageProperties v:drawingScale="1" v:pageScale="1" v:drawingUnits="0" v:shadowOffsetX="9" v:shadowOffsetY="-9"/>
		<g id="group47-1" transform="translate(3.0294,-0.25)" v:mID="47" v:groupContext="group">
			<title>Sheet.47</title>
			<g id="shape1-2" v:mID="1" v:groupContext="shape" transform="translate(177.75,-191.922)">
				<title>Sheet.1</title>
				<desc>Element</desc>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="54" cy="216" width="108" height="18"/>
				<rect x="0" y="207" width="108" height="18" class="st1"/>
				<text x="33.77" y="219.6" class="st2" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Element</text>			</g>
			<g id="shape2-5" v:mID="2" v:groupContext="shape" transform="translate(456.75,33.0781) rotate(90)">
				<title>Sheet.2</title>
				<v:userDefs>
					<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
				</v:userDefs>
				<path d="M0 225 L18.65 225" class="st3"/>
			</g>
			<g id="shape3-11" v:mID="3" v:groupContext="shape" transform="translate(0,-67.0469)">
				<title>Rectangle.54</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow3-12" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape4-15" v:mID="4" v:groupContext="shape" transform="translate(27,-67.0469)">
				<title>Rectangle.55</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow4-16" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape5-19" v:mID="5" v:groupContext="shape" transform="translate(54,-67.0469)">
				<title>Rectangle.56</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow5-20" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape6-23" v:mID="6" v:groupContext="shape" transform="translate(0,-53.5469)">
				<title>Rectangle.57</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow6-24" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape7-27" v:mID="7" v:groupContext="shape" transform="translate(27,-53.5469)">
				<title>Rectangle.58</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow7-28" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape8-31" v:mID="8" v:groupContext="shape" transform="translate(54,-53.5469)">
				<title>Rectangle.59</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow8-32" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape9-35" v:mID="9" v:groupContext="shape" transform="translate(5.625,-72.6719)">
				<title>Sheet.9</title>
				<desc>BF-1</desc>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="36" cy="211.5" width="72" height="27"/>
				<rect x="0" y="198" width="72" height="27" class="st8"/>
				<text x="23.29" y="215.7" class="st9" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>BF-1</text>			</g>
			<g id="shape10-38" v:mID="10" v:groupContext="shape" transform="translate(128.25,-65.0781)">
				<title>Rectangle.74</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow10-39" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape11-42" v:mID="11" v:groupContext="shape" transform="translate(155.25,-65.0781)">
				<title>Rectangle.75</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow11-43" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape12-46" v:mID="12" v:groupContext="shape" transform="translate(182.25,-65.0781)">
				<title>Rectangle.76</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow12-47" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape13-50" v:mID="13" v:groupContext="shape" transform="translate(128.25,-51.5781)">
				<title>Rectangle.77</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow13-51" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape14-54" v:mID="14" v:groupContext="shape" transform="translate(155.25,-51.5781)">
				<title>Rectangle.78</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow14-55" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape15-58" v:mID="15" v:groupContext="shape" transform="translate(182.25,-51.5781)">
				<title>Rectangle.79</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow15-59" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape16-62" v:mID="16" v:groupContext="shape" transform="translate(301.5,-65.0781)">
				<title>Rectangle.81</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow16-63" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape17-66" v:mID="17" v:groupContext="shape" transform="translate(328.5,-65.0781)">
				<title>Rectangle.82</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow17-67" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape18-70" v:mID="18" v:groupContext="shape" transform="translate(355.5,-65.0781)">
				<title>Rectangle.83</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow18-71" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape19-74" v:mID="19" v:groupContext="shape" transform="translate(301.5,-51.5781)">
				<title>Rectangle.84</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow19-75" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape20-78" v:mID="20" v:groupContext="shape" transform="translate(328.5,-51.5781)">
				<title>Rectangle.85</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow20-79" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape21-82" v:mID="21" v:groupContext="shape" transform="translate(355.5,-51.5781)">
				<title>Rectangle.86</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow21-83" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape22-86" v:mID="22" v:groupContext="shape" transform="translate(447.75,-65.6406)">
				<title>Rectangle.88</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow22-87" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape23-90" v:mID="23" v:groupContext="shape" transform="translate(474.75,-65.6406)">
				<title>Rectangle.89</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow23-91" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape24-94" v:mID="24" v:groupContext="shape" transform="translate(501.75,-65.6406)">
				<title>Rectangle.90</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow24-95" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape25-98" v:mID="25" v:groupContext="shape" transform="translate(447.75,-52.1406)">
				<title>Rectangle.91</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow25-99" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape26-102" v:mID="26" v:groupContext="shape" transform="translate(474.75,-52.1406)">
				<title>Rectangle.92</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow26-103" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape27-106" v:mID="27" v:groupContext="shape" transform="translate(501.75,-52.1406)">
				<title>Rectangle.93</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow27-107" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st5">
					<rect x="0" y="211.5" width="27" height="13.5" class="st6"/>
				</g>
				<rect x="0" y="211.5" width="27" height="13.5" class="st7"/>
			</g>
			<g id="shape28-110" v:mID="28" v:groupContext="shape" transform="translate(213.75,-63.9531)">
				<title>Sheet.28</title>
				<v:userDefs>
					<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
				</v:userDefs>
				<path d="M0 225 L83.25 225" class="st10"/>
			</g>
			<g id="shape29-113" v:mID="29" v:groupContext="shape" transform="translate(387,-63.9531)">
				<title>Sheet.29</title>
				<v:userDefs>
					<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
				</v:userDefs>
				<path d="M0 225 L54 225" class="st10"/>
			</g>
			<g id="group31-116" transform="translate(184.5,-113.172)" v:mID="31" v:groupContext="group">
				<title>Sheet.31</title>
				<g id="shape32-117" v:mID="32" v:groupContext="shape" transform="translate(225,173.25) rotate(90)">
					<title>Block Arrow</title>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					</v:userDefs>
					<g id="shadow32-118" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
							v:shadowType="1" transform="matrix(1,0,0,1,1.9728,-0.3456)" class="st5">
						<path d="M0 225 L25.87 225 L51.75 177.75 L25.87 130.5 L0 130.5 L0 225 Z" class="st11"/>
					</g>
					<path d="M0 225 L25.87 225 L51.75 177.75 L25.87 130.5 L0 130.5 L0 225 Z" class="st12"/>
				</g>
				<g id="shape33-121" v:mID="33" v:groupContext="shape" transform="translate(2.25,-24.3529)">
					<title>Sheet.33</title>
					<desc>h1, h2 .. hk</desc>
					<v:textBlock v:margins="rect(4,4,4,4)"/>
					<v:textRect cx="45" cy="215.868" width="90" height="18.2647"/>
					<rect x="0" y="206.735" width="90" height="18.2647" class="st8"/>
					<text x="17.56" y="219.47" class="st13" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>h1, h2 .. hk</text>				</g>
			</g>
			<g id="shape34-124" v:mID="34" v:groupContext="shape" transform="translate(307.011,286.73) rotate(152.323)">
				<title>Sheet.34</title>
				<v:userDefs>
					<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
				</v:userDefs>
				<path d="M0 225 L128.85 225" class="st3"/>
			</g>
			<g id="shape35-129" v:mID="35" v:groupContext="shape" transform="translate(433.272,125.452) rotate(99.7172)">
				<title>Sheet.35</title>
				<v:userDefs>
					<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
				</v:userDefs>
				<path d="M0 225 L58.31 225" class="st3"/>
			</g>
			<g id="shape36-134" v:mID="36" v:groupContext="shape" transform="translate(407.724,-64.1459) rotate(45)">
				<title>Sheet.36</title>
				<v:userDefs>
					<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
				</v:userDefs>
				<path d="M0 225 L79.16 225" class="st3"/>
			</g>
			<g id="shape37-139" v:mID="37" v:groupContext="shape" transform="translate(320.441,-127.12) rotate(15.6155)">
				<title>Sheet.37</title>
				<v:userDefs>
					<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
				</v:userDefs>
				<path d="M0 225 L200.75 225" class="st3"/>
			</g>
			<g id="shape38-144" v:mID="38" v:groupContext="shape" transform="translate(132.75,-75.2588)">
				<title>Sheet.38</title>
				<desc>BF-2</desc>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="36" cy="211.5" width="72" height="27"/>
				<rect x="0" y="198" width="72" height="27" class="st8"/>
				<text x="23.29" y="215.7" class="st9" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>BF-2</text>			</g>
			<g id="shape39-147" v:mID="39" v:groupContext="shape" transform="translate(303.75,-70.7588)">
				<title>Sheet.39</title>
				<desc>BF-X</desc>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="36" cy="211.5" width="72" height="27"/>
				<rect x="0" y="198" width="72" height="27" class="st8"/>
				<text x="23.2" y="215.7" class="st9" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>BF-X</text>			</g>
			<g id="shape40-150" v:mID="40" v:groupContext="shape" transform="translate(447.75,-75.2588)">
				<title>Sheet.40</title>
				<desc>BF-L</desc>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="36" cy="211.5" width="72" height="27"/>
				<rect x="0" y="198" width="72" height="27" class="st8"/>
				<text x="23.89" y="215.7" class="st9" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>BF-L</text>			</g>
			<g id="shape41-153" v:mID="41" v:groupContext="shape" transform="translate(300.375,-117)">
				<title>Sheet.41</title>
				<desc>Hashing for lookup/Insertion into a vector of BFs happens once</desc>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="90" cy="202.5" width="180" height="45"/>
				<rect x="0" y="180" width="180" height="45" class="st8"/>
				<text x="4.6" y="198.9" class="st14" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Hashing for lookup/Insertion into a <tspan
							x="23.06" dy="1.2em" class="st15">vector of BFs happens once</tspan></text>			</g>
			<g id="shape44-157" v:mID="44" v:groupContext="shape" transform="translate(249.698,-151.505) rotate(-3.74012)">
				<title>Sheet.44</title>
				<v:userDefs>
					<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
				</v:userDefs>
				<path d="M-0 225 A93.4958 45.6256 42.23 0 1 79.38 221.66 L79.68 221.85" class="st16"/>
			</g>
			<g id="shape45-163" v:mID="45" v:groupContext="shape" transform="translate(30.375,0.25)">
				<title>Sheet.45</title>
				<desc>Lookup/Insertion is done in the series of BFs, one by one or ...</desc>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="233.048" cy="202.5" width="466.1" height="45"/>
				<rect x="0" y="180" width="466.096" height="45" class="st8"/>
				<text x="4.34" y="206.1" class="st14" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Lookup/Insertion is done in the series of BFs, one by one or can be optimized to do in parallel.  </text>			</g>
			<g id="shape46-166" v:mID="46" v:groupContext="shape" transform="translate(123.252,-43.6868) rotate(17.0249)">
				<title>Sheet.46</title>
				<v:userDefs>
					<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
				</v:userDefs>
				<path d="M-0 225 A88.2185 43.0621 47.63 0 1 70.31 221.39 L70.6 221.6" class="st16"/>
			</g>
		</g>
	</g>
</svg>
