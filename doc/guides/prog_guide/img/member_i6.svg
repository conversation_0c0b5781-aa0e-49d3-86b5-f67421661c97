<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">
<!-- Generated by Microsoft Visio 11.0, SVG Export, v1.0 memship_i6.svg Page-1 -->
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:ev="http://www.w3.org/2001/xml-events"
		xmlns:v="http://schemas.microsoft.com/visio/2003/SVGExtensions/" width="8in" height="3.625in" viewBox="0 0 576 261"
		xml:space="preserve" color-interpolation-filters="sRGB" class="st16">
	<v:documentProperties v:langID="1033" v:viewMarkup="false"/>

	<style type="text/css">
	<![CDATA[
		.st1 {visibility:visible}
		.st2 {fill:#5b9bd5;fill-opacity:0.22;stroke:#5b9bd5;stroke-opacity:0.22;stroke-width:0.25}
		.st3 {fill:#5b9bd5;stroke:#c8c8c8;stroke-width:0.25}
		.st4 {fill:#feffff;font-family:Calibri;font-size:0.666664em;font-weight:bold}
		.st5 {font-size:1em}
		.st6 {fill:#70ad47;fill-opacity:0.5;stroke:#00b050;stroke-width:1.5}
		.st7 {fill:none;stroke:none;stroke-width:0.25}
		.st8 {fill:#00b050;font-family:Calibri;font-size:1.00001em}
		.st9 {fill:none;stroke:#ff0000;stroke-width:0.25}
		.st10 {fill:#5b9bd5;font-family:Calibri;font-size:0.833336em}
		.st11 {marker-end:url(#mrkr5-29);stroke:#ff0000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.25}
		.st12 {fill:#ff0000;fill-opacity:1;stroke:#ff0000;stroke-opacity:1;stroke-width:0.47169811320755}
		.st13 {fill:#5b9bd5;font-family:Calibri;font-size:0.75em}
		.st14 {fill:#92d050;stroke:#c8c8c8;stroke-width:0.25}
		.st15 {fill:#5b9bd5;font-family:Calibri;font-size:1.00001em;font-weight:bold}
		.st16 {fill:none;fill-rule:evenodd;font-size:12px;overflow:visible;stroke-linecap:square;stroke-miterlimit:3}
	]]>
	</style>

	<defs id="Markers">
		<g id="lend5">
			<path d="M 2 1 L 0 0 L 1.98117 -0.993387 C 1.67173 -0.364515 1.67301 0.372641 1.98465 1.00043 " style="stroke:none"/>
		</g>
		<marker id="mrkr5-29" class="st12" v:arrowType="5" v:arrowSize="2" v:setback="3.71" refX="-3.71" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend5" transform="scale(-2.12,-2.12) "/>
		</marker>
	</defs>
	<g v:mID="0" v:index="1" v:groupContext="foregroundPage">
		<title>Page-1</title>
		<v:pageProperties v:drawingScale="1" v:pageScale="1" v:drawingUnits="0" v:shadowOffsetX="9" v:shadowOffsetY="-9"/>
		<g id="group121-1" transform="translate(21.0294,-9.8478)" v:mID="121" v:groupContext="group">
			<title>Sheet.121</title>
			<g id="shape49-2" v:mID="49" v:groupContext="shape" transform="translate(396.989,-54.9268)">
				<title>Rectangle.2</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow49-3" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
					<rect x="0" y="219.549" width="99.4817" height="41.4507" class="st2"/>
				</g>
				<rect x="0" y="219.549" width="99.4817" height="41.4507" class="st3"/>
			</g>
			<g id="shape50-6" v:mID="50" v:groupContext="shape" transform="translate(248.261,-12.1936)">
				<title>Rectangle.4</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<g id="shadow50-7" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
					<rect x="0" y="178.099" width="99.4817" height="82.9014" class="st2"/>
				</g>
				<rect x="0" y="178.099" width="99.4817" height="82.9014" class="st3"/>
			</g>
			<g id="shape52-10" v:mID="52" v:groupContext="shape" transform="translate(6.07514E-013,-29.0155)">
				<title>Rectangle.10</title>
				<desc>Signatures for target 1</desc>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="49.7409" cy="225.767" width="99.49" height="70.4662"/>
				<g id="shadow52-11" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
					<rect x="0" y="190.534" width="99.4817" height="70.4662" class="st2"/>
				</g>
				<rect x="0" y="190.534" width="99.4817" height="70.4662" class="st3"/>
				<text x="26.54" y="223.37" class="st4" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Signatures for<v:newlineChar/><tspan
							x="36.73" dy="1.2em" class="st5">target </tspan>1</text>			</g>
			<g id="shape53-16" v:mID="53" v:groupContext="shape" transform="translate(239.971,-20.4837)">
				<title>Sheet.53</title>
				<v:userDefs>
					<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
				</v:userDefs>
				<rect x="0" y="248.565" width="116.062" height="12.4352" class="st6"/>
			</g>
			<g id="shape54-18" v:mID="54" v:groupContext="shape" transform="translate(353.649,-19.9346)">
				<title>Sheet.54</title>
				<desc>Match 1</desc>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="33.1606" cy="254.782" width="66.33" height="12.4352"/>
				<rect x="0" y="248.565" width="66.3211" height="12.4352" class="st7"/>
				<text x="13.06" y="258.38" class="st8" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Match 1</text>			</g>
			<g id="shape55-21" v:mID="55" v:groupContext="shape" transform="translate(216.989,-210.652)">
				<title>Sheet.55</title>
				<desc>Packet Payload</desc>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="49.7409" cy="252.71" width="99.49" height="16.5803"/>
				<rect x="0" y="244.42" width="99.4817" height="16.5803" class="st9"/>
				<text x="19.04" y="255.71" class="st10" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Packet Payload</text>			</g>
			<g id="shape56-24" v:mID="56" v:groupContext="shape" transform="translate(526.665,52.2365) rotate(90)">
				<title>Sheet.56</title>
				<v:userDefs>
					<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
				</v:userDefs>
				<path d="M0 261 L16.52 261" class="st11"/>
			</g>
			<g id="shape96-30" v:mID="96" v:groupContext="shape" transform="translate(-3.0294,-95.7818)">
				<title>Sheet.96</title>
				<desc>Attack Signature Length 1</desc>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="51.75" cy="248.565" width="103.5" height="24.8704"/>
				<rect x="0" y="236.13" width="103.5" height="24.8704" class="st7"/>
				<text x="4.79" y="251.26" class="st13" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Attack Signature Length 1</text>			</g>
			<g id="group114-33" transform="translate(228.359,-134.152)" v:mID="114" v:groupContext="group">
				<title>Sheet.114</title>
				<g id="group106-34" transform="translate(0,-24.8704)" v:mID="106" v:groupContext="group">
					<title>Sheet.106</title>
					<g id="shape100-35" v:mID="100" v:groupContext="shape" transform="translate(3.65707E-013,-12.4352)">
						<title>Rectangle.100</title>
						<v:userDefs>
							<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
						</v:userDefs>
						<g id="shadow100-36" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st2"/>
						</g>
						<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st3"/>
					</g>
					<g id="shape101-39" v:mID="101" v:groupContext="shape" transform="translate(24.8704,-12.4352)">
						<title>Rectangle.101</title>
						<v:userDefs>
							<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
						</v:userDefs>
						<g id="shadow101-40" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st2"/>
						</g>
						<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st3"/>
					</g>
					<g id="shape102-43" v:mID="102" v:groupContext="shape" transform="translate(49.7409,-12.4352)">
						<title>Rectangle.102</title>
						<v:userDefs>
							<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
						</v:userDefs>
						<g id="shadow102-44" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st2"/>
						</g>
						<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st3"/>
					</g>
					<g id="shape103-47" v:mID="103" v:groupContext="shape">
						<title>Rectangle.103</title>
						<v:userDefs>
							<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
						</v:userDefs>
						<g id="shadow103-48" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st2"/>
						</g>
						<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st3"/>
					</g>
					<g id="shape104-51" v:mID="104" v:groupContext="shape" transform="translate(24.8704,1.13687E-013)">
						<title>Rectangle.104</title>
						<v:userDefs>
							<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
						</v:userDefs>
						<g id="shadow104-52" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st2"/>
						</g>
						<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st3"/>
					</g>
					<g id="shape105-55" v:mID="105" v:groupContext="shape" transform="translate(49.7409,1.13687E-013)">
						<title>Rectangle.105</title>
						<v:userDefs>
							<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
						</v:userDefs>
						<g id="shadow105-56" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st2"/>
						</g>
						<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st3"/>
					</g>
				</g>
				<g id="group107-59" v:mID="107" v:groupContext="group">
					<title>Sheet.107</title>
					<g id="shape108-60" v:mID="108" v:groupContext="shape" transform="translate(3.65707E-013,-12.4352)">
						<title>Rectangle.100</title>
						<v:userDefs>
							<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
						</v:userDefs>
						<g id="shadow108-61" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st2"/>
						</g>
						<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st3"/>
					</g>
					<g id="shape109-64" v:mID="109" v:groupContext="shape" transform="translate(24.8704,-12.4352)">
						<title>Rectangle.101</title>
						<v:userDefs>
							<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
						</v:userDefs>
						<g id="shadow109-65" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st2"/>
						</g>
						<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st3"/>
					</g>
					<g id="shape110-68" v:mID="110" v:groupContext="shape" transform="translate(49.7409,-12.4352)">
						<title>Rectangle.102</title>
						<v:userDefs>
							<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
						</v:userDefs>
						<g id="shadow110-69" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st2"/>
						</g>
						<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st3"/>
					</g>
					<g id="shape111-72" v:mID="111" v:groupContext="shape">
						<title>Rectangle.103</title>
						<v:userDefs>
							<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
							<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
						</v:userDefs>
						<g id="shadow111-73" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st2"/>
						</g>
						<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st14"/>
					</g>
					<g id="shape112-76" v:mID="112" v:groupContext="shape" transform="translate(24.8704,1.13687E-013)">
						<title>Rectangle.104</title>
						<v:userDefs>
							<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
						</v:userDefs>
						<g id="shadow112-77" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st2"/>
						</g>
						<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st3"/>
					</g>
					<g id="shape113-80" v:mID="113" v:groupContext="shape" transform="translate(49.7409,1.13687E-013)">
						<title>Rectangle.105</title>
						<v:userDefs>
							<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
							<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
						</v:userDefs>
						<g id="shadow113-81" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728"
								v:shadowType="1" transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
							<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st2"/>
						</g>
						<rect x="0" y="248.565" width="24.8704" height="12.4352" class="st14"/>
					</g>
				</g>
			</g>
			<g id="shape89-84" v:mID="89" v:groupContext="shape" transform="translate(398.644,-116.927) rotate(24.4696)">
				<title>Sheet.89</title>
				<v:userDefs>
					<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
				</v:userDefs>
				<path d="M0 261 L143.75 261" class="st11"/>
			</g>
			<g id="shape115-89" v:mID="115" v:groupContext="shape" transform="translate(116.062,-1.19371E-012)">
				<title>Rectangle.115</title>
				<desc>Signatures for target 2</desc>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="49.7409" cy="211.259" width="99.49" height="99.4817"/>
				<g id="shadow115-90" v:groupContext="shadow" v:shadowOffsetX="0.3456" v:shadowOffsetY="-1.9728" v:shadowType="1"
						transform="matrix(1,0,0,1,0.3456,1.9728)" class="st1">
					<rect x="0" y="161.518" width="99.4817" height="99.4817" class="st2"/>
				</g>
				<rect x="0" y="161.518" width="99.4817" height="99.4817" class="st3"/>
				<text x="26.54" y="208.86" class="st4" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Signatures for<v:newlineChar/><tspan
							x="36.73" dy="1.2em" class="st5">target </tspan>2</text>			</g>
			<g id="shape116-95" v:mID="116" v:groupContext="shape" transform="translate(117.989,-95.7818)">
				<title>Sheet.116</title>
				<desc>Attack Signature Length 2</desc>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="51.9909" cy="248.565" width="103.99" height="24.8704"/>
				<rect x="0" y="236.13" width="103.982" height="24.8704" class="st7"/>
				<text x="5.03" y="251.26" class="st13" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Attack Signature Length 2</text>			</g>
			<g id="shape118-98" v:mID="118" v:groupContext="shape" transform="translate(392.971,-90.217)">
				<title>Sheet.118</title>
				<desc>Attack Signature Length L</desc>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="54" cy="248.565" width="108" height="24.8704"/>
				<rect x="0" y="236.13" width="108" height="24.8704" class="st7"/>
				<text x="7.43" y="251.26" class="st13" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Attack Signature Length L</text>			</g>
			<g id="shape119-101" v:mID="119" v:groupContext="shape" transform="translate(384.909,-64.9346)">
				<title>Sheet.119</title>
				<v:userDefs>
					<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
				</v:userDefs>
				<rect x="0" y="248.565" width="116.062" height="12.4352" class="st6"/>
			</g>
			<g id="shape120-103" v:mID="120" v:groupContext="shape" transform="translate(491.971,-64.9346)">
				<title>Sheet.120</title>
				<desc>Match 2</desc>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="33.1606" cy="254.782" width="66.33" height="12.4352"/>
				<rect x="0" y="248.565" width="66.3211" height="12.4352" class="st7"/>
				<text x="13.06" y="258.38" class="st8" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Match 2</text>			</g>
			<g id="shape85-106" v:mID="85" v:groupContext="shape" transform="translate(478.538,12.9307) rotate(65.6291)">
				<title>Sheet.85</title>
				<v:userDefs>
					<v:ud v:nameU="msvThemeColors" v:val="VT0(254):26"/>
				</v:userDefs>
				<path d="M0 261 L109.61 261" class="st11"/>
			</g>
			<g id="shape117-111" v:mID="117" v:groupContext="shape" transform="translate(247.054,-91.2818)">
				<title>Sheet.117</title>
				<desc>Attack Signature Length X</desc>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="52.7082" cy="248.565" width="105.42" height="24.8704"/>
				<rect x="0" y="236.13" width="105.416" height="24.8704" class="st7"/>
				<text x="5.7" y="251.26" class="st13" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Attack Signature Length X</text>			</g>
		</g>
		<g id="shape122-114" v:mID="122" v:groupContext="shape" transform="translate(315.114,-164.13)">
			<title>Sheet.122</title>
			<desc>HTSS</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="26.943" cy="248.565" width="53.89" height="24.8704"/>
			<rect x="0" y="236.13" width="53.8859" height="24.8704" class="st7"/>
			<text x="14.52" y="252.16" class="st15" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>HTSS</text>		</g>
	</g>
</svg>
