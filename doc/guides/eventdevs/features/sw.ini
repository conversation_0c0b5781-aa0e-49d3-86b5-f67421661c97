;
; Supported features of the 'software' eventdev driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Scheduling Features]
atomic_scheduling          = Y
ordered_scheduling         = Y
parallel_scheduling        = Y
queue_qos                  = Y
event_qos                  = Y
burst_mode                 = Y
implicit_release_disable   = Y
nonseq_mode                = Y
runtime_port_link          = Y
multiple_queue_port        = Y
carry_flow_id              = Y
maintenance_free           = Y

[Eth Rx adapter Features]
multi_eventq               = Y
override_flow_id           = Y
event_vector               = Y

[Eth Tx adapter Features]

[Crypto adapter Features]
session_private_data       = Y

[Timer adapter Features]
