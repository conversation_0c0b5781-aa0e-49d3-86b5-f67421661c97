;
; Features of a default eventdev driver.
;
; This file defines the features that are valid for inclusion in
; the other driver files and also the order that they appear in
; the features table in the documentation.
;
[Scheduling Features]
atomic_scheduling          =
ordered_scheduling         =
parallel_scheduling        =
queue_qos                  =
event_qos                  =
distributed_sched          =
queue_all_types            =
burst_mode                 =
implicit_release_disable   =
nonseq_mode                =
runtime_port_link          =
multiple_queue_port        =
carry_flow_id              =
maintenance_free           =
runtime_queue_attr         =
profile_links              =
independent_enq            =
preschedule                =

;
; Features of a default Ethernet Rx adapter.
;
[Eth Rx adapter Features]
internal_port              =
multi_eventq               =
override_flow_id           =
event_vector               =

;
; Features of a default Ethernet Tx adapter.
;
[Eth Tx adapter Features]
internal_port              =
event_vector               =

;
; Features of a default Crypto adapter.
;
[Crypto adapter Features]
internal_port_op_new       =
internal_port_op_fwd       =
internal_port_qp_ev_bind   =
session_private_data       =

;
; Features of a default DMA adapter.
;
[DMA adapter Features]
internal_port_op_new       =
internal_port_op_fwd       =
internal_port_vchan_ev_bind =

;
; Features of a default Timer adapter.
;
[Timer adapter Features]
internal_port              =
periodic                   =

;
; Features of a default Vector adapter
;
[Vector adapter Features]
internal_port              =
sov_eov                    =
