;
; Supported features of the 'dpaa2' eventdev driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Scheduling Features]
atomic_scheduling          = Y
parallel_scheduling        = Y
distributed_sched          = Y
queue_all_types            = Y
burst_mode                 = Y
nonseq_mode                = Y
runtime_port_link          = Y
multiple_queue_port        = Y
carry_flow_id              = Y
maintenance_free           = Y

[Eth Rx adapter Features]
internal_port              = Y
multi_eventq               = Y
override_flow_id           = Y

[Eth Tx adapter Features]
internal_port              = Y

[Crypto adapter Features]
internal_port_op_new       = Y
internal_port_qp_ev_bind   = Y
session_private_data       = Y

[Timer adapter Features]
