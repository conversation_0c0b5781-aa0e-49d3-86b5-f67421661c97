;
; Supported features of the 'zxdh' network poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Linux                = Y
x86-64               = Y
ARMv8                = Y
SR-IOV               = Y
Multiprocess aware   = Y
Scattered Rx         = Y
Link status          = Y
Link status event    = Y
Unicast MAC filter   = Y
Multicast MAC filter = Y
Promiscuous mode     = Y
Allmulticast mode    = Y
VLAN filter          = Y
VLAN offload         = Y
QinQ offload         = Y
RSS hash             = Y
RSS reta update      = Y
Inner RSS            = Y
Basic stats          = Y
Stats per queue      = Y
MTU update           = Y
L3 checksum offload  = Y
Inner L3 checksum    = Y
Inner L4 checksum    = Y
LRO                  = Y
TSO                  = Y
Extended stats       = Y
FW version           = Y
Module EEPROM dump   = Y

[rte_flow actions]
drop                 = Y
