;
; Supported features of the 'ixgbe' network poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Speed capabilities   = Y
Link speed configuration = Y
Link status          = Y
Link status event    = Y
Rx interrupt         = Y
Queue start/stop     = Y
Power mgmt address monitor = Y
MTU update           = Y
Scattered Rx         = Y
LRO                  = Y
TSO                  = Y
Promiscuous mode     = Y
Allmulticast mode    = Y
Unicast MAC filter   = Y
Multicast MAC filter = Y
RSS hash             = Y
RSS key update       = Y
RSS reta update      = Y
VMDq                 = Y
SR-IOV               = Y
DCB                  = Y
VLAN filter          = Y
Flow control         = Y
Rate limitation      = Y
Traffic manager      = Y
Inline crypto        = Y
CRC offload          = P
VLAN offload         = P
QinQ offload         = P
L3 checksum offload  = P
L4 checksum offload  = P
MACsec offload       = P
Inner L3 checksum    = P
Inner L4 checksum    = P
Packet type parsing  = Y
Timesync             = Y
Rx descriptor status = Y
Tx descriptor status = Y
Basic stats          = Y
Extended stats       = Y
Stats per queue      = Y
FW version           = Y
EEPROM dump          = Y
Module EEPROM dump   = Y
Registers dump       = Y
Multiprocess aware   = Y
FreeBSD              = Y
Linux                = Y
Windows              = Y
ARMv8                = Y
LoongArch64          = Y
rv64                 = Y
x86-32               = Y
x86-64               = Y

[rte_flow items]
eth                  = P
e_tag                = Y
fuzzy                = Y
ipv4                 = Y
ipv6                 = Y
nvgre                = Y
raw                  = Y
sctp                 = Y
tcp                  = Y
udp                  = Y
vlan                 = P
vxlan                = Y

[rte_flow actions]
drop                 = Y
mark                 = Y
pf                   = Y
queue                = Y
rss                  = Y
security             = Y
vf                   = Y
