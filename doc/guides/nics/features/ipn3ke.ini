;
; Supported features of the 'ipn3ke' network poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Speed capabilities   = Y
Link status          = Y
Link status event    = Y
Rx interrupt         = Y
Queue start/stop     = Y
Runtime Rx queue setup = Y
Runtime Tx queue setup = Y
Scattered Rx         = Y
TSO                  = Y
Promiscuous mode     = Y
Allmulticast mode    = Y
Unicast MAC filter   = Y
Multicast MAC filter = Y
RSS hash             = Y
RSS key update       = Y
RSS reta update      = Y
VMDq                 = Y
SR-IOV               = Y
DCB                  = Y
VLAN filter          = Y
Flow control         = Y
Traffic manager      = Y
CRC offload          = Y
VLAN offload         = Y
QinQ offload         = Y
L3 checksum offload  = Y
L4 checksum offload  = Y
Inner L3 checksum    = Y
Inner L4 checksum    = Y
Packet type parsing  = Y
Timesync             = Y
Rx descriptor status = Y
Tx descriptor status = Y
Basic stats          = Y
Extended stats       = Y
FW version           = Y
Module EEPROM dump   = Y
Multiprocess aware   = Y
FreeBSD              = Y
Linux                = Y
x86-32               = Y
x86-64               = Y

[rte_flow items]
eth                  = P
ipv4                 = Y
mpls                 = Y
nvgre                = Y
tcp                  = Y
udp                  = Y
vlan                 = P
vxlan                = Y

[rte_flow actions]
drop                 = Y
mark                 = Y
