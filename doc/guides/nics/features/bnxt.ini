;
; Supported features of the 'bnxt' network poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Speed capabilities   = Y
Link speed configuration = Y
Link status          = Y
Link status event    = Y
Rx interrupt         = Y
Fast mbuf free       = Y
Queue start/stop     = Y
Runtime Rx queue setup = Y
Runtime Tx queue setup = Y
Burst mode info      = Y
MTU update           = Y
Scattered Rx         = Y
LRO                  = Y
TSO                  = Y
Promiscuous mode     = Y
Allmulticast mode    = Y
Unicast MAC filter   = Y
Multicast MAC filter = Y
RSS hash             = Y
RSS key update       = Y
RSS reta update      = Y
Inner RSS            = Y
VMDq                 = Y
SR-IOV               = Y
VLAN filter          = Y
Flow control         = Y
CRC offload          = Y
L3 checksum offload  = Y
L4 checksum offload  = Y
Inner L3 checksum    = Y
Inner L4 checksum    = Y
Packet type parsing  = Y
Timesync             = Y
VLAN offload         = Y
Rx descriptor status = Y
Tx descriptor status = Y
Basic stats          = Y
Extended stats       = Y
Stats per queue      = Y
FW version           = Y
EEPROM dump          = Y
Module EEPROM dump   = Y
LED                  = Y
Multiprocess aware   = Y
FreeBSD              = Y
Linux                = Y
ARMv8                = Y
x86-32               = Y
x86-64               = Y
Usage doc            = Y
Perf doc             = Y

[rte_flow items]
any                  = Y
ecpri                = Y
eth                  = P
geneve               = Y
ipv4                 = Y
ipv6                 = Y
gre                  = Y
icmp                 = Y
icmp6                = Y
port_id              = Y
port_representor     = Y
represented_port     = Y
tcp                  = Y
udp                  = Y
vlan                 = P
vxlan                = Y
vxlan_gpe            = Y

[rte_flow actions]
count                = Y
dec_ttl              = Y
drop                 = Y
jump                 = Y
mark                 = Y
meter                = Y
of_pop_vlan          = Y
of_push_vlan         = Y
of_set_vlan_pcp      = Y
of_set_vlan_vid      = Y
pf                   = Y
port_id              = Y
port_representor     = Y
queue                = Y
represented_port     = Y
rss                  = Y
sample               = Y
set_ipv4_dst         = Y
set_ipv4_src         = Y
set_ipv6_dst         = Y
set_ipv6_src         = Y
set_mac_dst          = Y
set_mac_src          = Y
set_tp_dst           = Y
set_tp_src           = Y
set_ttl              = Y
vf                   = Y
vxlan_decap          = Y
vxlan_encap          = Y
