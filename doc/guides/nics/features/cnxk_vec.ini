;
; Supported features of the 'cnxk_vec' network poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Speed capabilities   = Y
Rx interrupt         = Y
Lock-free Tx queue   = Y
SR-IOV               = Y
Multiprocess aware   = Y
Link status          = Y
Link status event    = Y
Runtime Rx queue setup = Y
Runtime Tx queue setup = Y
Burst mode info      = Y
Fast mbuf free       = Y
Free Tx mbuf on demand = Y
Queue start/stop     = Y
MTU update           = Y
Promiscuous mode     = Y
Allmulticast mode    = Y
Unicast MAC filter   = Y
Multicast MAC filter = Y
RSS hash             = Y
RSS key update       = Y
RSS reta update      = Y
Inner RSS            = Y
Inline protocol      = Y
Flow control         = Y
L3 checksum offload  = Y
L4 checksum offload  = Y
Inner L3 checksum    = Y
Inner L4 checksum    = Y
Packet type parsing  = Y
Rx descriptor status = Y
Tx descriptor status = Y
Basic stats          = Y
Stats per queue      = Y
Extended stats       = Y
FW version           = Y
Module EEPROM dump   = Y
Registers dump       = Y
Linux                = Y
ARMv8                = Y
Usage doc            = Y

[rte_flow items]
any                  = Y
arp_eth_ipv4         = Y
esp                  = Y
eth                  = Y
e_tag                = Y
geneve               = Y
gre                  = Y
gre_key              = Y
gtpc                 = Y
gtpu                 = Y
higig2               = Y
icmp                 = Y
ipv4                 = Y
ipv6                 = Y
ipv6_ext             = Y
ipv6_frag_ext        = Y
mark                 = Y
mpls                 = Y
nvgre                = Y
port_representor     = Y
pppoes               = Y
raw                  = Y
represented_port     = Y
sctp                 = Y
tcp                  = Y
tx_queue             = Y
udp                  = Y
vlan                 = Y
vxlan                = Y
vxlan_gpe            = Y

[rte_flow actions]
count                = Y
drop                 = Y
flag                 = Y
mark                 = Y
of_pop_vlan          = Y
of_push_vlan         = Y
of_set_vlan_pcp      = Y
of_set_vlan_vid      = Y
pf                   = Y
port_representor     = Y
queue                = Y
represented_port     = Y
rss                  = Y
security             = Y
vf                   = Y
vxlan_decap          = I
vxlan_encap          = I
