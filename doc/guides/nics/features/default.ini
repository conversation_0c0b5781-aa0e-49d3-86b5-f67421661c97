;
; Features of a default network driver.
;
; This file defines the features that are valid for inclusion in
; the other driver files and also the order that they appear in
; the features table in the documentation. The feature description
; string should not exceed feature_str_len defined in conf.py.
;
[Features]
Speed capabilities   =
Link speed configuration =
Link status          =
Link status event    =
Removal event        =
Queue status event   =
Rx interrupt         =
Lock-free Tx queue   =
Fast mbuf free       =
Free Tx mbuf on demand =
Queue start/stop     =
Runtime Rx queue setup =
Runtime Tx queue setup =
Shared Rx queue      =
Burst mode info      =
Power mgmt address monitor =
MTU update           =
Buffer split on Rx   =
Scattered Rx         =
LRO                  =
TSO                  =
Promiscuous mode     =
Allmulticast mode    =
Unicast MAC filter   =
Multicast MAC filter =
RSS hash             =
RSS key update       =
RSS reta update      =
Inner RSS            =
VMDq                 =
SR-IOV               =
DCB                  =
VLAN filter          =
Flow control         =
Rate limitation      =
Congestion management =
Traffic manager      =
Inline crypto        =
Inline protocol      =
CRC offload          =
VLAN offload         =
QinQ offload         =
FEC                  =
IP reassembly        =
L3 checksum offload  =
L4 checksum offload  =
Timestamp offload    =
MACsec offload       =
Inner L3 checksum    =
Inner L4 checksum    =
Packet type parsing  =
Timesync             =
Rx descriptor status =
Tx descriptor status =
Tx queue count       =
Basic stats          =
Extended stats       =
Stats per queue      =
FW version           =
EEPROM dump          =
Module EEPROM dump   =
Registers dump       =
LED                  =
Multiprocess aware   =
FreeBSD              =
Linux                =
Windows              =
ARMv7                =
ARMv8                =
LoongArch64          =
Power8               =
rv64                 =
x86-32               =
x86-64               =
Usage doc            =
Design doc           =
Perf doc             =

[rte_flow items]
aggr_affinity        =
ah                   =
any                  =
arp_eth_ipv4         =
compare              =
conntrack            =
ecpri                =
esp                  =
eth                  =
e_tag                =
flex                 =
fuzzy                =
geneve               =
geneve_opt           =
gre                  =
gre_key              =
gre_option           =
gtp                  =
gtpc                 =
gtpu                 =
gtp_psc              =
higig2               =
ib_bth               =
icmp                 =
icmp6                =
icmp6_echo_request   =
icmp6_echo_reply     =
icmp6_nd_na          =
icmp6_nd_ns          =
icmp6_nd_opt         =
icmp6_nd_opt_sla_eth =
icmp6_nd_opt_tla_eth =
igmp                 =
integrity            =
invert               =
ipv4                 =
ipv6                 =
ipv6_ext             =
ipv6_frag_ext        =
ipv6_routing_ext     =
l2tpv2               =
l2tpv3oip            =
mark                 =
meta                 =
meter_color          =
mpls                 =
nsh                  =
nvgre                =
pfcp                 =
port_id              =
port_representor     =
ppp                  =
pppoed               =
pppoes               =
pppoe_proto_id       =
ptype                =
quota                =
random               =
raw                  =
represented_port     =
sctp                 =
tag                  =
tcp                  =
tx_queue             =
udp                  =
vlan                 =
vxlan                =
vxlan_gpe            =

[rte_flow actions]
age                  =
conntrack            =
count                =
dec_tcp_ack          =
dec_tcp_seq          =
dec_ttl              =
drop                 =
flag                 =
inc_tcp_ack          =
inc_tcp_seq          =
indirect_list        =
ipv6_ext_push        =
ipv6_ext_remove      =
jump                 =
jump_to_table_index  =
mac_swap             =
mark                 =
meter                =
meter_mark           =
modify_field         =
nat64                =
nvgre_decap          =
nvgre_encap          =
of_copy_ttl_in       =
of_copy_ttl_out      =
of_dec_mpls_ttl      =
of_dec_nw_ttl        =
of_pop_mpls          =
of_pop_vlan          =
of_push_mpls         =
of_push_vlan         =
of_set_mpls_ttl      =
of_set_nw_ttl        =
of_set_vlan_pcp      =
of_set_vlan_vid      =
passthru             =
pf                   =
port_id              =
port_representor     =
prog                 =
queue                =
quota                =
raw_decap            =
raw_encap            =
represented_port     =
rss                  =
sample               =
security             =
send_to_kernel       =
set_ipv4_dscp        =
set_ipv4_dst         =
set_ipv4_src         =
set_ipv6_dscp        =
set_ipv6_dst         =
set_ipv6_src         =
set_mac_dst          =
set_mac_src          =
set_meta             =
set_tag              =
set_tp_dst           =
set_tp_src           =
set_ttl              =
skip_cman            =
vf                   =
vxlan_decap          =
vxlan_encap          =
