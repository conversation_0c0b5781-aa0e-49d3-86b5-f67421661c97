;
; Supported features of the 'hns3' network poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Link status          = Y
Link status event    = Y
Rx interrupt         = Y
Queue start/stop     = Y
Runtime Rx queue setup = Y
Runtime Tx queue setup = Y
Burst mode info      = Y
Free Tx mbuf on demand = Y
MTU update           = Y
Scattered Rx         = Y
TSO                  = Y
LRO                  = Y
Promiscuous mode     = Y
Allmulticast mode    = Y
Unicast MAC filter   = Y
Multicast MAC filter = Y
RSS hash             = Y
RSS key update       = Y
RSS reta update      = Y
VLAN filter          = Y
CRC offload          = Y
VLAN offload         = Y
L3 checksum offload  = Y
L4 checksum offload  = Y
Inner L3 checksum    = Y
Inner L4 checksum    = Y
Packet type parsing  = Y
Tx descriptor status = Y
Rx descriptor status = Y
Basic stats          = Y
Extended stats       = Y
Stats per queue      = Y
Registers dump       = Y
Multiprocess aware   = Y
Linux                = Y
ARMv8                = Y
