;
; Supported features of the 'ena' network poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Link status          = Y
Link status event    = Y
Rx interrupt         = Y
Fast mbuf free       = Y
Free Tx mbuf on demand = Y
MTU update           = Y
Scattered Rx         = Y
TSO                  = Y
RSS hash             = Y
RSS key update       = Y
RSS reta update      = Y
L3 checksum offload  = Y
L4 checksum offload  = Y
Basic stats          = Y
Extended stats       = Y
Multiprocess aware   = Y
Linux                = Y
ARMv8                = Y
x86-32               = Y
x86-64               = Y
Usage doc            = Y
Design doc           = Y
