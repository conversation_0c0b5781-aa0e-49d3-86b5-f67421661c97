;
; Supported features of the 'dpaa2' network poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Speed capabilities   = Y
Link status          = Y
Link status event    = Y
Burst mode info      = Y
Queue start/stop     = Y
Scattered Rx         = Y
MTU update           = Y
Promiscuous mode     = Y
Allmulticast mode    = Y
Unicast MAC filter   = Y
RSS hash             = Y
VLAN filter          = Y
Flow control         = Y
Traffic manager      = Y
VLAN offload         = Y
L3 checksum offload  = Y
L4 checksum offload  = Y
Packet type parsing  = Y
Timesync             = Y
Timestamp offload    = Y
Basic stats          = Y
Extended stats       = Y
FW version           = Y
Linux                = Y
ARMv8                = Y
Usage doc            = Y

[rte_flow items]
ah                   = Y
ecpri                = Y
esp                  = Y
eth                  = P
gre                  = Y
gtp                  = Y
icmp                 = Y
ipv4                 = Y
ipv6                 = Y
raw                  = Y
sctp                 = Y
tcp                  = Y
udp                  = Y
vlan                 = P
vxlan                = Y

[rte_flow actions]
drop                 = Y
port_id              = Y
queue                = Y
represented_port     = Y
rss                  = Y
