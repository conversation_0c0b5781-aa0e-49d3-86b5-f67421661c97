;
; Supported features of the 'sfc_efx' network poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Speed capabilities   = Y
Link speed configuration = Y
Link status          = Y
Link status event    = Y
Rx interrupt         = Y
Fast mbuf free       = Y
Queue start/stop     = Y
Runtime Rx queue setup = Y
Runtime Tx queue setup = Y
MTU update           = Y
Scattered Rx         = Y
TSO                  = Y
Promiscuous mode     = Y
Allmulticast mode    = Y
Multicast MAC filter = Y
RSS hash             = Y
RSS key update       = Y
RSS reta update      = Y
SR-IOV               = Y
Flow control         = Y
CRC offload          = Y
VLAN offload         = P
FEC                  = Y
L3 checksum offload  = Y
L4 checksum offload  = Y
Inner L3 checksum    = Y
Inner L4 checksum    = Y
Packet type parsing  = Y
Rx descriptor status = Y
Tx descriptor status = Y
Basic stats          = Y
Extended stats       = Y
FW version           = Y
Multiprocess aware   = Y
FreeBSD              = Y
Linux                = Y
ARMv8                = Y
x86-64               = Y

[rte_flow items]
eth                  = Y
geneve               = Y
ipv4                 = Y
ipv6                 = Y
mark                 = P
nvgre                = Y
port_id              = Y
port_representor     = Y
pppoed               = Y
pppoes               = Y
represented_port     = Y
tcp                  = Y
udp                  = Y
vlan                 = Y
vxlan                = Y

[rte_flow actions]
count                = Y
dec_ttl              = Y
drop                 = Y
flag                 = Y
jump                 = P
mark                 = Y
of_dec_nw_ttl        = Y
of_pop_vlan          = Y
of_push_vlan         = Y
of_set_vlan_pcp      = Y
of_set_vlan_vid      = Y
pf                   = Y
port_id              = Y
port_representor     = Y
represented_port     = Y
queue                = Y
rss                  = Y
set_ipv4_dst         = Y
set_ipv4_src         = Y
set_mac_dst          = Y
set_mac_src          = Y
set_tp_dst           = Y
set_tp_src           = Y
vf                   = Y
vxlan_decap          = Y
vxlan_encap          = Y
