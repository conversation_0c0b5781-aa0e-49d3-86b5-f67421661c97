; Supported features of the 'igc' network poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Speed capabilities   = Y
Link speed configuration = Y
Link status          = Y
Link status event    = Y
FW version           = Y
LED                  = Y
Packet type parsing  = Y
Rx descriptor status = Y
Tx descriptor status = Y
MTU update           = Y
Scattered Rx         = Y
TSO                  = Y
Promiscuous mode     = Y
Allmulticast mode    = Y
Unicast MAC filter   = Y
Multicast MAC filter = Y
RSS hash             = Y
CRC offload          = Y
L3 checksum offload  = Y
L4 checksum offload  = Y
Timesync             = Y
Basic stats          = Y
Extended stats       = Y
Stats per queue      = Y
Rx interrupt         = Y
Flow control         = Y
RSS key update       = Y
RSS reta update      = Y
VLAN filter          = Y
VLAN offload         = Y
Linux                = Y
x86-64               = Y

[rte_flow items]
eth                  = P
ipv4                 = Y
ipv6                 = Y
tcp                  = Y
udp                  = Y

[rte_flow actions]
queue                = Y
rss                  = Y
