;
; Supported features of the 'hns3' network poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Speed capabilities   = Y
Link speed configuration = Y
Link status          = Y
Link status event    = Y
Rx interrupt         = Y
Queue start/stop     = Y
Runtime Rx queue setup = Y
Runtime Tx queue setup = Y
Burst mode info      = Y
Fast mbuf free       = Y
Free Tx mbuf on demand = Y
MTU update           = Y
Scattered Rx         = Y
TSO                  = Y
LRO                  = Y
Promiscuous mode     = Y
Allmulticast mode    = Y
Unicast MAC filter   = Y
Multicast MAC filter = Y
RSS hash             = Y
RSS key update       = Y
RSS reta update      = Y
DCB                  = Y
VLAN filter          = Y
Flow control         = Y
Traffic manager      = Y
CRC offload          = Y
VLAN offload         = Y
FEC                  = Y
L3 checksum offload  = Y
L4 checksum offload  = Y
Inner L3 checksum    = Y
Inner L4 checksum    = Y
Packet type parsing  = Y
Rx descriptor status = Y
Tx descriptor status = Y
Basic stats          = Y
Extended stats       = Y
Stats per queue      = Y
FW version           = Y
Registers dump       = Y
Module EEPROM dump   = Y
Timesync             = Y
Timestamp offload    = Y
Multiprocess aware   = Y
Linux                = Y
ARMv8                = Y

[rte_flow items]
eth                  = P
geneve               = Y
icmp                 = Y
ipv4                 = Y
ipv6                 = Y
nvgre                = Y
ptype                = P
sctp                 = Y
tcp                  = Y
udp                  = Y
vlan                 = Y
vxlan                = Y
vxlan_gpe            = Y

[rte_flow actions]
count                = Y
drop                 = Y
flag                 = Y
mark                 = Y
queue                = Y
rss                  = Y
