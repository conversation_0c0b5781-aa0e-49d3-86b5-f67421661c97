;
; Supported features of the 'ice_dcf' network poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
; A feature with "P" indicates only be supported when non-vector path
; is selected.
;
[Features]
Queue start/stop     = Y
Scattered Rx         = Y
RSS hash             = P
CRC offload          = Y
L3 checksum offload  = P
L4 checksum offload  = P
Inner L3 checksum    = P
Inner L4 checksum    = P
RSS reta update      = Y
RSS key update       = Y
MTU update           = Y
Promiscuous mode     = Y
Allmulticast mode    = Y
Unicast MAC filter   = Y
VLAN filter          = Y
Traffic manager      = Y
VLAN offload         = Y
Extended stats       = Y
Basic stats          = Y
Power mgmt address monitor = Y
Linux                = Y
x86-32               = Y
x86-64               = Y
