;
; Supported features of the 'mvpp2' network poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Speed capabilities   = Y
Link status          = Y
MTU update           = Y
Promiscuous mode     = Y
Allmulticast mode    = Y
Unicast MAC filter   = Y
Multicast MAC filter = Y
RSS hash             = Y
VLAN filter          = Y
Flow control         = Y
Traffic manager      = Y
CRC offload          = Y
L3 checksum offload  = Y
L4 checksum offload  = Y
Packet type parsing  = Y
Basic stats          = Y
Extended stats       = Y
ARMv8                = Y
Usage doc            = Y

[rte_flow items]
eth                  = P
ipv4                 = Y
ipv6                 = Y
raw                  = Y
tcp                  = Y
udp                  = Y
vlan                 = P

[rte_flow actions]
drop                 = Y
meter                = Y
queue                = Y
