;
; Supported features of the 'idpf' network poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
; A feature with "P" indicates only be supported when non-vector path
; is selected.
;
[Features]
Speed capabilities   = Y
Link status          = Y
Queue start/stop     = Y
MTU update           = Y
Scattered Rx         = P
TSO                  = P
RSS hash             = Y
RSS key update       = Y
RSS reta update      = Y
L3 checksum offload  = P
L4 checksum offload  = P
Timestamp offload    = P
Packet type parsing  = Y
Basic stats          = Y
Linux                = Y
x86-32               = Y
x86-64               = Y
