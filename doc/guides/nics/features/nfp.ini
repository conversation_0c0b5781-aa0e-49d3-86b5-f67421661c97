;
; Supported features of the 'nfp' network poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Speed capabilities   = Y
Link status          = Y
Link status event    = Y
Rx interrupt         = Y
Queue start/stop     = Y
MTU update           = Y
Promiscuous mode     = Y
Allmulticast mode    = Y
TSO                  = Y
RSS hash             = Y
RSS key update       = Y
RSS reta update      = Y
Flow control         = Y
VLAN offload         = Y
QinQ offload         = Y
FEC                  = Y
L3 checksum offload  = Y
L4 checksum offload  = Y
Packet type parsing  = Y
Basic stats          = Y
Stats per queue      = Y
EEPROM dump          = Y
Module EEPROM dump   = Y
LED                  = Y
Linux                = Y
Multiprocess aware   = Y
x86-64               = Y
Usage doc            = Y

[rte_flow items]
conntrack            = Y
eth                  = Y
geneve               = Y
gre                  = Y
gre_key              = Y
ipv4                 = Y
ipv6                 = Y
port_id              = Y
sctp                 = Y
tcp                  = Y
udp                  = Y
vlan                 = Y
vxlan                = Y

[rte_flow actions]
conntrack            = Y
count                = Y
drop                 = Y
jump                 = Y
mark                 = Y
meter                = Y
modify_field         = Y
of_pop_vlan          = Y
of_push_vlan         = Y
of_set_vlan_pcp      = Y
of_set_vlan_vid      = Y
queue                = Y
raw_decap            = Y
raw_encap            = Y
represented_port     = Y
rss                  = Y
port_id              = Y
set_ipv4_dscp        = Y
set_ipv4_dst         = Y
set_ipv4_src         = Y
set_ipv6_dscp        = Y
set_ipv6_dst         = Y
set_ipv6_src         = Y
set_mac_dst          = Y
set_mac_src          = Y
set_tp_dst           = Y
set_tp_src           = Y
set_ttl              = Y
vxlan_decap          = Y
vxlan_encap          = Y
