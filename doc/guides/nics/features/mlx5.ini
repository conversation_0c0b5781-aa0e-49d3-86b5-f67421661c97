;
; Supported features of the 'mlx5' network poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Speed capabilities   = Y
Link status          = Y
Link status event    = Y
Removal event        = Y
Rx interrupt         = Y
Fast mbuf free       = Y
Queue start/stop     = Y
Shared Rx queue      = Y
Burst mode info      = Y
Power mgmt address monitor = Y
MTU update           = Y
Buffer split on Rx   = Y
Scattered Rx         = Y
LRO                  = Y
TSO                  = Y
Promiscuous mode     = Y
Allmulticast mode    = Y
Unicast MAC filter   = Y
Multicast MAC filter = Y
RSS hash             = Y
RSS key update       = Y
RSS reta update      = Y
Inner RSS            = Y
SR-IOV               = Y
VLAN filter          = Y
Flow control         = Y
CRC offload          = Y
VLAN offload         = Y
L3 checksum offload  = Y
L4 checksum offload  = Y
Timestamp offload    = Y
Inner L3 checksum    = Y
Inner L4 checksum    = Y
Packet type parsing  = Y
Rx descriptor status = Y
Tx descriptor status = Y
Basic stats          = Y
Extended stats       = Y
Stats per queue      = Y
FW version           = Y
Module EEPROM dump   = Y
Multiprocess aware   = Y
Linux                = Y
Windows              = P
ARMv8                = Y
Power8               = Y
x86-32               = Y
x86-64               = Y
Usage doc            = Y

[rte_flow items]
aggr_affinity        = Y
compare              = Y
conntrack            = Y
ecpri                = Y
esp                  = Y
eth                  = Y
flex                 = Y
geneve               = Y
geneve_opt           = Y
gre                  = Y
gre_key              = Y
gre_option           = Y
gtp                  = Y
gtp_psc              = Y
ib_bth               = Y
icmp                 = Y
icmp6                = Y
icmp6_echo_request   = Y
icmp6_echo_reply     = Y
integrity            = Y
ipv4                 = Y
ipv6                 = Y
ipv6_frag_ext        = Y
ipv6_routing_ext     = Y
mark                 = Y
meta                 = Y
meter_color          = Y
mpls                 = Y
nsh                  = Y
nvgre                = Y
port_id              = Y
port_representor     = Y
ptype                = Y
quota                = Y
random               = Y
tag                  = Y
tcp                  = Y
tx_queue             = Y
udp                  = Y
vlan                 = Y
vxlan                = Y
vxlan_gpe            = Y
represented_port     = Y

[rte_flow actions]
age                  = I
conntrack            = I
count                = I
dec_tcp_ack          = Y
dec_tcp_seq          = Y
dec_ttl              = Y
drop                 = Y
flag                 = Y
inc_tcp_ack          = Y
inc_tcp_seq          = Y
indirect_list        = Y
ipv6_ext_push        = Y
ipv6_ext_remove      = Y
jump                 = Y
jump_to_table_index  = Y
mark                 = Y
meter                = Y
meter_mark           = Y
modify_field         = Y
nat64                = Y
nvgre_decap          = Y
nvgre_encap          = Y
of_pop_vlan          = Y
of_push_vlan         = Y
of_set_vlan_pcp      = Y
of_set_vlan_vid      = Y
port_id              = Y
port_representor     = Y
quota                = I
queue                = Y
raw_decap            = Y
raw_encap            = Y
represented_port     = Y
rss                  = I
sample               = Y
send_to_kernel       = Y
set_ipv4_dscp        = Y
set_ipv4_dst         = Y
set_ipv4_src         = Y
set_ipv6_dscp        = Y
set_ipv6_dst         = Y
set_ipv6_src         = Y
set_mac_dst          = Y
set_mac_src          = Y
set_meta             = Y
set_tag              = Y
set_tp_dst           = Y
set_tp_src           = Y
set_ttl              = Y
vxlan_decap          = Y
vxlan_encap          = Y
