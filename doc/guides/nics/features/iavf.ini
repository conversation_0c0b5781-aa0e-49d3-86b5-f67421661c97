;
; Supported features of the 'iavf' network poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
; A feature with "P" indicates only be supported when non-vector path
; is selected.
;
[Features]
Speed capabilities   = Y
Link status          = Y
Rx interrupt         = Y
Queue start/stop     = Y
Runtime Rx queue setup = Y
Runtime Tx queue setup = Y
Power mgmt address monitor = Y
MTU update           = Y
Scattered Rx         = Y
TSO                  = Y
Promiscuous mode     = Y
Allmulticast mode    = Y
Unicast MAC filter   = Y
Multicast MAC filter = Y
RSS hash             = Y
RSS key update       = Y
RSS reta update      = Y
VLAN filter          = Y
Traffic manager      = Y
Inline crypto        = Y
CRC offload          = Y
VLAN offload         = P
L3 checksum offload  = Y
L4 checksum offload  = Y
Timestamp offload    = Y
Inner L3 checksum    = Y
Inner L4 checksum    = P
Packet type parsing  = Y
Rx descriptor status = Y
Tx descriptor status = Y
Basic stats          = Y
Multiprocess aware   = Y
FreeBSD              = Y
Linux                = Y
x86-32               = Y
x86-64               = Y

[rte_flow items]
ah                   = Y
arp_eth_ipv4         = Y
ecpri                = Y
esp                  = Y
eth                  = P
gre                  = Y
gtpc                 = Y
gtpu                 = Y
gtp_psc              = Y
icmp                 = Y
icmp6                = Y
ipv4                 = Y
ipv6                 = Y
ipv6_frag_ext        = Y
l2tpv2               = Y
l2tpv3oip            = Y
pfcp                 = Y
ppp                  = Y
raw                  = Y
sctp                 = Y
tcp                  = Y
udp                  = Y
vlan                 = P

[rte_flow actions]
count                = Y
drop                 = Y
mark                 = Y
passthru             = Y
port_representor     = Y
queue                = Y
rss                  = Y
security             = Y
