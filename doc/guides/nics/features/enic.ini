;
; Supported features of the 'enic' network poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Speed capabilities   = Y
Link status          = Y
Link status event    = Y
Rx interrupt         = Y
Queue start/stop     = Y
Burst mode info      = Y
MTU update           = Y
Scattered Rx         = Y
TSO                  = Y
Promiscuous mode     = Y
Allmulticast mode    = Y
Unicast MAC filter   = Y
Multicast MAC filter = Y
RSS hash             = Y
RSS key update       = Y
RSS reta update      = Y
Inner RSS            = Y
SR-IOV               = Y
CRC offload          = Y
VLAN offload         = Y
L3 checksum offload  = Y
L4 checksum offload  = Y
Inner L3 checksum    = Y
Inner L4 checksum    = Y
Packet type parsing  = Y
Basic stats          = Y
FW version           = Y
Multiprocess aware   = Y
FreeBSD              = Y
Linux                = Y
x86-32               = Y
x86-64               = Y
Usage doc            = Y

[rte_flow items]
ecpri                = Y
eth                  = P
geneve               = Y
geneve_opt           = Y
gtp                  = Y
gtpc                 = Y
gtpu                 = Y
ipv4                 = Y
ipv6                 = Y
raw                  = Y
sctp                 = Y
tcp                  = Y
udp                  = Y
vlan                 = P
vxlan                = Y

[rte_flow actions]
count                = Y
drop                 = Y
flag                 = Y
jump                 = Y
mark                 = Y
of_pop_vlan          = Y
of_push_vlan         = Y
of_set_vlan_pcp      = Y
of_set_vlan_vid      = Y
passthru             = Y
port_id              = Y
port_representor     = Y
represented_port     = Y
queue                = Y
rss                  = Y
vxlan_decap          = Y
vxlan_encap          = Y
