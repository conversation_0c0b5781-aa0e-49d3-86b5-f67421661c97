;
; Supported features of the 'mlx4' network poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
[Features]
Speed capabilities   = P
Link status          = Y
Link status event    = Y
Removal event        = Y
Rx interrupt         = Y
Queue start/stop     = Y
MTU update           = Y
Scattered Rx         = Y
TSO                  = Y
Promiscuous mode     = Y
Allmulticast mode    = Y
Unicast MAC filter   = Y
Multicast MAC filter = Y
RSS hash             = Y
SR-IOV               = Y
VLAN filter          = Y
Flow control         = Y
CRC offload          = Y
L3 checksum offload  = Y
L4 checksum offload  = Y
Inner L3 checksum    = Y
Inner L4 checksum    = Y
Packet type parsing  = Y
Basic stats          = Y
Stats per queue      = Y
FW version           = Y
Multiprocess aware   = Y
Linux                = Y
Power8               = Y
x86-32               = Y
x86-64               = Y
Usage doc            = Y

[rte_flow items]
eth                  = P
ipv4                 = Y
tcp                  = Y
udp                  = Y
vlan                 = P

[rte_flow actions]
drop                 = Y
queue                = Y
rss                  = Y
