;
; Supported features of the 'ice' network poll mode driver.
;
; Refer to default.ini for the full list of available PMD features.
;
; A feature with "P" indicates only be supported when non-vector path
; is selected.
;
[Features]
Speed capabilities   = Y
Link speed configuration = Y
Link status          = Y
Link status event    = Y
FEC                  = Y
Rx interrupt         = Y
Fast mbuf free       = P
Queue start/stop     = Y
Burst mode info      = Y
Power mgmt address monitor = Y
MTU update           = Y
Buffer split on Rx   = P
Scattered Rx         = Y
TSO                  = Y
Promiscuous mode     = Y
Allmulticast mode    = Y
Unicast MAC filter   = Y
RSS hash             = Y
RSS key update       = Y
RSS reta update      = Y
VLAN filter          = Y
Traffic manager      = Y
CRC offload          = Y
VLAN offload         = Y
QinQ offload         = P
L3 checksum offload  = P
L4 checksum offload  = P
Timestamp offload    = P
Inner L3 checksum    = P
Inner L4 checksum    = P
Packet type parsing  = Y
Timesync             = Y
Rx descriptor status = Y
Tx descriptor status = Y
Basic stats          = Y
Extended stats       = Y
FW version           = Y
Module EEPROM dump   = Y
LED                  = Y
Multiprocess aware   = Y
FreeBSD              = Y
Linux                = Y
Windows              = Y
x86-32               = Y
x86-64               = Y

[rte_flow items]
ah                   = Y
any                  = Y
arp_eth_ipv4         = Y
esp                  = Y
eth                  = P
gtpu                 = Y
gtp_psc              = Y
icmp                 = Y
icmp6                = Y
ipv4                 = Y
ipv6                 = Y
ipv6_frag_ext        = Y
l2tpv3oip            = Y
nvgre                = Y
pfcp                 = Y
pppoed               = Y
pppoes               = Y
pppoe_proto_id       = Y
raw                  = Y
sctp                 = Y
tcp                  = Y
udp                  = Y
vlan                 = P
vxlan                = Y

[rte_flow actions]
count                = Y
drop                 = Y
mark                 = Y
passthru             = Y
port_representor     = Y
queue                = Y
represented_port     = Y
rss                  = Y
