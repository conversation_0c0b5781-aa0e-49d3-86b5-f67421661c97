@startuml CXI_PMD_Architecture
!theme plain
title CXI PMD Architecture - Complete Call Flow

' Define participants
participant "DPDK App" as APP
participant "DPDK Framework" as DPDK
participant "CXI PMD" as PMD
participant "libc<PERSON>" as LIBCXI
participant "Kernel CXI Driver" as KERNEL
participant "CXI Hardware" as HW

== Device Initialization ==

APP -> DPDK: rte_eth_dev_configure()
DPDK -> PMD: cxi_dev_configure()
PMD -> PMD: Validate configuration

APP -> DPDK: rte_eth_tx_queue_setup()
DPDK -> PMD: cxi_tx_queue_setup()
PMD -> PMD: cxi_hw_cq_alloc()
PMD -> LIBCXI: cxil_alloc_cmdq()
LIBCXI -> KERNEL: device_write(CXI_OP_CQ_ALLOC)
KERNEL -> KERNEL: cxi_cq_alloc()
KERNEL -> HW: Configure Command Queue
KERNEL --> LIBCXI: Return CQ handle
LIBCXI -> LIBCXI: mmap(CQ memory)
LIBCXI --> PMD: CQ allocated

PMD -> PMD: cxi_hw_eq_alloc()
PMD -> LIBCXI: cxil_alloc_evtq()
LIBCXI -> KERNEL: device_write(CXI_OP_EQ_ALLOC)
KERNEL -> KERNEL: cxi_eq_alloc()
KERNEL -> HW: Configure Event Queue
KERNEL --> LIBCXI: Return EQ handle
LIBCXI -> LIBCXI: mmap(EQ memory)
LIBCXI --> PMD: EQ allocated

APP -> DPDK: rte_eth_dev_start()
DPDK -> PMD: cxi_dev_start()
PMD -> PMD: cxi_hw_init_device()
PMD -> LIBCXI: cxil_init_eth_device()
LIBCXI -> KERNEL: device_write(CXI_OP_ETH_INIT)
KERNEL -> KERNEL: cxi_eth_init()
KERNEL -> KERNEL: Setup ethernet netdev
KERNEL -> HW: Initialize MAC/PHY
KERNEL --> LIBCXI: Success
LIBCXI --> PMD: Device ready

== Packet Transmission Flow ==

APP -> DPDK: rte_eth_tx_burst(pkts)
DPDK -> PMD: cxi_xmit_pkts(pkts)

loop For each packet
    PMD -> PMD: cxi_hw_tx_process_events()
    PMD -> LIBCXI: cxi_eq_get_event()
    LIBCXI -> LIBCXI: Read from mmap'd EQ
    LIBCXI --> PMD: Completion events
    PMD -> PMD: rte_atomic32_inc(tx_credits)
    
    PMD -> PMD: Check tx_credits
    alt Credits available
        PMD -> PMD: Packet size check
        alt Small packet (< IDC_MAX)
            PMD -> PMD: cxi_hw_tx_idc()
            PMD -> LIBCXI: cxi_cq_emit_idc_eth()
            LIBCXI -> LIBCXI: Write IDC command to mmap'd CQ
            LIBCXI -> HW: MMIO doorbell write
            HW -> HW: Process IDC command
            HW -> HW: Copy packet data inline
        else Large packet
            PMD -> PMD: cxi_hw_tx_dma()
            PMD -> LIBCXI: cxi_cq_emit_dma_eth()
            LIBCXI -> LIBCXI: Write DMA command to mmap'd CQ
            LIBCXI -> HW: MMIO doorbell write
            HW -> HW: Process DMA command
            HW -> HW: DMA packet from memory
        end
        
        PMD -> PMD: rte_atomic32_dec(tx_credits)
        HW -> HW: Packet processing (checksum, etc.)
        HW -> HW: Transmit to network
        HW -> HW: Generate completion event
        HW -> LIBCXI: Write event to mmap'd EQ
    else No credits
        PMD -> PMD: Break transmission loop
    end
end

PMD -> LIBCXI: cxi_cq_ring()
LIBCXI -> HW: MMIO doorbell write
PMD --> DPDK: Number of packets transmitted
DPDK --> APP: tx_count

== Memory Management ==

note over LIBCXI, KERNEL
libcxi uses mmap() to provide direct hardware access:
- Command Queues: Direct write access for commands
- Event Queues: Direct read access for completions  
- CSRs: Direct access for configuration
- Doorbells: Direct MMIO write access
end note

note over HW
CXI Hardware Features:
- IDC: Immediate Data Copy (small packets)
- DMA: Scatter-gather DMA (large packets)
- Hardware checksum offload
- Event-driven completion model
- Zero-copy packet processing
end note

@enduml

@startuml CXI_PMD_Components
!theme plain
title CXI PMD Component Architecture

package "User Space" {
    package "DPDK Application" {
        [testpmd] as APP1
        [l3fwd] as APP2
        [Custom App] as APP3
    }

    package "DPDK Framework" {
        [Ethdev API] as ETHDEV
        [Memory Pool] as MEMPOOL
        [Ring Library] as RING
    }

    package "CXI PMD" {
        [Device Operations] as DEV_OPS
        [Queue Management] as QUEUE_MGR
        [Packet TX/RX] as PKT_PROC
        [Hardware Abstraction] as HW_ABS

        DEV_OPS ..> QUEUE_MGR
        QUEUE_MGR ..> PKT_PROC
        PKT_PROC ..> HW_ABS
    }

    package "libcxi Library" {
        [Device Management] as LIBCXI_DEV
        [Resource Allocation] as LIBCXI_RES
        [Command Interface] as LIBCXI_CMD
        [Memory Mapping] as LIBCXI_MEM

        LIBCXI_DEV ..> LIBCXI_RES
        LIBCXI_RES ..> LIBCXI_CMD
        LIBCXI_CMD ..> LIBCXI_MEM
    }
}

package "Kernel Space" {
    package "CXI Core Driver" {
        [Character Device] as CHAR_DEV
        [IOCTL Handler] as IOCTL
        [Memory Manager] as MEM_MGR
        [Resource Manager] as RES_MGR
    }

    package "CXI Ethernet Driver" {
        [Network Device] as NETDEV
        [Queue Management] as K_QUEUE
        [Interrupt Handler] as IRQ
    }
}

package "Hardware" {
    package "CXI NIC" {
        [Command Queues] as HW_CQ
        [Event Queues] as HW_EQ
        [IDC Engine] as HW_IDC
        [DMA Engine] as HW_DMA
        [MAC/PHY] as HW_MAC
        [CSRs] as HW_CSR
    }
}

' Connections
APP1 --> ETHDEV
APP2 --> ETHDEV
APP3 --> ETHDEV

ETHDEV --> DEV_OPS
ETHDEV --> PKT_PROC

HW_ABS --> LIBCXI_DEV
HW_ABS --> LIBCXI_RES
HW_ABS --> LIBCXI_CMD

LIBCXI_MEM --> CHAR_DEV : mmap()
LIBCXI_CMD --> CHAR_DEV : ioctl()

CHAR_DEV --> IOCTL
IOCTL --> RES_MGR
RES_MGR --> K_QUEUE
K_QUEUE --> NETDEV

MEM_MGR --> HW_CQ : DMA mapping
MEM_MGR --> HW_EQ : DMA mapping

LIBCXI_MEM -.-> HW_CQ : Direct access
LIBCXI_MEM -.-> HW_EQ : Direct access
LIBCXI_MEM -.-> HW_CSR : Direct access

HW_IDC --> HW_MAC
HW_DMA --> HW_MAC

note right of LIBCXI_MEM
Direct hardware access via mmap:
- Zero-copy packet processing
- Low-latency command submission
- Direct event queue access
end note

note bottom of HW_CQ
Hardware Command Processing:
- IDC: Inline data copy
- DMA: Scatter-gather lists
- Doorbell-triggered execution
end note

@enduml

@startuml CXI_PMD_DataFlow
!theme plain
title CXI PMD Data Flow - Packet Transmission

skinparam backgroundColor #FFFFFF
skinparam componentStyle rectangle

rectangle "Application Layer" as APP_LAYER {
    component [DPDK Application] as APP
    component [rte_mbuf] as MBUF
}

rectangle "DPDK Framework" as DPDK_LAYER {
    component [Ethdev API] as ETHDEV
    component [Memory Pool] as MEMPOOL
}

rectangle "CXI PMD Layer" as PMD_LAYER {
    component [cxi_xmit_pkts] as TX_FUNC
    component [Credit Manager] as CREDIT
    component [IDC/DMA Decision] as DECISION
    component [Command Builder] as CMD_BUILD
}

rectangle "libcxi Layer" as LIBCXI_LAYER {
    component [cxi_cq_emit_*] as EMIT
    component [Memory Mapping] as MMAP
    component [Doorbell] as DOORBELL
}

rectangle "Kernel Layer" as KERNEL_LAYER {
    component [Character Device] as CDEV
    component [DMA Manager] as DMA_MGR
    component [Interrupt Handler] as IRQ_HANDLER
}

rectangle "Hardware Layer" as HW_LAYER {
    component [Command Queue] as CQ
    component [IDC Engine] as IDC
    component [DMA Engine] as DMA
    component [Event Queue] as EQ
    component [MAC/PHY] as MAC
}

' Data flow for packet transmission
APP -> MBUF : Allocate packet
MBUF -> ETHDEV : rte_eth_tx_burst()
ETHDEV -> TX_FUNC : Call PMD

TX_FUNC -> CREDIT : Check credits
CREDIT -> TX_FUNC : Credits available

TX_FUNC -> DECISION : Packet size check
DECISION -> CMD_BUILD : IDC or DMA path

CMD_BUILD -> EMIT : Build command
EMIT -> MMAP : Write to mapped CQ
MMAP -> CQ : Direct memory write

EMIT -> DOORBELL : Ring doorbell
DOORBELL -> CQ : MMIO trigger

CQ -> IDC : Small packets
CQ -> DMA : Large packets

IDC -> MAC : Inline data
DMA -> MAC : DMA from memory

MAC -> EQ : Completion event
EQ -> MMAP : Event available
MMAP -> TX_FUNC : Process completion
TX_FUNC -> CREDIT : Return credit

note right of MMAP
Zero-copy design:
- Direct hardware access
- No kernel involvement
- Minimal CPU overhead
end note

note bottom of CQ
Command formats:
- c_idc_eth_cmd: Inline data
- c_dma_eth_cmd: Scatter-gather
- Hardware-specific layouts
end note

@enduml
