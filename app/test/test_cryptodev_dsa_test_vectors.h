/* SPDX-License-Identifier: BSD-3-Clause
 * Copyright(c) 2018 Cavium Networks
 */

#ifndef TEST_CRYPTODEV_DSA_TEST_VECTORS_H_
#define TEST_CRYPTODEV_DSA_TEST_VECTORS_H_

#include "rte_crypto_asym.h"

#define TEST_DATA_SIZE 4096


struct dsa_test_param {
	rte_crypto_param y;
};

static unsigned char dsa_x[] = {
	0xc5, 0x3e, 0xae, 0x6d, 0x45, 0x32, 0x31, 0x64,
	0xc7, 0xd0, 0x7a, 0xf5, 0x71, 0x57, 0x03, 0x74,
	0x4a, 0x63, 0xfc, 0x3a
};

uint8_t dsa_y[] = {
	0x31, 0x3f, 0xd9, 0xeb, 0xca, 0x91, 0x57, 0x4e,
	0x1c, 0x2e, 0xeb, 0xe1, 0x51, 0x7c, 0x57, 0xe0,
	0xc2, 0x1b, 0x02, 0x09, 0x87, 0x21, 0x40, 0xc5,
	0x32, 0x87, 0x61, 0xbb, 0xb2, 0x45, 0x0b, 0x33,
	0xf1, 0xb1, 0x8b, 0x40, 0x9c, 0xe9, 0xab, 0x7c,
	0x4c, 0xd8, 0xfd, 0xa3, 0x39, 0x1e, 0x8e, 0x34,
	0x86, 0x83, 0x57, 0xc1, 0x99, 0xe1, 0x6a, 0x6b,
	0x2e, 0xba, 0x06, 0xd6, 0x74, 0x9d, 0xef, 0x79,
	0x1d, 0x79, 0xe9, 0x5d, 0x3a, 0x4d, 0x09, 0xb2,
	0x4c, 0x39, 0x2a, 0xd8, 0x9d, 0xbf, 0x10, 0x09,
	0x95, 0xae, 0x19, 0xc0, 0x10, 0x62, 0x05, 0x6b,
	0xb1, 0x4b, 0xce, 0x00, 0x5e, 0x87, 0x31, 0xef,
	0xde, 0x17, 0x5f, 0x95, 0xb9, 0x75, 0x08, 0x9b,
	0xdc, 0xda, 0xea, 0x56, 0x2b, 0x32, 0x78, 0x6d,
	0x96, 0xf5, 0xa3, 0x1a, 0xed, 0xf7, 0x53, 0x64,
	0x00, 0x8a, 0xd4, 0xff, 0xfe, 0xbb, 0x97, 0x0b
};

static unsigned char dsa_p[] = {
	0xa8,  0xf9,  0xcd,  0x20,  0x1e,  0x5e,  0x35,  0xd8,
	0x92,  0xf8,  0x5f,  0x80,  0xe4,  0xdb,  0x25,  0x99,
	0xa5,  0x67,  0x6a,  0x3b,  0x1d,  0x4f,  0x19,  0x03,
	0x30,  0xed,  0x32,  0x56,  0xb2,  0x6d,  0x0e,  0x80,
	0xa0,  0xe4,  0x9a,  0x8f,  0xff,  0xaa,  0xad,  0x2a,
	0x24,  0xf4,  0x72,  0xd2,  0x57,  0x32,  0x41,  0xd4,
	0xd6,  0xd6,  0xc7,  0x48,  0x0c,  0x80,  0xb4,  0xc6,
	0x7b,  0xb4,  0x47,  0x9c,  0x15,  0xad,  0xa7,  0xea,
	0x84,  0x24,  0xd2,  0x50,  0x2f,  0xa0,  0x14,  0x72,
	0xe7,  0x60,  0x24,  0x17,  0x13,  0xda,  0xb0,  0x25,
	0xae,  0x1b,  0x02,  0xe1,  0x70,  0x3a,  0x14,  0x35,
	0xf6,  0x2d,  0xdf,  0x4e,  0xe4,  0xc1,  0xb6,  0x64,
	0x06,  0x6e,  0xb2,  0x2f,  0x2e,  0x3b,  0xf2,  0x8b,
	0xb7,  0x0a,  0x2a,  0x76,  0xe4,  0xfd,  0x5e,  0xbe,
	0x2d,  0x12,  0x29,  0x68,  0x1b,  0x5b,  0x06,  0x43,
	0x9a,  0xc9,  0xc7,  0xe9,  0xd8,  0xbd,  0xe2,  0x83
};

static unsigned char dsa_q[] = {
	0xf8,  0x5f,  0x0f,  0x83,  0xac,  0x4d,  0xf7,  0xea,
	0x0c,  0xdf,  0x8f,  0x46,  0x9b,  0xfe,  0xea,  0xea,
	0x14,  0x15,  0x64,  0x95
};

static unsigned char dsa_g[] = {
	0x2b,  0x31,  0x52,  0xff,  0x6c,  0x62,  0xf1,  0x46,
	0x22,  0xb8,  0xf4,  0x8e,  0x59,  0xf8,  0xaf,  0x46,
	0x88,  0x3b,  0x38,  0xe7,  0x9b,  0x8c,  0x74,  0xde,
	0xea,  0xe9,  0xdf,  0x13,  0x1f,  0x8b,  0x85,  0x6e,
	0x3a,  0xd6,  0xc8,  0x45,  0x5d,  0xab,  0x87,  0xcc,
	0x0d,  0xa8,  0xac,  0x97,  0x34,  0x17,  0xce,  0x4f,
	0x78,  0x78,  0x55,  0x7d,  0x6c,  0xdf,  0x40,  0xb3,
	0x5b,  0x4a,  0x0c,  0xa3,  0xeb,  0x31,  0x0c,  0x6a,
	0x95,  0xd6,  0x8c,  0xe2,  0x84,  0xad,  0x4e,  0x25,
	0xea,  0x28,  0x59,  0x16,  0x11,  0xee,  0x08,  0xb8,
	0x44,  0x4b,  0xd6,  0x4b,  0x25,  0xf3,  0xf7,  0xc5,
	0x72,  0x41,  0x0d,  0xdf,  0xb3,  0x9c,  0xc7,  0x28,
	0xb9,  0xc9,  0x36,  0xf8,  0x5f,  0x41,  0x91,  0x29,
	0x86,  0x99,  0x29,  0xcd,  0xb9,  0x09,  0xa6,  0xa3,
	0xa9,  0x9b,  0xbe,  0x08,  0x92,  0x16,  0x36,  0x81,
	0x71,  0xbd,  0x0b,  0xa8,  0x1d,  0xe4,  0xfe,  0x33
};

struct dsa_test_param dsa_test_params = {
	.y = {
		.data = dsa_y,
		.length = sizeof(dsa_y)
	}
};

struct rte_crypto_asym_xform dsa_xform = {
	.next = NULL,
	.xform_type = RTE_CRYPTO_ASYM_XFORM_DSA,
	.dsa = {
		.p = {
			.data = dsa_p,
			.length = sizeof(dsa_p)
		},
		.q = {
			.data = dsa_q,
			.length = sizeof(dsa_q)
		},
		.g = {
			.data = dsa_g,
			.length = sizeof(dsa_g)
		},
		.x = {
			.data = dsa_x,
			.length = sizeof(dsa_x)
		}

	}
};

#endif /* TEST_CRYPTODEV_DSA_TEST_VECTORS_H__ */
