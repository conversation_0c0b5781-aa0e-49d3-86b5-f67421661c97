/* SPDX-License-Identifier: BSD-3-Clause
 * Copyright(c) 2015-2017 Intel Corporation
 */

#ifndef TEST_CRYPTODEV_AEAD_TEST_VECTORS_H_
#define TEST_CRYPTODEV_AEAD_TEST_VECTORS_H_

#include "test_cryptodev.h"

#define GMAC_LARGE_PLAINTEXT_LENGTH		65344
#define MAX_AAD_LENGTH				(65535 - RTE_PKTMBUF_HEADROOM)
#define GCM_LARGE_AAD_LENGTH			65296
#define AEAD_TEXT_MAX_LENGTH			8096

static_assert(GMAC_LARGE_PLAINTEXT_LENGTH <= LARGE_MBUF_DATAPAYLOAD_SIZE,
	      "GMAC_LARGE_PLAINTEXT_LENGTH should not be greater than LARGE_MBUF_DATAPAYLOAD_SIZE");
static_assert(MAX_AAD_LENGTH <= LARGE_MBUF_DATAPAYLOAD_SIZE,
	      "MAX_AAD_LENGTH should not be greater than LARGE_MBUF_DATAPAYLOAD_SIZE");
static_assert(GCM_LARGE_AAD_LENGTH <= LARGE_MBUF_DATAPAYLOAD_SIZE,
	      "GCM_LARGE_AAD_LENGTH should not be greater than LARGE_MBUF_DATAPAYLOAD_SIZE");
static_assert(AEAD_TEXT_MAX_LENGTH <= LARGE_MBUF_DATAPAYLOAD_SIZE,
	      "AEAD_TEXT_MAX_LENGTH should not be greater than LARGE_MBUF_DATAPAYLOAD_SIZE");


static uint8_t gcm_aad_zero_text[MAX_AAD_LENGTH] = { 0 };

static uint8_t gcm_aad_text[MAX_AAD_LENGTH] = {
		0xfe, 0xed, 0xfa, 0xce, 0xde, 0xad, 0xbe, 0xef,
		0xfe, 0xed, 0xfa, 0xce, 0xde, 0xad, 0xbe, 0xef,
		0x00, 0xf1, 0xe2, 0xd3, 0xc4, 0xb5, 0xa6, 0x97,
		0x88, 0x79, 0x6a, 0x5b, 0x4c, 0x3d, 0x2e, 0x1f };

static uint8_t gcm_aad_64B_text[MAX_AAD_LENGTH] = {
		0xED, 0x3E, 0xA8, 0x1F, 0x74, 0xE5, 0xD1, 0x96,
		0xA4, 0xD5, 0x4B, 0x26, 0xBB, 0x20, 0x61, 0x7B,
		0x3B, 0x9C, 0x2A, 0x69, 0x90, 0xEF, 0xD7, 0x9A,
		0x94, 0xC2, 0xF5, 0x86, 0xBD, 0x00, 0xF6, 0xEA,
		0x0B, 0x14, 0x24, 0xF2, 0x08, 0x67, 0x42, 0x3A,
		0xB5, 0xB8, 0x32, 0x97, 0xB5, 0x99, 0x69, 0x75,
		0x60, 0x00, 0x8F, 0xF7, 0x6F, 0x16, 0x52, 0x66,
		0xF1, 0xA9, 0x38, 0xFD, 0xB0, 0x61, 0x60, 0xB5 };

static uint8_t ccm_aad_test_1[8] = {
		0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07
};

static uint8_t ccm_aad_test_2[22] = {
		0x08, 0x40, 0x0F, 0xD2, 0xE1, 0x28, 0xA5, 0x7C,
		0x50, 0x30, 0xF1, 0x84, 0x44, 0x08, 0xAB, 0xAE,
		0xA5, 0xB8, 0xFC, 0xBA, 0x00, 0x00
};

static uint8_t sm4_gcm_aad_test_2[] = {
		0x3f, 0x89, 0x42, 0x20
};

static uint8_t sm4_gcm_aad_test_3[] = {
		0x36, 0x94, 0xf6, 0x7b, 0x8a, 0x58, 0x4d, 0xed
};

static uint8_t sm4_gcm_aad_test_4[] = {
		0xd5, 0x66, 0x06, 0x8f, 0xbc, 0x11, 0xb8
};

static uint8_t sm4_gcm_aad_test_6[] = {
		0x1b, 0xcd
};

static uint8_t sm4_gcm_aad_test_7[] = {
		0x6c, 0xac, 0xc4
};

static uint8_t sm4_gcm_aad_test_8[] = {
		0x76, 0xb3, 0xad, 0x45, 0x78
};

static uint8_t sm4_gcm_aad_test_9[] = {
		0x29
};

static uint8_t sm4_gcm_aad_test_11[] = {
		0xfe, 0xed, 0xfa, 0xce, 0xde, 0xad, 0xbe, 0xef,
		0xfe, 0xed, 0xfa, 0xce, 0xde, 0xad, 0xbe, 0xef,
		0xab, 0xad, 0xda, 0xd2
};

static uint8_t sm4_gcm_aad_test_14[] = {
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00
};

static uint8_t sm4_gcm_aad_test_15[] = {
		0xc9, 0x9a, 0x66, 0x32, 0x0d, 0xb7, 0x31, 0x58,
		0xa3, 0x5a, 0x25, 0x5d, 0x05, 0x17, 0x58, 0xe9,
		0x5e, 0xd4, 0xab, 0xb2, 0xcd, 0xc6, 0x9b, 0xb4,
		0x54, 0x11, 0x0e, 0x82, 0x74, 0x41, 0x21, 0x3d,
		0xdc, 0x87, 0x70, 0xe9, 0x3e, 0xa1, 0x41,
};

struct aead_test_data {
	enum rte_crypto_aead_algorithm algo;

	struct {
		uint8_t data[64];
		unsigned len;
	} key;

	struct {
		alignas(16) uint8_t data[64];
		unsigned len;
	} iv;

	struct {
		uint8_t *data;
		unsigned len;
	} aad;

	struct {
		uint8_t data[AEAD_TEXT_MAX_LENGTH];
		unsigned len;
	} plaintext;

	struct {
		uint8_t data[AEAD_TEXT_MAX_LENGTH];
		unsigned len;
	} ciphertext;

	struct {
		uint8_t data[16];
		unsigned len;
	} auth_tag;
};

struct gmac_test_data {
	struct {
		uint8_t data[64];
		unsigned len;
	} key;

	struct {
		alignas(16) uint8_t data[64];
		unsigned len;
	} iv;

	struct {
		uint8_t *data;
		unsigned len;
	} plaintext;

	struct {
		uint8_t data[16];
		unsigned len;
	} gmac_tag;

};

/** AES-GCM-128 Test Vectors */
static const struct aead_test_data gcm_test_case_1 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 },
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00 },
		.len = 12
	},
	.aad = {
		.data = gcm_aad_zero_text,
		.len = 0
	},
	.plaintext = {
		.data = {
			0x00 },
		.len = 0
	},
	.ciphertext = {
		.data = {
			0x00
		},
		.len = 0
	},
	.auth_tag = {
		.data = {
			0x58, 0xe2, 0xfc, 0xce, 0xfa, 0x7e, 0x30, 0x61,
			0x36, 0x7f, 0x1d, 0x57, 0xa4, 0xe7, 0x45, 0x5a },
		.len = 16
	}
};

static const struct aead_test_data gcm_test_case_2 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 },
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00 },
		.len = 12
	},
	.aad = {
		.data = gcm_aad_zero_text,
		.len = 0
	},
	.plaintext = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 },
		.len = 16
	},
	.ciphertext = {
		.data = {
			0x03, 0x88, 0xda, 0xce, 0x60, 0xb6, 0xa3, 0x92,
			0xf3, 0x28, 0xc2, 0xb9, 0x71, 0xb2, 0xfe, 0x78 },
		.len = 16
	},
	.auth_tag = {
		.data = {
			0xab, 0x6e, 0x47, 0xd4, 0x2c, 0xec, 0x13, 0xbd,
			0xf5, 0x3a, 0x67, 0xb2, 0x12, 0x57, 0xbd, 0xdf },
		.len = 16
	}
};

static const struct aead_test_data gcm_test_case_3 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xfe, 0xff, 0xe9, 0x92, 0x86, 0x65, 0x73, 0x1c,
			0x6d, 0x6a, 0x8f, 0x94, 0x67, 0x30, 0x83, 0x08 },
		.len = 16
	},
	.iv = {
		.data = {
			0xca, 0xfe, 0xba, 0xbe, 0xfa, 0xce, 0xdb, 0xad,
			0xde, 0xca, 0xf8, 0x88 },
		.len = 12
	},
	.aad = {
		.data = gcm_aad_zero_text,
		.len = 0
	},
	.plaintext = {
		.data = {
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55 },
		.len = 64
	},
	.ciphertext = {
		.data = {
			0x42, 0x83, 0x1e, 0xc2, 0x21, 0x77, 0x74, 0x24,
			0x4b, 0x72, 0x21, 0xb7, 0x84, 0xd0, 0xd4, 0x9c,
			0xe3, 0xaa, 0x21, 0x2f, 0x2c, 0x02, 0xa4, 0xe0,
			0x35, 0xc1, 0x7e, 0x23, 0x29, 0xac, 0xa1, 0x2e,
			0x21, 0xd5, 0x14, 0xb2, 0x54, 0x66, 0x93, 0x1c,
			0x7d, 0x8f, 0x6a, 0x5a, 0xac, 0x84, 0xaa, 0x05,
			0x1b, 0xa3, 0x0b, 0x39, 0x6a, 0x0a, 0xac, 0x97,
			0x3d, 0x58, 0xe0, 0x91, 0x47, 0x3f, 0x59, 0x85
		},
		.len = 64
	},
	.auth_tag = {
		.data = {
			0x4d, 0x5c, 0x2a, 0xf3, 0x27, 0xcd, 0x64, 0xa6,
			0x2c, 0xf3, 0x5a, 0xbd, 0x2b, 0xa6, 0xfa, 0xb4 },
		.len = 16
	}
};

static const struct aead_test_data gcm_test_case_4 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xfe, 0xff, 0xe9, 0x92, 0x86, 0x65, 0x73, 0x1c,
			0x6d, 0x6a, 0x8f, 0x94, 0x67, 0x30, 0x83, 0x08
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xca, 0xfe, 0xba, 0xbe, 0xfa, 0xce, 0xdb, 0xad,
			0xde, 0xca, 0xf8, 0x88 },
		.len = 12
	},
	.aad = {
		.data = gcm_aad_zero_text,
		.len = 8
	},
	.plaintext = {
		.data = {
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39
		},
		.len = 60
	},
	.ciphertext = {
		.data = {
			0x42, 0x83, 0x1e, 0xc2, 0x21, 0x77, 0x74, 0x24,
			0x4b, 0x72, 0x21, 0xb7, 0x84, 0xd0, 0xd4, 0x9c,
			0xe3, 0xaa, 0x21, 0x2f, 0x2c, 0x02, 0xa4, 0xe0,
			0x35, 0xc1, 0x7e, 0x23, 0x29, 0xac, 0xa1, 0x2e,
			0x21, 0xd5, 0x14, 0xb2, 0x54, 0x66, 0x93, 0x1c,
			0x7d, 0x8f, 0x6a, 0x5a, 0xac, 0x84, 0xaa, 0x05,
			0x1b, 0xa3, 0x0b, 0x39, 0x6a, 0x0a, 0xac, 0x97,
			0x3d, 0x58, 0xe0, 0x91
		},
		.len = 60
	},
	.auth_tag = {
		.data = {
			0xA2, 0xA4, 0x35, 0x75, 0xDC, 0xB0, 0x57, 0x74,
			0x07, 0x02, 0x30, 0xC2, 0xE7, 0x52, 0x02, 0x00
		},
		.len = 16
	}

};

static const struct aead_test_data gcm_test_case_5 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xfe, 0xff, 0xe9, 0x92, 0x86, 0x65, 0x73, 0x1c,
			0x6d, 0x6a, 0x8f, 0x94, 0x67, 0x30, 0x83, 0x08
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xca, 0xfe, 0xba, 0xbe, 0xfa, 0xce, 0xdb, 0xad,
			0xde, 0xca, 0xf8, 0x88 },
		.len = 12
	},
	.aad = {
		.data = gcm_aad_text,
		.len = 8
	},
	.plaintext = {
		.data = {
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39
		},
		.len = 60
	},
	.ciphertext = {
		.data = {
			0x42, 0x83, 0x1e, 0xc2, 0x21, 0x77, 0x74, 0x24,
			0x4b, 0x72, 0x21, 0xb7, 0x84, 0xd0, 0xd4, 0x9c,
			0xe3, 0xaa, 0x21, 0x2f, 0x2c, 0x02, 0xa4, 0xe0,
			0x35, 0xc1, 0x7e, 0x23, 0x29, 0xac, 0xa1, 0x2e,
			0x21, 0xd5, 0x14, 0xb2, 0x54, 0x66, 0x93, 0x1c,
			0x7d, 0x8f, 0x6a, 0x5a, 0xac, 0x84, 0xaa, 0x05,
			0x1b, 0xa3, 0x0b, 0x39, 0x6a, 0x0a, 0xac, 0x97,
			0x3d, 0x58, 0xe0, 0x91
		},
		.len = 60
	},
	.auth_tag = {
		.data = {
			0xC5, 0x2D, 0xFB, 0x54, 0xAF, 0xBB, 0x07, 0xA1,
			0x9A, 0xFF, 0xBE, 0xE0, 0x61, 0x4C, 0xE7, 0xA5
		},
		.len = 16
	}

};

static const struct aead_test_data gcm_test_case_6 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xfe, 0xff, 0xe9, 0x92, 0x86, 0x65, 0x73, 0x1c,
			0x6d, 0x6a, 0x8f, 0x94, 0x67, 0x30, 0x83, 0x08
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xca, 0xfe, 0xba, 0xbe, 0xfa, 0xce, 0xdb, 0xad,
			0xde, 0xca, 0xf8, 0x88
		},
		.len = 12
	},
	.aad = {
		.data = gcm_aad_zero_text,
		.len = 12
	},
	.plaintext = {
		.data = {
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39
		},
		.len = 60
	},
	.ciphertext = {
		.data = {
			0x42, 0x83, 0x1e, 0xc2, 0x21, 0x77, 0x74, 0x24,
			0x4b, 0x72, 0x21, 0xb7, 0x84, 0xd0, 0xd4, 0x9c,
			0xe3, 0xaa, 0x21, 0x2f, 0x2c, 0x02, 0xa4, 0xe0,
			0x35, 0xc1, 0x7e, 0x23, 0x29, 0xac, 0xa1, 0x2e,
			0x21, 0xd5, 0x14, 0xb2, 0x54, 0x66, 0x93, 0x1c,
			0x7d, 0x8f, 0x6a, 0x5a, 0xac, 0x84, 0xaa, 0x05,
			0x1b, 0xa3, 0x0b, 0x39, 0x6a, 0x0a, 0xac, 0x97,
			0x3d, 0x58, 0xe0, 0x91
		},
		.len = 60
	},
	.auth_tag = {
		.data = {
			0x74, 0xFC, 0xFA, 0x29, 0x3E, 0x60, 0xCC, 0x66,
			0x09, 0xD6, 0xFD, 0x00, 0xC8, 0x86, 0xD5, 0x42
		},
		.len = 16
	}
};

static const struct aead_test_data gcm_test_case_7 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xfe, 0xff, 0xe9, 0x92, 0x86, 0x65, 0x73, 0x1c,
			0x6d, 0x6a, 0x8f, 0x94, 0x67, 0x30, 0x83, 0x08
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xca, 0xfe, 0xba, 0xbe, 0xfa, 0xce, 0xdb, 0xad,
			0xde, 0xca, 0xf8, 0x88
		},
		.len = 12
	},
	.aad = {
		.data = gcm_aad_text,
		.len = 12
	},
	.plaintext = {
		.data = {
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39
		},
		.len = 60
	},
	.ciphertext = {
		.data = {
			0x42, 0x83, 0x1e, 0xc2, 0x21, 0x77, 0x74, 0x24,
			0x4b, 0x72, 0x21, 0xb7, 0x84, 0xd0, 0xd4, 0x9c,
			0xe3, 0xaa, 0x21, 0x2f, 0x2c, 0x02, 0xa4, 0xe0,
			0x35, 0xc1, 0x7e, 0x23, 0x29, 0xac, 0xa1, 0x2e,
			0x21, 0xd5, 0x14, 0xb2, 0x54, 0x66, 0x93, 0x1c,
			0x7d, 0x8f, 0x6a, 0x5a, 0xac, 0x84, 0xaa, 0x05,
			0x1b, 0xa3, 0x0b, 0x39, 0x6a, 0x0a, 0xac, 0x97,
			0x3d, 0x58, 0xe0, 0x91
		},
		.len = 60
	},
	.auth_tag = {
		.data = {
			0xE9, 0xE4, 0xAB, 0x76, 0xB7, 0xFF, 0xEA, 0xDC,
			0x69, 0x79, 0x38, 0xA2, 0x0D, 0xCA, 0xF5, 0x92
		},
		.len = 16
	}
};

static const struct aead_test_data gcm_test_case_8 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xfe, 0xff, 0xe9, 0x92, 0x86, 0x65, 0x73, 0x1c,
			0x6d, 0x6a, 0x8f, 0x94, 0x67, 0x30, 0x83, 0x08
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xca, 0xfe, 0xba, 0xbe, 0xfa, 0xce, 0xdb, 0xad,
			0xde, 0xca, 0xf8, 0x88
		},
		.len = 12
	},
	.aad = {
		.data = gcm_aad_text,
		.len = 12
	},
	.plaintext = {
		.data = {
			0xC5, 0x34, 0x2E, 0x83, 0xEB, 0x4C, 0x02, 0x03,
			0xF7, 0xB2, 0x57, 0x35, 0x26, 0x81, 0x63, 0xAE,
			0x1F, 0xCD, 0x2D, 0x02, 0x91, 0x5A, 0xDB, 0x3A,
			0xF1, 0x38, 0xD8, 0x75, 0x86, 0x20, 0xCC, 0x1E,
			0xE6, 0xDC, 0xFF, 0xB5, 0xEA, 0x0E, 0x18, 0x7A,
			0x86, 0x6C, 0xAB, 0x39, 0x2D, 0x90, 0xAC, 0x77,
			0x5D, 0xED, 0x65, 0xB3, 0x05, 0x29, 0xBB, 0x09,
			0xD0, 0x21, 0x74, 0x6A, 0x67, 0x1C, 0x95, 0x42,
			0x55, 0xAD, 0xC8, 0x91, 0x28, 0xFE, 0x16, 0x9A,
			0xE1, 0xCB, 0xCD, 0x68, 0x3B, 0xDF, 0x3E, 0x3A,
			0x34, 0xFE, 0x9B, 0xFB, 0xF5, 0x15, 0x2A, 0x29,
			0x18, 0x99, 0x24, 0xBF, 0xB6, 0x43, 0xDB, 0xD1,
			0x69, 0x26, 0x1E, 0x31, 0x2C, 0x8C, 0x3C, 0x6B,
			0x7F, 0x06, 0xA6, 0x03, 0xE2, 0x1A, 0x50, 0xFE,
			0x7C, 0x69, 0xE5, 0x5F, 0x35, 0x93, 0xE9, 0x20,
			0x14, 0xB1, 0xCA, 0x61, 0xE7, 0x9C, 0x89, 0x08,
			0xD6, 0xB1, 0xC2, 0x63, 0x1B, 0x86, 0x5E, 0xF1,
			0xF5, 0x23, 0x0E, 0x9B, 0xE5, 0xBD, 0x5D, 0x04,
			0xF7, 0xEF, 0x8E, 0x46, 0xB0, 0x11, 0x4F, 0x69,
			0x62, 0x35, 0x51, 0xB7, 0x24, 0xA2, 0x31, 0xD0,
			0x32, 0x4E, 0xB8, 0x44, 0xC7, 0x59, 0xDE, 0x25,
			0xEA, 0x2D, 0x00, 0x0E, 0xF1, 0x07, 0xBA, 0xBB,
			0x9A, 0xBC, 0x4F, 0x57, 0xB7, 0x13, 0x57, 0xEF,
			0xD9, 0xF6, 0x80, 0x69, 0xEA, 0xE8, 0x47, 0x9C,
			0x51, 0x71, 0xE6, 0x8F, 0x69, 0x29, 0xB4, 0x60,
			0xE8, 0x50, 0xE5, 0xD0, 0x9B, 0xD2, 0x62, 0x6F,
			0x09, 0x5C, 0xD1, 0x4B, 0x85, 0xE2, 0xFD, 0xD3,
			0xEB, 0x28, 0x55, 0x77, 0x97, 0xCA, 0xD6, 0xA8,
			0xDC, 0x35, 0x68, 0xF7, 0x6A, 0xCF, 0x48, 0x3F,
			0x49, 0x31, 0x00, 0x65, 0xB7, 0x31, 0x1A, 0x49,
			0x75, 0xDE, 0xCE, 0x7F, 0x18, 0xB5, 0x31, 0x9A,
			0x64, 0x6D, 0xE5, 0x49, 0x1D, 0x6D, 0xF2, 0x21,
			0x9F, 0xF5, 0xFF, 0x7C, 0x41, 0x30, 0x33, 0x06,
			0x7B, 0xA4, 0xD8, 0x99, 0xF6, 0xCC, 0xDF, 0xC4,
			0x3F, 0xF3, 0xCD, 0xE7, 0x74, 0xC4, 0x4A, 0x19,
			0x5C, 0xCA, 0x42, 0x31, 0xF1, 0x3B, 0x65, 0x1C,
			0x3D, 0x56, 0x08, 0xBE, 0x15, 0x37, 0x23, 0x50,
			0xD6, 0xA3, 0x57, 0x64, 0x25, 0xBE, 0xDA, 0xC2,
			0x4E, 0xF5, 0x1A, 0xAD, 0x6F, 0x43, 0x78, 0x21,
			0xF9, 0x36, 0x39, 0x1F, 0x5F, 0xF7, 0x1B, 0xA0,
			0xEE, 0x8B, 0x4F, 0x8A, 0x9D, 0xD8, 0xED, 0x37,
			0xCE, 0x0D, 0x70, 0xE0, 0x3F, 0xE7, 0x11, 0x30,
			0x17, 0x1D, 0x03, 0x5E, 0xA0, 0x3D, 0x3F, 0x9E,
			0xF5, 0xD3, 0x74, 0x2E, 0xC1, 0xD6, 0xFF, 0xF7,
			0x2E, 0xE7, 0x80, 0x88, 0xCF, 0x0E, 0x7F, 0x12,
			0x71, 0x62, 0xC7, 0xF1, 0xC4, 0x2B, 0x64, 0x5D,
			0x1C, 0x9A, 0xB4, 0xCB, 0xB8, 0x24, 0xB3, 0x0B,
			0x33, 0xF2, 0x8A, 0x8F, 0x76, 0xC8, 0x81, 0xDA,
			0x1A, 0x10, 0xB5, 0xA9, 0xCD, 0xDC, 0x1A, 0x02,
			0xC1, 0xAE, 0x4F, 0x02, 0x1B, 0x13, 0x96, 0x5A,
			0x2E, 0x03, 0xA2, 0x68, 0xB2, 0x29, 0xAC, 0x28,
			0xB8, 0xDC, 0xD5, 0x27, 0x55, 0xEC, 0x43, 0xDC,
			0xB7, 0x49, 0x1D, 0xE1, 0x30, 0x25, 0x81, 0xA6,
			0x90, 0x1F, 0x75, 0xBA, 0x19, 0x1E, 0xF7, 0xC5,
			0x77, 0x35, 0xEE, 0x68, 0x71, 0x22, 0xA0, 0xB4,
			0xCC, 0x99, 0x86, 0x1B, 0x1B, 0xC8, 0x27, 0xFC,
			0x6D, 0x8D, 0xE7, 0x8B, 0xC3, 0x40, 0x3D, 0xA8,
			0xCB, 0x9B, 0xC4, 0x12, 0x07, 0xDD, 0xA1, 0x92,
			0xE5, 0x80, 0x7A, 0xF4, 0xDB, 0x4C, 0xE6, 0xEE,
			0xF9, 0xD5, 0x1C, 0x20, 0x18, 0xD3, 0x8F, 0xDF,
			0x1C, 0xD3, 0x51, 0x4E, 0x0E, 0xED, 0x06, 0x61,
			0xF7, 0xBA, 0x81, 0x3A, 0x2F, 0xEA, 0xED, 0x70,
			0xA9, 0xD9, 0x54, 0x4D, 0xFC, 0x1D, 0x19, 0xEA,
			0xA6, 0x39, 0x8C, 0x6C, 0x78, 0xA8, 0x05, 0xEB,
			0xF2, 0xB5, 0xDE, 0x06, 0x9D, 0x8A, 0x78, 0x2A,
			0xF5, 0x50, 0xA4, 0xBD, 0x9B, 0xDA, 0xCA, 0x66,
			0xC0, 0x23, 0xAB, 0xE8, 0x95, 0x7E, 0xC9, 0xD2,
			0x6F, 0x09, 0xF2, 0x9A, 0x17, 0x89, 0xDA, 0x47,
			0x65, 0x8C, 0x20, 0xFA, 0x4E, 0x86, 0x18, 0xEB,
			0x7C, 0x08, 0xEC, 0x8A, 0x05, 0x54, 0x96, 0xD2,
			0x7A, 0x8A, 0x81, 0x58, 0x75, 0x8C, 0x7B, 0x02,
			0xEE, 0x1F, 0x51, 0x88, 0xD0, 0xD1, 0x90, 0x99,
			0x0C, 0xAE, 0x51, 0x2E, 0x54, 0x3E, 0xB1, 0x7D,
			0xBC, 0xE8, 0x54, 0x93, 0x6D, 0x10, 0x3C, 0xC6,
			0x71, 0xF6, 0xF5, 0x0B, 0x07, 0x0A, 0x6E, 0x59,
			0x20, 0x45, 0x21, 0x7D, 0x37, 0x64, 0x92, 0x09,
			0xA7, 0xE2, 0x34, 0x6F, 0xFC, 0xCC, 0x66, 0x0E,
			0x88, 0x1B, 0x19, 0x86, 0x11, 0xD7, 0x81, 0x25,
			0xF1, 0x8A, 0x03, 0xB7, 0x7A, 0xF0, 0x98, 0x4A,
			0x5C, 0xA1, 0x6D, 0x85, 0xA4, 0x8C, 0x4B, 0x65,
			0x9F, 0x72, 0x64, 0x14, 0xBA, 0x74, 0xEE, 0xA3,
			0x88, 0xFE, 0x1B, 0xCF, 0x11, 0x4F, 0xD1, 0xAC,
			0xFA, 0x14, 0xC3, 0xA7, 0xDD, 0x06, 0x85, 0x4E,
			0x64, 0x06, 0x92, 0x9C, 0xDF, 0x06, 0x09, 0xF1,
			0x4D, 0xE8, 0xF8, 0x2F, 0x69, 0xB6, 0x8A, 0xAF,
			0x25, 0x21, 0xB5, 0x48, 0x59, 0xF8, 0x9D, 0x60,
			0xAE, 0x42, 0x11, 0x7A, 0x68, 0x4D, 0x7E, 0x76,
			0xB0, 0xD2, 0xE3, 0xD9, 0x24, 0x16, 0x20, 0x0A,
			0xEB, 0xE0, 0x68, 0xCB, 0xBC, 0xAB, 0x67, 0xE4,
			0xF3, 0x25, 0x1F, 0xD3, 0x85, 0xA7, 0x1D, 0x7E,
			0x3C, 0x63, 0xCB, 0xC2, 0x50, 0x90, 0x0F, 0x4B,
			0x6E, 0x68, 0x06, 0x84, 0x65, 0xF7, 0xD0, 0xD4,
			0x12, 0xED, 0xFA, 0xC9, 0x40, 0xE2, 0xC0, 0xC9,
			0x46, 0x22, 0x47, 0x5E, 0x6D, 0xC1, 0x63, 0xDB,
			0x51, 0x98, 0xDA, 0x1A, 0xC4, 0xB9, 0xED, 0xE9,
			0x09, 0xB9, 0xCF, 0x91, 0x04, 0x1C, 0x63, 0xD8,
			0xC5, 0xA5, 0xAE, 0x53, 0x7B, 0xA1, 0x29, 0x83,
			0x37, 0xFB, 0xBF, 0x96, 0xBB, 0x24, 0x3D, 0x77,
			0x8C, 0x0F, 0xB3, 0x4B, 0x66, 0x9C, 0x54, 0xBB,
			0xF6, 0xDD, 0xD1, 0xB4, 0xD2, 0xF6, 0xAA, 0xED,
			0x18, 0x56, 0x63, 0x3E, 0x0B, 0xCA, 0xAB, 0x70,
			0xBB, 0x63, 0xEA, 0xB1, 0x00, 0x65, 0x90, 0x18,
			0xB8, 0x63, 0xA2, 0xF2, 0xB6, 0x1E, 0x61, 0x7B,
			0xD5, 0x01, 0xD9, 0x4D, 0xC9, 0x9D, 0x99, 0xC1,
			0x57, 0x9D, 0x6F, 0xAE, 0x64, 0xE4, 0x0C, 0x7E,
			0xFA, 0x15, 0x5E, 0xB6, 0x43, 0xB8, 0x8B, 0x89,
			0x87, 0xCD, 0x4F, 0xAD, 0x30, 0x1E, 0xA5, 0x03,
			0x7A, 0xC2, 0x10, 0x42, 0x14, 0x88, 0xD6, 0x7A,
			0x6D, 0x56, 0x52, 0x2E, 0x8D, 0x1B, 0x5D, 0x36,
			0x27, 0xA0, 0x21, 0x4B, 0x64, 0xF0, 0xC5, 0x41,
			0xAD, 0x05, 0x4A, 0x24, 0xE4, 0x70, 0x88, 0x63,
			0x12, 0xD0, 0xBC, 0x05, 0x38, 0xD9, 0x41, 0x68,
			0x9F, 0x16, 0x9A, 0x54, 0x09, 0x21, 0x64, 0x36,
			0x63, 0x97, 0x3A, 0xB5, 0xE0, 0x25, 0x43, 0x8A,
			0x6A, 0x59, 0x97, 0xC1, 0x31, 0xA5, 0x66, 0xD2,
			0xF0, 0x1C, 0xDF, 0x97, 0x51, 0xD0, 0x61, 0xBA,
			0x55, 0x5F, 0xD7, 0x0D, 0xD4, 0x75, 0x8E, 0x79,
			0x04, 0x75, 0x00, 0xB9, 0xC0, 0x7A, 0x66, 0x05,
			0x9F, 0x2B, 0x44, 0x42, 0x75, 0x0F, 0xD5, 0x15,
			0xD6, 0x16, 0x8F, 0x6C, 0x6E, 0xD4, 0x37, 0xCF,
			0xB4, 0xDA, 0x93, 0x00, 0x11, 0xFB, 0xBE, 0xEE,
			0x3B, 0x6D, 0x1D, 0xBA, 0x33, 0xD1, 0x52, 0x8B,
			0x16, 0x39, 0x42, 0x27, 0xE6, 0x56, 0x4C, 0x41,
			0x91, 0xB0, 0x98, 0xAE, 0x9B, 0x2D, 0x9B, 0x23,
			0x80, 0x4C, 0xEA, 0x98, 0x57, 0x95, 0x28, 0x94,
			0x43, 0xD3, 0x88, 0x12, 0xDF, 0x89, 0x5A, 0x7B,
			0xC5, 0xCB, 0x36, 0x54, 0x65, 0x74, 0xB8, 0x4E,
			0xE2, 0x4D, 0x01, 0xD5, 0x9C, 0x82, 0xB9, 0x1A,
			0x09, 0xD2, 0xCE, 0x04, 0x36, 0xD8, 0x41, 0xAC,
			0x4C, 0xAD, 0xC6, 0x52, 0x91, 0x1A, 0x06, 0x6D,
			0xFC, 0xAB, 0x29, 0x93, 0x87, 0x88, 0xB9, 0x8C,
			0xFA, 0x57, 0x2B, 0x05, 0x03, 0xD0, 0x18, 0xED,
			0x7A, 0x7B, 0x81, 0x6A, 0x97, 0x65, 0x5B, 0x90,
			0xDE, 0xA9, 0xFC, 0x8F, 0xFC, 0xBB, 0x98, 0xD8,
			0xFA, 0x32, 0x3F, 0x3F, 0x7F, 0x74, 0x65, 0x38,
			0xC4, 0x28, 0xEC, 0x27, 0x1F, 0x28, 0x01, 0xB1,
			0xAF, 0x2B, 0x8A, 0x05, 0x38, 0x7B, 0x77, 0xC9,
			0x61, 0x77, 0x34, 0x2C, 0x22, 0xE5, 0xEB, 0xDC,
			0x9D, 0x18, 0x6E, 0x23, 0x25, 0x52, 0x69, 0xB7,
			0x05, 0xDB, 0x66, 0x5D, 0xEA, 0x76, 0x83, 0x82,
			0x97, 0x39, 0xAF, 0xC0, 0x50, 0x81, 0x18, 0x0D,
			0x22, 0xFA, 0xB7, 0x44, 0x5C, 0x3F, 0x69, 0xF3,
			0xAC, 0xC5, 0x63, 0x9F, 0xD8, 0x72, 0x7E, 0x9A,
			0xC2, 0xEB, 0x79, 0xD0, 0x74, 0x65, 0xE8, 0xCA,
			0xFD, 0xA8, 0x7D, 0x23, 0x07, 0x99, 0x3E, 0xAF,
			0xDB, 0x67, 0x10, 0xC0, 0xE5, 0x61, 0x77, 0xC6,
			0x8D, 0xC4, 0x0E, 0xAA, 0x55, 0xE3, 0xC0, 0xC7,
			0xA5, 0x36, 0x28, 0x61, 0xDB, 0x16, 0x96, 0x5E,
			0x01, 0x47, 0x82, 0xE3, 0xEB, 0x20, 0x3F, 0x10,
			0xFA, 0x5A, 0xBC, 0xD3, 0xF9, 0xCE, 0x04, 0x87,
			0x51, 0x07, 0xF9, 0xD0, 0xE7, 0x6D, 0xCB, 0xCC,
			0xC4, 0x15, 0x00, 0xE2, 0xDC, 0x8E, 0x7B, 0x5C,
			0x9A, 0xF2, 0x78, 0x70, 0x4D, 0xA1, 0xAA, 0xB5,
			0x13, 0xCC, 0x71, 0x66, 0x5A, 0x79, 0x13, 0x3B,
			0x12, 0xCD, 0x40, 0x30, 0x5A, 0x49, 0xD4, 0x20,
			0xED, 0xCF, 0x4A, 0x75, 0xE6, 0xD5, 0xDD, 0x0F,
			0xD4, 0xBE, 0x98, 0x9F, 0xD7, 0x1F, 0xC0, 0x02,
			0x31, 0xFA, 0x67, 0x37, 0x25, 0x86, 0x56, 0x85,
			0x2B, 0xA2, 0x57, 0xCD, 0x8E, 0x74, 0xE7, 0x69,
			0xEE, 0x33, 0x5A, 0x3F, 0xCD, 0x1E, 0xE3, 0xB9,
			0xAA, 0x52, 0xF5, 0x22, 0x4E, 0xE3, 0xFF, 0xC8,
			0xE3, 0x13, 0xA3, 0x9A, 0x63, 0x23, 0xC3, 0xD7,
			0xE5, 0x88, 0x3E, 0x0A, 0x4B, 0xA5, 0x01, 0xE6,
			0x13, 0xCF, 0xED, 0xEE, 0x2A, 0x58, 0x09, 0x3F,
			0x2F, 0x28, 0xE7, 0xC4, 0x6B, 0xEC, 0x49, 0x51,
			0x79, 0x8F, 0xD5, 0x19, 0x5D, 0xA5, 0x10, 0xCE,
			0x8E, 0xF6, 0x26, 0x78, 0x7A, 0xA8, 0x11, 0x52,
			0x5F, 0x97, 0x14, 0xC9, 0x29, 0x87, 0xB8, 0xA0,
			0x2D, 0xE6, 0xA7, 0x2A, 0xD4, 0xFF, 0xEB, 0xBA,
			0xFD, 0x58, 0x39, 0x33, 0xB1, 0xCE, 0x0E, 0x78,
			0x67, 0x1E, 0xA1, 0x92, 0x77, 0x63, 0xF8, 0xC0,
			0x02, 0x49, 0x73, 0xC0, 0xA1, 0x26, 0x83, 0x04,
			0x9A, 0x5D, 0x85, 0x68, 0x2A, 0x2F, 0xCB, 0x88,
			0x8D, 0x14, 0xB1, 0x33, 0xFA, 0xFB, 0xE9, 0x05,
			0xBE, 0x24, 0x1A, 0x6B, 0x29, 0x2B, 0x3F, 0x52,
			0x8F, 0xFB, 0xE6, 0x02, 0x77, 0x50, 0x71, 0xDB,
			0xE9, 0x92, 0x3F, 0xE1, 0x20, 0x62, 0x80, 0xAE,
			0xA4, 0x98, 0xC6, 0xCD, 0xE0, 0xB1, 0xC3, 0x33,
			0xB1, 0xC5, 0x91, 0x3C, 0x19, 0x34, 0xA8, 0xD9,
			0xB3, 0x25, 0x69, 0xE3, 0x9C, 0x5F, 0x78, 0xD0,
			0x83, 0x1F, 0xAB, 0x85, 0x13, 0x56, 0x69, 0xB5,
			0x06, 0x47, 0x62, 0x37, 0x27, 0x15, 0x14, 0x05,
			0x4A, 0xF4, 0x6A, 0x68, 0x2A, 0x6A, 0xC3, 0x5A,
			0xDF, 0xB5, 0xAE, 0x2F, 0x8D, 0x8F, 0x21, 0xDB,
			0x33, 0x00, 0x9B, 0xD4, 0xC4, 0x08, 0x3B, 0x81,
			0x63, 0x4C, 0xB0, 0x39, 0x4C, 0x0A, 0xD5, 0x71,
			0x3E, 0x5A, 0x50, 0x58, 0x9C, 0x07, 0x89, 0x79,
			0x79, 0x2F, 0x0B, 0xD9, 0x50, 0xBC, 0xCF, 0x46,
			0x7A, 0x68, 0x5C, 0xBF, 0x1E, 0x49, 0x77, 0x92,
			0x85, 0x11, 0x39, 0xA6, 0x2F, 0xDA, 0x7B, 0xFA,
			0x72, 0x87, 0x06, 0xCD, 0x84, 0x41, 0x20, 0x1B,
			0x66, 0x3F, 0x42, 0x0C, 0x9E, 0x19, 0xD3, 0x18,
			0x57, 0xA0, 0xEE, 0x16, 0x3A, 0xC7, 0xF9, 0xD3,
			0x8B, 0xC9, 0x24, 0x70, 0x70, 0x51, 0x7C, 0x06,
			0x68, 0xD3, 0x29, 0xC9, 0x85, 0x9A, 0x1C, 0xE6,
			0x8C, 0x17, 0xF4, 0x88, 0xDF, 0xEA, 0xFF, 0x44,
			0x8D, 0x54, 0xBE, 0x22, 0x07, 0xA5, 0x7C, 0x0C,
			0xF4, 0x8D, 0xB1, 0x0C, 0x07, 0xED, 0xBD, 0x28,
			0x19, 0xDA, 0x07, 0x71, 0xA8, 0xA1, 0xE0, 0xDD,
			0xEE, 0x08, 0x18, 0xA5, 0xBD, 0xDD, 0x32, 0x0B,
			0x70, 0x1C, 0xD9, 0xEE, 0x19, 0xC2, 0xAE, 0x5C,
			0xE3, 0x02, 0x74, 0x70, 0x96, 0x61, 0xB1, 0x73,
			0x3B, 0xD6, 0x74, 0xC0, 0x82, 0xA9, 0x1F, 0xE0,
			0xF1, 0x22, 0x50, 0xF3, 0x9F, 0xE5, 0x13, 0x92,
			0xFC, 0x0A, 0x1A, 0x3C, 0xB4, 0x46, 0xFB, 0x81,
			0x00, 0x84, 0xA4, 0x5E, 0x6B, 0x8C, 0x25, 0x6E,
			0xD7, 0xB7, 0x3B, 0x01, 0x65, 0xFB, 0x0B, 0x46,
			0x67, 0x27, 0x2D, 0x51, 0xAD, 0xB5, 0xE0, 0x85,
			0xC2, 0x95, 0xA3, 0xE3, 0x68, 0x4D, 0x9E, 0x8C,
			0x11, 0x53, 0xF0, 0xB2, 0x85, 0xFA, 0x52, 0x4E,
			0xEC, 0xF9, 0xB7, 0x3C, 0x89, 0x2C, 0x4D, 0x32,
			0x9A, 0xCB, 0x17, 0xF3, 0x16, 0xBF, 0x44, 0x40,
			0xE9, 0x5E, 0x51, 0x8C, 0x1E, 0x52, 0x0A, 0xC2,
			0xCD, 0xA5, 0xAA, 0x03, 0x27, 0xB0, 0x8F, 0x64,
			0xDB, 0xD7, 0x03, 0x01, 0x8A, 0x24, 0x28, 0x7E,
			0x53, 0x6F, 0x24, 0xFD, 0xAA, 0xE3, 0x78, 0xB6,
			0xA5, 0x5D, 0x5A, 0x67, 0x20, 0xE2, 0xBE, 0x3A,
			0x2B, 0xE7, 0x86, 0x11, 0xDD, 0x96, 0xCB, 0x09,
			0x65, 0xA0, 0x36, 0xF9, 0xB0, 0x20, 0x21, 0x8E,
			0xDB, 0xC0, 0x73, 0xC7, 0x79, 0xD8, 0xDA, 0xC2,
			0x66, 0x13, 0x64, 0x34, 0x0C, 0xE1, 0x22, 0x24,
			0x61, 0x67, 0x08, 0x39, 0x97, 0x3F, 0x33, 0x96,
			0xF2, 0x44, 0x18, 0x75, 0xBB, 0xF5, 0x6A, 0x5C,
			0x2C, 0xAE, 0x2A, 0x79, 0x3D, 0x47, 0x19, 0x53,
			0x50, 0x6C, 0x9F, 0xB3, 0x82, 0x55, 0x09, 0x78,
			0x7B, 0xAD, 0xBC, 0x05, 0x6F, 0xC8, 0x3D, 0xB6,
			0x7B, 0x30, 0xE6, 0xBB, 0x8B, 0xD0, 0x2F, 0xA6,
			0x15, 0xCC, 0x77, 0x8C, 0x21, 0xBA, 0x03, 0xED,
			0x56, 0x85, 0x82, 0x4F, 0x97, 0x8C, 0x59, 0x4F,
			0x53, 0x5A, 0xD2, 0x70, 0xD9, 0x07, 0xB3, 0xBD,
			0x1D, 0x3E, 0x97, 0xD4, 0x7D, 0x93, 0x35, 0xA4,
			0x82, 0x6E, 0xEA, 0x4B, 0xC8, 0x6C, 0xF5, 0xE6,
			0xEB, 0xAF, 0x11, 0xB0, 0xB4, 0x71, 0x8F, 0x7B,
			0xC4, 0x8C, 0xE2, 0x66, 0x51, 0x31, 0x99, 0x01,
			0x5B, 0xE7, 0x48, 0xF8, 0x4C, 0xE3, 0x9A, 0x77,
			0xF1, 0xC6, 0x09, 0xDE, 0x76, 0xD4, 0xE3, 0x5C,
			0xDF, 0xA3, 0xEC, 0x3C, 0x86, 0x7C, 0xA5, 0x3F,
			0x8D, 0x2A, 0xF3, 0x0B, 0x54, 0xB7, 0x54, 0xA2,
			0xC1, 0x69, 0xC0, 0x6F, 0x1C, 0x1C, 0x76, 0xD8,
			0x9F, 0x7A, 0x32, 0xB0, 0xA1, 0xA6, 0x9B, 0xB7,
			0x21, 0x56, 0x28, 0x2D, 0xB6, 0x97, 0x03, 0x5E,
			0x65, 0xE3, 0x74, 0x9A, 0x96, 0x7A, 0xF9, 0xF5,
			0xDD, 0x85, 0xCA, 0x4C, 0xB4, 0x03, 0x6A, 0xCD,
			0xB6, 0x01, 0xDC, 0x8B, 0xD8, 0x73, 0x8F, 0x4D,
			0x7F, 0xD6, 0x71, 0xEC, 0xD7, 0xC6, 0x0B, 0x5F,
			0x09, 0x21, 0xB2, 0x78, 0xA8, 0xAF, 0xAD, 0x2C,
			0xD4, 0x93, 0x9F, 0x71, 0xF7, 0x05, 0x89, 0x42,
			0xC9, 0x15, 0x6F, 0x2D, 0xE0, 0xBA, 0xC3, 0xD6,
			0xBF, 0xAC, 0xF8, 0x24, 0x58, 0x79, 0xA9, 0xC4,
			0xB4, 0x49, 0x3E, 0x0B, 0x9E, 0x5E, 0xE4, 0xA6,
			0x8B, 0xE8, 0xDE, 0xFB, 0x4A, 0xF1, 0x69, 0x9D,
			0x4F, 0x77, 0x83, 0x78, 0x55, 0x19, 0x42, 0x45,
			0xBF, 0xBD, 0xBD, 0x12, 0x0F, 0xEF, 0x8D, 0x04,
			0xD8, 0x5C, 0xF2, 0xC9, 0xF1, 0xA6, 0xE0, 0x3E,
			0x22, 0xA8, 0xA2, 0x5E, 0x66, 0xE9, 0xAB, 0xB4,
			0x71, 0xBE, 0x4B, 0x3F, 0xBE, 0xC4, 0xBA, 0x4A
		},
		.len = 2048
	},
	.ciphertext = {
		.data = {
			0x5E, 0x86, 0x02, 0x64, 0x32, 0xBF, 0x70, 0xC2,
			0x19, 0x99, 0x7F, 0x47, 0x0D, 0xA4, 0x91, 0xA8,
			0x7A, 0xC0, 0xA5, 0x7E, 0xA8, 0x6C, 0x88, 0x00,
			0xEA, 0xB5, 0x96, 0x6B, 0x25, 0xBD, 0xE7, 0x42,
			0xDB, 0x35, 0xE7, 0x92, 0x2B, 0x00, 0x82, 0x35,
			0xD4, 0x2C, 0xCF, 0x47, 0xC8, 0xB2, 0xB3, 0x57,
			0xF7, 0x24, 0x83, 0x7F, 0xC5, 0x2E, 0xF1, 0xC9,
			0x57, 0x1A, 0xEF, 0xC2, 0x3A, 0x8C, 0x1E, 0x92,
			0x88, 0x05, 0xAF, 0x55, 0xE6, 0x0C, 0xA7, 0x6B,
			0x59, 0x62, 0x32, 0x21, 0xF1, 0xFF, 0xB5, 0x5B,
			0x22, 0x26, 0x6F, 0x0A, 0x36, 0xDC, 0x0D, 0x16,
			0x3B, 0x4E, 0x7C, 0xA3, 0x75, 0x30, 0x3F, 0xB0,
			0x99, 0x38, 0x42, 0x8E, 0x89, 0xA3, 0x7C, 0x99,
			0x2F, 0x0A, 0xA1, 0xC7, 0xFD, 0x2D, 0x21, 0x8F,
			0xBD, 0xD4, 0x11, 0xEA, 0x55, 0xF5, 0x6A, 0x50,
			0x90, 0x3B, 0x60, 0x57, 0xE1, 0x86, 0x1E, 0x50,
			0x28, 0x67, 0x3F, 0xD2, 0xF3, 0xBD, 0xFA, 0xEE,
			0xD6, 0x5A, 0x38, 0x30, 0xA3, 0xDD, 0x78, 0xC4,
			0x37, 0x59, 0x52, 0xC0, 0x92, 0x54, 0xC7, 0x53,
			0xF0, 0xE6, 0xA9, 0x63, 0x1F, 0x9B, 0x97, 0xFB,
			0x40, 0x23, 0xFE, 0x52, 0x6A, 0xF0, 0x3A, 0x94,
			0xEB, 0x6A, 0x9E, 0x8F, 0xC5, 0x05, 0x9C, 0x04,
			0x1B, 0x00, 0x34, 0x96, 0x12, 0xDA, 0x60, 0xC6,
			0xAA, 0x1A, 0x3E, 0xEB, 0x70, 0x17, 0x10, 0xBC,
			0xF5, 0xC2, 0xE2, 0x71, 0xF3, 0xB8, 0x1D, 0xCE,
			0x47, 0x94, 0x21, 0x71, 0x34, 0x8C, 0xCC, 0xDD,
			0x27, 0xCE, 0x6F, 0x68, 0xFF, 0x91, 0x4E, 0xC4,
			0xA0, 0xCA, 0xB0, 0x4F, 0x17, 0x53, 0x73, 0x92,
			0x6C, 0xA8, 0x16, 0x06, 0xE3, 0xD9, 0x92, 0x99,
			0xBE, 0xB0, 0x7D, 0x56, 0xF2, 0x72, 0x30, 0xDA,
			0xC4, 0x4E, 0xF4, 0xA6, 0x8F, 0xD2, 0xC7, 0x8A,
			0xA2, 0xFC, 0xF5, 0x63, 0x17, 0x48, 0x56, 0x4D,
			0xBE, 0x94, 0xFE, 0xF5, 0xB1, 0xA9, 0x96, 0xAB,
			0x3F, 0x2D, 0xD4, 0x15, 0xEE, 0x4F, 0xFA, 0x2C,
			0xBE, 0x91, 0xB7, 0xBC, 0x18, 0xC8, 0xDB, 0x02,
			0x20, 0x29, 0xF1, 0xC1, 0x88, 0x8C, 0x8D, 0xD1,
			0xB3, 0x4E, 0x93, 0x96, 0xDD, 0x22, 0xAB, 0x55,
			0xB5, 0x9F, 0x8B, 0x20, 0xAE, 0xC6, 0x0E, 0x26,
			0xC6, 0xFE, 0x2D, 0x5F, 0x95, 0x89, 0x06, 0x15,
			0x3D, 0x88, 0x16, 0xEC, 0x9B, 0x4A, 0x1B, 0x5D,
			0x2E, 0xB2, 0x13, 0x56, 0x9F, 0x33, 0xB3, 0x45,
			0xBF, 0x5F, 0x25, 0x7E, 0x75, 0x22, 0xD2, 0xE6,
			0x9F, 0xAC, 0x2D, 0xFD, 0x99, 0xC2, 0x9B, 0xFC,
			0xD7, 0x7A, 0x9B, 0x05, 0x30, 0x0F, 0xB7, 0x4A,
			0xFE, 0x24, 0xDD, 0x39, 0x9B, 0xBB, 0x2F, 0xDD,
			0xF9, 0xFB, 0xCA, 0x6C, 0x87, 0xBA, 0x73, 0xD4,
			0x85, 0x7B, 0xB2, 0x6F, 0x5C, 0xD8, 0xFB, 0xE9,
			0x41, 0x24, 0x3A, 0x3B, 0x4F, 0x91, 0x77, 0xA2,
			0x35, 0x78, 0xE5, 0x4C, 0xFE, 0x8B, 0x04, 0x03,
			0xD3, 0x84, 0xA9, 0x1C, 0xA7, 0x7C, 0x45, 0x13,
			0x7D, 0xC5, 0x0A, 0x2F, 0x02, 0xF8, 0x56, 0xD5,
			0x5F, 0x35, 0xED, 0x06, 0xBF, 0x67, 0xBA, 0x51,
			0x02, 0x95, 0x36, 0xF2, 0x9A, 0xBA, 0x9D, 0xF6,
			0xD6, 0x77, 0x50, 0xC9, 0xFC, 0x1E, 0x32, 0xB5,
			0x2F, 0xEA, 0x3C, 0x76, 0xB4, 0xE1, 0xCC, 0x42,
			0xEB, 0x71, 0x79, 0xD3, 0x7D, 0xB7, 0xC0, 0x88,
			0x25, 0x81, 0xE8, 0xC0, 0xB8, 0x38, 0x7E, 0x7B,
			0xFD, 0x18, 0xAB, 0x08, 0xB2, 0x71, 0xA5, 0xAD,
			0xA7, 0xBE, 0x48, 0x5F, 0x86, 0xE2, 0x41, 0x3D,
			0x7C, 0x37, 0x7A, 0xAB, 0xDB, 0xE0, 0x3B, 0x3D,
			0xB6, 0xE8, 0x23, 0x7C, 0xF1, 0x8F, 0xBA, 0xB7,
			0xE9, 0x78, 0x0B, 0xCA, 0x67, 0xA8, 0x10, 0x36,
			0xEB, 0x72, 0xED, 0xDD, 0xF0, 0x5C, 0x74, 0x8E,
			0xE5, 0x2A, 0xAE, 0x6E, 0xC4, 0xF1, 0xFC, 0xD8,
			0xEE, 0x56, 0x07, 0x88, 0x02, 0xDC, 0x9D, 0xB7,
			0xF9, 0x13, 0xE1, 0xE1, 0x9D, 0x89, 0x26, 0x0B,
			0x23, 0x74, 0x4A, 0x43, 0xAA, 0xA0, 0xA8, 0x97,
			0x85, 0x15, 0x58, 0xAB, 0x2B, 0xB5, 0xDA, 0x1A,
			0xBA, 0x29, 0x62, 0xCF, 0xDD, 0xA3, 0xBA, 0x9D,
			0x7D, 0x83, 0xA5, 0x18, 0xD4, 0x03, 0x0F, 0x61,
			0x9F, 0xB1, 0x7E, 0xEC, 0xD2, 0x6E, 0xAF, 0xCF,
			0x1E, 0xC1, 0x88, 0x97, 0x99, 0xD6, 0xBF, 0x47,
			0xB9, 0x0A, 0x69, 0x11, 0x3A, 0x55, 0x8B, 0x1D,
			0x2D, 0xFF, 0x78, 0xC8, 0xDE, 0x82, 0x29, 0xD6,
			0x08, 0x3C, 0xC4, 0xCB, 0x2F, 0x01, 0xD0, 0xE8,
			0xB1, 0x75, 0x5E, 0x23, 0xE0, 0x37, 0x7C, 0x1C,
			0xB6, 0xD9, 0x47, 0xDE, 0x23, 0x87, 0xD3, 0x68,
			0x47, 0x46, 0x78, 0xF3, 0xBF, 0x54, 0xA3, 0xB9,
			0x54, 0xD5, 0xC5, 0x0A, 0x7C, 0x92, 0x2A, 0xC2,
			0x14, 0x76, 0xA6, 0x5C, 0x6D, 0x0B, 0x94, 0x56,
			0x00, 0x6B, 0x5C, 0x27, 0xDE, 0x77, 0x9B, 0xF1,
			0xB1, 0x8C, 0xA7, 0x49, 0x77, 0xFC, 0x4E, 0x29,
			0x23, 0x8F, 0x2F, 0xF7, 0x83, 0x8D, 0x36, 0xD9,
			0xAB, 0x0E, 0x78, 0xF5, 0x90, 0x05, 0xB9, 0x79,
			0x70, 0x88, 0x59, 0x6F, 0xE2, 0xC5, 0xD7, 0x80,
			0x95, 0x04, 0x29, 0xE0, 0xFA, 0x37, 0xE8, 0x8B,
			0xC5, 0x21, 0x51, 0x1A, 0x62, 0xCE, 0x93, 0xAF,
			0x1A, 0xFE, 0xC3, 0x6F, 0x86, 0x94, 0x5E, 0x13,
			0xA6, 0x9A, 0x26, 0xF0, 0xB5, 0x7C, 0x41, 0x9A,
			0x80, 0xB8, 0x84, 0x5A, 0x55, 0xA9, 0xB0, 0x6A,
			0xFA, 0xEB, 0x46, 0x32, 0x0B, 0xE2, 0x9C, 0x65,
			0x86, 0x11, 0x39, 0x7E, 0xAF, 0x93, 0x19, 0x09,
			0x70, 0x40, 0x80, 0x14, 0xBA, 0x1D, 0xB3, 0x62,
			0x5B, 0xF3, 0x9A, 0x21, 0x98, 0x7E, 0x63, 0xB6,
			0x1A, 0xBD, 0x65, 0x98, 0x35, 0x2A, 0xA9, 0x76,
			0x29, 0x59, 0x84, 0x25, 0x81, 0xB8, 0xDE, 0x25,
			0x32, 0x10, 0x50, 0xB7, 0xD3, 0xB3, 0x69, 0xC8,
			0xE1, 0x33, 0xCB, 0x9E, 0x9C, 0x7A, 0x7C, 0xD2,
			0x6C, 0x92, 0x97, 0xA9, 0xFA, 0xAF, 0x30, 0xBA,
			0x9A, 0xB3, 0x3D, 0x9A, 0xE5, 0x0A, 0x9B, 0x8D,
			0x89, 0xE2, 0x2B, 0xB8, 0xBC, 0xF0, 0x23, 0xFF,
			0x7B, 0x0D, 0x00, 0x36, 0xEE, 0x79, 0xCB, 0xA5,
			0x70, 0x4C, 0x66, 0x02, 0x79, 0x2E, 0x5B, 0x83,
			0xCE, 0x55, 0x8B, 0x89, 0xD6, 0xE3, 0x71, 0x63,
			0xBC, 0xB1, 0x5F, 0x67, 0xB4, 0x7E, 0x05, 0x0D,
			0xAC, 0x6D, 0x4E, 0x2C, 0xA5, 0xF4, 0x47, 0x89,
			0xAC, 0x5E, 0xBE, 0x2F, 0xFC, 0x9B, 0x2F, 0x0B,
			0xBE, 0x63, 0x54, 0x97, 0xBB, 0x23, 0x27, 0xCD,
			0xB9, 0xB2, 0x28, 0x0D, 0xA4, 0x78, 0x2C, 0xAB,
			0xD1, 0xC9, 0x94, 0x40, 0x54, 0xF2, 0x35, 0x61,
			0x49, 0x01, 0x87, 0x55, 0xA5, 0xB5, 0x1E, 0x84,
			0x92, 0x9E, 0xC1, 0xA4, 0x0B, 0x66, 0x2B, 0xF8,
			0xAF, 0xC3, 0x1E, 0xAF, 0x66, 0x3F, 0x6F, 0x5F,
			0x70, 0xEC, 0x25, 0x29, 0xE4, 0x65, 0xB2, 0x04,
			0x47, 0xF6, 0x3C, 0xB5, 0x5F, 0x66, 0x9F, 0xA4,
			0x1B, 0xFC, 0xA2, 0xD5, 0x3E, 0x84, 0xBA, 0x88,
			0x0D, 0xF1, 0x6A, 0xF2, 0xF6, 0x1D, 0xF1, 0xA3,
			0x45, 0xB2, 0x51, 0xD8, 0xA2, 0x8F, 0x55, 0xA6,
			0x89, 0xC4, 0x15, 0xD5, 0x73, 0xA8, 0xB1, 0x31,
			0x66, 0x9E, 0xC1, 0x43, 0xE1, 0x5D, 0x4E, 0x04,
			0x84, 0x8F, 0xF2, 0xBC, 0xE1, 0x4E, 0x4D, 0x60,
			0x81, 0xCA, 0x53, 0x34, 0x95, 0x17, 0x3B, 0xAE,
			0x8F, 0x95, 0xA7, 0xC6, 0x47, 0xC6, 0xAC, 0x32,
			0x12, 0x39, 0xCA, 0xEF, 0xE0, 0x07, 0xBF, 0x17,
			0x4F, 0xDC, 0x1B, 0x4E, 0x3C, 0x84, 0xF1, 0x9F,
			0x43, 0x70, 0x19, 0xE6, 0xF3, 0x8B, 0x8B, 0x5D,
			0xDB, 0xD2, 0x9D, 0xD4, 0xB2, 0x30, 0x45, 0x55,
			0xA2, 0x67, 0xA2, 0x76, 0x4A, 0x74, 0xAD, 0x88,
			0x71, 0xE6, 0x3E, 0x13, 0x06, 0x30, 0x17, 0xE1,
			0xEF, 0xAC, 0x71, 0xFB, 0x43, 0xCD, 0xF6, 0xFA,
			0x0E, 0x4C, 0x4E, 0x16, 0xF6, 0x6A, 0x09, 0x86,
			0x6B, 0xEA, 0x47, 0x6C, 0x70, 0xE7, 0xAD, 0xA2,
			0xE0, 0xFD, 0x7F, 0xF0, 0x5C, 0x21, 0x53, 0x0F,
			0x28, 0xA1, 0x43, 0xE1, 0x06, 0xCA, 0x0B, 0x31,
			0x88, 0x22, 0xA6, 0xE6, 0x34, 0x5B, 0xE6, 0xCF,
			0x25, 0x81, 0x63, 0xFF, 0x78, 0x66, 0x85, 0x19,
			0xE2, 0x0A, 0x7E, 0x81, 0x8A, 0x17, 0x1A, 0x18,
			0x8A, 0x5F, 0x5D, 0x9E, 0x82, 0x13, 0x10, 0xB9,
			0xD3, 0xE6, 0x93, 0x1C, 0xE4, 0x2C, 0xCB, 0x49,
			0x1E, 0xB6, 0x36, 0x13, 0xBF, 0x28, 0xEE, 0xCC,
			0x49, 0xF5, 0x79, 0xFC, 0x20, 0x65, 0xBD, 0xE8,
			0xF0, 0x1B, 0x4E, 0xC0, 0x0D, 0x3E, 0x89, 0x91,
			0xCC, 0x64, 0x10, 0xC0, 0x2A, 0x2B, 0xA3, 0xFA,
			0x60, 0x3D, 0xC3, 0x52, 0x2F, 0x93, 0xDE, 0xB7,
			0x6E, 0x8A, 0xDF, 0x6C, 0x08, 0xCC, 0x8B, 0x3B,
			0xC8, 0x50, 0xEF, 0x58, 0x64, 0x9A, 0x3D, 0x16,
			0x70, 0x94, 0x11, 0xD8, 0x94, 0x2B, 0x70, 0x91,
			0x10, 0x70, 0x88, 0xF0, 0x40, 0x75, 0x9A, 0x2B,
			0x39, 0xA1, 0x27, 0x3F, 0x2E, 0x91, 0xEA, 0xA1,
			0xCC, 0x12, 0xC1, 0x7F, 0x73, 0x8C, 0x5C, 0x6B,
			0xFC, 0xC5, 0x6A, 0x1C, 0x05, 0xF1, 0x3D, 0x30,
			0x82, 0x4A, 0x65, 0x35, 0xCE, 0x80, 0x10, 0xBB,
			0x41, 0x94, 0xFB, 0x84, 0x80, 0x7B, 0x91, 0xC4,
			0x4D, 0xA3, 0x5F, 0xB9, 0xFB, 0xF9, 0xC9, 0x1D,
			0x4F, 0x99, 0x1C, 0x1F, 0x47, 0x44, 0x89, 0x0E,
			0xED, 0x6D, 0xB5, 0x85, 0x41, 0x94, 0xEF, 0xF9,
			0x2E, 0xA0, 0xC8, 0xCA, 0xFB, 0x44, 0x02, 0xC6,
			0xBF, 0x96, 0x87, 0x80, 0x1D, 0xEF, 0x2A, 0x81,
			0xAB, 0xB2, 0x56, 0xDF, 0x54, 0x8B, 0xAB, 0xAF,
			0xFE, 0x18, 0x8C, 0xAA, 0xD4, 0x00, 0x17, 0xBE,
			0xCF, 0x06, 0xE5, 0xA6, 0xBF, 0x5A, 0x52, 0x3B,
			0x4E, 0xF5, 0x65, 0x60, 0x95, 0xDE, 0x8A, 0x25,
			0x88, 0xA5, 0x24, 0x96, 0x29, 0x13, 0x0D, 0x19,
			0x45, 0x95, 0x91, 0x08, 0xD2, 0x9C, 0x4C, 0x34,
			0x42, 0xF0, 0xA5, 0x72, 0xEB, 0xFB, 0x5E, 0xAA,
			0x68, 0x80, 0x82, 0xAC, 0x34, 0xAD, 0x89, 0xF6,
			0xAF, 0x54, 0x82, 0xCF, 0x98, 0x8C, 0x75, 0x63,
			0x8D, 0xBD, 0x1C, 0x2A, 0xD7, 0x00, 0xA7, 0x8E,
			0xB9, 0x33, 0xB6, 0x3B, 0x95, 0x9A, 0x59, 0x1D,
			0x3F, 0x23, 0x6B, 0x18, 0xF8, 0x4F, 0x1A, 0x8D,
			0xC0, 0x26, 0x9F, 0x87, 0x61, 0xB6, 0xC6, 0x60,
			0x38, 0x22, 0x73, 0x1C, 0x99, 0x23, 0xEF, 0xD9,
			0xFD, 0xCB, 0x54, 0x74, 0xBB, 0x77, 0x14, 0xA3,
			0xA9, 0xE6, 0x7C, 0x7E, 0x03, 0x3A, 0x13, 0x6E,
			0x1D, 0x6F, 0x64, 0xB3, 0xFA, 0xFB, 0x52, 0xDE,
			0xDF, 0x08, 0xFB, 0x6F, 0xC5, 0xFA, 0x51, 0x6A,
			0x69, 0x29, 0x9B, 0x96, 0xE8, 0x16, 0xC8, 0xD1,
			0xE4, 0x19, 0xBD, 0x14, 0x74, 0x27, 0xE7, 0x10,
			0xF0, 0xC3, 0xE2, 0xA7, 0x60, 0x48, 0xBF, 0xDD,
			0xC4, 0x0D, 0xD0, 0xF2, 0xEF, 0xA6, 0xC9, 0xA2,
			0x73, 0xD1, 0xCF, 0x41, 0xE1, 0x3B, 0xE5, 0x49,
			0x91, 0x5D, 0x09, 0xFD, 0x1D, 0x95, 0x29, 0xDB,
			0x52, 0x48, 0xEB, 0xF5, 0x1D, 0xF8, 0x06, 0x67,
			0x75, 0xF2, 0x57, 0xA4, 0x20, 0x60, 0xEA, 0xB0,
			0x85, 0x93, 0x7C, 0xDD, 0x52, 0x01, 0xD4, 0x57,
			0xA8, 0x31, 0x2D, 0xF9, 0x0A, 0xD2, 0x2A, 0xD1,
			0x34, 0x18, 0x35, 0x16, 0xB6, 0x8B, 0x0F, 0x0B,
			0xCF, 0x50, 0x80, 0xFE, 0x76, 0xCC, 0x4F, 0x30,
			0x98, 0x19, 0x16, 0x3D, 0x01, 0xEA, 0x8D, 0x8A,
			0x3D, 0xDC, 0xFB, 0x1F, 0x77, 0x8D, 0x72, 0x76,
			0x02, 0x3C, 0x5D, 0xEE, 0x55, 0x13, 0x5B, 0x6E,
			0x5A, 0x2D, 0xD5, 0x77, 0xD7, 0x01, 0x84, 0x7D,
			0x21, 0x8C, 0xDD, 0x94, 0x7D, 0x31, 0x3D, 0xF0,
			0xE7, 0x28, 0xF5, 0x72, 0x36, 0x60, 0xE0, 0x59,
			0x5F, 0xFE, 0x38, 0xF8, 0x2F, 0xDB, 0x9E, 0x55,
			0x5A, 0xD6, 0xBA, 0x6C, 0x87, 0xF3, 0xC0, 0x76,
			0x5F, 0xA3, 0x0A, 0xC3, 0xA3, 0x8D, 0x0E, 0x52,
			0xA8, 0xDA, 0x26, 0x3A, 0xF9, 0x3E, 0x36, 0xB1,
			0x06, 0xF8, 0x20, 0x2D, 0x1C, 0x0B, 0x93, 0xBB,
			0xD3, 0x64, 0x77, 0xCE, 0x11, 0xFC, 0xA2, 0x0E,
			0x1B, 0x5B, 0x9E, 0x13, 0x9F, 0x20, 0x8B, 0xAA,
			0xCD, 0x72, 0xD7, 0xA6, 0xF3, 0x1E, 0x4F, 0x72,
			0xC6, 0x49, 0x0F, 0x7B, 0xF0, 0x4C, 0x61, 0x1F,
			0x43, 0x0D, 0x4F, 0x0D, 0x33, 0x13, 0xED, 0x63,
			0xE5, 0xDB, 0x71, 0xAB, 0xA4, 0x83, 0xEF, 0xDC,
			0x86, 0x9D, 0x4B, 0xBD, 0x1B, 0x8A, 0xFE, 0x39,
			0xA8, 0x8B, 0xBA, 0x4C, 0x85, 0x28, 0xFC, 0xB3,
			0x62, 0x85, 0xD2, 0xF0, 0x38, 0xD0, 0x4B, 0xA4,
			0xD1, 0x3B, 0xD4, 0xD0, 0x2C, 0x78, 0x6C, 0x6A,
			0xC2, 0x64, 0x2C, 0x31, 0x4A, 0xD8, 0x69, 0x24,
			0xED, 0x77, 0x7D, 0x68, 0x9A, 0xA1, 0x78, 0x81,
			0xD9, 0x7E, 0x6C, 0xFE, 0x0A, 0x0D, 0x76, 0xF7,
			0x4B, 0x58, 0xE7, 0xC9, 0xB5, 0x11, 0x07, 0x87,
			0x88, 0x6A, 0x9F, 0x3D, 0xE0, 0xEE, 0xCC, 0x60,
			0x6B, 0x6B, 0xE6, 0xB5, 0x54, 0x8B, 0x32, 0x1F,
			0x04, 0x1D, 0x0E, 0x9E, 0xFA, 0x6D, 0xB0, 0xE0,
			0x6D, 0xF9, 0x79, 0xB4, 0xAB, 0x5E, 0xDF, 0x23,
			0x7F, 0x95, 0xAD, 0x80, 0x17, 0x23, 0x90, 0x1F,
			0xF0, 0xC3, 0xD9, 0x2D, 0xAC, 0x3F, 0x63, 0xF5,
			0x77, 0xC5, 0x05, 0xAC, 0x06, 0xB6, 0xA1, 0xB4,
			0xA2, 0x40, 0xB3, 0x99, 0x34, 0x7D, 0x31, 0xD4,
			0xB1, 0xD4, 0xC1, 0xBB, 0x71, 0x1E, 0xDA, 0x3F,
			0xA9, 0x12, 0x68, 0xFA, 0x5B, 0x20, 0x24, 0x6D,
			0x4D, 0x72, 0x43, 0x18, 0xBF, 0x66, 0x71, 0x69,
			0x26, 0x7D, 0x77, 0x78, 0xF8, 0xE5, 0x20, 0xAE,
			0x56, 0x6C, 0x0F, 0x72, 0x94, 0x42, 0x85, 0x4F,
			0xE4, 0xFB, 0x32, 0x26, 0x1B, 0x1C, 0x6E, 0x0B,
			0xF0, 0xB8, 0x58, 0x00, 0xD2, 0x36, 0x64, 0xAD,
			0xA9, 0x00, 0xCE, 0x35, 0x3C, 0x88, 0x79, 0x94,
			0x0C, 0x0C, 0x9B, 0xF2, 0xDA, 0xBD, 0xCA, 0x93,
			0x37, 0x26, 0xD3, 0x08, 0x54, 0xD2, 0x0D, 0xBC,
			0x5D, 0x43, 0x5F, 0xCF, 0x28, 0xB5, 0xAA, 0x15,
			0x28, 0x46, 0x45, 0x6B, 0xE8, 0xDF, 0xE8, 0xCE,
			0x8F, 0xC0, 0x1A, 0x53, 0x63, 0x3B, 0x53, 0x75,
			0xDD, 0x43, 0x1F, 0x07, 0x0A, 0xD5, 0xA1, 0x2A,
			0x6E, 0x28, 0xE1, 0xD7, 0xD0, 0x09, 0xCF, 0x62,
			0xC1, 0x5F, 0x21, 0xDB, 0xC5, 0x40, 0x99, 0x48,
			0x87, 0x6E, 0x11, 0xF5, 0x5A, 0x4E, 0xBC, 0xF9,
			0xA8, 0x02, 0x7C, 0x47, 0x39, 0xA5, 0xD8, 0x52,
			0xB1, 0x80, 0xDC, 0xFE, 0x08, 0x4B, 0x5D, 0x09,
			0xDE, 0x06, 0xF3, 0x2A, 0xAD, 0x14, 0x76, 0x40,
			0x2F, 0x82, 0x28, 0x6A, 0xB6, 0x43, 0xEF, 0x71,
			0x63, 0xC2, 0x56, 0xEB, 0x3B, 0x4B, 0x52, 0x2F,
			0x93, 0xD3, 0x18, 0x3E, 0x18, 0xA8, 0xF7, 0x58,
			0xFC, 0x8B, 0x3D, 0x4D, 0x4B, 0x72, 0xBD, 0xF7,
			0x04, 0xC9, 0xB8, 0xD7, 0x6C, 0x8C, 0x67, 0xBB,
			0x4C, 0x9B, 0x57, 0xF7, 0x22, 0x4E, 0x41, 0xB6,
			0xFD, 0xD9, 0xF8, 0x41, 0x62, 0x0F, 0xFF, 0xAA,
			0xC6, 0x87, 0x95, 0xFF, 0xFD, 0x58, 0xD9, 0xB2,
			0xBA, 0x47, 0x61, 0x24, 0xEA, 0x92, 0x6E, 0x74,
			0xB3, 0xDA, 0xE5, 0x83, 0x99, 0x24, 0xB1, 0x71,
			0x2A, 0x33, 0xB2, 0xD5, 0x8F, 0xF0, 0x32, 0xCE,
			0x37, 0xCF, 0xC7, 0x1C, 0xE8, 0xDE, 0x46, 0x78,
			0x96, 0x97, 0xF6, 0x73, 0x90, 0xE5, 0x71, 0x05,
			0xEA, 0x0D, 0xC2, 0x1D, 0x9E, 0x43, 0x34, 0xBC,
			0x8F, 0x45, 0xE5, 0x08, 0xCA, 0x20, 0x0C, 0x84
		},
		.len = 2048
	},
	.auth_tag = {
		.data = {
			0xD0, 0x62, 0x1F, 0x20, 0x1C, 0xE8, 0xDD, 0x36,
			0x00, 0x74, 0xF6, 0xD7, 0xFD, 0x2C, 0xA0, 0xAF
		},
		.len = 16
	}
};

static const struct aead_test_data gcm_J0_test_case_1 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xfe, 0xff, 0xe9, 0x92, 0x86, 0x65, 0x73, 0x1c,
			0x6d, 0x6a, 0x8f, 0x94, 0x67, 0x30, 0x83, 0x08 },
		.len = 16
	},
	.iv = {
		.data = {
			0xca, 0xfe, 0xba, 0xbe, 0xfa, 0xce, 0xdb, 0xad,
			0xde, 0xca, 0xf8, 0x88, 0x00, 0x00, 0x00, 0x01 },
		.len = 0
	},
	.aad = {
		.data = gcm_aad_zero_text,
		.len = 0
	},
	.plaintext = {
		.data = {
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55 },
		.len = 64
	},
	.ciphertext = {
		.data = {
			0x42, 0x83, 0x1e, 0xc2, 0x21, 0x77, 0x74, 0x24,
			0x4b, 0x72, 0x21, 0xb7, 0x84, 0xd0, 0xd4, 0x9c,
			0xe3, 0xaa, 0x21, 0x2f, 0x2c, 0x02, 0xa4, 0xe0,
			0x35, 0xc1, 0x7e, 0x23, 0x29, 0xac, 0xa1, 0x2e,
			0x21, 0xd5, 0x14, 0xb2, 0x54, 0x66, 0x93, 0x1c,
			0x7d, 0x8f, 0x6a, 0x5a, 0xac, 0x84, 0xaa, 0x05,
			0x1b, 0xa3, 0x0b, 0x39, 0x6a, 0x0a, 0xac, 0x97,
			0x3d, 0x58, 0xe0, 0x91, 0x47, 0x3f, 0x59, 0x85
		},
		.len = 64
	},
	.auth_tag = {
		.data = {
			0x4d, 0x5c, 0x2a, 0xf3, 0x27, 0xcd, 0x64, 0xa6,
			0x2c, 0xf3, 0x5a, 0xbd, 0x2b, 0xa6, 0xfa, 0xb4 },
		.len = 16
	}
};

/** AES-GCM-192 Test Vectors */
static const struct aead_test_data gcm_test_case_192_1 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
		},
		.len = 24
	},
	.iv = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00
		},
		.len = 12
	},
	.aad = {
		.data = gcm_aad_zero_text,
		.len = 0
	},
	.plaintext = {
		.data = { 0x00 },
		.len = 0
	},
	.ciphertext = {
		.data = { 0x00 },
		.len = 0
	},
	.auth_tag = {
		.data = {
			0xCD, 0x33, 0xB2, 0x8A, 0xC7, 0x73, 0xF7, 0x4B,
			0xA0, 0x0E, 0xD1, 0xF3, 0x12, 0x57, 0x24, 0x35
		},
		.len = 16
	}
};

static const struct aead_test_data gcm_test_case_192_2 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
		},
		.len = 24
	},
	.iv = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00
		},
		.len = 12
	},
	.aad = {
		.data = gcm_aad_zero_text,
		.len = 0
	},
	.plaintext = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
		},
		.len = 16
	},
	.ciphertext = {
		.data = {
			0x98, 0xE7, 0x24, 0x7C, 0x07, 0xF0, 0xFE, 0x41,
			0x1C, 0x26, 0x7E, 0x43, 0x84, 0xB0, 0xF6, 0x00
		},
		.len = 16
	},
	.auth_tag = {
		.data = {
			0x2F, 0xF5, 0x8D, 0x80, 0x03, 0x39, 0x27, 0xAB,
			0x8E, 0xF4, 0xD4, 0x58, 0x75, 0x14, 0xF0, 0xFB

		},
		.len = 16
	}
};

static const struct aead_test_data gcm_test_case_192_3 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xFE, 0xFF, 0xE9, 0x92, 0x86, 0x65, 0x73, 0x1C,
			0x6D, 0x6A, 0x8F, 0x94, 0x67, 0x30, 0x83, 0x08,
			0xFE, 0xFF, 0xE9, 0x92, 0x86, 0x65, 0x73, 0x1C
		},
		.len = 24
	},
	.iv = {
		.data = {
			0xCA, 0xFE, 0xBA, 0xBE, 0xFA, 0xCE, 0xDB, 0xAD,
			0xDE, 0xCA, 0xF8, 0x88
		},
		.len = 12
	},
	.aad = {
		.data = gcm_aad_zero_text,
		.len = 0
	},
	.plaintext = {
		.data = {
			0xD9, 0x31, 0x32, 0x25, 0xF8, 0x84, 0x06, 0xE5,
			0xA5, 0x59, 0x09, 0xC5, 0xAF, 0xF5, 0x26, 0x9A,
			0x86, 0xA7, 0xA9, 0x53, 0x15, 0x34, 0xF7, 0xDA,
			0x2E, 0x4C, 0x30, 0x3D, 0x8A, 0x31, 0x8A, 0x72,
			0x1C, 0x3C, 0x0C, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2F, 0xCF, 0x0E, 0x24, 0x49, 0xA6, 0xB5, 0x25,
			0xB1, 0x6A, 0xED, 0xF5, 0xAA, 0x0D, 0xE6, 0x57,
			0xBA, 0x63, 0x7B, 0x39, 0x1A, 0xAF, 0xD2, 0x55
		},
		.len = 64
	},
	.ciphertext = {
		.data = {
			0x39, 0x80, 0xCA, 0x0B, 0x3C, 0x00, 0xE8, 0x41,
			0xEB, 0x06, 0xFA, 0xC4, 0x87, 0x2A, 0x27, 0x57,
			0x85, 0x9E, 0x1C, 0xEA, 0xA6, 0xEF, 0xD9, 0x84,
			0x62, 0x85, 0x93, 0xB4, 0x0C, 0xA1, 0xE1, 0x9C,
			0x7D, 0x77, 0x3D, 0x00, 0xC1, 0x44, 0xC5, 0x25,
			0xAC, 0x61, 0x9D, 0x18, 0xC8, 0x4A, 0x3F, 0x47,
			0x18, 0xE2, 0x44, 0x8B, 0x2F, 0xE3, 0x24, 0xD9,
			0xCC, 0xDA, 0x27, 0x10, 0xAC, 0xAD, 0xE2, 0x56
		},
		.len = 64
	},
	.auth_tag = {
		.data = {
			0x99, 0x24, 0xA7, 0xC8, 0x58, 0x73, 0x36, 0xBF,
			0xB1, 0x18, 0x02, 0x4D, 0xB8, 0x67, 0x4A, 0x14
		},
		.len = 16
	}
};

static const struct aead_test_data gcm_test_case_192_4 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xFE, 0xFF, 0xE9, 0x92, 0x86, 0x65, 0x73, 0x1C,
			0x6D, 0x6A, 0x8F, 0x94, 0x67, 0x30, 0x83, 0x08,
			0xFE, 0xFF, 0xE9, 0x92, 0x86, 0x65, 0x73, 0x1C
		},
		.len = 24
	},
	.iv = {
		.data = {
			0xCA, 0xFE, 0xBA, 0xBE, 0xFA, 0xCE, 0xDB, 0xAD,
			0xDE, 0xCA, 0xF8, 0x88
		},
		.len = 12
	},
	.aad = {
		.data = gcm_aad_zero_text,
		.len = 8
	},
	.plaintext = {
		.data = {
			0xD9, 0x31, 0x32, 0x25, 0xF8, 0x84, 0x06, 0xE5,
			0xA5, 0x59, 0x09, 0xC5, 0xAF, 0xF5, 0x26, 0x9A,
			0x86, 0xA7, 0xA9, 0x53, 0x15, 0x34, 0xF7, 0xDA,
			0x2E, 0x4C, 0x30, 0x3D, 0x8A, 0x31, 0x8A, 0x72,
			0x1C, 0x3C, 0x0C, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2F, 0xCF, 0x0E, 0x24, 0x49, 0xA6, 0xB5, 0x25,
			0xB1, 0x6A, 0xED, 0xF5, 0xAA, 0x0D, 0xE6, 0x57,
			0xBA, 0x63, 0x7B, 0x39
		},
		.len = 60
	},
	.ciphertext = {
		.data = {
			0x39, 0x80, 0xCA, 0x0B, 0x3C, 0x00, 0xE8, 0x41,
			0xEB, 0x06, 0xFA, 0xC4, 0x87, 0x2A, 0x27, 0x57,
			0x85, 0x9E, 0x1C, 0xEA, 0xA6, 0xEF, 0xD9, 0x84,
			0x62, 0x85, 0x93, 0xB4, 0x0C, 0xA1, 0xE1, 0x9C,
			0x7D, 0x77, 0x3D, 0x00, 0xC1, 0x44, 0xC5, 0x25,
			0xAC, 0x61, 0x9D, 0x18, 0xC8, 0x4A, 0x3F, 0x47,
			0x18, 0xE2, 0x44, 0x8B, 0x2F, 0xE3, 0x24, 0xD9,
			0xCC, 0xDA, 0x27, 0x10
		},
		.len = 60
	},
	.auth_tag = {
		.data = {
			0x57, 0x5F, 0x03, 0xA0, 0x8D, 0x8F, 0x40, 0x26,
			0xE5, 0x64, 0x1F, 0x5B, 0x5C, 0xC2, 0xFD, 0x4B
		},
		.len = 16
	}
};

static const struct aead_test_data gcm_test_case_192_5 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xFE, 0xFF, 0xE9, 0x92, 0x86, 0x65, 0x73, 0x1C,
			0x6D, 0x6A, 0x8F, 0x94, 0x67, 0x30, 0x83, 0x08,
			0xFE, 0xFF, 0xE9, 0x92, 0x86, 0x65, 0x73, 0x1C
		},
		.len = 24
	},
	.iv = {
		.data = {
			0xCA, 0xFE, 0xBA, 0xBE, 0xFA, 0xCE, 0xDB, 0xAD,
			0xDE, 0xCA, 0xF8, 0x88
		},
		.len = 12
	},
	.aad = {
		.data = gcm_aad_text,
		.len = 8
	},
	.plaintext = {
		.data = {
			0xD9, 0x31, 0x32, 0x25, 0xF8, 0x84, 0x06, 0xE5,
			0xA5, 0x59, 0x09, 0xC5, 0xAF, 0xF5, 0x26, 0x9A,
			0x86, 0xA7, 0xA9, 0x53, 0x15, 0x34, 0xF7, 0xDA,
			0x2E, 0x4C, 0x30, 0x3D, 0x8A, 0x31, 0x8A, 0x72,
			0x1C, 0x3C, 0x0C, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2F, 0xCF, 0x0E, 0x24, 0x49, 0xA6, 0xB5, 0x25,
			0xB1, 0x6A, 0xED, 0xF5, 0xAA, 0x0D, 0xE6, 0x57,
			0xBA, 0x63, 0x7B, 0x39
		},
		.len = 60
	},
	.ciphertext = {
		.data = {
			0x39, 0x80, 0xCA, 0x0B, 0x3C, 0x00, 0xE8, 0x41,
			0xEB, 0x06, 0xFA, 0xC4, 0x87, 0x2A, 0x27, 0x57,
			0x85, 0x9E, 0x1C, 0xEA, 0xA6, 0xEF, 0xD9, 0x84,
			0x62, 0x85, 0x93, 0xB4, 0x0C, 0xA1, 0xE1, 0x9C,
			0x7D, 0x77, 0x3D, 0x00, 0xC1, 0x44, 0xC5, 0x25,
			0xAC, 0x61, 0x9D, 0x18, 0xC8, 0x4A, 0x3F, 0x47,
			0x18, 0xE2, 0x44, 0x8B, 0x2F, 0xE3, 0x24, 0xD9,
			0xCC, 0xDA, 0x27, 0x10
		},
		.len = 60
	},
	.auth_tag = {
		.data = {
			0xB6, 0x35, 0x56, 0xE7, 0xBA, 0x46, 0xA3, 0x38,
			0xED, 0xAD, 0x79, 0x9F, 0xB3, 0x5B, 0x34, 0xA8
		},
		.len = 16
	}
};

static const struct aead_test_data gcm_test_case_192_6 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xFE, 0xFF, 0xE9, 0x92, 0x86, 0x65, 0x73, 0x1C,
			0x6D, 0x6A, 0x8F, 0x94, 0x67, 0x30, 0x83, 0x08,
			0xFE, 0xFF, 0xE9, 0x92, 0x86, 0x65, 0x73, 0x1C
		},
		.len = 24
	},
	.iv = {
		.data = {
			0xCA, 0xFE, 0xBA, 0xBE, 0xFA, 0xCE, 0xDB, 0xAD,
			0xDE, 0xCA, 0xF8, 0x88
		},
		.len = 12
	},
	.aad = {
		.data = gcm_aad_zero_text,
		.len = 12
	},
	.plaintext = {
		.data = {
			0xD9, 0x31, 0x32, 0x25, 0xF8, 0x84, 0x06, 0xE5,
			0xA5, 0x59, 0x09, 0xC5, 0xAF, 0xF5, 0x26, 0x9A,
			0x86, 0xA7, 0xA9, 0x53, 0x15, 0x34, 0xF7, 0xDA,
			0x2E, 0x4C, 0x30, 0x3D, 0x8A, 0x31, 0x8A, 0x72,
			0x1C, 0x3C, 0x0C, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2F, 0xCF, 0x0E, 0x24, 0x49, 0xA6, 0xB5, 0x25,
			0xB1, 0x6A, 0xED, 0xF5, 0xAA, 0x0D, 0xE6, 0x57,
			0xBA, 0x63, 0x7B, 0x39
		},
		.len = 60
	},
	.ciphertext = {
		.data = {
			0x39, 0x80, 0xCA, 0x0B, 0x3C, 0x00, 0xE8, 0x41,
			0xEB, 0x06, 0xFA, 0xC4, 0x87, 0x2A, 0x27, 0x57,
			0x85, 0x9E, 0x1C, 0xEA, 0xA6, 0xEF, 0xD9, 0x84,
			0x62, 0x85, 0x93, 0xB4, 0x0C, 0xA1, 0xE1, 0x9C,
			0x7D, 0x77, 0x3D, 0x00, 0xC1, 0x44, 0xC5, 0x25,
			0xAC, 0x61, 0x9D, 0x18, 0xC8, 0x4A, 0x3F, 0x47,
			0x18, 0xE2, 0x44, 0x8B, 0x2F, 0xE3, 0x24, 0xD9,
			0xCC, 0xDA, 0x27, 0x10
		},
		.len = 60
	},
	.auth_tag = {
		.data = {
			0xCA, 0x8A, 0x8A, 0x91, 0x5A, 0xF9, 0x76, 0xE3,
			0xFF, 0x2C, 0xE4, 0x7D, 0xE5, 0x62, 0x75, 0x18
		},
		.len = 16
	}
};

static const struct aead_test_data gcm_test_case_192_7 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xFE, 0xFF, 0xE9, 0x92, 0x86, 0x65, 0x73, 0x1C,
			0x6D, 0x6A, 0x8F, 0x94, 0x67, 0x30, 0x83, 0x08,
			0xFE, 0xFF, 0xE9, 0x92, 0x86, 0x65, 0x73, 0x1C
		},
		.len = 24
	},
	.iv = {
		.data = {
			0xCA, 0xFE, 0xBA, 0xBE, 0xFA, 0xCE, 0xDB, 0xAD,
			0xDE, 0xCA, 0xF8, 0x88
		},
		.len = 12
	},
	.aad = {
		.data = gcm_aad_text,
		.len = 12
	},
	.plaintext = {
		.data = {
			0xD9, 0x31, 0x32, 0x25, 0xF8, 0x84, 0x06, 0xE5,
			0xA5, 0x59, 0x09, 0xC5, 0xAF, 0xF5, 0x26, 0x9A,
			0x86, 0xA7, 0xA9, 0x53, 0x15, 0x34, 0xF7, 0xDA,
			0x2E, 0x4C, 0x30, 0x3D, 0x8A, 0x31, 0x8A, 0x72,
			0x1C, 0x3C, 0x0C, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2F, 0xCF, 0x0E, 0x24, 0x49, 0xA6, 0xB5, 0x25,
			0xB1, 0x6A, 0xED, 0xF5, 0xAA, 0x0D, 0xE6, 0x57,
			0xBA, 0x63, 0x7B, 0x39
		},
		.len = 60
	},
	.ciphertext = {
		.data = {
			0x39, 0x80, 0xCA, 0x0B, 0x3C, 0x00, 0xE8, 0x41,
			0xEB, 0x06, 0xFA, 0xC4, 0x87, 0x2A, 0x27, 0x57,
			0x85, 0x9E, 0x1C, 0xEA, 0xA6, 0xEF, 0xD9, 0x84,
			0x62, 0x85, 0x93, 0xB4, 0x0C, 0xA1, 0xE1, 0x9C,
			0x7D, 0x77, 0x3D, 0x00, 0xC1, 0x44, 0xC5, 0x25,
			0xAC, 0x61, 0x9D, 0x18, 0xC8, 0x4A, 0x3F, 0x47,
			0x18, 0xE2, 0x44, 0x8B, 0x2F, 0xE3, 0x24, 0xD9,
			0xCC, 0xDA, 0x27, 0x10
		},
		.len = 60
	},
	.auth_tag = {
		.data = {
			0xC2, 0xD8, 0x4C, 0x6B, 0xA8, 0x3B, 0xA5, 0x6B,
			0x18, 0x9F, 0xE6, 0xEF, 0x66, 0x24, 0xDD, 0xDA
		},
		.len = 16
	}
};

/** AES-GCM-256 Test Vectors */
static const struct aead_test_data gcm_test_case_256_1 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 },
		.len = 32
	},
	.iv = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00 },
		.len = 12
	},
	.aad = {
		.data = gcm_aad_zero_text,
		.len = 0
	},
	.plaintext = {
		.data = { 0x00 },
		.len = 0
	},
	.ciphertext = {
		.data = { 0x00 },
		.len = 0
	},
	.auth_tag = {
		.data = {
			0x53, 0x0F, 0x8A, 0xFB, 0xC7, 0x45, 0x36, 0xB9,
			0xA9, 0x63, 0xB4, 0xF1, 0xC4, 0xCB, 0x73, 0x8B },
		.len = 16
	}
};

static const struct aead_test_data gcm_test_case_256_2 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 },
		.len = 32
	},
	.iv = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00 },
		.len = 12
	},
	.aad = {
		.data = gcm_aad_zero_text,
		.len = 0
	},
	.plaintext = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 },
		.len = 16
	},
	.ciphertext = {
		.data = {
			0xCE, 0xA7, 0x40, 0x3D, 0x4D, 0x60, 0x6B, 0x6E,
			0x07, 0x4E, 0xC5, 0xD3, 0xBA, 0xF3, 0x9D, 0x18 },
		.len = 16
	},
	.auth_tag = {
		.data = {
			0xD0, 0xD1, 0xC8, 0xA7, 0x99, 0x99, 0x6B, 0xF0,
			0x26, 0x5B, 0x98, 0xB5, 0xD4, 0x8A, 0xB9, 0x19 },
		.len = 16
	}
};

static const struct aead_test_data gcm_test_case_256_3 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xfe, 0xff, 0xe9, 0x92, 0x86, 0x65, 0x73, 0x1c,
			0x6d, 0x6a, 0x8f, 0x94, 0x67, 0x30, 0x83, 0x08,
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a },
		.len = 32
	},
	.iv = {
		.data = {
			0xca, 0xfe, 0xba, 0xbe, 0xfa, 0xce, 0xdb, 0xad,
			0xde, 0xca, 0xf8, 0x88 },
		.len = 12
	},
	.aad = {
		.data = gcm_aad_zero_text,
		.len = 0
	},
	.plaintext = {
		.data = {
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55 },
		.len = 64
	},
	.ciphertext = {
		.data = {
			0x05, 0xA2, 0x39, 0xA5, 0xE1, 0x1A, 0x74, 0xEA,
			0x6B, 0x2A, 0x55, 0xF6, 0xD7, 0x88, 0x44, 0x7E,
			0x93, 0x7E, 0x23, 0x64, 0x8D, 0xF8, 0xD4, 0x04,
			0x3B, 0x40, 0xEF, 0x6D, 0x7C, 0x6B, 0xF3, 0xB9,
			0x50, 0x15, 0x97, 0x5D, 0xB8, 0x28, 0xA1, 0xD5,
			0x22, 0xDE, 0x36, 0x26, 0xD0, 0x6A, 0x7A, 0xC0,
			0xB5, 0x14, 0x36, 0xAF, 0x3A, 0xC6, 0x50, 0xAB,
			0xFA, 0x47, 0xC8, 0x2E, 0xF0, 0x68, 0xE1, 0x3E },
		.len = 64
	},
	.auth_tag = {
		.data = {
			0x64, 0xAF, 0x1D, 0xFB, 0xE8, 0x0D, 0x37, 0xD8,
			0x92, 0xC3, 0xB9, 0x1D, 0xD3, 0x08, 0xAB, 0xFC },
		.len = 16
	}
};

static const struct aead_test_data gcm_test_case_256_4 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xfe, 0xff, 0xe9, 0x92, 0x86, 0x65, 0x73, 0x1c,
			0x6d, 0x6a, 0x8f, 0x94, 0x67, 0x30, 0x83, 0x08,
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a },
		.len = 32
	},
	.iv = {
		.data = {
			0xca, 0xfe, 0xba, 0xbe, 0xfa, 0xce, 0xdb, 0xad,
			0xde, 0xca, 0xf8, 0x88 },
		.len = 12
	},
	.aad = {
		.data = gcm_aad_zero_text,
		.len = 8
	},
	.plaintext = {
		.data = {
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39 },
		.len = 60
	},
	.ciphertext = {
		.data = {
			0x05, 0xA2, 0x39, 0xA5, 0xE1, 0x1A, 0x74, 0xEA,
			0x6B, 0x2A, 0x55, 0xF6, 0xD7, 0x88, 0x44, 0x7E,
			0x93, 0x7E, 0x23, 0x64, 0x8D, 0xF8, 0xD4, 0x04,
			0x3B, 0x40, 0xEF, 0x6D, 0x7C, 0x6B, 0xF3, 0xB9,
			0x50, 0x15, 0x97, 0x5D, 0xB8, 0x28, 0xA1, 0xD5,
			0x22, 0xDE, 0x36, 0x26, 0xD0, 0x6A, 0x7A, 0xC0,
			0xB5, 0x14, 0x36, 0xAF, 0x3A, 0xC6, 0x50, 0xAB,
			0xFA, 0x47, 0xC8, 0x2E },
		.len = 60
	},
	.auth_tag = {
		.data = {
			0x63, 0x16, 0x91, 0xAE, 0x17, 0x05, 0x5E, 0xA6,
			0x6D, 0x0A, 0x51, 0xE2, 0x50, 0x21, 0x85, 0x4A },
		.len = 16
	}

};

static const struct aead_test_data gcm_test_case_256_5 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xfe, 0xff, 0xe9, 0x92, 0x86, 0x65, 0x73, 0x1c,
			0x6d, 0x6a, 0x8f, 0x94, 0x67, 0x30, 0x83, 0x08,
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a },
		.len = 32
	},
	.iv = {
		.data = {
			0xca, 0xfe, 0xba, 0xbe, 0xfa, 0xce, 0xdb, 0xad,
			0xde, 0xca, 0xf8, 0x88 },
		.len = 12
	},
	.aad = {
		.data = gcm_aad_text,
		.len = 8
	},
	.plaintext = {
		.data = {
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39 },
		.len = 60
	},
	.ciphertext = {
		.data = {
			0x05, 0xA2, 0x39, 0xA5, 0xE1, 0x1A, 0x74, 0xEA,
			0x6B, 0x2A, 0x55, 0xF6, 0xD7, 0x88, 0x44, 0x7E,
			0x93, 0x7E, 0x23, 0x64, 0x8D, 0xF8, 0xD4, 0x04,
			0x3B, 0x40, 0xEF, 0x6D, 0x7C, 0x6B, 0xF3, 0xB9,
			0x50, 0x15, 0x97, 0x5D, 0xB8, 0x28, 0xA1, 0xD5,
			0x22, 0xDE, 0x36, 0x26, 0xD0, 0x6A, 0x7A, 0xC0,
			0xB5, 0x14, 0x36, 0xAF, 0x3A, 0xC6, 0x50, 0xAB,
			0xFA, 0x47, 0xC8, 0x2E },
		.len = 60
	},
	.auth_tag = {
		.data = {
			0xA7, 0x99, 0xAC, 0xB8, 0x27, 0xDA, 0xB1, 0x82,
			0x79, 0xFD, 0x83, 0x73, 0x52, 0x4D, 0xDB, 0xF1 },
		.len = 16
	}

};

static const struct aead_test_data gcm_test_case_256_6 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xfe, 0xff, 0xe9, 0x92, 0x86, 0x65, 0x73, 0x1c,
			0x6d, 0x6a, 0x8f, 0x94, 0x67, 0x30, 0x83, 0x08,
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a },
		.len = 32
	},
	.iv = {
		.data = {
			0xca, 0xfe, 0xba, 0xbe, 0xfa, 0xce, 0xdb, 0xad,
			0xde, 0xca, 0xf8, 0x88 },
		.len = 12
	},
	.aad = {
		.data = gcm_aad_zero_text,
		.len = 12
	},
	.plaintext = {
		.data = {
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39 },
		.len = 60
	},
	.ciphertext = {
		.data = {
			0x05, 0xA2, 0x39, 0xA5, 0xE1, 0x1A, 0x74, 0xEA,
			0x6B, 0x2A, 0x55, 0xF6, 0xD7, 0x88, 0x44, 0x7E,
			0x93, 0x7E, 0x23, 0x64, 0x8D, 0xF8, 0xD4, 0x04,
			0x3B, 0x40, 0xEF, 0x6D, 0x7C, 0x6B, 0xF3, 0xB9,
			0x50, 0x15, 0x97, 0x5D, 0xB8, 0x28, 0xA1, 0xD5,
			0x22, 0xDE, 0x36, 0x26, 0xD0, 0x6A, 0x7A, 0xC0,
			0xB5, 0x14, 0x36, 0xAF, 0x3A, 0xC6, 0x50, 0xAB,
			0xFA, 0x47, 0xC8, 0x2E },
		.len = 60
	},
	.auth_tag = {
		.data = {
			0x5D, 0xA5, 0x0E, 0x53, 0x64, 0x7F, 0x3F, 0xAE,
			0x1A, 0x1F, 0xC0, 0xB0, 0xD8, 0xBE, 0xF2, 0x64 },
		.len = 16
	}
};

static const struct aead_test_data gcm_test_case_256_7 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xfe, 0xff, 0xe9, 0x92, 0x86, 0x65, 0x73, 0x1c,
			0x6d, 0x6a, 0x8f, 0x94, 0x67, 0x30, 0x83, 0x08,
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a },
		.len = 32
	},
	.iv = {
		.data = {
			0xca, 0xfe, 0xba, 0xbe, 0xfa, 0xce, 0xdb, 0xad,
			0xde, 0xca, 0xf8, 0x88 },
		.len = 12
	},
	.aad = {
		.data = gcm_aad_text,
		.len = 12
	},
	.plaintext = {
		.data = {
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39 },
		.len = 60
	},
	.ciphertext = {
		.data = {
			0x05, 0xA2, 0x39, 0xA5, 0xE1, 0x1A, 0x74, 0xEA,
			0x6B, 0x2A, 0x55, 0xF6, 0xD7, 0x88, 0x44, 0x7E,
			0x93, 0x7E, 0x23, 0x64, 0x8D, 0xF8, 0xD4, 0x04,
			0x3B, 0x40, 0xEF, 0x6D, 0x7C, 0x6B, 0xF3, 0xB9,
			0x50, 0x15, 0x97, 0x5D, 0xB8, 0x28, 0xA1, 0xD5,
			0x22, 0xDE, 0x36, 0x26, 0xD0, 0x6A, 0x7A, 0xC0,
			0xB5, 0x14, 0x36, 0xAF, 0x3A, 0xC6, 0x50, 0xAB,
			0xFA, 0x47, 0xC8, 0x2E },
		.len = 60
	},
	.auth_tag = {
		.data = {
			0x4E, 0xD0, 0x91, 0x95, 0x83, 0xA9, 0x38, 0x72,
			0x09, 0xA9, 0xCE, 0x5F, 0x89, 0x06, 0x4E, 0xC8 },
		.len = 16
	}
};

static const struct aead_test_data gcm_test_case_256_8 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xD8, 0xFD, 0x8F, 0x5A, 0x13, 0x7B, 0x05, 0x2C,
			0xA4, 0x64, 0x7A, 0xDD, 0x1E, 0x9A, 0x68, 0x33,
			0x04, 0x70, 0xE8, 0x1E, 0x42, 0x84, 0x64, 0xD2,
			0x23, 0xA1, 0x6A, 0x0A, 0x05, 0x7B, 0x90, 0xDE},
		.len = 32
	},
	.iv = {
		.data = {
			0x8D, 0xDF, 0xB8, 0x7F, 0xD0, 0x79, 0x77, 0x55,
			0xD5, 0x48, 0x03, 0x05},
		.len = 12
	},
	.aad = {
		.data = gcm_aad_64B_text,
		.len = 64
	},
	.plaintext = {
		.data = {
			0x4D, 0xBC, 0x2C, 0x7F, 0x25, 0x1F, 0x07, 0x25,
			0x54, 0x8C, 0x43, 0xDB, 0xD8, 0x06, 0x9F, 0xBF,
			0xCA, 0x60, 0xF4, 0xEF, 0x13, 0x87, 0xE8, 0x2F,
			0x4D, 0x9D, 0x1D, 0x87, 0x9F, 0x91, 0x79, 0x7E,
			0x3E, 0x98, 0xA3, 0x63, 0xC6, 0xFE, 0xDB, 0x35,
			0x96, 0x59, 0xB2, 0x0C, 0x80, 0x96, 0x70, 0x07,
			0x87, 0x42, 0xAB, 0x4F, 0x31, 0x73, 0xC4, 0xF9,
			0xB0, 0x1E, 0xF1, 0xBC, 0x7D, 0x45, 0xE5, 0xF3},
		.len = 64
	},
	.ciphertext = {
	    .data = {
			0x21, 0xFA, 0x59, 0x4F, 0x1F, 0x6B, 0x19, 0xC2,
			0x68, 0xBC, 0x05, 0x93, 0x4E, 0x48, 0x6C, 0x5B,
			0x0B, 0x7A, 0x43, 0xB7, 0x60, 0x8E, 0x00, 0xC4,
			0xAB, 0x14, 0x6B, 0xCC, 0xA1, 0x27, 0x6A, 0xDE,
			0x8E, 0xB6, 0x98, 0xBB, 0x4F, 0xD0, 0x6F, 0x30,
			0x0F, 0x04, 0xA8, 0x5B, 0xDC, 0xD8, 0xE8, 0x8A,
			0x73, 0xD9, 0xB8, 0x60, 0x7C, 0xE4, 0x32, 0x4C,
			0x3A, 0x0B, 0xC2, 0x82, 0xDA, 0x88, 0x17, 0x69},
	    .len = 64
	},
	.auth_tag = {
		.data = {
			0x3B, 0x80, 0x83, 0x72, 0xE5, 0x1B, 0x94, 0x15,
			0x75, 0xC8, 0x62, 0xBC, 0xA1, 0x66, 0x91, 0x45},
		.len = 16
	}
};

/** variable AAD AES-GCM-128 Test Vectors */
static const struct aead_test_data gcm_test_case_aad_1 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xfe, 0xff, 0xe9, 0x92, 0x86, 0x65, 0x73, 0x1c,
			0x6d, 0x6a, 0x8f, 0x94, 0x67, 0x30, 0x83, 0x08 },
		.len = 16
	},
	.iv = {
		.data = {
			0xca, 0xfe, 0xba, 0xbe, 0xfa, 0xce, 0xdb, 0xad,
			0xde, 0xca, 0xf8, 0x88 },
		.len = 12
	},
	.aad = {
		.data = gcm_aad_text,
		.len = GCM_LARGE_AAD_LENGTH
	},
	.plaintext = {
		.data = {
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55 },
		.len = 64
	},
	.ciphertext = {
		.data = {
			0x42, 0x83, 0x1E, 0xC2, 0x21, 0x77, 0x74, 0x24,
			0x4B, 0x72, 0x21, 0xB7, 0x84, 0xD0, 0xD4, 0x9C,
			0xE3, 0xAA, 0x21, 0x2F, 0x2C, 0x02, 0xA4, 0xE0,
			0x35, 0xC1, 0x7E, 0x23, 0x29, 0xAC, 0xA1, 0x2E,
			0x21, 0xD5, 0x14, 0xB2, 0x54, 0x66, 0x93, 0x1C,
			0x7D, 0x8F, 0x6A, 0x5A, 0xAC, 0x84, 0xAA, 0x05,
			0x1B, 0xA3, 0x0B, 0x39, 0x6A, 0x0A, 0xAC, 0x97,
			0x3D, 0x58, 0xE0, 0x91, 0x47, 0x3F, 0x59, 0x85
			},
		.len = 64
	},
	.auth_tag = {
		.data = {
			0xCA, 0x70, 0xAF, 0x96, 0xA8, 0x5D, 0x40, 0x47,
			0x0C, 0x3C, 0x48, 0xF5, 0xF0, 0xF5, 0xA5, 0x7D
			},
		.len = 16
	}
};

/** variable AAD AES-GCM-256 Test Vectors */
static const struct aead_test_data gcm_test_case_aad_2 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xfe, 0xff, 0xe9, 0x92, 0x86, 0x65, 0x73, 0x1c,
			0x6d, 0x6a, 0x8f, 0x94, 0x67, 0x30, 0x83, 0x08,
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a },
		.len = 32
	},
	.iv = {
		.data = {
			0xca, 0xfe, 0xba, 0xbe, 0xfa, 0xce, 0xdb, 0xad,
			0xde, 0xca, 0xf8, 0x88 },
		.len = 12
	},
	.aad = {
		.data = gcm_aad_text,
		.len = GCM_LARGE_AAD_LENGTH
	},
	.plaintext = {
		.data = {
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55 },
		.len = 64
	},
	.ciphertext = {
		.data = {
			0x05, 0xA2, 0x39, 0xA5, 0xE1, 0x1A, 0x74, 0xEA,
			0x6B, 0x2A, 0x55, 0xF6, 0xD7, 0x88, 0x44, 0x7E,
			0x93, 0x7E, 0x23, 0x64, 0x8D, 0xF8, 0xD4, 0x04,
			0x3B, 0x40, 0xEF, 0x6D, 0x7C, 0x6B, 0xF3, 0xB9,
			0x50, 0x15, 0x97, 0x5D, 0xB8, 0x28, 0xA1, 0xD5,
			0x22, 0xDE, 0x36, 0x26, 0xD0, 0x6A, 0x7A, 0xC0,
			0xB5, 0x14, 0x36, 0xAF, 0x3A, 0xC6, 0x50, 0xAB,
			0xFA, 0x47, 0xC8, 0x2E, 0xF0, 0x68, 0xE1, 0x3E
			},
		.len = 64
	},
	.auth_tag = {
		.data = {
			0xBA, 0x06, 0xDA, 0xA1, 0x91, 0xE1, 0xFE, 0x22,
			0x59, 0xDA, 0x67, 0xAF, 0x9D, 0xA5, 0x43, 0x94
			},
		.len = 16
	}
};

/** GMAC Test Vectors */
static uint8_t gmac_plaintext[GMAC_LARGE_PLAINTEXT_LENGTH] = {
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
			0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10,
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
			0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10,
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
			0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10,
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
			0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10,
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
			0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10,
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
			0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10,
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
			0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10,
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
			0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10,
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
			0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10,
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
			0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10
};

static const struct gmac_test_data gmac_test_case_1 = {
	.key = {
		.data = {
			0xfe, 0xff, 0xe9, 0x92, 0x86, 0x65, 0x73, 0x1c,
			0x6d, 0x6a, 0x8f, 0x94, 0x67, 0x30, 0x83, 0x08
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xca, 0xfe, 0xba, 0xbe, 0xfa, 0xce, 0xdb, 0xad,
			0xde, 0xca, 0xf8, 0x88 },
		.len = 12
	},
	.plaintext = {
		.data = gmac_plaintext,
		.len = 160
	},
	.gmac_tag = {
		.data = {
			0x4C, 0x0C, 0x4F, 0x47, 0x2D, 0x78, 0xF6, 0xD8,
			0x03, 0x53, 0x20, 0x2F, 0x1A, 0xDF, 0x90, 0xD0
		},
		.len = 16
	},
};

static const struct gmac_test_data gmac_test_case_2 = {
	.key = {
		.data = {
		    0xaa, 0x74, 0x0a, 0xbf, 0xad, 0xcd, 0xa7, 0x79,
		    0x22, 0x0d, 0x3b, 0x40, 0x6c, 0x5d, 0x7e, 0xc0,
		    0x9a, 0x77, 0xfe, 0x9d, 0x94, 0x10, 0x45, 0x39,
		},
		.len = 24
	},
	.iv = {
		.data = {
		    0xab, 0x22, 0x65, 0xb4, 0xc1, 0x68, 0x95,
		    0x55, 0x61, 0xf0, 0x43, 0x15, },
		.len = 12
	},
	.plaintext = {
		.data = gmac_plaintext,
		.len = 80
	},
	.gmac_tag = {
		.data = {
		    0xCF, 0x82, 0x80, 0x64, 0x02, 0x46, 0xF4, 0xFB,
		    0x33, 0xAE, 0x1D, 0x90, 0xEA, 0x48, 0x83, 0xDB
		},
		.len = 16
	},
};

static const struct gmac_test_data gmac_test_case_3 = {
	.key = {
		.data = {
		    0xb5, 0x48, 0xe4, 0x93, 0x4f, 0x5c, 0x64, 0xd3,
		    0xc0, 0xf0, 0xb7, 0x8f, 0x7b, 0x4d, 0x88, 0x24,
		    0xaa, 0xc4, 0x6b, 0x3c, 0x8d, 0x2c, 0xc3, 0x5e,
		    0xe4, 0xbf, 0xb2, 0x54, 0xe4, 0xfc, 0xba, 0xf7,
		},
		.len = 32
	},
	.iv = {
		.data = {
		    0x2e, 0xed, 0xe1, 0xdc, 0x64, 0x47, 0xc7,
		    0xaf, 0xc4, 0x41, 0x53, 0x58,
		},
		.len = 12
	},
	.plaintext = {
		.data = gmac_plaintext,
		.len = 65
	},
	.gmac_tag = {
		.data = {
			0x77, 0x46, 0x0D, 0x6F, 0xB1, 0x87, 0xDB, 0xA9,
			0x46, 0xAD, 0xCD, 0xFB, 0xB7, 0xF9, 0x13, 0xA1
		},
		.len = 16
	},
};

/******* GCM PERF VECTORS ***********/

struct cryptodev_perf_test_data {
	struct {
		uint8_t data[64];
		unsigned len;
	} key;

	struct {
		alignas(16) uint8_t data[64];
		unsigned len;
	} iv;

	struct {
		uint8_t data[64];
		unsigned len;
	} aad;

	struct {
		uint8_t data[2048];
		unsigned len;
	} plaintext;

	struct {
		uint8_t data[2048];
		unsigned len;
	} ciphertext;

	struct {
		uint8_t data[16];
		unsigned len;
	} auth_tag;

	struct {
		uint32_t size;
		uint8_t data[16];
		unsigned len;
	} auth_tags[7];

};

/* 2048B */
static const struct cryptodev_perf_test_data AES_GCM_128_12IV_0AAD = {
	.key = {
		.data = {
			0xfe, 0xff, 0xe9, 0x92, 0x86, 0x65, 0x73, 0x1c,
			0x6d, 0x6a, 0x8f, 0x94, 0x67, 0x30, 0x83, 0x08 },
		.len = 16
	},
	.iv = {
		.data = {
			0xca, 0xfe, 0xba, 0xbe, 0xfa, 0xce, 0xdb, 0xad,
			0xde, 0xca, 0xf8, 0x88 },
		.len = 12
	},
	.aad = {
		.data = { 0 },
		.len = 0
	},
	.plaintext = {
		.data = {
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
		    0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
		    0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
		    0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
		    0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
		    0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
		    0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
		    0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
		    0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55
		},
		.len = 2048
	},
	.ciphertext = {
		.data = {
		    0x42, 0x83, 0x1E, 0xC2, 0x21, 0x77, 0x74, 0x24,
		    0x4B, 0x72, 0x21, 0xB7, 0x84, 0xD0, 0xD4, 0x9C,
		    0xE3, 0xAA, 0x21, 0x2F, 0x2C, 0x02, 0xA4, 0xE0,
		    0x35, 0xC1, 0x7E, 0x23, 0x29, 0xAC, 0xA1, 0x2E,
		    0x21, 0xD5, 0x14, 0xB2, 0x54, 0x66, 0x93, 0x1C,
		    0x7D, 0x8F, 0x6A, 0x5A, 0xAC, 0x84, 0xAA, 0x05,
		    0x1B, 0xA3, 0x0B, 0x39, 0x6A, 0x0A, 0xAC, 0x97,
		    0x3D, 0x58, 0xE0, 0x91, 0x47, 0x3F, 0x59, 0x85,
		    0x04, 0x99, 0x55, 0xE1, 0x36, 0x76, 0xB7, 0x14,
		    0x1D, 0xF0, 0xF6, 0x8C, 0x65, 0xD5, 0xAD, 0xFB,
		    0x90, 0x7F, 0x5D, 0xA2, 0xD6, 0xFD, 0xD0, 0xE5,
		    0x0D, 0x9B, 0x68, 0x21, 0x49, 0x42, 0x6E, 0x13,
		    0xEC, 0x22, 0x50, 0x2A, 0x30, 0x47, 0x49, 0xA1,
		    0x7F, 0xC3, 0x09, 0xE0, 0x56, 0x91, 0xC4, 0x54,
		    0x70, 0xD7, 0x19, 0x40, 0xCA, 0x6B, 0x65, 0x27,
		    0x3E, 0xE9, 0xD1, 0x0F, 0x1C, 0xB5, 0x45, 0x0D,
		    0x27, 0xE7, 0xCF, 0x94, 0x10, 0xBF, 0xA2, 0xFA,
		    0x86, 0x20, 0x3F, 0x6E, 0xE9, 0x95, 0x03, 0x5A,
		    0x46, 0x11, 0x75, 0xD5, 0x37, 0x71, 0x7F, 0xE0,
		    0xBC, 0x9F, 0xC8, 0xE9, 0xB1, 0x08, 0x2C, 0x59,
		    0x6E, 0x51, 0x4A, 0x83, 0x38, 0xC1, 0xED, 0xE2,
		    0x2E, 0x88, 0x90, 0xA5, 0x7D, 0xA4, 0x93, 0x9A,
		    0x30, 0xD6, 0x96, 0x34, 0x0F, 0xC4, 0xD1, 0x7E,
		    0xC9, 0x8F, 0xC5, 0xBB, 0x80, 0x50, 0x85, 0x75,
		    0x7D, 0x82, 0x36, 0xDB, 0x62, 0x15, 0xAF, 0x4B,
		    0x0A, 0x9D, 0xCD, 0x64, 0x00, 0xAB, 0x88, 0x28,
		    0xA8, 0x35, 0x17, 0x70, 0x6F, 0x47, 0x44, 0xCD,
		    0x65, 0xAE, 0xD5, 0x05, 0x0A, 0xA8, 0x2F, 0x48,
		    0xAC, 0xA1, 0x72, 0x64, 0x1C, 0x7E, 0xD3, 0xF5,
		    0xD8, 0x4E, 0x73, 0x17, 0x0C, 0xE5, 0x9F, 0xB6,
		    0x00, 0xFA, 0xD7, 0x2C, 0x3D, 0x6A, 0x10, 0x47,
		    0x7C, 0xF2, 0x6B, 0x13, 0x10, 0x8A, 0x76, 0x39,
		    0xF8, 0x50, 0x33, 0xAC, 0x08, 0x1D, 0xA3, 0x48,
		    0xE1, 0xD0, 0x05, 0x49, 0xB7, 0x76, 0x03, 0x72,
		    0x07, 0xC5, 0xD3, 0x08, 0x79, 0x38, 0x66, 0xC1,
		    0x52, 0xAF, 0x83, 0xCD, 0xF3, 0x86, 0x62, 0xBF,
		    0x92, 0x24, 0x97, 0xBD, 0x5D, 0x7D, 0x81, 0x56,
		    0x4C, 0xF3, 0xD2, 0x60, 0xC2, 0xDE, 0x61, 0xC1,
		    0x39, 0x61, 0xDA, 0x07, 0x50, 0xC7, 0x98, 0x63,
		    0x7E, 0xDD, 0x54, 0xCA, 0xDE, 0x12, 0xD2, 0xA8,
		    0x19, 0x08, 0x6E, 0xF9, 0xFA, 0x6F, 0x58, 0x97,
		    0xD4, 0x0B, 0x5C, 0x5B, 0xE5, 0x30, 0xE5, 0x4C,
		    0x0E, 0x16, 0x87, 0xF0, 0x2C, 0xCB, 0x53, 0xB8,
		    0x0C, 0xE5, 0xDF, 0x16, 0x7B, 0xE8, 0xC2, 0xCF,
		    0xCC, 0xFF, 0x51, 0x24, 0xC1, 0xDD, 0x59, 0x9C,
		    0xA7, 0x56, 0x03, 0xB9, 0x0A, 0x37, 0xA2, 0xAC,
		    0x28, 0x8B, 0xEB, 0x51, 0x4E, 0xF1, 0xAE, 0xB5,
		    0xC8, 0xB5, 0xCB, 0x8D, 0x23, 0xF6, 0x24, 0x2D,
		    0xF6, 0x59, 0x62, 0xC0, 0xCB, 0xD3, 0x18, 0xE4,
		    0xB7, 0x73, 0xEF, 0xDB, 0x13, 0x9A, 0xF5, 0xD3,
		    0xD5, 0x61, 0x01, 0x14, 0xA5, 0xE5, 0x0D, 0x27,
		    0xC9, 0xA5, 0x08, 0x1C, 0x60, 0xBA, 0x73, 0xFF,
		    0xA9, 0xE0, 0x27, 0x86, 0x3F, 0xF7, 0x15, 0x03,
		    0x69, 0xA7, 0x2B, 0x57, 0xAC, 0xA6, 0x70, 0x55,
		    0xE9, 0xB5, 0x3F, 0xEB, 0x6F, 0xCE, 0x8A, 0xA1,
		    0x9D, 0x8B, 0x84, 0xF1, 0x7C, 0xD0, 0x35, 0x21,
		    0x91, 0x3D, 0x3D, 0x6E, 0x83, 0xFC, 0x45, 0x36,
		    0x93, 0xDA, 0x66, 0xDF, 0x1A, 0x59, 0x22, 0xA5,
		    0xC4, 0x99, 0x9B, 0xF8, 0x48, 0x9A, 0x50, 0x09,
		    0xAB, 0xAE, 0x56, 0xB6, 0x49, 0x02, 0x3E, 0x90,
		    0xB6, 0x07, 0x7E, 0xA7, 0x6A, 0x0A, 0xB5, 0x85,
		    0x31, 0x0D, 0x84, 0xD4, 0x01, 0xE4, 0x48, 0x63,
		    0xF3, 0xC1, 0x54, 0x65, 0xA6, 0x4C, 0x8B, 0x33,
		    0xF9, 0x70, 0x59, 0x3B, 0xA6, 0xF6, 0x2B, 0x66,
		    0xC5, 0xD2, 0xEB, 0xAB, 0x67, 0xD2, 0xE3, 0x78,
		    0xA9, 0x1A, 0x4C, 0x99, 0xA9, 0xA6, 0xCA, 0xF7,
		    0x65, 0xF0, 0x48, 0xF8, 0x2A, 0xEA, 0x96, 0x9F,
		    0xC4, 0x50, 0x9A, 0x0C, 0xB6, 0x0D, 0x8A, 0x2F,
		    0xC3, 0x99, 0x4E, 0xA0, 0x06, 0x4D, 0xAB, 0x25,
		    0x2E, 0x44, 0x47, 0xB6, 0x98, 0xF1, 0x2C, 0x96,
		    0x54, 0x51, 0x12, 0x41, 0x0D, 0xEF, 0x32, 0x9A,
		    0x4A, 0xBD, 0xA2, 0x26, 0x53, 0xA8, 0xFD, 0x8B,
		    0x6C, 0x95, 0x0A, 0x1A, 0x96, 0xEF, 0x3C, 0x85,
		    0x34, 0x4E, 0x25, 0x9E, 0x1C, 0x67, 0x33, 0x8A,
		    0xFF, 0x6D, 0x98, 0x93, 0x3D, 0x3F, 0x49, 0x6B,
		    0xBF, 0x7C, 0x4F, 0x63, 0x5D, 0x62, 0x64, 0x67,
		    0x0D, 0x07, 0x7F, 0x24, 0x4A, 0x23, 0xBC, 0x35,
		    0xE0, 0x92, 0x6F, 0x51, 0xE7, 0x25, 0x97, 0xB9,
		    0x14, 0x35, 0x2B, 0x48, 0xAC, 0x6F, 0x54, 0xDF,
		    0xF2, 0xB4, 0xB0, 0xE0, 0xD3, 0x28, 0x0D, 0x66,
		    0x46, 0x28, 0x0A, 0x16, 0x9C, 0x87, 0x73, 0xB7,
		    0x9C, 0x2B, 0xB5, 0x43, 0xC9, 0x46, 0xB9, 0x1F,
		    0x5F, 0x3C, 0x45, 0x03, 0x4B, 0xBF, 0x44, 0x4D,
		    0xE1, 0x44, 0xDA, 0x54, 0xC5, 0x32, 0x3A, 0xFA,
		    0x21, 0x5C, 0xAD, 0xD5, 0x1E, 0x1B, 0x54, 0x7C,
		    0x9F, 0xEA, 0x92, 0x8C, 0xEA, 0x69, 0xC0, 0xCE,
		    0xDA, 0x09, 0xAD, 0x95, 0xA0, 0x8E, 0x0B, 0x8E,
		    0x10, 0x4F, 0x5B, 0x8F, 0xB8, 0x2D, 0xAC, 0x4C,
		    0x94, 0x4B, 0x7C, 0x1E, 0xF1, 0x53, 0x20, 0x9B,
		    0xD6, 0xC4, 0x92, 0x4C, 0x7F, 0xFB, 0x8B, 0x8E,
		    0x40, 0x2F, 0x24, 0xA3, 0x4E, 0x46, 0x64, 0xF4,
		    0xC6, 0x35, 0x0F, 0xC7, 0x40, 0x55, 0x43, 0xAF,
		    0x7E, 0x91, 0x76, 0x48, 0x6F, 0x97, 0x7A, 0xF8,
		    0x32, 0x1E, 0xD3, 0x5B, 0xBC, 0x19, 0xB5, 0x48,
		    0xFA, 0x4F, 0x52, 0x77, 0x5B, 0x9E, 0xA2, 0xC8,
		    0x9A, 0x83, 0x30, 0x8D, 0x9F, 0x0B, 0x6F, 0xA8,
		    0x2E, 0x84, 0xCC, 0xC1, 0x50, 0x96, 0x46, 0xAE,
		    0x73, 0x91, 0x7D, 0xCD, 0x88, 0xAB, 0x67, 0x3F,
		    0x66, 0x3A, 0x8D, 0xB1, 0x89, 0x07, 0x93, 0xDB,
		    0x42, 0x22, 0xDC, 0x13, 0xBD, 0xCD, 0xBB, 0x12,
		    0x8D, 0x88, 0x44, 0x13, 0x22, 0x52, 0x81, 0xDC,
		    0xEF, 0xA1, 0xE4, 0xA3, 0xA7, 0xBA, 0xEE, 0x98,
		    0x79, 0x45, 0x29, 0x05, 0x65, 0x3D, 0xDC, 0xAF,
		    0xA1, 0x37, 0x29, 0xFD, 0x05, 0xD1, 0x3A, 0xF7,
		    0x32, 0x1D, 0x02, 0xEC, 0x28, 0x1E, 0x0F, 0x96,
		    0xF3, 0x21, 0x19, 0x5F, 0x49, 0xB9, 0xEA, 0x9A,
		    0xAD, 0x34, 0x58, 0xD1, 0xD9, 0xB1, 0x7D, 0xD2,
		    0xEA, 0xED, 0x74, 0xE8, 0x25, 0x9A, 0x7B, 0xC5,
		    0xC8, 0xD8, 0x76, 0xB6, 0xBC, 0x0B, 0x78, 0xCE,
		    0xD9, 0xA6, 0xBB, 0x2F, 0x79, 0xA4, 0x45, 0x05,
		    0x55, 0x6E, 0x20, 0x84, 0xEB, 0xC8, 0x70, 0xB0,
		    0x3A, 0x2D, 0x06, 0x98, 0x29, 0x10, 0xB8, 0xC5,
		    0xE9, 0xE4, 0xB6, 0xDE, 0x97, 0x9A, 0x0D, 0x8C,
		    0xB6, 0x22, 0x16, 0x59, 0xAB, 0xB5, 0xD7, 0x14,
		    0xAB, 0x08, 0x02, 0x27, 0x7B, 0xF7, 0x0E, 0xAC,
		    0xC5, 0xAC, 0x4D, 0x7F, 0xE5, 0x65, 0x51, 0x40,
		    0x44, 0x92, 0xB1, 0x6A, 0xB7, 0x00, 0x76, 0x89,
		    0x6E, 0x08, 0x5F, 0x45, 0x2B, 0x53, 0x86, 0x86,
		    0xA7, 0x85, 0xBC, 0x62, 0xAC, 0xAA, 0x82, 0x73,
		    0x0A, 0xEB, 0x35, 0x16, 0x95, 0x26, 0xAB, 0x9E,
		    0xE9, 0x64, 0x53, 0x99, 0x08, 0x31, 0xF5, 0x6B,
		    0x1F, 0xFE, 0x47, 0x4B, 0x09, 0x33, 0x4F, 0xBF,
		    0x1F, 0x0B, 0x4C, 0xB2, 0xB4, 0xA4, 0x17, 0xA9,
		    0xAD, 0xC5, 0x62, 0x7C, 0xF1, 0x1B, 0xAE, 0x46,
		    0xD3, 0xAC, 0xFD, 0x43, 0xFE, 0x79, 0xD0, 0x58,
		    0x2F, 0x6C, 0x9F, 0xD0, 0x65, 0xA4, 0x64, 0x03,
		    0xAF, 0x73, 0x46, 0x75, 0x7D, 0x49, 0x1B, 0x4C,
		    0xFA, 0x49, 0xD8, 0x9A, 0xCC, 0x59, 0xC6, 0xC7,
		    0xA1, 0x05, 0xC2, 0x32, 0xC8, 0x6C, 0x50, 0xA8,
		    0x06, 0x58, 0xBE, 0x6C, 0x7D, 0x22, 0xD6, 0x0D,
		    0x74, 0x40, 0xCE, 0xD6, 0x64, 0xD6, 0x47, 0xD0,
		    0xBF, 0xF1, 0x5C, 0x54, 0xF9, 0x06, 0x3F, 0x3D,
		    0x86, 0xBA, 0xF2, 0x0F, 0x5E, 0x2C, 0x01, 0xCC,
		    0xD9, 0xC7, 0xB1, 0x4A, 0xB3, 0xD7, 0x26, 0xCC,
		    0xC3, 0x7A, 0x74, 0x2C, 0xE1, 0x22, 0x65, 0xA0,
		    0x5B, 0xCA, 0xF4, 0xE1, 0x7D, 0xE1, 0x56, 0xFD,
		    0x94, 0x10, 0xC6, 0xA1, 0x4A, 0xE8, 0x6B, 0x34,
		    0x4E, 0x71, 0x60, 0x77, 0x0F, 0x03, 0xDD, 0xFF,
		    0xC8, 0x59, 0x54, 0x6C, 0xD4, 0x4A, 0x55, 0x24,
		    0x35, 0x21, 0x60, 0x73, 0xDF, 0x6F, 0xE7, 0x3C,
		    0xC2, 0xF0, 0xDA, 0xA9, 0xE5, 0x8C, 0xAC, 0xB6,
		    0xFD, 0x2E, 0xF7, 0xA0, 0x18, 0xA7, 0x55, 0x47,
		    0xD1, 0xCB, 0x9E, 0xAA, 0x58, 0x54, 0x3B, 0x37,
		    0x18, 0xB5, 0xC1, 0xBB, 0x41, 0x59, 0xE4, 0x28,
		    0x4A, 0x13, 0x90, 0x6A, 0xF7, 0xD1, 0xB3, 0x71,
		    0xB6, 0x6E, 0xF6, 0x5D, 0x2E, 0x0E, 0x6C, 0x4A,
		    0x7B, 0xF7, 0xB6, 0x21, 0xD4, 0xFC, 0x47, 0x8C,
		    0x9B, 0x0A, 0x90, 0xAC, 0x11, 0x52, 0x86, 0x07,
		    0x24, 0xDA, 0xA9, 0x49, 0x50, 0xD9, 0xDC, 0xE2,
		    0x19, 0x87, 0x73, 0x88, 0xC3, 0xE4, 0xED, 0xC9,
		    0x1C, 0xA8, 0x7E, 0x39, 0x48, 0x91, 0x10, 0xAB,
		    0xFC, 0x3C, 0x1E, 0xEE, 0x08, 0xA1, 0xB9, 0xB2,
		    0x02, 0x57, 0xB1, 0xD1, 0x35, 0x5E, 0x3D, 0x94,
		    0xFB, 0x36, 0x27, 0x1A, 0x0E, 0x75, 0xFC, 0xBC,
		    0xDB, 0xF3, 0xF5, 0x7C, 0x08, 0x39, 0xAA, 0xF4,
		    0x2E, 0xEE, 0xCF, 0xCD, 0x2D, 0x70, 0xB8, 0x84,
		    0xE6, 0x22, 0x5C, 0xC0, 0xB9, 0x33, 0xCB, 0x97,
		    0xA1, 0xA3, 0xEE, 0x93, 0x71, 0xCF, 0xC9, 0x21,
		    0x31, 0x7A, 0xEC, 0xE7, 0x70, 0xF2, 0xAA, 0x91,
		    0xAA, 0x48, 0xAD, 0xAC, 0x03, 0xB1, 0x26, 0x52,
		    0xBC, 0x65, 0x22, 0xA1, 0x09, 0x3D, 0xAB, 0x16,
		    0x08, 0xBF, 0xCF, 0x3F, 0x59, 0x08, 0x6F, 0x68,
		    0xEB, 0x8A, 0xB3, 0xCF, 0x77, 0x82, 0xFB, 0x25,
		    0x78, 0x16, 0x4C, 0xDB, 0x72, 0xF5, 0xCF, 0x79,
		    0x71, 0xE4, 0x4E, 0x23, 0x15, 0x7F, 0x1E, 0xA8,
		    0x3E, 0xC0, 0x59, 0x91, 0x20, 0xAE, 0x2C, 0x1D,
		    0x90, 0xC8, 0x49, 0x42, 0x48, 0x29, 0x82, 0x66,
		    0x68, 0x49, 0x73, 0xDA, 0xE4, 0x28, 0xCD, 0x7B,
		    0x4D, 0xE4, 0x23, 0x34, 0xB9, 0xE1, 0xB4, 0x42,
		    0x67, 0x22, 0x5B, 0xEE, 0xE6, 0x74, 0x32, 0x6F,
		    0x21, 0x9F, 0x97, 0x46, 0x03, 0xE1, 0xC9, 0x7A,
		    0x14, 0x27, 0x30, 0xE1, 0xB2, 0x34, 0xE6, 0xAF,
		    0x7B, 0xAA, 0xDD, 0x89, 0x04, 0x30, 0xD6, 0x78,
		    0x0B, 0x3D, 0xC3, 0x69, 0xB0, 0x67, 0x4F, 0x4E,
		    0x12, 0x21, 0x93, 0x2D, 0x79, 0xDD, 0x8B, 0xDB,
		    0xEA, 0x90, 0x66, 0x54, 0xA8, 0x05, 0xF2, 0xE4,
		    0x59, 0x8A, 0x96, 0x52, 0x30, 0xF0, 0x4E, 0x9A,
		    0xE5, 0xD8, 0x72, 0x1C, 0x3B, 0x63, 0x02, 0xB9,
		    0xC7, 0xA1, 0xDA, 0xC8, 0x6C, 0x48, 0xE0, 0xDE,
		    0x59, 0x64, 0x89, 0x2C, 0xF9, 0xC8, 0x3B, 0x00,
		    0xEC, 0xF2, 0x68, 0x51, 0x67, 0x05, 0x85, 0xAF,
		    0xB8, 0xD5, 0x65, 0xEE, 0x73, 0x26, 0x88, 0xFB,
		    0xA9, 0xD6, 0x6C, 0x68, 0x9D, 0x9F, 0x23, 0x6A,
		    0x10, 0x24, 0x82, 0xB2, 0xB7, 0x40, 0x19, 0x3E,
		    0x6F, 0xA2, 0xD5, 0x2C, 0x6E, 0x8D, 0xE9, 0x33,
		    0x6E, 0x24, 0x94, 0x05, 0xE9, 0x2D, 0xD9, 0x3A,
		    0x8C, 0xE5, 0xCC, 0x1D, 0x3F, 0xB8, 0x71, 0xA8,
		    0x98, 0x33, 0xBB, 0x1A, 0xAC, 0x41, 0x0A, 0x04,
		    0xFE, 0x4D, 0x46, 0x17, 0x8A, 0xCB, 0xF3, 0x4B,
		    0x97, 0x02, 0xCC, 0x9D, 0x11, 0xF1, 0xBC, 0xA9,
		    0xC1, 0xD1, 0xB6, 0xD6, 0x7B, 0x5F, 0x9D, 0x22,
		    0x86, 0x71, 0xEC, 0x42, 0x53, 0xB7, 0x85, 0x30,
		    0xAF, 0x1D, 0x01, 0xA7, 0xBF, 0x72, 0xC2, 0xC6,
		    0xC9, 0xB8, 0xD8, 0xC7, 0xE9, 0xC4, 0xBA, 0xC5,
		    0xB1, 0x8A, 0xB8, 0x62, 0xBF, 0x75, 0x75, 0x69,
		    0xF8, 0x8D, 0x7E, 0xD9, 0xD2, 0x28, 0xB5, 0x40,
		    0xCE, 0xCB, 0xB8, 0x74, 0x31, 0x40, 0x7B, 0x0D,
		    0x73, 0x98, 0x99, 0x12, 0xB7, 0x75, 0x3E, 0xBC,
		    0xAE, 0x48, 0xCA, 0xA9, 0x1E, 0xA7, 0x95, 0x31,
		    0x87, 0x0F, 0x14, 0x52, 0xB6, 0x8E, 0x42, 0x50,
		    0xB2, 0x76, 0x75, 0xD8, 0x7E, 0x66, 0x23, 0x13,
		    0x8B, 0x29, 0xAA, 0x13, 0xCA, 0x8A, 0xD8, 0x9B,
		    0x7B, 0x38, 0xD2, 0xE8, 0x67, 0xD1, 0x89, 0x25,
		    0x9C, 0x63, 0x2F, 0xC3, 0x26, 0xC7, 0x74, 0x83,
		    0x05, 0xED, 0x67, 0x02, 0x85, 0xAD, 0x1D, 0x0E,
		    0xA9, 0xD6, 0xE1, 0xC7, 0x39, 0xA0, 0x6E, 0x72,
		    0xCE, 0x56, 0x6C, 0xB8, 0x4A, 0xDE, 0x11, 0xA2,
		    0xBF, 0xC1, 0x84, 0x98, 0x8F, 0xCA, 0x79, 0x74,
		    0xCA, 0x9F, 0x45, 0x16, 0xBC, 0xB1, 0xF4, 0x03,
		    0x76, 0x6E, 0xD5, 0x46, 0x60, 0xD7, 0x1D, 0xF0,
		    0x87, 0x29, 0x63, 0x07, 0x06, 0xB9, 0xC2, 0x69,
		    0x6D, 0xF9, 0x4B, 0x30, 0x96, 0x83, 0xB8, 0xC5,
		    0xBE, 0x3A, 0xBA, 0xD0, 0x3E, 0x2B, 0x04, 0x16,
		    0x6A, 0x00, 0x3B, 0x1A, 0x8E, 0xF8, 0xF6, 0x21,
		    0x01, 0xD6, 0x08, 0x41, 0x74, 0xA2, 0xFC, 0x36,
		    0xED, 0x11, 0x51, 0x5A, 0x4A, 0x21, 0x1A, 0x03,
		    0x11, 0x95, 0x11, 0xF6, 0x73, 0x38, 0x67, 0xFC,
		    0xF1, 0x2B, 0x22, 0x54, 0x65, 0x40, 0x7D, 0x8C,
		    0x13, 0xC4, 0x46, 0x87, 0x09, 0x2B, 0xB5, 0xA1,
		    0x82, 0x49, 0x46, 0x56, 0xF5, 0x5F, 0xF1, 0x04,
		    0xD8, 0x6F, 0xDB, 0x38, 0xAD, 0xF4, 0x1A, 0xA3,
		    0xFF, 0x7C, 0xC7, 0xA6, 0xAF, 0x87, 0x5C, 0x8C,
		    0xEA, 0x3C, 0x9D, 0x7A, 0x4A, 0xD8, 0xA8, 0x66,
		    0xDB, 0xBF, 0x12, 0x58, 0x98, 0x8E, 0xBA, 0x6F,
		    0xAF, 0x20, 0xDA, 0xEE, 0x82, 0x34, 0x2F, 0x33,
		    0x88, 0x98, 0xBA, 0xB2, 0x54, 0x7F, 0x9E, 0x63,
		    0x19, 0x6C, 0x7D, 0xCE, 0x85, 0xF8, 0xB6, 0x77,
		    0xCB, 0x38, 0x1F, 0xB1, 0x79, 0xBD, 0xED, 0x32,
		    0xE3, 0xB9, 0x40, 0xEF, 0x3E, 0x6C, 0x29, 0x88,
		    0x70, 0x99, 0x47, 0xA6, 0x4A, 0x1C, 0xCC, 0x0B,
		    0x9B, 0x72, 0xA9, 0x29, 0x83, 0x4C, 0xDE, 0x4F,
		    0x65, 0x4E, 0xCE, 0xBD, 0xFA, 0x76, 0x8D, 0xA6,
		    0x1A, 0xD8, 0x66, 0xFE, 0xA4, 0x2A, 0x61, 0x50,
		    0xEE, 0x15, 0xF1, 0xF0, 0x9D, 0xFF, 0xEC, 0xEE,
		    0x00, 0x03, 0xFE, 0xAC, 0x53, 0x02, 0xCC, 0x87,
		    0xB1, 0xA2, 0xD8, 0x34, 0x2C, 0xEC, 0xA6, 0x4C,
		    0x02, 0xC0, 0xC1, 0x72, 0xD6, 0x54, 0x35, 0x24,
		    0x25, 0x8B, 0xEC, 0xDA, 0x47, 0x5F, 0x5D, 0x7E,
		    0xD8, 0x01, 0x51, 0xDD, 0x8F, 0xB4, 0x48, 0xDD,
		    0x94, 0x99, 0x95, 0x77, 0xB3, 0x42, 0x14, 0xEB,
		    0x26, 0x61, 0xE9, 0x22, 0xE3, 0x07, 0x73, 0xFB,
		    0xEF, 0x38, 0x55, 0x35, 0x8F, 0xCC, 0x30, 0x1E,
		    0x38, 0xE0, 0x35, 0xF4, 0x9A, 0x7C, 0xCF, 0x38,
		    0x0B, 0x9E, 0xF4, 0x88, 0x4A, 0xEA, 0xF2, 0x67,
		    0x9F, 0x61, 0x40, 0x34, 0x09, 0xDC, 0xBF, 0xFB,
		    0x22, 0x27, 0x04, 0x8B, 0x8D, 0x85, 0x7F, 0xB2,
		    0x29, 0x62, 0x25, 0x73, 0x7F, 0x46, 0x2E, 0xA3,
		    0x8E, 0xAF, 0xEC, 0x55, 0x98, 0x1A, 0xEE, 0x29,
		    0xA0, 0x1A, 0x5F, 0xFE, 0x5D, 0xA5, 0x76, 0x93,
		    0xAB, 0x57, 0x56, 0xEA, 0xDB, 0x39, 0xAC, 0x48,
		    0xBE, 0x95, 0x92, 0x2B, 0xC6, 0xE1, 0x2F, 0x36,
		    0x4B, 0x08, 0x01, 0x90, 0x50, 0xD8, 0xFA, 0xF9,
		    0x94, 0x4E, 0x76, 0x9B, 0x72, 0x59, 0xC2, 0x2F,
		    0x61, 0x04, 0x0A, 0x9E, 0x28, 0xE5, 0x24, 0x1E,
		    0x79, 0xCF, 0x8D, 0xB6, 0x52, 0xA7, 0x79, 0x5F,
		    0x44, 0x98, 0xD5, 0x0E, 0x6E, 0x4B, 0x64, 0x9B,
		},
		.len = 2048
	},
	.auth_tags[0] = {
		.size = 64,
		.data = { 0x4d, 0x5c, 0x2a, 0xf3, 0x27, 0xcd, 0x64, 0xa6,
			0x2c, 0xf3, 0x5a, 0xbd, 0x2b, 0xa6, 0xfa, 0xb4 },
		.len = 16
	},
	.auth_tags[1] = {
		.size = 128,
		.data = { 0xE9, 0xA9, 0x75, 0xB6, 0xEF, 0x6F, 0x8C, 0xF1,
		    0xB3, 0xA9, 0x19, 0xA4, 0xAE, 0x66, 0xBD, 0x9E },
		.len = 16
	},
	.auth_tags[2] = {
		.size = 256,
		.data = { 0x29, 0xC3, 0x18, 0x96, 0x54, 0xCB, 0xF5, 0xAA,
		    0x4E, 0x62, 0xB6, 0xFF, 0x45, 0xA6, 0x18, 0x0C },
		.len = 16
	},
	.auth_tags[3] = {
		.size = 512,
		.data = { 0x3B, 0xD7, 0xC3, 0x5F, 0xE4, 0x1B, 0xC2, 0xBC,
		    0xE9, 0xAC, 0xF2, 0xCE, 0xA7, 0x7B, 0x1D, 0x70 },
		.len = 16
	},
	.auth_tags[4] = {
		.size = 1024,
		.data = { 0xCC, 0xBB, 0xBC, 0xCF, 0x86, 0x01, 0x4D, 0x93,
		    0x4B, 0x68, 0x55, 0x19, 0xA1, 0x40, 0xCD, 0xEA },
		.len = 16
	},
	.auth_tags[5] = {
		.size = 1536,
		.data = { 0x67, 0x31, 0x11, 0xA2, 0x58, 0xB5, 0x1C, 0x23,
		    0xC0, 0x41, 0x05, 0x30, 0xC6, 0xBA, 0xFA, 0x88 },
		.len = 16
	},
	.auth_tags[6] = {
		.size = 2048,
		.data = { 0x03, 0x9C, 0x6B, 0xB9, 0x57, 0xBF, 0x6E, 0x86,
			0x3A, 0x09, 0x5F, 0x08, 0xA9, 0xE4, 0xF2, 0x1F },
		.len = 16
	},
	.auth_tag = {
		.data = {
		    0x03, 0x9C, 0x6B, 0xB9, 0x57, 0xBF, 0x6E, 0x86,
		    0x3A, 0x09, 0x5F, 0x08, 0xA9, 0xE4, 0xF2, 0x1F
		},
		.len = 16
	},
};

static const struct gmac_test_data gmac_test_case_4 = {
	.key = {
		.data = {
			0xfe, 0xff, 0xe9, 0x92, 0x86, 0x65, 0x73, 0x1c,
			0x6d, 0x6a, 0x8f, 0x94, 0x67, 0x30, 0x83, 0x08
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xca, 0xfe, 0xba, 0xbe, 0xfa, 0xce, 0xdb, 0xad,
			0xde, 0xca, 0xf8, 0x88
		},
		.len = 12
	},
	.plaintext = {
		.data = gmac_plaintext,
		.len = GMAC_LARGE_PLAINTEXT_LENGTH
	},
	.gmac_tag = {
		.data = {
			0x3f, 0x07, 0xcb, 0xb9, 0x86, 0x3a, 0xea, 0xc2,
			0x2f, 0x3a, 0x2a, 0x93, 0xd8, 0x09, 0x6b, 0xda
		},
		.len = 16
	}
};

/*
 * Test vector used to test GMAC SGL with 16 segments
 * plaintext length = ~32KB / segment size = ~2 KB
 */
static const struct gmac_test_data gmac_test_case_5 = {
	.key = {
		.data = {
			0xfe, 0xff, 0xe9, 0x92, 0x86, 0x65, 0x73, 0x1c,
			0x6d, 0x6a, 0x8f, 0x94, 0x67, 0x30, 0x83, 0x08
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xca, 0xfe, 0xba, 0xbe, 0xfa, 0xce, 0xdb, 0xad,
			0xde, 0xca, 0xf8, 0x88
		},
		.len = 12
	},
	.plaintext = {
		.data = gmac_plaintext,
		.len = GMAC_LARGE_PLAINTEXT_LENGTH/2
	},
	.gmac_tag = {
		.data = {
			0xb1, 0xba, 0xe7, 0x28, 0xd3, 0x95, 0x80, 0xd7,
			0x2e, 0xf5, 0xd0, 0x20, 0x80, 0x95, 0x16, 0x97
		},
		.len = 16
	}
};

static const struct aead_test_data gcm_test_case_SGL_1 = {
	.algo = RTE_CRYPTO_AEAD_AES_GCM,
	.key = {
		.data = {
			0xfe, 0xff, 0xe9, 0x92, 0x86, 0x65, 0x73, 0x1c,
			0x6d, 0x6a, 0x8f, 0x94, 0x67, 0x30, 0x83, 0x08 },
		.len = 16
	},
	.iv = {
		.data = {
			0xca, 0xfe, 0xba, 0xbe, 0xfa, 0xce, 0xdb, 0xad,
			0xde, 0xca, 0xf8, 0x88 },
		.len = 12
	},
	.aad = {
		.data = gcm_aad_zero_text,
		.len = 0
	},
	.plaintext = {
		.data = {
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
			0xd8, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9b,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0xd7, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9c,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0xd7, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9c,
			0xd8, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9b,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
			0xd8, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9b,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0xd7, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9c,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0xd7, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9c,
			0xd8, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9b,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
			0xd8, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9b,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0xd7, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9c,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0xd7, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9c,
			0xd8, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9b,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
			0xd8, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9b,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0xd7, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9c,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0xd7, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9c,
			0xd8, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9b,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
			0xd8, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9b,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0xd7, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9c,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0xd7, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9c,
			0xd8, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9b,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
			0xd8, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9b,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0xd7, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9c,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0xd7, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9c,
			0xd8, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9b,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
			0xd8, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9b,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0xd7, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9c,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0xd7, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9c,
			0xd8, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9b,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
			0xd8, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9b,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0xd7, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9c,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0xd7, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9c,
			0xd8, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9b,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0xd9, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9a,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x55,
			0xd8, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9b,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0xd7, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9c,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0xba, 0x63, 0x7b, 0x39, 0x1a, 0xaf, 0xd2, 0x54,
			0xd7, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9c,
			0xd8, 0x31, 0x32, 0x25, 0xf8, 0x84, 0x06, 0xe5,
			0xa5, 0x59, 0x09, 0xc5, 0xaf, 0xf5, 0x26, 0x9b,
			0x86, 0xa7, 0xa9, 0x53, 0x15, 0x34, 0xf7, 0xda,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,
			0x2e, 0x4c, 0x30, 0x3d, 0x8a, 0x31, 0x8a, 0x72,
			0x1c, 0x3c, 0x0c, 0x95, 0x95, 0x68, 0x09, 0x53,
			0x2f, 0xcf, 0x0e, 0x24, 0x49, 0xa6, 0xb5, 0x25,
			0xb1, 0x6a, 0xed, 0xf5, 0xaa, 0x0d, 0xe6, 0x57,

		},
		.len = 3120
	},
		.ciphertext = {
			.data = {
			0x42, 0x83, 0x1E, 0xC2, 0x21, 0x77, 0x74, 0x24,
			0x4B, 0x72, 0x21, 0xB7, 0x84, 0xD0, 0xD4, 0x9C,
			0xE3, 0xAA, 0x21, 0x2F, 0x2C, 0x02, 0xA4, 0xE0,
			0x35, 0xC1, 0x7E, 0x23, 0x29, 0xAC, 0xA1, 0x2E,
			0x21, 0xD5, 0x14, 0xB2, 0x54, 0x66, 0x93, 0x1C,
			0x7D, 0x8F, 0x6A, 0x5A, 0xAC, 0x84, 0xAA, 0x05,
			0x1B, 0xA3, 0x0B, 0x39, 0x6A, 0x0A, 0xAC, 0x97,
			0x3D, 0x58, 0xE0, 0x91, 0x47, 0x3F, 0x59, 0x85,
			0x05, 0x99, 0x55, 0xE1, 0x36, 0x76, 0xB7, 0x14,
			0x1D, 0xF0, 0xF6, 0x8C, 0x65, 0xD5, 0xAD, 0xFA,
			0x90, 0x7F, 0x5D, 0xA2, 0xD6, 0xFD, 0xD0, 0xE5,
			0x0D, 0x9B, 0x68, 0x21, 0x49, 0x42, 0x6E, 0x13,
			0xEC, 0x22, 0x50, 0x2A, 0x30, 0x47, 0x49, 0xA1,
			0x7F, 0xC3, 0x09, 0xE0, 0x56, 0x91, 0xC4, 0x54,
			0x70, 0xD7, 0x19, 0x40, 0xCA, 0x6B, 0x65, 0x27,
			0x3E, 0xE9, 0xD1, 0x0F, 0x1C, 0xB5, 0x45, 0x0C,
			0x29, 0xE7, 0xCF, 0x94, 0x10, 0xBF, 0xA2, 0xFA,
			0x86, 0x20, 0x3F, 0x6E, 0xE9, 0x95, 0x03, 0x5C,
			0x46, 0x11, 0x75, 0xD5, 0x37, 0x71, 0x7F, 0xE0,
			0xBC, 0x9F, 0xC8, 0xE9, 0xB1, 0x08, 0x2C, 0x59,
			0x6E, 0x51, 0x4A, 0x83, 0x38, 0xC1, 0xED, 0xE2,
			0x2E, 0x88, 0x90, 0xA5, 0x7D, 0xA4, 0x93, 0x9A,
			0x30, 0xD6, 0x96, 0x34, 0x0F, 0xC4, 0xD1, 0x7E,
			0xC9, 0x8F, 0xC5, 0xBB, 0x80, 0x50, 0x85, 0x73,
			0x8B, 0x7C, 0x0A, 0xDA, 0xD3, 0x37, 0x1C, 0x8B,
			0x1E, 0xAE, 0x29, 0x54, 0x05, 0x53, 0x48, 0xE5,
			0x94, 0xF1, 0xC5, 0x1A, 0x60, 0xDC, 0x61, 0x43,
			0xCD, 0x45, 0x4C, 0x6B, 0x95, 0xAD, 0x52, 0xE0,
			0x9E, 0xD1, 0x4E, 0xCC, 0x03, 0x27, 0x50, 0xD4,
			0xEB, 0xBD, 0x71, 0xA6, 0xD0, 0x2B, 0x23, 0xC0,
			0x9E, 0x5F, 0x34, 0xFD, 0xDE, 0xC1, 0x43, 0x35,
			0x77, 0xFB, 0xFD, 0xDF, 0xA0, 0x28, 0x42, 0x3B,
			0x0F, 0x2D, 0x31, 0xB4, 0x7A, 0xA8, 0x2F, 0xDF,
			0x58, 0xB5, 0x00, 0x19, 0x8D, 0xEB, 0x2C, 0xBB,
			0xAE, 0xAD, 0x74, 0x7F, 0x25, 0xAA, 0x24, 0x3E,
			0xCD, 0x89, 0x5E, 0x05, 0xD3, 0xBA, 0x0E, 0x9A,
			0x34, 0x7B, 0xE0, 0x11, 0xD2, 0xBA, 0x5A, 0x51,
			0xB4, 0x0D, 0xEE, 0x61, 0x73, 0xFC, 0xD2, 0x01,
			0x2D, 0x52, 0x3E, 0x37, 0x55, 0x3F, 0x58, 0xA8,
			0x1C, 0x8F, 0x1D, 0xD6, 0x3C, 0x39, 0x06, 0x18,
			0x65, 0x60, 0x55, 0x19, 0xAD, 0x1E, 0x78, 0xE9,
			0xF7, 0xF5, 0xFC, 0xCD, 0x5F, 0xF1, 0x34, 0x0C,
			0xA6, 0xFD, 0x1E, 0x9E, 0xB3, 0xCE, 0x2E, 0x10,
			0xFB, 0x98, 0xDD, 0x0E, 0x09, 0x5D, 0x4E, 0x58,
			0x75, 0x9A, 0x54, 0x74, 0xFB, 0x40, 0x76, 0x55,
			0x0E, 0x3E, 0xA4, 0xCE, 0x56, 0xA5, 0xE0, 0x53,
			0xB7, 0xAD, 0x36, 0x99, 0x6E, 0xCD, 0xC2, 0x90,
			0x6E, 0xEA, 0xBC, 0x21, 0xAC, 0x31, 0xFF, 0x2B,
			0x00, 0xA7, 0x5E, 0xC1, 0x7A, 0xF1, 0xAB, 0x24,
			0xA3, 0x40, 0x0B, 0xEB, 0x16, 0x62, 0x35, 0x1E,
			0xE9, 0xA5, 0xD3, 0x7E, 0xAA, 0x7E, 0x28, 0xA8,
			0x3F, 0xD8, 0x0A, 0x04, 0x12, 0x0F, 0xFF, 0x68,
			0x10, 0x85, 0x22, 0xD6, 0x05, 0x6A, 0x3A, 0xCB,
			0xC0, 0xCF, 0x8C, 0x20, 0xF0, 0x34, 0x32, 0xAA,
			0x76, 0x93, 0xE2, 0x23, 0x4F, 0xF2, 0xE6, 0x84,
			0x3B, 0xD4, 0xF3, 0x5D, 0xF3, 0x17, 0xEE, 0x27,
			0x67, 0xC3, 0x01, 0x6F, 0x32, 0xDE, 0xF6, 0xF6,
			0x87, 0xE9, 0x82, 0xEF, 0x1F, 0xA1, 0xE2, 0x68,
			0xF8, 0x5D, 0x49, 0x92, 0x47, 0x01, 0x75, 0x87,
			0x52, 0xD3, 0x54, 0xAE, 0x3B, 0xB7, 0xB2, 0x07,
			0x0F, 0x62, 0x7B, 0xF7, 0x50, 0x97, 0x9A, 0x4A,
			0x98, 0x65, 0x23, 0xA3, 0x5D, 0x76, 0x0A, 0x9C,
			0x6C, 0xE7, 0x89, 0xAD, 0x86, 0x70, 0xE7, 0x16,
			0x5F, 0x2F, 0x2E, 0x97, 0x29, 0x31, 0xF0, 0x60,
			0x33, 0x2C, 0xD7, 0xAA, 0xD6, 0xF0, 0x50, 0xB8,
			0xBD, 0x29, 0xA8, 0xA9, 0xAC, 0x5E, 0x0A, 0x3A,
			0x59, 0x34, 0x9A, 0x92, 0x25, 0x71, 0xB3, 0x16,
			0xC5, 0xD3, 0xA4, 0x15, 0x75, 0x9A, 0xB5, 0x78,
			0x6E, 0xCF, 0xAF, 0xC0, 0x39, 0x28, 0x44, 0x21,
			0xBB, 0xE8, 0x32, 0xAB, 0xCB, 0xF8, 0x4B, 0xE7,
			0x63, 0x9C, 0x56, 0xE7, 0xB2, 0xD6, 0x23, 0x17,
			0xDE, 0x92, 0xE9, 0x22, 0xC3, 0x36, 0xA5, 0xAC,
			0xA9, 0x98, 0x34, 0xAA, 0xFB, 0x03, 0x33, 0x33,
			0xBE, 0xD8, 0x22, 0x7F, 0xFA, 0x34, 0xA0, 0x35,
			0xC8, 0xA0, 0xDC, 0x35, 0x82, 0x06, 0x58, 0xE6,
			0xBF, 0x7C, 0x4F, 0x63, 0x5D, 0x62, 0x64, 0x67,
			0x0D, 0x07, 0x7F, 0x24, 0x4A, 0x23, 0xBC, 0x35,
			0xE0, 0x92, 0x6F, 0x51, 0xE7, 0x25, 0x97, 0xB9,
			0x14, 0x35, 0x2B, 0x48, 0xAC, 0x6F, 0x54, 0xDF,
			0xF2, 0xB4, 0xB0, 0xE0, 0xD3, 0x28, 0x0D, 0x67,
			0x48, 0x28, 0x0A, 0x16, 0x9C, 0x87, 0x73, 0xB7,
			0x9C, 0x2B, 0xB5, 0x43, 0xC9, 0x46, 0xB9, 0x19,
			0x01, 0xAA, 0xDE, 0x75, 0xA6, 0x0F, 0xB5, 0x72,
			0x6A, 0x51, 0xE3, 0xAC, 0xE0, 0xF6, 0x96, 0x13,
			0xBB, 0xC7, 0x08, 0x13, 0x9E, 0x47, 0xAA, 0xF5,
			0x9E, 0x69, 0xAC, 0x95, 0x29, 0xFE, 0xFF, 0x99,
			0xB2, 0x52, 0x72, 0x45, 0xF2, 0x07, 0xEB, 0x3C,
			0x0F, 0x75, 0x29, 0x73, 0x0D, 0x77, 0x58, 0x83,
			0xCB, 0xDD, 0xE7, 0x68, 0x1C, 0xE3, 0xD1, 0xA4,
			0x5D, 0xD1, 0xAB, 0xB4, 0x5A, 0x3F, 0x27, 0x66,
			0xDA, 0xB4, 0x81, 0x65, 0xCE, 0x1A, 0x9A, 0x7D,
			0xC7, 0xB6, 0x31, 0xDE, 0x83, 0xC2, 0x7C, 0xF8,
			0xD3, 0xC7, 0x97, 0x28, 0x50, 0xF2, 0x95, 0xFC,
			0xA7, 0xB2, 0xA6, 0x46, 0xEF, 0x10, 0xD2, 0x38,
			0x93, 0x14, 0x8D, 0xA7, 0x09, 0x17, 0x42, 0x7A,
			0x85, 0xB9, 0x42, 0x71, 0x2A, 0x51, 0x9B, 0x66,
			0x71, 0x12, 0x57, 0xB7, 0xBD, 0x26, 0xB7, 0x91,
			0xF8, 0x84, 0x44, 0x35, 0xAD, 0x6F, 0xCB, 0xD7,
			0xFC, 0xA1, 0x28, 0x77, 0x09, 0x5B, 0x6D, 0x52,
			0x43, 0xA1, 0xE2, 0x0A, 0x7E, 0x5A, 0x84, 0x45,
			0x20, 0xDE, 0xA5, 0x73, 0x1D, 0x37, 0x6E, 0xD8,
			0x7A, 0x0D, 0x91, 0xBE, 0xF4, 0xB3, 0x89, 0xE9,
			0x1F, 0x1E, 0xF6, 0xD5, 0x37, 0xB4, 0x3C, 0x1D,
			0xBE, 0x0D, 0x5B, 0x01, 0xB0, 0x8B, 0xCE, 0x3E,
			0x6D, 0x8B, 0x99, 0x9A, 0xC5, 0xAE, 0xFE, 0xA9,
			0x78, 0x34, 0x20, 0xA7, 0x6C, 0x7D, 0x46, 0x72,
			0x37, 0xAF, 0xFD, 0x17, 0x59, 0xED, 0x83, 0x5B,
			0xEB, 0x6E, 0x4A, 0xF1, 0xE6, 0x0D, 0x44, 0x92,
			0x65, 0x8E, 0x97, 0xD6, 0x83, 0x6E, 0x97, 0xCA,
			0x4C, 0x0A, 0xCE, 0x32, 0x2A, 0xAD, 0x22, 0x73,
			0xCB, 0xCB, 0xC3, 0x55, 0x08, 0x63, 0x23, 0xC2,
			0x31, 0x24, 0x90, 0x54, 0x99, 0xB2, 0x8C, 0xC7,
			0x8A, 0xB6, 0xFF, 0xC2, 0x75, 0xB1, 0xD9, 0x3D,
			0x95, 0xDC, 0xB6, 0xCF, 0x11, 0x74, 0x06, 0x54,
			0x03, 0xE3, 0x9B, 0x49, 0xE4, 0xF2, 0x73, 0x04,
			0xF7, 0xDC, 0x71, 0xD7, 0xFA, 0x3C, 0xD2, 0x61,
			0x77, 0x61, 0xB3, 0xDB, 0x6B, 0xCE, 0xCA, 0xFF,
			0xF0, 0xAD, 0xBC, 0x94, 0xC8, 0xF8, 0xD5, 0xF4,
			0x38, 0xA3, 0x61, 0xAA, 0x8C, 0x96, 0xEE, 0x56,
			0xAC, 0xB4, 0x42, 0xBA, 0x1A, 0xE1, 0x70, 0x98,
			0x1F, 0x9A, 0x6F, 0x98, 0xB9, 0x13, 0x46, 0xAB,
			0x0B, 0xCD, 0xA3, 0x7B, 0x0C, 0xCB, 0x8F, 0x72,
			0x23, 0xCF, 0x9E, 0xD8, 0xBB, 0x3F, 0x32, 0x27,
			0x54, 0xB8, 0x60, 0x64, 0x83, 0xAE, 0x22, 0xD1,
			0x6A, 0xC9, 0xF8, 0x13, 0xC4, 0xE4, 0xFF, 0x97,
			0xD8, 0x92, 0xA3, 0xD1, 0xD4, 0x86, 0xD7, 0xC3,
			0xBB, 0x40, 0xA2, 0x45, 0x78, 0xB1, 0xDB, 0x80,
			0xC6, 0x8D, 0x0A, 0xF0, 0xC3, 0xC2, 0xE3, 0x48,
			0xA1, 0x05, 0xC2, 0x32, 0xC8, 0x6C, 0x50, 0xA8,
			0x06, 0x58, 0xBE, 0x6C, 0x7D, 0x22, 0xD6, 0x0D,
			0x74, 0x40, 0xCE, 0xD6, 0x64, 0xD6, 0x47, 0xD0,
			0xBF, 0xF1, 0x5C, 0x54, 0xF9, 0x06, 0x3F, 0x3D,
			0x86, 0xBA, 0xF2, 0x0F, 0x5E, 0x2C, 0x01, 0xCC,
			0xD9, 0xC7, 0xB1, 0x4A, 0xB3, 0xD7, 0x26, 0xCC,
			0xC3, 0x7A, 0x74, 0x2C, 0xE1, 0x22, 0x65, 0xA0,
			0x5B, 0xCA, 0xF4, 0xE1, 0x7D, 0xE1, 0x56, 0xFD,
			0x95, 0x10, 0xC6, 0xA1, 0x4A, 0xE8, 0x6B, 0x34,
			0x4E, 0x71, 0x60, 0x77, 0x0F, 0x03, 0xDD, 0xFE,
			0xC8, 0x59, 0x54, 0x6C, 0xD4, 0x4A, 0x55, 0x24,
			0x35, 0x21, 0x60, 0x73, 0xDF, 0x6F, 0xE7, 0x3C,
			0xC2, 0xF0, 0xDA, 0xA9, 0xE5, 0x8C, 0xAC, 0xB6,
			0xFD, 0x2E, 0xF7, 0xA0, 0x18, 0xA7, 0x55, 0x47,
			0xD1, 0xCB, 0x9E, 0xAA, 0x58, 0x54, 0x3B, 0x37,
			0x18, 0xB5, 0xC1, 0xBB, 0x41, 0x59, 0xE4, 0x29,
			0x44, 0x13, 0x90, 0x6A, 0xF7, 0xD1, 0xB3, 0x71,
			0xB6, 0x6E, 0xF6, 0x5D, 0x2E, 0x0E, 0x6C, 0x4C,
			0x7B, 0xF7, 0xB6, 0x21, 0xD4, 0xFC, 0x47, 0x8C,
			0x9B, 0x0A, 0x90, 0xAC, 0x11, 0x52, 0x86, 0x07,
			0x24, 0xDA, 0xA9, 0x49, 0x50, 0xD9, 0xDC, 0xE2,
			0x19, 0x87, 0x73, 0x88, 0xC3, 0xE4, 0xED, 0xC9,
			0x1C, 0xA8, 0x7E, 0x39, 0x48, 0x91, 0x10, 0xAB,
			0xFC, 0x3C, 0x1E, 0xEE, 0x08, 0xA1, 0xB9, 0xB4,
			0xF4, 0xA9, 0x8D, 0xD0, 0x84, 0x7C, 0x8E, 0x54,
			0xEF, 0x05, 0xC3, 0x2A, 0x0B, 0x8D, 0x3C, 0x71,
			0xE7, 0x37, 0x27, 0x16, 0x07, 0xA2, 0x8F, 0x7A,
			0x86, 0x05, 0x56, 0xA3, 0xB2, 0x75, 0xC5, 0x2C,
			0xD4, 0x52, 0x60, 0x68, 0xA6, 0x6A, 0x48, 0xB6,
			0x92, 0x50, 0xEC, 0x22, 0xAD, 0x01, 0x75, 0x57,
			0xAF, 0xDF, 0x0F, 0x36, 0x93, 0x59, 0xF9, 0xE3,
			0xA1, 0x41, 0x3B, 0x60, 0xB3, 0x13, 0x12, 0x50,
			0x4B, 0x18, 0x20, 0xB9, 0x7B, 0x88, 0x27, 0x81,
			0xB1, 0xDA, 0xCA, 0x6F, 0x63, 0x95, 0x40, 0xA1,
			0x42, 0xE2, 0x14, 0xB8, 0x2B, 0x10, 0xB9, 0xDA,
			0xE7, 0x30, 0x91, 0x13, 0x52, 0xC9, 0xA3, 0x5C,
			0xD7, 0xBB, 0x39, 0x8F, 0x9A, 0xB8, 0xC5, 0xAF,
			0xC6, 0x3E, 0x65, 0x90, 0x91, 0x8C, 0x9F, 0xDD,
			0x84, 0xFB, 0xAD, 0x72, 0x4D, 0xD1, 0x42, 0xAD,
			0x0A, 0x1B, 0x3A, 0xC6, 0x06, 0x03, 0x19, 0xCB,
			0x31, 0x8C, 0x18, 0xD4, 0xEE, 0x90, 0x94, 0x3C,
			0x44, 0xDC, 0xFB, 0x78, 0x5C, 0xB5, 0xE3, 0x2F,
			0x89, 0x74, 0x0E, 0x28, 0x9C, 0xE4, 0xB4, 0xD2,
			0xE3, 0x5A, 0x32, 0xF9, 0xC0, 0x81, 0x6A, 0x38,
			0xC2, 0xCF, 0xD8, 0xD9, 0x3E, 0xAD, 0xF9, 0xB1,
			0xA2, 0x55, 0x64, 0x1E, 0xEC, 0xF5, 0x0D, 0xB1,
			0x8D, 0x07, 0x4E, 0xE5, 0x59, 0xE1, 0xE7, 0xFE,
			0x4C, 0xCF, 0x11, 0xF8, 0x27, 0xC2, 0x29, 0xE2,
			0xAF, 0x74, 0xAA, 0x53, 0x81, 0xD2, 0xFD, 0x5A,
			0xF1, 0xEB, 0x96, 0x2C, 0x3E, 0x9B, 0xC2, 0x74,
			0xFB, 0x65, 0x08, 0xA2, 0x63, 0xD3, 0xC5, 0x51,
			0xAF, 0x19, 0x8B, 0x34, 0x8B, 0x7D, 0xB7, 0x97,
			0x55, 0x97, 0x6D, 0x01, 0x5D, 0x98, 0xAA, 0x67,
			0x11, 0xBD, 0xC2, 0x99, 0x2F, 0xB4, 0xCA, 0x04,
			0x36, 0xF0, 0xB1, 0xA0, 0xBD, 0xA3, 0x4F, 0x4F,
			0xB6, 0x7B, 0xF5, 0x1E, 0x38, 0x87, 0xC2, 0x38,
			0x99, 0x5C, 0xE9, 0x2D, 0xDF, 0xAF, 0x5A, 0xF3,
			0x7A, 0x17, 0x70, 0x35, 0xEC, 0xD5, 0x19, 0xF7,
			0xB0, 0x21, 0x1E, 0x77, 0x30, 0x23, 0x54, 0x26,
			0x61, 0x4E, 0xB9, 0x02, 0xDE, 0xF4, 0x86, 0x93,
			0x47, 0x28, 0x43, 0x47, 0xB0, 0x56, 0xDC, 0x84,
			0x3E, 0x6A, 0x6B, 0xEA, 0x4D, 0x63, 0xFE, 0x56,
			0x5E, 0xF7, 0x6B, 0x1E, 0x5B, 0x63, 0xF1, 0x07,
			0x20, 0x2E, 0x9B, 0xEE, 0xDC, 0x70, 0x5E, 0x36,
			0x59, 0xE3, 0x3D, 0xA6, 0x0E, 0x50, 0x71, 0x06,
			0xDD, 0x8B, 0x3C, 0xF7, 0xEC, 0x3C, 0x7A, 0x08,
			0x8D, 0x4E, 0x6A, 0x08, 0xB0, 0xEE, 0x50, 0xE0,
			0xF9, 0x0E, 0x40, 0xC0, 0x11, 0xBF, 0x8A, 0x17,
			0x63, 0x9D, 0x59, 0x14, 0x0E, 0x25, 0x94, 0x09,
			0xE6, 0x34, 0xEC, 0x0F, 0xE4, 0x7C, 0x59, 0xCD,
			0x99, 0x85, 0x8E, 0x0F, 0xA1, 0x9E, 0x84, 0xBC,
			0x13, 0x20, 0x5F, 0x56, 0x26, 0x10, 0x1A, 0x77,
			0x77, 0x7B, 0x4B, 0x68, 0x13, 0x8A, 0x2C, 0xA5,
			0x01, 0xBF, 0xAD, 0xF2, 0x2C, 0xD9, 0x4B, 0x24,
			0x4C, 0xF5, 0x96, 0x4E, 0xD8, 0xE8, 0x98, 0xA8,
			0x9C, 0x63, 0x2F, 0xC3, 0x26, 0xC7, 0x74, 0x83,
			0x05, 0xED, 0x67, 0x02, 0x85, 0xAD, 0x1D, 0x0E,
			0xA9, 0xD6, 0xE1, 0xC7, 0x39, 0xA0, 0x6E, 0x72,
			0xCE, 0x56, 0x6C, 0xB8, 0x4A, 0xDE, 0x11, 0xA2,
			0xBF, 0xC1, 0x84, 0x98, 0x8F, 0xCA, 0x79, 0x75,
			0xC4, 0x9F, 0x45, 0x16, 0xBC, 0xB1, 0xF4, 0x03,
			0x76, 0x6E, 0xD5, 0x46, 0x60, 0xD7, 0x1D, 0xF6,
			0xD9, 0xBF, 0xF8, 0x71, 0xEB, 0x09, 0x33, 0x56,
			0xE6, 0xEC, 0x72, 0xC8, 0xB3, 0x47, 0x14, 0x2C,
			0x24, 0xA1, 0x1F, 0x16, 0xBE, 0x77, 0xFA, 0x9F,
			0x6B, 0x83, 0x05, 0x03, 0x4D, 0x6F, 0xC9, 0x76,
			0x69, 0x8D, 0xD7, 0x91, 0x26, 0x2B, 0x1C, 0x84,
			0xF2, 0x2B, 0x23, 0xA6, 0xFF, 0x7B, 0xEE, 0xCC,
			0x4E, 0x03, 0x8A, 0x80, 0x9E, 0x88, 0x96, 0xC3,
			0x7A, 0x3E, 0x1B, 0xAC, 0x40, 0x84, 0xD1, 0x64,
			0x89, 0x5F, 0xE3, 0x41, 0x89, 0x77, 0x4B, 0x28,
			0x83, 0xCA, 0x78, 0x4F, 0x36, 0xC8, 0xCE, 0x53,
			0x75, 0x39, 0x3A, 0x58, 0x92, 0x91, 0xF5, 0xA7,
			0x6A, 0xD0, 0xB2, 0xBB, 0xFC, 0x8E, 0x3B, 0xFC,
			0x83, 0x67, 0x42, 0xAA, 0x18, 0x51, 0x48, 0xD4,
			0xC4, 0x85, 0x60, 0xA4, 0x2D, 0xD4, 0x4E, 0xA1,
			0xF0, 0xB6, 0x41, 0x98, 0x6F, 0x84, 0xDE, 0x0C,
			0x03, 0x8D, 0x83, 0x4A, 0x71, 0xBB, 0x32, 0x8B,
			0x83, 0xF7, 0xD8, 0x08, 0x05, 0xA4, 0x48, 0xFE,
			0xCA, 0xBB, 0x21, 0xA8, 0xBA, 0x2A, 0xD2, 0x65,
			0x4E, 0xEF, 0xA1, 0x8F, 0x01, 0x09, 0xC6, 0x8C,
			0xE5, 0x35, 0x32, 0xBB, 0x19, 0x15, 0xAB, 0x7A,
			0xFD, 0x29, 0x76, 0xF9, 0xD1, 0xC5, 0x3E, 0xFD,
			0x7A, 0x74, 0xBC, 0x41, 0x4F, 0x2C, 0x79, 0x6F,
			0x45, 0x4E, 0xFD, 0x88, 0x49, 0x9A, 0x90, 0x6F,
			0x65, 0x00, 0xC8, 0x08, 0xB8, 0x3B, 0x40, 0x06,
			0x9A, 0x98, 0x5B, 0x6A, 0xD3, 0x5E, 0x32, 0x0E,
			0xB0, 0x21, 0xE6, 0x2D, 0xEF, 0x7B, 0x99, 0x1B,
			0xAF, 0x96, 0x20, 0x12, 0xE9, 0x31, 0xDA, 0x20,
			0xB0, 0x27, 0x99, 0xC7, 0x14, 0x56, 0x3A, 0x08,
			0x46, 0xA4, 0xB2, 0x0C, 0x6C, 0x1F, 0x1B, 0xAF,
			0x9F, 0x90, 0x03, 0xBB, 0x03, 0xE0, 0x20, 0xE9,
			0x45, 0x33, 0xA0, 0x3E, 0x01, 0x2C, 0xA7, 0x4A,
			0xCC, 0xC6, 0xF5, 0xA3, 0x35, 0x0D, 0xE1, 0x5E,
			0x90, 0x0B, 0xAC, 0x9A, 0x05, 0x79, 0xB2, 0x90,
			0x39, 0xEE, 0xC8, 0x20, 0x55, 0xB3, 0x71, 0x46,
			0xAC, 0x92, 0x42, 0x85, 0xD5, 0x12, 0x03, 0x8D,
			0xBC, 0x82, 0xE7, 0x5A, 0x6E, 0x2E, 0x2C, 0xC0,
			0xB6, 0x44, 0xF8, 0xBB, 0x5F, 0x7A, 0x42, 0x86,
			0x28, 0xF0, 0x9B, 0xF9, 0x17, 0xDD, 0x35, 0x2F,
			0x56, 0xE4, 0x63, 0xFF, 0xEC, 0x87, 0xC5, 0x53,
			0xBF, 0x64, 0xB2, 0xDA, 0xDE, 0xC1, 0x6C, 0x85,
			0x82, 0x51, 0x40, 0x41, 0xC9, 0x7A, 0x0A, 0xB8,
			0xB2, 0x75, 0x03, 0x88, 0x22, 0x6D, 0x76, 0x6E,
			0x2D, 0x2B, 0x73, 0xCB, 0x48, 0xC4, 0xED, 0xE0,
			0x96, 0xFA, 0x36, 0x9F, 0x99, 0xC7, 0x97, 0xDE,
			0x6D, 0xFC, 0x69, 0x86, 0x57, 0x5F, 0xB9, 0x93,
			0x78, 0x5C, 0x07, 0x64, 0x61, 0xD0, 0x41, 0x14,
			0x32, 0xED, 0xC0, 0xE4, 0xAC, 0xFC, 0x10, 0x0D,
			0xAF, 0xEE, 0xDA, 0xB3, 0x6D, 0xB8, 0x7C, 0x10,
			0xD5, 0x3B, 0x88, 0xE1, 0x15, 0xE1, 0xA4, 0x27,
			0xFE, 0xEE, 0x0A, 0xC8, 0x95, 0xCF, 0xCA, 0x99,
			0x98, 0x1D, 0xF3, 0x0E, 0xB8, 0x03, 0xD5, 0x51,
			0x4B, 0x56, 0xB9, 0x07, 0x85, 0x58, 0x17, 0x51,
			0x16, 0xC4, 0x86, 0xBB, 0xD3, 0x50, 0x01, 0x0E,
			0x7B, 0x9C, 0xEF, 0xF0, 0x28, 0x4A, 0xD7, 0x3D,
			0x1E, 0x3A, 0xBB, 0xCF, 0x2C, 0x90, 0x12, 0x2A,
			0xB3, 0x90, 0x72, 0xE3, 0x93, 0x81, 0xE8, 0xA4,
			0xEF, 0x8F, 0xD9, 0x45, 0x4F, 0xB1, 0xD0, 0x21,
			0xDA, 0x20, 0x5C, 0xE9, 0x41, 0x41, 0x4E, 0x48,
			0x95, 0x4D, 0x5A, 0xB3, 0xE5, 0x8B, 0xFC, 0xDE,
			0xB9, 0x7B, 0x93, 0xBE, 0xA2, 0x74, 0x1B, 0xFA,
			0xED, 0xCC, 0x0E, 0xDD, 0x96, 0x13, 0x2C, 0xAC,
			0xDE, 0x2B, 0x2D, 0x8A, 0x30, 0x5A, 0xB8, 0x4B,
			0x08, 0x2C, 0x74, 0xF7, 0xB4, 0x45, 0xD3, 0xA5,
			0x62, 0x87, 0xCA, 0x16, 0xEB, 0x49, 0x46, 0x0C,
			0x87, 0x7F, 0x11, 0x1D, 0x22, 0x66, 0x0A, 0x38,
			0x90, 0x3A, 0x31, 0x38, 0x73, 0xB2, 0xD5, 0x5E,
			0x06, 0xC4, 0x1E, 0x3D, 0xB7, 0x52, 0xB8, 0xE5,
			0xC0, 0xF9, 0x72, 0xBC, 0x7A, 0x8A, 0xD3, 0xB4,
			0x1D, 0xA9, 0x93, 0x3B, 0x7E, 0xFF, 0x8E, 0xA0,
			0x96, 0x52, 0xE9, 0x9E, 0x60, 0x4C, 0x02, 0x90,
			0xE5, 0x46, 0x92, 0xB3, 0xB8, 0x24, 0xE9, 0xD0,
			0xCE, 0xD3, 0x0B, 0xCD, 0x8B, 0xE8, 0x72, 0xEA,
			0x6E, 0xBF, 0x2B, 0x99, 0x6F, 0xC0, 0x65, 0xE8,
			0x92, 0x30, 0x03, 0x28, 0xA9, 0xB0, 0xA7, 0x03,
			0x92, 0x2C, 0xC8, 0x38, 0x8C, 0x38, 0x56, 0xEE,
			0xDB, 0x39, 0xBD, 0x7E, 0xE9, 0x8D, 0xDB, 0xC1,
			0xD5, 0x71, 0xC7, 0x84, 0xF3, 0xB2, 0x23, 0x22,
			0xB5, 0x98, 0xB3, 0x36, 0xF1, 0xC4, 0xB1, 0xA4,
			0xF2, 0x84, 0x24, 0xE5, 0x97, 0x48, 0x34, 0x43,
			0xEF, 0xD9, 0xF4, 0x10, 0xE4, 0x13, 0xEE, 0x6C,
			0xE7, 0x5D, 0x9B, 0xBA, 0x35, 0xF5, 0x7D, 0xE5,
			0xBF, 0x8A, 0xCC, 0x3D, 0x28, 0xCF, 0xE8, 0x90,
			0xE3, 0xCF, 0x01, 0x69, 0xD7, 0xC0, 0xD2, 0x2C,
			0xC2, 0x9B, 0x89, 0xF2, 0xA9, 0x83, 0xA2, 0xA9,
			0x12, 0xAA, 0x56, 0xD8, 0xCB, 0xA5, 0x8B, 0x0A,
			0x03, 0xC1, 0xE1, 0x8E, 0x02, 0x36, 0x3D, 0x8F,
			0x58, 0x4D, 0xEB, 0x93, 0x91, 0xC6, 0xE7, 0x22,
			0xCE, 0xA8, 0x02, 0xD2, 0x82, 0x0D, 0x43, 0x4D,
			0x4E, 0x11, 0xF8, 0x7B, 0x45, 0xD0, 0x23, 0xF7,
			0x14, 0x35, 0x16, 0xA4, 0x0B, 0xAD, 0xFE, 0xE2,
			0x2B, 0xFD, 0xF7, 0x17, 0xA9, 0x93, 0x77, 0x82,
			0x45, 0x6E, 0x51, 0x1F, 0x5C, 0x2C, 0x5F, 0xFF,
			0x1A, 0xA3, 0x0E, 0x29, 0xA5, 0x1D, 0xFD, 0x0E,
			0xDD, 0x14, 0xF6, 0x69, 0x20, 0x15, 0xFD, 0xBB,
			0xF8, 0xAF, 0x3D, 0xF3, 0xCC, 0xB8, 0x7E, 0x64,
			0xED, 0x99, 0xF3, 0x1D, 0xFC, 0x96, 0xA2, 0x0A,
			0x9C, 0xC2, 0x9B, 0xD7, 0x03, 0xA6, 0x79, 0x3B,
			0x16, 0x0C, 0x6C, 0x5C, 0x2B, 0x61, 0x0E, 0x48,
			0x96, 0x5C, 0x46, 0x7F, 0xC3, 0xCD, 0x3C, 0x10,
			0x30, 0x8F, 0xC4, 0xB5, 0x92, 0x46, 0x1C, 0xDF,
			0x10, 0xEE, 0x43, 0x27, 0x42, 0x70, 0xD2, 0xC4,
			0x5E, 0x77, 0x78, 0x0E, 0x0E, 0xC3, 0x8B, 0x72,
			0xA0, 0xFC, 0x4C, 0x0F, 0x5D, 0xBE, 0xBE, 0x07,
			0x5B, 0x53, 0x38, 0xC8, 0x96, 0x82, 0x2D, 0x2D,
			0x8E, 0xA8, 0x6C, 0x68, 0x34, 0x42, 0x31, 0x90,
			0xD6, 0x4D, 0x29, 0xA9, 0x90, 0x95, 0x19, 0xD6,
			0x8F, 0x2F, 0xF4, 0xD3, 0x71, 0x21, 0xB7, 0x7D,
			0x51, 0xA6, 0x15, 0xE5, 0xDA, 0x08, 0x6A, 0x23,
			0xDE, 0x6C, 0xBA, 0xCF, 0x84, 0xF1, 0x47, 0x25,
			0x4A, 0xF1, 0x2F, 0x24, 0xED, 0x3B, 0xED, 0xF0,
			0xA7, 0x48, 0xAE, 0x58, 0x7F, 0x0B, 0x3B, 0x78,
			0xCE, 0x94, 0x32, 0x82, 0x63, 0x22, 0x67, 0xAA,
			0x45, 0x37, 0xCC, 0x43, 0xD5, 0x10, 0x59, 0x5B,
			0x09, 0xC6, 0x1C, 0x32, 0xCD, 0x19, 0xA2, 0x3C,
			0x2B, 0x84, 0x03, 0xD5, 0x97, 0x20, 0xE7, 0xFB,
			0x2D, 0x0A, 0x3C, 0x5C, 0xFD, 0x39, 0x9C, 0xDE,
			0x02, 0x3D, 0xC7, 0xDD, 0x51, 0xDE, 0x99, 0xB3,
			0x65, 0x00, 0x60, 0xCF, 0xAE, 0xCD, 0xE2, 0x83,
			0xD5, 0x36, 0x2C, 0x89, 0x28, 0x6D, 0xC3, 0x6A,
			0x80, 0xCD, 0x1A, 0xC3, 0x75, 0x11, 0x7E, 0x65,
			0x2A, 0x44, 0x9D, 0xB5, 0x12, 0x2A, 0x78, 0xD0,
			0x4D, 0xF8, 0x5E, 0xBF, 0xEC, 0x6B, 0x60, 0xD2,
			0x89, 0x92, 0x5E, 0x17, 0xDA, 0x33, 0x83, 0xDB,
			0xED, 0xF4, 0x5E, 0x82, 0xE9, 0x04, 0xD7, 0xE0,
			0xA4, 0x1B, 0xFE, 0x32, 0x93, 0x05, 0x2C, 0xCF,
			0xA2, 0xAE, 0x83, 0xCA, 0x2F, 0x5E, 0x47, 0x1C,
			0x85, 0x0D, 0x01, 0xE5, 0x44, 0x3D, 0xE4, 0x58,
			0x8E, 0xC0, 0x46, 0x05, 0x95, 0xBE, 0x59, 0xED,
			0x0F, 0x7B, 0xA1, 0xF7, 0xDB, 0x2C, 0x79, 0x86,
			0xE9, 0x54, 0x98, 0xA6, 0x2A, 0xD0, 0xFE, 0xC9,
			0x59, 0x1D, 0x31, 0xC6, 0x27, 0x83, 0x2C, 0x12,
			0x9C, 0xE1, 0x43, 0x3C, 0xEC, 0x65, 0x3B, 0xEF,
			0xFD, 0x92, 0xBC, 0x0E, 0x38, 0xBA, 0x56, 0x1C,
			0xC0, 0x81, 0x9E, 0xBE, 0x76, 0x59, 0x88, 0xA4,
			0x0C, 0x6B, 0xD9, 0x7C, 0xD6, 0x8C, 0x32, 0xCD,
			0x3F, 0xB6, 0xEF, 0xBF, 0xA6, 0xC7, 0xC9, 0xD3,
			0x02, 0xB0, 0x3B, 0xFF, 0xFC, 0x4A, 0x97, 0x14,
			0xFF, 0xF2, 0x48, 0xFE, 0x1B, 0xCE, 0x7D, 0x24,
			0xA1, 0xD6, 0x03, 0xB0, 0x2F, 0xAA, 0xF7, 0x71,
			0xC9, 0x0E, 0xCB, 0x57, 0xBA, 0xEF, 0xB5, 0x65,
			0xE1, 0x44, 0xE4, 0x6A, 0xEB, 0xE8, 0x2B, 0x8F,
			0x06, 0x23, 0x7A, 0xA9, 0x70, 0xAE, 0x48, 0x65,
			0x94, 0xEE, 0xA5, 0x94, 0x78, 0x7D, 0x09, 0xF8,
			0xB5, 0x4D, 0x64, 0x67, 0x10, 0x16, 0xA2, 0xFC,
			0x49, 0x93, 0x76, 0x71, 0xED, 0x56, 0x25, 0xB5,
			0x87, 0xE8, 0x84, 0x16, 0x55, 0xE1, 0x1E, 0x34,
			0xE3, 0xB2, 0x49, 0x8F, 0xDC, 0xDA, 0xC3, 0x17,
			0x82, 0x0E, 0x19, 0xD7, 0xE0, 0x09, 0xD7, 0xD9,
			0x59, 0x6B, 0x55, 0x60, 0x1C, 0x1B, 0x02, 0xE8,
			0xD1, 0x90, 0xF6, 0x3E, 0x94, 0x4A, 0x12, 0x0C,
			0xBB, 0x69, 0xFD, 0x7C, 0xA0, 0xDD, 0x5F, 0x93,
			0x9F, 0xFE, 0x2E, 0x79, 0xDB, 0xBE, 0x6F, 0x85,
			0xAD, 0x9B, 0xDE, 0xAA, 0x10, 0xCA, 0xDB, 0xF2,
			0xF9, 0xD0, 0x54, 0x15, 0x00, 0xF0, 0x6F, 0x86,
			0x16, 0xF6, 0xA8, 0xA4, 0x08, 0x7B, 0x50, 0xF1,
			0x35, 0xAC, 0xB6, 0xBB, 0x8B, 0xA0, 0x86, 0x3B,
			0x3B, 0xDA, 0x9F, 0x89, 0xB5, 0x9C, 0x44, 0x41,
			0x6A, 0xFD, 0x8A, 0x79, 0xA0, 0xFB, 0x7D, 0x1B,
			0xE8, 0xC4, 0xA7, 0x3F, 0x66, 0x97, 0xA9, 0xF8,
			0xEA, 0x0C, 0x30, 0x81, 0x63, 0xE4, 0xE3, 0x84,
			0x62, 0xC5, 0x19, 0xFB, 0x00, 0xD6, 0x72, 0xE6,
			0xC9, 0x6C, 0xDB, 0xEB, 0xF3, 0x6F, 0xDB, 0xE7,
			0x00, 0x53, 0xCE, 0x1D, 0xE5, 0xF5, 0x53, 0x18,
			0xE5, 0xAA, 0xDA, 0x90, 0x7B, 0xCB, 0x2B, 0x74,
			0xED, 0x70, 0xFE, 0x90, 0xA8, 0xC8, 0x80, 0x2B,
			0x93, 0x08, 0xDB, 0x6A, 0x0F, 0x3D, 0xA1, 0xFA,
			0xB6, 0x63, 0x18, 0xF8, 0x43, 0x68, 0x00, 0xD0,
			0x7A, 0x97, 0xCD, 0x5B, 0xB2, 0x84, 0x90, 0x06,
			0xB9, 0x81, 0xC5, 0x81, 0x05, 0x55, 0x8C, 0xC4,
			0x03, 0x89, 0xF5, 0x63, 0x87, 0x39, 0xEC, 0xD6,
			0x89, 0x01, 0xE7, 0x1C, 0x4C, 0xDF, 0x5D, 0x65,
			0xFE, 0x4B, 0x91, 0x04, 0x5B, 0x0E, 0x03, 0x38,
			0x2F, 0x21, 0xA8, 0x36, 0x58, 0x93, 0xAD, 0x1F,
			0xEB, 0xC3, 0x91, 0x90, 0x9B, 0x95, 0xCD, 0x53,
			0x81, 0xAA, 0xA9, 0x48, 0x4D, 0x2B, 0x22, 0xC7,
			0xBE, 0x1B, 0x38, 0x21, 0xA1, 0xFE, 0x23, 0xB4,
			0xAC, 0x66, 0x92, 0x9E, 0xF2, 0x27, 0xDC, 0x23,
			0x70, 0x6E, 0xBA, 0xF9, 0xED, 0x3B, 0xCE, 0x63,
			0xAD, 0x68, 0xF2, 0x80, 0xFA, 0x1B, 0x14, 0xB5,
			0xB4, 0x07, 0xE3, 0x5A, 0x81, 0x74, 0xE1, 0xF2,
			},
		.len = 3120
	},
	.auth_tag = {
		.data = {
			0xEA, 0xE9, 0x10, 0xB6, 0xB7, 0xAB, 0xEA, 0x90,
			0x8A, 0xD5, 0x63, 0x88, 0xDB, 0x2B, 0x8F, 0x23,
		},
		.len = 16
	}
};

/** AES-CCM-128 Test Vectors */
static const struct aead_test_data ccm_test_case_128_1 = {
	.algo = RTE_CRYPTO_AEAD_AES_CCM,
	.key = {
		.data = {
			0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
			0x48, 0x49, 0x4A, 0x4B, 0x4C, 0x4D, 0x4E, 0x4F
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16
		},
		.len = 7
	},
	.aad = {
		.data = ccm_aad_test_1,
		.len = 8
	},
	.plaintext = {
		.data = {
			0x20, 0x21, 0x22, 0x23
		},
		.len = 4
	},
	.ciphertext = {
		.data = {
			0x71, 0x62, 0x01, 0x5B
		},
		.len = 4
	},
	.auth_tag = {
		.data = {
			0x4D, 0xAC, 0x25, 0x5D
		},
		.len = 4
	}
};

static const struct aead_test_data ccm_test_case_128_2 = {
	.algo = RTE_CRYPTO_AEAD_AES_CCM,
	.key = {
		.data = {
			0xC9, 0x7C, 0x1F, 0x67, 0xCE, 0x37, 0x11, 0x85,
			0x51, 0x4A, 0x8A, 0x19, 0xF2, 0xBD, 0xD5, 0x2F
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x50, 0x30, 0xF1, 0x84, 0x44, 0x08, 0xB5,
			0x03, 0x97, 0x76, 0xE7, 0x0C
		},
		.len = 13
	},
	.aad = {
		.data = ccm_aad_test_2,
		.len = 22
	},
	.plaintext = {
		.data = {
			0xF8, 0xBA, 0x1A, 0x55, 0xD0, 0x2F, 0x85, 0xAE,
			0x96, 0x7B, 0xB6, 0x2F, 0xB6, 0xCD, 0xA8, 0xEB,
			0x7E, 0x78, 0xA0, 0x50
		},
		.len = 20
	},
	.ciphertext = {
		.data = {
			0xF3, 0xD0, 0xA2, 0xFE, 0x9A, 0x3D, 0xBF, 0x23,
			0x42, 0xA6, 0x43, 0xE4, 0x32, 0x46, 0xE8, 0x0C,
			0x3C, 0x04, 0xD0, 0x19
		},
		.len = 20
	},
	.auth_tag = {
		.data = {
			0x78, 0x45, 0xCE, 0x0B, 0x16, 0xF9, 0x76, 0x23
		},
		.len = 8
	}
};

static const struct aead_test_data ccm_test_case_128_3 = {
	.algo = RTE_CRYPTO_AEAD_AES_CCM,
	.key = {
		.data = {
			0xC9, 0x7C, 0x1F, 0x67, 0xCE, 0x37, 0x11, 0x85,
			0x51, 0x4A, 0x8A, 0x19, 0xF2, 0xBD, 0xD5, 0x2F
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x50, 0x30, 0xF1, 0x84, 0x44, 0x08, 0xB5,
			0x03, 0x97, 0x76, 0xE7, 0x0C
		},
		.len = 13
	},
	.aad = {
		.data = gcm_aad_zero_text,
		.len = 0
	},
	.plaintext = {
		.data = {
			0xF8, 0xBA, 0x1A, 0x55, 0xD0, 0x2F, 0x85, 0xAE,
			0x96, 0x7B, 0xB6, 0x2F, 0xB6, 0xCD, 0xA8, 0xEB,
			0x7E, 0x78, 0xA0, 0x50
		},
		.len = 20
	},
	.ciphertext = {
		.data = {
			0xF3, 0xD0, 0xA2, 0xFE, 0x9A, 0x3D, 0xBF, 0x23,
			0x42, 0xA6, 0x43, 0xE4, 0x32, 0x46, 0xE8, 0x0C,
			0x3C, 0x04, 0xD0, 0x19
		},
		.len = 20
	},
	.auth_tag = {
		.data = {
			0x41, 0x83, 0x21, 0x89, 0xA3, 0xD3, 0x1B, 0x43
		},
		.len = 8
	}
};

/** AES-CCM-192 Test Vectors */
static const struct aead_test_data ccm_test_case_192_1 = {
	.algo = RTE_CRYPTO_AEAD_AES_CCM,
	.key = {
		.data = {
			0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
			0x48, 0x49, 0x4A, 0x4B, 0x4C, 0x4D, 0x4E, 0x4F,
			0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57
		},
		.len = 24
	},
	.iv = {
		.data = {
			0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16
		},
		.len = 7
	},
	.aad = {
		.data = ccm_aad_test_1,
		.len = 8
	},
	.plaintext = {
		.data = {
			0x20, 0x21, 0x22, 0x23
		},
		.len = 4
	},
	.ciphertext = {
		.data = {
			0x18, 0xEE, 0x17, 0x30
		},
		.len = 4
	},
	.auth_tag = {
		.data = {
			0xC8, 0xC3, 0x26, 0xD5
		},
		.len = 4
	}
};

static const struct aead_test_data ccm_test_case_192_2 = {
	.algo = RTE_CRYPTO_AEAD_AES_CCM,
	.key = {
		.data = {
			0xC9, 0x7C, 0x1F, 0x67, 0xCE, 0x37, 0x11, 0x85,
			0x51, 0x4A, 0x8A, 0x19, 0xF2, 0xBD, 0xD5, 0x2F,
			0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57
		},
		.len = 24
	},
	.iv = {
		.data = {
			0x00, 0x50, 0x30, 0xF1, 0x84, 0x44, 0x08, 0xB5,
			0x03, 0x97, 0x76, 0xE7, 0x0C
		},
		.len = 13
	},
	.aad = {
		.data = ccm_aad_test_2,
		.len = 22
	},
	.plaintext = {
		.data = {
			0xF8, 0xBA, 0x1A, 0x55, 0xD0, 0x2F, 0x85, 0xAE,
			0x96, 0x7B, 0xB6, 0x2F, 0xB6, 0xCD, 0xA8, 0xEB,
			0x7E, 0x78, 0xA0, 0x50
		},
		.len = 20
	},
	.ciphertext = {
		.data = {
			0x41, 0xC6, 0x2D, 0xD5, 0x31, 0xF2, 0xD5, 0xC8,
			0xCC, 0x57, 0x01, 0x2E, 0x7E, 0x2B, 0xF1, 0x26,
			0x6A, 0xC7, 0xCB, 0xA5
		},
		.len = 20
	},
	.auth_tag = {
		.data = {
			0x77, 0xA3, 0x41, 0xD5, 0x2A, 0xE3, 0x25, 0x37
		},
		.len = 8
	}
};

static const struct aead_test_data ccm_test_case_192_3 = {
	.algo = RTE_CRYPTO_AEAD_AES_CCM,
	.key = {
		.data = {
			0xC9, 0x7C, 0x1F, 0x67, 0xCE, 0x37, 0x11, 0x85,
			0x51, 0x4A, 0x8A, 0x19, 0xF2, 0xBD, 0xD5, 0x2F,
			0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57
		},
		.len = 24
	},
	.iv = {
		.data = {
			0x00, 0x50, 0x30, 0xF1, 0x84, 0x44, 0x08, 0xB5,
			0x03, 0x97, 0x76, 0xE7, 0x0C
		},
		.len = 13
	},
	.aad = {
		.data = gcm_aad_zero_text,
		.len = 0
	},
	.plaintext = {
		.data = {
			0xF8, 0xBA, 0x1A, 0x55, 0xD0, 0x2F, 0x85, 0xAE,
			0x96, 0x7B, 0xB6, 0x2F, 0xB6, 0xCD, 0xA8, 0xEB,
			0x7E, 0x78, 0xA0, 0x50
		},
		.len = 20
	},
	.ciphertext = {
		.data = {
			0x41, 0xC6, 0x2D, 0xD5, 0x31, 0xF2, 0xD5, 0xC8,
			0xCC, 0x57, 0x01, 0x2E, 0x7E, 0x2B, 0xF1, 0x26,
			0x6A, 0xC7, 0xCB, 0xA5
		},
		.len = 20
	},
	.auth_tag = {
		.data = {
			0x84, 0x72, 0x6E, 0xE7, 0x8E, 0x8E, 0x3A, 0xC6
		},
		.len = 8
	}
};

/** AES-CCM-256 Test Vectors */
static const struct aead_test_data ccm_test_case_256_1 = {
	.algo = RTE_CRYPTO_AEAD_AES_CCM,
	.key = {
		.data = {
			0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
			0x48, 0x49, 0x4A, 0x4B, 0x4C, 0x4D, 0x4E, 0x4F,
			0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57,
			0x58, 0x59, 0x5A, 0x5B, 0x5C, 0x5D, 0x5E, 0x5F
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16
		},
		.len = 7
	},
	.aad = {
		.data = ccm_aad_test_1,
		.len = 8
	},
	.plaintext = {
		.data = {
			0x20, 0x21, 0x22, 0x23
		},
		.len = 4
	},
	.ciphertext = {
		.data = {
			0x8A, 0xB1, 0xA8, 0x74
		},
		.len = 4
	},
	.auth_tag = {
		.data = {
			0x95, 0xFC, 0x08, 0x20
		},
		.len = 4
	}
};

static const struct aead_test_data ccm_test_case_256_2 = {
	.algo = RTE_CRYPTO_AEAD_AES_CCM,
	.key = {
		.data = {
			0xC9, 0x7C, 0x1F, 0x67, 0xCE, 0x37, 0x11, 0x85,
			0x51, 0x4A, 0x8A, 0x19, 0xF2, 0xBD, 0xD5, 0x2F,
			0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57,
			0x58, 0x59, 0x5A, 0x5B, 0x5C, 0x5D, 0x5E, 0x5F
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x00, 0x50, 0x30, 0xF1, 0x84, 0x44, 0x08, 0xB5,
			0x03, 0x97, 0x76, 0xE7, 0x0C
		},
		.len = 13
	},
	.aad = {
		.data = ccm_aad_test_2,
		.len = 22
	},
	.plaintext = {
		.data = {
			0xF8, 0xBA, 0x1A, 0x55, 0xD0, 0x2F, 0x85, 0xAE,
			0x96, 0x7B, 0xB6, 0x2F, 0xB6, 0xCD, 0xA8, 0xEB,
			0x7E, 0x78, 0xA0, 0x50
		},
		.len = 20
	},
	.ciphertext = {
		.data = {
			0x25, 0x82, 0x89, 0x09, 0x3E, 0x39, 0x1F, 0x16,
			0xD2, 0x82, 0x3D, 0xF6, 0xCE, 0x97, 0x72, 0x07,
			0xEC, 0x23, 0x17, 0x98
		},
		.len = 20
	},
	.auth_tag = {
		.data = {
			0xAB, 0x02, 0xE9, 0xD1, 0x16, 0x69, 0xED, 0x0A
		},
		.len = 8
	}
};

static const struct aead_test_data ccm_test_case_256_3 = {
	.algo = RTE_CRYPTO_AEAD_AES_CCM,
	.key = {
		.data = {
			0xC9, 0x7C, 0x1F, 0x67, 0xCE, 0x37, 0x11, 0x85,
			0x51, 0x4A, 0x8A, 0x19, 0xF2, 0xBD, 0xD5, 0x2F,
			0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57,
			0x58, 0x59, 0x5A, 0x5B, 0x5C, 0x5D, 0x5E, 0x5F
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x00, 0x50, 0x30, 0xF1, 0x84, 0x44, 0x08, 0xB5,
			0x03, 0x97, 0x76, 0xE7, 0x0C
		},
		.len = 13
	},
	.aad = {
		.data = gcm_aad_zero_text,
		.len = 0
	},
	.plaintext = {
		.data = {
			0xF8, 0xBA, 0x1A, 0x55, 0xD0, 0x2F, 0x85, 0xAE,
			0x96, 0x7B, 0xB6, 0x2F, 0xB6, 0xCD, 0xA8, 0xEB,
			0x7E, 0x78, 0xA0, 0x50
		},
		.len = 20
	},
	.ciphertext = {
		.data = {
			0x25, 0x82, 0x89, 0x09, 0x3E, 0x39, 0x1F, 0x16,
			0xD2, 0x82, 0x3D, 0xF6, 0xCE, 0x97, 0x72, 0x07,
			0xEC, 0x23, 0x17, 0x98
		},
		.len = 20
	},
	.auth_tag = {
		.data = {
			0x15, 0x80, 0xC4, 0xC9, 0x3F, 0xAB, 0x2A, 0xFD
		},
		.len = 8
	}
};
static uint8_t chacha_aad_rfc8439[] = {
			0x50, 0x51, 0x52, 0x53, 0xc0, 0xc1, 0xc2, 0xc3,
			0xc4, 0xc5, 0xc6, 0xc7
};

static const struct aead_test_data chacha20_poly1305_case_rfc8439 = {
	.algo = RTE_CRYPTO_AEAD_CHACHA20_POLY1305,
	.key = {
		.data = {
			0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
			0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
			0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
			0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x07, 0x00, 0x00, 0x00, 0x40, 0x41, 0x42, 0x43,
			0x44, 0x45, 0x46, 0x47
		},
		.len = 12
	},
	.aad = {
		.data = chacha_aad_rfc8439,
		.len = 12
	},
	.plaintext = {
		.data = {
			0x4c, 0x61, 0x64, 0x69, 0x65, 0x73, 0x20, 0x61,
			0x6e, 0x64, 0x20, 0x47, 0x65, 0x6e, 0x74, 0x6c,
			0x65, 0x6d, 0x65, 0x6e, 0x20, 0x6f, 0x66, 0x20,
			0x74, 0x68, 0x65, 0x20, 0x63, 0x6c, 0x61, 0x73,
			0x73, 0x20, 0x6f, 0x66, 0x20, 0x27, 0x39, 0x39,
			0x3a, 0x20, 0x49, 0x66, 0x20, 0x49, 0x20, 0x63,
			0x6f, 0x75, 0x6c, 0x64, 0x20, 0x6f, 0x66, 0x66,
			0x65, 0x72, 0x20, 0x79, 0x6f, 0x75, 0x20, 0x6f,
			0x6e, 0x6c, 0x79, 0x20, 0x6f, 0x6e, 0x65, 0x20,
			0x74, 0x69, 0x70, 0x20, 0x66, 0x6f, 0x72, 0x20,
			0x74, 0x68, 0x65, 0x20, 0x66, 0x75, 0x74, 0x75,
			0x72, 0x65, 0x2c, 0x20, 0x73, 0x75, 0x6e, 0x73,
			0x63, 0x72, 0x65, 0x65, 0x6e, 0x20, 0x77, 0x6f,
			0x75, 0x6c, 0x64, 0x20, 0x62, 0x65, 0x20, 0x69,
			0x74, 0x2e
		},
		.len = 114
	},
	.ciphertext = {
		.data = {
			0xd3, 0x1a, 0x8d, 0x34, 0x64, 0x8e, 0x60, 0xdb,
			0x7b, 0x86, 0xaf, 0xbc, 0x53, 0xef, 0x7e, 0xc2,
			0xa4, 0xad, 0xed, 0x51, 0x29, 0x6e, 0x08, 0xfe,
			0xa9, 0xe2, 0xb5, 0xa7, 0x36, 0xee, 0x62, 0xd6,
			0x3d, 0xbe, 0xa4, 0x5e, 0x8c, 0xa9, 0x67, 0x12,
			0x82, 0xfa, 0xfb, 0x69, 0xda, 0x92, 0x72, 0x8b,
			0x1a, 0x71, 0xde, 0x0a, 0x9e, 0x06, 0x0b, 0x29,
			0x05, 0xd6, 0xa5, 0xb6, 0x7e, 0xcd, 0x3b, 0x36,
			0x92, 0xdd, 0xbd, 0x7f, 0x2d, 0x77, 0x8b, 0x8c,
			0x98, 0x03, 0xae, 0xe3, 0x28, 0x09, 0x1b, 0x58,
			0xfa, 0xb3, 0x24, 0xe4, 0xfa, 0xd6, 0x75, 0x94,
			0x55, 0x85, 0x80, 0x8b, 0x48, 0x31, 0xd7, 0xbc,
			0x3f, 0xf4, 0xde, 0xf0, 0x8e, 0x4b, 0x7a, 0x9d,
			0xe5, 0x76, 0xd2, 0x65, 0x86, 0xce, 0xc6, 0x4b,
			0x61, 0x16
		},
		.len = 114
	},
	.auth_tag = {
		.data = {
			0x1a, 0xe1, 0x0b, 0x59, 0x4f, 0x09, 0xe2, 0x6a,
			0x7e, 0x90, 0x2e, 0xcb, 0xd0, 0x60, 0x06, 0x91
		},
		.len = 16
	}
};

static uint8_t chacha_aad_2[] = {
			0xf3, 0x33, 0x88, 0x86, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x4e, 0x91
};

static const struct aead_test_data chacha20_poly1305_case_2 = {
	.algo = RTE_CRYPTO_AEAD_CHACHA20_POLY1305,
	.key = {
		.data = {
				0x1c, 0x92, 0x40, 0xa5, 0xeb, 0x55, 0xd3, 0x8a,
				0xf3, 0x33, 0x88, 0x86, 0x04, 0xf6, 0xb5, 0xf0,
				0x47, 0x39, 0x17, 0xc1, 0x40, 0x2b, 0x80, 0x09,
				0x9d, 0xca, 0x5c, 0xbc, 0x20, 0x70, 0x75, 0xc0
		},
		.len = 32
	},
	.iv = {
		.data = {
				0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x03, 0x04,
				0x05, 0x06, 0x07, 0x08
		},
		.len = 12
	},
	.aad = {
		.data = chacha_aad_2,
		.len = 12
	},
	.plaintext = {
		.data = {
				0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74,
				0x2d, 0x44, 0x72, 0x61, 0x66, 0x74, 0x73, 0x20,
				0x61, 0x72, 0x65, 0x20, 0x64, 0x72, 0x61, 0x66,
				0x74, 0x20, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
				0x6e, 0x74, 0x73, 0x20, 0x76, 0x61, 0x6c, 0x69,
				0x64, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x61, 0x20,
				0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x20,
				0x6f, 0x66, 0x20, 0x73, 0x69, 0x78, 0x20, 0x6d,
				0x6f, 0x6e, 0x74, 0x68, 0x73, 0x20, 0x61, 0x6e,
				0x64, 0x20, 0x6d, 0x61, 0x79, 0x20, 0x62, 0x65,
				0x20, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
				0x2c, 0x20, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x63,
				0x65, 0x64, 0x2c, 0x20, 0x6f, 0x72, 0x20, 0x6f,
				0x62, 0x73, 0x6f, 0x6c, 0x65, 0x74, 0x65, 0x64,
				0x20, 0x62, 0x79, 0x20, 0x6f, 0x74, 0x68, 0x65,
				0x72, 0x20, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
				0x6e, 0x74, 0x73, 0x20, 0x61, 0x74, 0x20, 0x61,
				0x6e, 0x79, 0x20, 0x74, 0x69, 0x6d, 0x65, 0x2e,
				0x20, 0x49, 0x74, 0x20, 0x69, 0x73, 0x20, 0x69,
				0x6e, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x70, 0x72,
				0x69, 0x61, 0x74, 0x65, 0x20, 0x74, 0x6f, 0x20,
				0x75, 0x73, 0x65, 0x20, 0x49, 0x6e, 0x74, 0x65,
				0x72, 0x6e, 0x65, 0x74, 0x2d, 0x44, 0x72, 0x61,
				0x66, 0x74, 0x73, 0x20, 0x61, 0x73, 0x20, 0x72,
				0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
				0x20, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
				0x6c, 0x20, 0x6f, 0x72, 0x20, 0x74, 0x6f, 0x20,
				0x63, 0x69, 0x74, 0x65, 0x20, 0x74, 0x68, 0x65,
				0x6d, 0x20, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x20,
				0x74, 0x68, 0x61, 0x6e, 0x20, 0x61, 0x73, 0x20,
				0x2f, 0xe2, 0x80, 0x9c, 0x77, 0x6f, 0x72, 0x6b,
				0x20, 0x69, 0x6e, 0x20, 0x70, 0x72, 0x6f, 0x67,
				0x72, 0x65, 0x73, 0x73, 0x2e, 0x2f, 0xe2, 0x80,
				0x9d
		},
		.len = 265
	},
	.ciphertext = {
		.data = {
				0x64, 0xa0, 0x86, 0x15, 0x75, 0x86, 0x1a, 0xf4,
				0x60, 0xf0, 0x62, 0xc7, 0x9b, 0xe6, 0x43, 0xbd,
				0x5e, 0x80, 0x5c, 0xfd, 0x34, 0x5c, 0xf3, 0x89,
				0xf1, 0x08, 0x67, 0x0a, 0xc7, 0x6c, 0x8c, 0xb2,
				0x4c, 0x6c, 0xfc, 0x18, 0x75, 0x5d, 0x43, 0xee,
				0xa0, 0x9e, 0xe9, 0x4e, 0x38, 0x2d, 0x26, 0xb0,
				0xbd, 0xb7, 0xb7, 0x3c, 0x32, 0x1b, 0x01, 0x00,
				0xd4, 0xf0, 0x3b, 0x7f, 0x35, 0x58, 0x94, 0xcf,
				0x33, 0x2f, 0x83, 0x0e, 0x71, 0x0b, 0x97, 0xce,
				0x98, 0xc8, 0xa8, 0x4a, 0xbd, 0x0b, 0x94, 0x81,
				0x14, 0xad, 0x17, 0x6e, 0x00, 0x8d, 0x33, 0xbd,
				0x60, 0xf9, 0x82, 0xb1, 0xff, 0x37, 0xc8, 0x55,
				0x97, 0x97, 0xa0, 0x6e, 0xf4, 0xf0, 0xef, 0x61,
				0xc1, 0x86, 0x32, 0x4e, 0x2b, 0x35, 0x06, 0x38,
				0x36, 0x06, 0x90, 0x7b, 0x6a, 0x7c, 0x02, 0xb0,
				0xf9, 0xf6, 0x15, 0x7b, 0x53, 0xc8, 0x67, 0xe4,
				0xb9, 0x16, 0x6c, 0x76, 0x7b, 0x80, 0x4d, 0x46,
				0xa5, 0x9b, 0x52, 0x16, 0xcd, 0xe7, 0xa4, 0xe9,
				0x90, 0x40, 0xc5, 0xa4, 0x04, 0x33, 0x22, 0x5e,
				0xe2, 0x82, 0xa1, 0xb0, 0xa0, 0x6c, 0x52, 0x3e,
				0xaf, 0x45, 0x34, 0xd7, 0xf8, 0x3f, 0xa1, 0x15,
				0x5b, 0x00, 0x47, 0x71, 0x8c, 0xbc, 0x54, 0x6a,
				0x0d, 0x07, 0x2b, 0x04, 0xb3, 0x56, 0x4e, 0xea,
				0x1b, 0x42, 0x22, 0x73, 0xf5, 0x48, 0x27, 0x1a,
				0x0b, 0xb2, 0x31, 0x60, 0x53, 0xfa, 0x76, 0x99,
				0x19, 0x55, 0xeb, 0xd6, 0x31, 0x59, 0x43, 0x4e,
				0xce, 0xbb, 0x4e, 0x46, 0x6d, 0xae, 0x5a, 0x10,
				0x73, 0xa6, 0x72, 0x76, 0x27, 0x09, 0x7a, 0x10,
				0x49, 0xe6, 0x17, 0xd9, 0x1d, 0x36, 0x10, 0x94,
				0xfa, 0x68, 0xf0, 0xff, 0x77, 0x98, 0x71, 0x30,
				0x30, 0x5b, 0xea, 0xba, 0x2e, 0xda, 0x04, 0xdf,
				0x99, 0x7b, 0x71, 0x4d, 0x6c, 0x6f, 0x2c, 0x29,
				0xa6, 0xad, 0x5c, 0xb4, 0x02, 0x2b, 0x02, 0x70,
				0x9b
		},
		.len = 265
	},
	.auth_tag = {
		.data = {
				0xee, 0xad, 0x9d, 0x67, 0x89, 0x0c, 0xbb, 0x22,
				0x39, 0x23, 0x36, 0xfe, 0xa1, 0x85, 0x1f, 0x38
		},
		.len = 16
	}
};

static const struct aead_test_data sm4_gcm_case_1 = {
	.algo = RTE_CRYPTO_AEAD_SM4_GCM,
	.key = {
		.data = {
			0x22, 0x04, 0xb5, 0x07, 0x83, 0x5a, 0xf3, 0x3e,
			0xb1, 0x07, 0xa2, 0x71, 0x31, 0x4a, 0x65, 0x8c
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x25, 0xc1, 0xe9, 0xce, 0x6e, 0x61, 0xe7, 0xf4,
			0x7c, 0xcf, 0x2c, 0xe7
		},
		.len = 12
	},
	.aad = {
		.data = 0,
		.len = 0
	},
	.plaintext = {
		.data = {
			0xf1, 0x7b, 0xe7, 0x3b, 0x74, 0x08, 0x40, 0x66,
			0xd1, 0x5f, 0x0f, 0x9e, 0xd6, 0xcf, 0x29, 0xd3
		},
		.len = 16
	},
	.ciphertext = {
		.data = {
			0x27, 0xd5, 0x79, 0x8a, 0x80, 0x45, 0x9e, 0xee,
			0x00, 0x56, 0xb4, 0x93, 0xda, 0x8d, 0x4d, 0x3d
		},
		.len = 16
	},
	.auth_tag = {
		.data = {
			0x13, 0xc4, 0xe1, 0xda, 0x30, 0xd0, 0xad, 0x72,
			0x55, 0x7c, 0xb7, 0xe4, 0x9f, 0xad, 0xd8, 0xae
		},
		.len = 16
	}
};

static const struct aead_test_data sm4_gcm_case_2 = {
	.algo = RTE_CRYPTO_AEAD_SM4_GCM,
	.key = {
		.data = {
			0x1f, 0x52, 0x3c, 0x62, 0x96, 0xcf, 0xee, 0x91,
			0x4c, 0x54, 0x28, 0xda, 0xdd, 0x6a, 0xa9, 0xad
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xe3, 0x94, 0xea, 0x81, 0x82, 0x30, 0x16, 0x4b,
			0xea, 0x28, 0xeb, 0x3d
		},
		.len = 12
	},
	.aad = {
		.data = sm4_gcm_aad_test_2,
		.len = 4
	},
	.plaintext = {
		.data = {
			0x0f, 0xd2, 0x41, 0x06, 0x07, 0x52, 0x06, 0xf3,
			0xff, 0x36, 0x37, 0x68, 0x2e, 0x59, 0x33, 0xfd
		},
		.len = 16
	},
	.ciphertext = {
		.data = {
			0x64, 0xa7, 0x9c, 0x9d, 0xd4, 0xeb, 0xec, 0x07,
			0x2b, 0xe3, 0xd2, 0x47, 0xf1, 0xce, 0x54, 0x80
		},
		.len = 16
	},
	.auth_tag = {
		.data = {
			0x06, 0x70, 0x8c, 0x2c, 0x8a, 0x52, 0xd1, 0x7e,
			0x35, 0x53, 0x43, 0x31, 0xea, 0x1a, 0xe6, 0xdc
		},
		.len = 16
	}
};

static const struct aead_test_data sm4_gcm_case_3 = {
	.algo = RTE_CRYPTO_AEAD_SM4_GCM,
	.key = {
		.data = {
			0x4f, 0xae, 0xca, 0xe5, 0x31, 0xaf, 0xc0, 0xed,
			0x41, 0xf4, 0xaf, 0xe4, 0xb4, 0x3a, 0x68, 0xcd
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x6c, 0x2a, 0xf4, 0x2c, 0xb0, 0xca, 0x71, 0x5a,
			0x54, 0xc5, 0xb5, 0xfc
		},
		.len = 12
	},
	.aad = {
		.data = sm4_gcm_aad_test_3,
		.len = 8
	},
	.plaintext = {
		.data = {
			0x51, 0x71, 0xc0, 0xf1, 0x11, 0xaa, 0xd7, 0xe3,
			0xdd, 0x03, 0xfa, 0x65, 0x3e, 0xfa, 0x38, 0xe6
		},
		.len = 16
	},
	.ciphertext = {
		.data = {
			0xf9, 0xff, 0xb9, 0xbf, 0x8b, 0xcb, 0xba, 0xd9,
			0x28, 0x8d, 0x9e, 0x7b, 0x53, 0x77, 0x24, 0x6c
		},
		.len = 16
	},
	.auth_tag = {
		.data = {
			0x51, 0x72, 0xbb, 0x14, 0xdb, 0x45, 0xc8, 0x1e,
			0x55, 0x7a, 0x24, 0x0d, 0xa8, 0x39, 0x06, 0x86
		},
		.len = 16
	}
};

static const struct aead_test_data sm4_gcm_case_4 = {
	.algo = RTE_CRYPTO_AEAD_SM4_GCM,
	.key = {
		.data = {
			0x17, 0xc4, 0x8b, 0x7a, 0x40, 0x2d, 0xc0, 0x4b,
			0x26, 0xe4, 0x45, 0x47, 0x72, 0x08, 0x5f, 0x20
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xf0, 0x1e, 0x3a, 0xcd, 0x3b, 0xfd, 0x25, 0x71,
			0xb4, 0x02, 0xa9, 0x5b
		},
		.len = 12
	},
	.aad = {
		.data = sm4_gcm_aad_test_4,
		.len = 7
	},
	.plaintext = {
		.data = {
			0xe9, 0xec, 0x00, 0x14, 0x57, 0x99, 0xb0, 0xc6,
			0x05, 0xa0, 0xfa, 0x01, 0x8f, 0xcf, 0x82, 0xd8
		},
		.len = 16
	},
	.ciphertext = {
		.data = {
			0x62, 0xae, 0x61, 0x4d, 0xcc, 0xb6, 0x2f, 0xce,
			0xe7, 0x81, 0x61, 0x87, 0xe6, 0x95, 0xbc, 0x39
		},
		.len = 16
	},
	.auth_tag = {
		.data = {
			0xbf, 0x20, 0x8d, 0xda, 0x95, 0xc5, 0x63, 0xa8,
			0x13, 0xf2, 0x4c, 0xaf, 0xef, 0xab, 0xa1, 0x38
		},
		.len = 16
	}
};

static const struct aead_test_data sm4_gcm_case_5 = {
	.algo = RTE_CRYPTO_AEAD_SM4_GCM,
	.key = {
		.data = {
			0xc4, 0xc5, 0xa7, 0x1c, 0xef, 0xba, 0x2f, 0x10,
			0x59, 0x2e, 0xd7, 0x19, 0x0e, 0xdf, 0xe5, 0xe0
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x40, 0xa7, 0x92, 0xa1, 0x9b, 0x29, 0x15, 0x3b,
			0x1b, 0xfc, 0x29, 0x6e
		},
		.len = 12
	},
	.aad = {
		.data = 0,
		.len = 0
	},
	.plaintext = {
		.data = {
			0x6e, 0x3d, 0xce, 0x73, 0x73, 0xe9, 0x30, 0xf1,
			0x83, 0x26, 0x7e, 0xeb, 0x8a, 0x16, 0xa5, 0xb6
		},
		.len = 16
	},
	.ciphertext = {
		.data = {
			0x1c, 0x97, 0x37, 0xc3, 0x2c, 0xb6, 0x6c, 0x3c,
			0xb1, 0xbc, 0x49, 0x9c, 0x32, 0x2b, 0x95, 0xca
		},
		.len = 16
	},
	.auth_tag = {
		.data = {
			0x8d, 0xa3, 0x12, 0xa0, 0x68, 0xa0, 0x8c, 0xd7,
			0xf1, 0x72, 0x72, 0xc0, 0xe3, 0x90, 0x3b, 0x50
		},
		.len = 16
	}
};

static const struct aead_test_data sm4_gcm_case_6 = {
	.algo = RTE_CRYPTO_AEAD_SM4_GCM,
	.key = {
		.data = {
			0x96, 0x89, 0x6f, 0xae, 0x4b, 0x9f, 0x16, 0x8a,
			0x61, 0xa2, 0xef, 0x71, 0x7f, 0xee, 0xde, 0x61
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xad, 0x94, 0x73, 0x37, 0x14, 0x2e, 0x60, 0x24,
			0x28, 0xcf, 0xfd, 0x5b
		},
		.len = 12
	},
	.aad = {
		.data = sm4_gcm_aad_test_6,
		.len = 2
	},
	.plaintext = {
		.data = {
			0x64, 0xdd, 0x83, 0x7e, 0xb7, 0x4a, 0x98, 0x0a,
			0x5e, 0xe0, 0xba, 0x48, 0xd4, 0xc7, 0x91, 0x86
		},
		.len = 16
	},
	.ciphertext = {
		.data = {
			0x53, 0xcf, 0x5f, 0x9b, 0x39, 0x40, 0x63, 0x33,
			0x3f, 0x1b, 0xbb, 0xb3, 0x95, 0xd1, 0x3e, 0xa7
		},
		.len = 16
	},
	.auth_tag = {
		.data = {
			0xd7, 0x52, 0x13, 0x2a, 0xb7, 0xe3, 0x5a, 0xaf,
			0xf2, 0x8c, 0x8a, 0x0b, 0xa6, 0xab, 0x0c, 0x8e
		},
		.len = 16
	}
};

static const struct aead_test_data sm4_gcm_case_7 = {
	.algo = RTE_CRYPTO_AEAD_SM4_GCM,
	.key = {
		.data = {
			0x79, 0x86, 0x37, 0x4a, 0x61, 0xea, 0x12, 0x4b,
			0xa8, 0x0c, 0xc4, 0xf8, 0xd7, 0x20, 0xd6, 0x71
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x3f, 0x2b, 0x3a, 0x8b, 0x4d, 0x61, 0x84, 0xe1,
			0x36, 0xfe, 0x9e, 0x35
		},
		.len = 12
	},
	.aad = {
		.data = sm4_gcm_aad_test_7,
		.len = 3
	},
	.plaintext = {
		.data = {
			0x87, 0xc8, 0x7b, 0xa1, 0xc2, 0xf9, 0x58, 0x44,
			0x19, 0x87, 0xd3, 0x43, 0xd9, 0x1a, 0x2f, 0xba
		},
		.len = 16
	},
	.ciphertext = {
		.data = {
			0x36, 0xe2, 0x4f, 0x1d, 0xa0, 0xfa, 0xb8, 0x6e,
			0x07, 0xc1, 0x31, 0xd7, 0x0a, 0x07, 0x0e, 0xcb
		},
		.len = 16
	},
	.auth_tag = {
		.data = {
			0xd3, 0xf0, 0x78, 0x87, 0x90, 0x80, 0x52, 0xcf,
			0x69, 0xb6, 0x6d, 0xd4, 0x59, 0x59, 0x61, 0x05
		},
		.len = 16
	}
};

static const struct aead_test_data sm4_gcm_case_8 = {
	.algo = RTE_CRYPTO_AEAD_SM4_GCM,
	.key = {
		.data = {
			0x34, 0xce, 0x5b, 0x8d, 0x57, 0x57, 0xd0, 0x1b,
			0x8b, 0x96, 0xd7, 0x38, 0x25, 0x44, 0x51, 0xd6
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x0f, 0xc7, 0x27, 0xb8, 0x5a, 0x32, 0x09, 0x70,
			0xe5, 0x46, 0x62, 0xa0
		},
		.len = 12
	},
	.aad = {
		.data = sm4_gcm_aad_test_8,
		.len = 5
	},
	.plaintext = {
		.data = {
			0xc2, 0x32, 0xfa, 0x82, 0xe1, 0x49, 0xda, 0x2e,
			0x2c, 0x9a, 0xc4, 0x87, 0x4c, 0xdc, 0x45, 0x42
		},
		.len = 16
	},
	.ciphertext = {
		.data = {
			0xa3, 0xac, 0xf5, 0xb4, 0xbe, 0xf7, 0x20, 0xdc,
			0xe9, 0x20, 0xf4, 0x21, 0x63, 0xdf, 0xf4, 0x8c
		},
		.len = 16
	},
	.auth_tag = {
		.data = {
			0x60, 0x32, 0x24, 0x47, 0x0b, 0xae, 0xba, 0xfb,
			0x3f, 0xea, 0xc3, 0xf6, 0x92, 0x69, 0x1a, 0xa7
		},
		.len = 16
	}
};

static const struct aead_test_data sm4_gcm_case_9 = {
	.algo = RTE_CRYPTO_AEAD_SM4_GCM,
	.key = {
		.data = {
			0xc3, 0xa6, 0x1f, 0xff, 0xe6, 0x36, 0x44, 0x6a,
			0xc5, 0xc0, 0x87, 0xeb, 0x1a, 0xe5, 0x0d, 0xc4
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xc6, 0x4d, 0x6a, 0xde, 0x76, 0xc6, 0xca, 0xe0,
			0x57, 0x5e, 0x2c, 0xd0
		},
		.len = 12
	},
	.aad = {
		.data = sm4_gcm_aad_test_9,
		.len = 1
	},
	.plaintext = {
		.data = {
			0x3e, 0x4a, 0xac, 0x5b, 0x89, 0xec, 0x2a, 0x83,
			0x2e, 0x7e, 0x93, 0x2b, 0x56, 0xb4, 0x0b, 0xce
		},
		.len = 16
	},
	.ciphertext = {
		.data = {
			0x99, 0xf0, 0x47, 0x8e, 0x8f, 0x92, 0x64, 0x83,
			0xc4, 0xb6, 0x01, 0x2a, 0x4c, 0x17, 0xaa, 0xb5
		},
		.len = 16
	},
	.auth_tag = {
		.data = {
			0x73, 0x84, 0xf9, 0xb7, 0xe2, 0xb9, 0x04, 0xc6,
			0x4f, 0xe5, 0x5d, 0x69, 0x3c, 0xdd, 0xfb, 0xbd
		},
		.len = 16
	}
};

static const struct aead_test_data sm4_gcm_case_10 = {
	.algo = RTE_CRYPTO_AEAD_SM4_GCM,
	.key = {
		.data = {
			0xa8, 0xbe, 0xf3, 0x0b, 0x73, 0x1e, 0xfb, 0x64,
			0x9a, 0x28, 0x58, 0x55, 0x2c, 0xe2, 0x99, 0x4c
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x3f, 0xa7, 0x24, 0x18, 0x2e, 0xc1, 0xaf, 0xae,
			0xe1, 0xb9, 0x70, 0x48
		},
		.len = 12
	},
	.aad = {
		.data = 0,
		.len = 0
	},
	.plaintext = {
		.data = {
			0x2b, 0x85, 0x74, 0x6a, 0xd0, 0x2b, 0x6c, 0x79,
			0x4a, 0x93, 0x97, 0x39, 0xfc, 0xa1, 0x65, 0x96
		},
		.len = 16
	},
	.ciphertext = {
		.data = {
			0x5a, 0x3c, 0xb9, 0x84, 0x17, 0x5a, 0x2c, 0xed,
			0x75, 0xd8, 0x97, 0x60, 0xfa, 0x9b, 0xc2, 0xe8
		},
		.len = 16
	},
	.auth_tag = {
		.data = {
			0xb9, 0xe3, 0xd7, 0x80, 0x7d, 0xea, 0x7a, 0x09,
			0xdc, 0x21, 0x18, 0x3c, 0x8f, 0xfb, 0xe7, 0x63
		},
		.len = 16
	}
};

/*
 * Vector from RFC-8998
 * https://datatracker.ietf.org/doc/html/rfc8998
 * Appendix A.
 */
static const struct aead_test_data sm4_gcm_case_11 = {
	.algo = RTE_CRYPTO_AEAD_SM4_GCM,
	.key = {
		.data = {
			0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef,
			0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x00, 0x12, 0x34, 0x56, 0x78, 0x00, 0x00,
			0x00, 0x00, 0xab, 0xcd
		},
		.len = 12
	},
	.aad = {
		.data = sm4_gcm_aad_test_11,
		.len = 20
	},
	.plaintext = {
		.data = {
			0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
			0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
			0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
			0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
			0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
			0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
			0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
			0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa
		},
		.len = 64
	},
	.ciphertext = {
		.data = {
			0x17, 0xf3, 0x99, 0xf0, 0x8c, 0x67, 0xd5, 0xee,
			0x19, 0xd0, 0xdc, 0x99, 0x69, 0xc4, 0xbb, 0x7d,
			0x5f, 0xd4, 0x6f, 0xd3, 0x75, 0x64, 0x89, 0x06,
			0x91, 0x57, 0xb2, 0x82, 0xbb, 0x20, 0x07, 0x35,
			0xd8, 0x27, 0x10, 0xca, 0x5c, 0x22, 0xf0, 0xcc,
			0xfa, 0x7c, 0xbf, 0x93, 0xd4, 0x96, 0xac, 0x15,
			0xa5, 0x68, 0x34, 0xcb, 0xcf, 0x98, 0xc3, 0x97,
			0xb4, 0x02, 0x4a, 0x26, 0x91, 0x23, 0x3b, 0x8d
		},
		.len = 64
	},
	.auth_tag = {
		.data = {
			0x83, 0xde, 0x35, 0x41, 0xe4, 0xc2, 0xb5, 0x81,
			0x77, 0xe0, 0x65, 0xa9, 0xbf, 0x7b, 0x62, 0xec
		},
		.len = 16
	}
};

/*
 * No plaintext, no AAD vector
 */
static const struct aead_test_data sm4_gcm_case_12 = {
	.algo = RTE_CRYPTO_AEAD_SM4_GCM,
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00
		},
		.len = 12
	},
	.aad = {
		.data = 0,
		.len = 0
	},
	.plaintext = {
		.data = { 0 },
		.len = 0
	},
	.ciphertext = {
		.data = { 0 },
		.len = 0
	},
	.auth_tag = {
		.data = {
			0x23, 0x2f, 0x0c, 0xfe, 0x30, 0x8b, 0x49, 0xea,
			0x6f, 0xc8, 0x82, 0x29, 0xb5, 0xdc, 0x85, 0x8d
		},
		.len = 16
	}
};

/*
 * 16-byte plaintext with all zeros, no AAD
 */
static const struct aead_test_data sm4_gcm_case_13 = {
	.algo = RTE_CRYPTO_AEAD_SM4_GCM,
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00
		},
		.len = 12
	},
	.aad = {
		.data = 0,
		.len = 0
	},
	.plaintext = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
		},
		.len = 16
	},
	.ciphertext = {
		.data = {
			0x7d, 0xe2, 0xaa, 0x7f, 0x11, 0x10, 0x18, 0x82,
			0x18, 0x06, 0x3b, 0xe1, 0xbf, 0xeb, 0x6d, 0x89
		},
		.len = 16
	},
	.auth_tag = {
		.data = {
			0xb8, 0x51, 0xb5, 0xf3, 0x94, 0x93, 0x75, 0x2b,
			0xe5, 0x08, 0xf1, 0xbb, 0x44, 0x82, 0xc5, 0x57
		},
		.len = 16
	}
};

/*
 * No plaintext, 20-byte AAD
 */
static const struct aead_test_data sm4_gcm_case_14 = {
	.algo = RTE_CRYPTO_AEAD_SM4_GCM,
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00
		},
		.len = 12
	},
	.aad = {
		.data = sm4_gcm_aad_test_14,
		.len = 20
	},
	.plaintext = {
		.data = { 0 },
		.len = 0
	},
	.ciphertext = {
		.data = { 0 },
		.len = 0
	},
	.auth_tag = {
		.data = {
			0x97, 0x20, 0x01, 0xb2, 0xd6, 0x04, 0xac, 0xcd,
			0x37, 0x6d, 0x82, 0x9d, 0x35, 0x89, 0xf3, 0xd3
		},
		.len = 16
	}
};

/*
 * Variable sized plaintext, AAD
 */
static const struct aead_test_data sm4_gcm_case_15 = {
	.algo = RTE_CRYPTO_AEAD_SM4_GCM,
	.key = {
		.data = {
			0x69, 0x73, 0x51, 0xff, 0x4a, 0xec, 0x29, 0xcd,
			0xba, 0xab, 0xf2, 0xfb, 0xe3, 0x46, 0x7c, 0xc2
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x54, 0xf8, 0x1b, 0xe8, 0xe7, 0x8d, 0x76, 0x5a,
			0x2e, 0x63, 0x33, 0x9f
		},
		.len = 12
	},
	.aad = {
		.data = sm4_gcm_aad_test_15,
		.len = 39
	},
	.plaintext = {
		.data = {
			0xe1, 0xfc, 0x67, 0x3e, 0x01, 0x7e
		},
		.len = 6
	},
	.ciphertext = {
		.data = {
			0x79, 0x0c, 0x5b, 0x40, 0xcb, 0xbe
		},
		.len = 6
	},
	.auth_tag = {
		.data = {
			0x81, 0x96, 0xee, 0x15, 0x59, 0xac, 0xc9, 0x3d,
			0xac, 0xc0, 0xdc, 0x7c, 0x9a, 0x40, 0x0e, 0x8d
		},
		.len = 16
	}
};

#endif /* TEST_CRYPTODEV_AEAD_TEST_VECTORS_H_ */
