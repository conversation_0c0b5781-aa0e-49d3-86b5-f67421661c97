/* SPDX-License-Identifier: BSD-3-Clause
 * Copyright(C) 2023 Marvell.
 */

#ifndef __TEST_CRYPTODEV_SM2_TEST_VECTORS_H__
#define __TEST_CRYPTODEV_SM2_TEST_VECTORS_H__

#include "rte_crypto_asym.h"

struct crypto_testsuite_sm2_params {
	rte_crypto_param pubkey_qx;
	rte_crypto_param pubkey_qy;
	rte_crypto_param pkey;
	rte_crypto_param k;
	rte_crypto_param sign_r;
	rte_crypto_param sign_s;
	rte_crypto_param id;
	rte_crypto_param cipher;
	rte_crypto_param message;
	rte_crypto_param digest;
	int curve;
};

static uint8_t fp256_pkey[] = {
	0x77, 0x84, 0x35, 0x65, 0x4c, 0x7a, 0x6d, 0xb1,
	0x1e, 0x63, 0x0b, 0x41, 0x97, 0x36, 0x04, 0xf4,
	0xec, 0x35, 0xee, 0x3b, 0x76, 0xc2, 0x34, 0x08,
	0xd9, 0x4a, 0x22, 0x0d, 0x7f, 0xf6, 0xc6, 0x90
};

static uint8_t fp256_qx[] = {
	0x7b, 0x24, 0xa3, 0x03, 0xcf, 0xb2, 0x22, 0xfa,
	0x4c, 0xb3, 0x88, 0x54, 0xf9, 0x30, 0xd1, 0x4d,
	0xe3, 0x50, 0xda, 0xba, 0xe6, 0xa7, 0x0b, 0x91,
	0x4c, 0x04, 0x0d, 0x5c, 0xe0, 0x8e, 0x86, 0xc5
};

static uint8_t fp256_qy[] = {
	0xbc, 0x39, 0xe3, 0x19, 0x4e, 0xd2, 0x29, 0x22,
	0x5b, 0x37, 0x2d, 0xeb, 0xcc, 0x05, 0x52, 0x8d,
	0xb9, 0x40, 0xa3, 0xab, 0x3c, 0xbe, 0x16, 0x30,
	0x1c, 0xe4, 0xe8, 0x7f, 0xba, 0x6e, 0x0b, 0xae
};

static uint8_t fp256_k[] = {
	0x01, 0x04, 0x02, 0x05, 0x04, 0x06, 0x03, 0x07
};

static uint8_t fp256_sign_r[] = {
	0x75, 0x2B, 0x8C, 0x15, 0x38, 0x10, 0xF6, 0xC0,
	0x28, 0xC9, 0x8A, 0x51, 0xD0, 0x62, 0x69, 0x4B,
	0xF6, 0x58, 0x06, 0xEB, 0xF1, 0x91, 0x1F, 0x15,
	0x8B, 0x08, 0x09, 0xF9, 0x88, 0x0A, 0x44, 0x24
};

static uint8_t fp256_sign_s[] = {
	0x5A, 0x3C, 0x96, 0x3E, 0x1C, 0xB4, 0x19, 0xF9,
	0xD7, 0x78, 0xB8, 0xCE, 0xFF, 0x9D, 0xB1, 0x31,
	0x77, 0xDB, 0xA0, 0xFE, 0x84, 0x61, 0x1A, 0xD9,
	0x4E, 0xFF, 0x82, 0x13, 0x1C, 0xCA, 0x04, 0x75,
};

static uint8_t fp256_id[] = {
	0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8
};

static uint8_t fp256_message[] = {
	0x6D, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x20,
	0x64, 0x69, 0x67, 0x65, 0x73, 0x74
};

static uint8_t fp256_digest[] = {
	0x0F, 0xB5, 0xCE, 0xF3, 0x3C, 0xB7, 0xD1, 0x35,
	0xA9, 0x3A, 0xC7, 0xA7, 0x89, 0x2A, 0x6D, 0x9A,
	0xF3, 0x1E, 0xC5, 0x38, 0xD3, 0x65, 0x1B, 0xB9,
	0xDF, 0x5F, 0x7F, 0x4A, 0xD8, 0x89, 0x57, 0xF1
};

static uint8_t fp256_cipher[] = {
	0x30, 0x78, 0x02, 0x21, 0x00, 0xAB, 0xBD, 0xE8,
	0xE8, 0x80, 0x93, 0x36, 0x77, 0xB6, 0x44, 0x47,
	0x6D, 0x00, 0xF6, 0x51, 0xC8, 0x80, 0x9C, 0x9E,
	0xD9, 0xEC, 0x36, 0x8A, 0x60, 0x8E, 0x26, 0x2D,
	0x71, 0x31, 0xB7, 0xC1, 0x38, 0x02, 0x21, 0x00,
	0xE1, 0xBF, 0x4C, 0x13, 0x7A, 0x87, 0x40, 0x32,
	0xF5, 0xA1, 0xE2, 0xA1, 0x3B, 0x83, 0xBF, 0x6B,
	0x3F, 0xFB, 0xC8, 0x13, 0x01, 0xDE, 0xCF, 0xC0,
	0xF4, 0x24, 0x66, 0x52, 0x89, 0xDA, 0x6D, 0x7A,
	0x04, 0x20, 0x8E, 0xFD, 0x52, 0x77, 0xC9, 0xE7,
	0x90, 0xD1, 0x17, 0x75, 0xDE, 0xEE, 0xF3, 0xE5,
	0x11, 0x0C, 0x5D, 0xE1, 0x3A, 0xB6, 0x2B, 0x72,
	0x60, 0xE5, 0xD5, 0xF3, 0x0F, 0xE2, 0x44, 0xDB,
	0xBC, 0x66, 0x04, 0x0E, 0x78, 0x2D, 0xC0, 0x3D,
	0x38, 0xA2, 0x42, 0xA4, 0x8E, 0x8B, 0xF5, 0x06,
	0x32, 0xFA
};

/** SM2 Fp256 elliptic curve test params */
struct crypto_testsuite_sm2_params sm2_param_fp256 = {
	.pubkey_qx = {
		.data = fp256_qx,
		.length = sizeof(fp256_qx),
	},
	.pubkey_qy = {
		.data = fp256_qy,
		.length = sizeof(fp256_qy),
	},
	.k = {
		.data = fp256_k,
		.length = sizeof(fp256_k),
	},
	.sign_r = {
		.data = fp256_sign_r,
		.length = sizeof(fp256_sign_r),
	},
	.sign_s = {
		.data = fp256_sign_s,
		.length = sizeof(fp256_sign_s),
	},
	.id = {
		.data = fp256_id,
		.length = sizeof(fp256_id),
	},
	.pkey = {
		.data = fp256_pkey,
		.length = sizeof(fp256_pkey),
	},
	.message = {
		.data = fp256_message,
		.length = sizeof(fp256_message),
	},
	.digest = {
		.data = fp256_digest,
		.length = sizeof(fp256_digest),
	},
	.cipher = {
		.data = fp256_cipher,
		.length = sizeof(fp256_cipher),
	},
	.curve = RTE_CRYPTO_EC_GROUP_SM2
};

#endif /* __TEST_CRYPTODEV_SM2_TEST_VECTORS_H__ */
