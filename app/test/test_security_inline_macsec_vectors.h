/* SPDX-License-Identifier: BSD-3-Clause
 * Copyright(C) 2023 Marvell.
 */

#ifndef TEST_INLINE_MACSEC_VECTORS_H
#define TEST_INLINE_MACSEC_VECTORS_H

#define MCS_MAX_DATA_SZ				256
#define MCS_MAX_KEY_LEN				32
#define MCS_IV_LEN				12
#define MCS_SALT_LEN				12

enum mcs_op {
	MCS_NO_OP,
	MCS_ENCAP,
	MCS_DECAP,
	MCS_ENCAP_DECAP,
	MCS_AUTH_ONLY,
	MCS_VERIFY_ONLY,
	MCS_AUTH_VERIFY,
};

struct mcs_test_vector {
	uint32_t test_idx;
	enum rte_security_macsec_alg alg;
	uint32_t ssci;
	uint32_t xpn;
	uint8_t salt[MCS_SALT_LEN];
	struct {
		uint8_t data[MCS_MAX_KEY_LEN];
		uint16_t len;
	} sa_key;
	struct {
		uint8_t data[MCS_MAX_DATA_SZ];
		uint16_t len;
	} plain_pkt;
	struct {
		uint8_t data[MCS_MAX_DATA_SZ];
		uint16_t len;
	} secure_pkt;
};

struct mcs_err_vector {
	const struct mcs_test_vector *td;
	const struct mcs_test_vector *rekey_td;
	enum rte_eth_event_macsec_type event;
	enum rte_eth_event_macsec_subtype event_subtype;
	bool notify_event;
};

static const struct mcs_test_vector list_mcs_cipher_vectors[] = {
/* gcm_128_64B_cipher */
{
	.test_idx = 0,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42,
			0x43, 0x44, 0x45, 0x46,
		},
		.len = 64,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x2C,
			/* SL */
			0x0,
			/* PN */
			0x0, 0x0, 0x0, 0x2,
			/* SCI */
			0xFE, 0x2F, 0xCD, 0x14, 0x24, 0x1B, 0x88, 0x2C,
			/* Secure Data */
			0x39, 0x38, 0x97, 0x44, 0xA2, 0x6D, 0x71, 0x3D,
			0x14, 0x27, 0xC7, 0x3E, 0x02, 0x96, 0x81, 0xAD,
			0x47, 0x82, 0x2A, 0xCF, 0x19, 0x79, 0x12, 0x49,
			0x0F, 0x93, 0x5A, 0x32, 0x43, 0x79, 0xEF, 0x9D,
			0x70, 0xF8, 0xA9, 0xBE, 0x3D, 0x00, 0x5D, 0x22,
			0xDA, 0x87, 0x3D, 0xC1, 0xBE, 0x1B, 0x13, 0xD9,
			0x99, 0xDB, 0xF1, 0xC8,
			/* ICV */
			0x4B, 0xC4, 0xF8, 0xC6,	0x09, 0x78, 0xB9, 0xBB,
			0x5D, 0xC0, 0x04, 0xF3,	0x20, 0x7D, 0x14, 0x87,
		},
		.len = 96,
	},
},
/* gcm_128_54B_cipher */
{
	.test_idx = 1,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.xpn = 0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x00, 0x04,
		},
		.len = 54,
	},

	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x4C,
			/* SL */
			0x2A,
			/* PN */
			0x76, 0xD4, 0x57, 0xED,
			/* Secure Data */
			0x13, 0xB4, 0xC7, 0x2B, 0x38, 0x9D, 0xC5, 0x01,
			0x8E, 0x72, 0xA1, 0x71, 0xDD, 0x85, 0xA5, 0xD3,
			0x75, 0x22, 0x74, 0xD3, 0xA0, 0x19, 0xFB, 0xCA,
			0xED, 0x09, 0xA4, 0x25, 0xCD, 0x9B, 0x2E, 0x1C,
			0x9B, 0x72, 0xEE, 0xE7, 0xC9, 0xDE, 0x7D, 0x52,
			0xB3, 0xF3,
			/* ICV */
			0xD6, 0xA5, 0x28, 0x4F, 0x4A, 0x6D, 0x3F, 0xE2,
			0x2A, 0x5D, 0x6C, 0x2B, 0x96, 0x04, 0x94, 0xC3,
		},
		.len = 78,
	},
},
/* gcm_256_54B_cipher */
{
	.test_idx = 2,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_256,
	.ssci = 0x0,
	.xpn = 0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x69, 0x1D, 0x3E, 0xE9, 0x09, 0xD7, 0xF5, 0x41,
			0x67, 0xFD, 0x1C, 0xA0, 0xB5, 0xD7, 0x69, 0x08,
			0x1F, 0x2B, 0xDE, 0x1A, 0xEE, 0x65, 0x5F, 0xDB,
			0xAB, 0x80, 0xBD, 0x52, 0x95, 0xAE, 0x6B, 0xE7,
		},
		.len = 32,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x00, 0x04,
		},
		.len = 54,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x4C,
			/* SL */
			0x2A,
			/* PN */
			0x76, 0xD4, 0x57, 0xED,
			/* Secure Data */
			0xC1, 0x62, 0x3F, 0x55, 0x73, 0x0C, 0x93, 0x53,
			0x30, 0x97, 0xAD, 0xDA, 0xD2, 0x56, 0x64, 0x96,
			0x61, 0x25, 0x35, 0x2B, 0x43, 0xAD, 0xAC, 0xBD,
			0x61, 0xC5, 0xEF, 0x3A, 0xC9, 0x0B, 0x5B, 0xEE,
			0x92, 0x9C, 0xE4, 0x63, 0x0E, 0xA7, 0x9F, 0x6C,
			0xE5, 0x19,
			/* ICV */
			0x12, 0xAF, 0x39, 0xC2, 0xD1, 0xFD, 0xC2, 0x05,
			0x1F, 0x8B, 0x7B, 0x3C, 0x9D, 0x39, 0x7E, 0xF2,
		},
		.len = 78,
	},
},
/* gcm_128_xpn_54B_cipher */
{
	.test_idx = 3,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_128,
	.ssci = 0x7A30C118,
	.xpn = 0xB0DF459C, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE,
		0x86, 0xA2, 0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x00, 0x04,
		},
		.len = 54,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x4C,
			/* SL */
			0x2A,
			/* PN */
			0x76, 0xD4, 0x57, 0xED,
			/* Secure Data */
			0x9C, 0xA4, 0x69, 0x84, 0x43, 0x02, 0x03, 0xED,
			0x41, 0x6E, 0xBD, 0xC2, 0xFE, 0x26, 0x22, 0xBA,
			0x3E, 0x5E, 0xAB, 0x69, 0x61, 0xC3, 0x63, 0x83,
			0x00, 0x9E, 0x18, 0x7E, 0x9B, 0x0C, 0x88, 0x56,
			0x46, 0x53, 0xB9, 0xAB, 0xD2, 0x16, 0x44, 0x1C,
			0x6A, 0xB6,
			/* ICV */
			0xF0, 0xA2, 0x32, 0xE9, 0xE4, 0x4C, 0x97, 0x8C,
			0xF7, 0xCD, 0x84, 0xD4, 0x34, 0x84, 0xD1, 0x01,
		},
		.len = 78,
	},
},
/* gcm_256_xpn_54B_cipher */
{
	.test_idx = 4,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_256,
	.ssci = 0x7A30C118,
	.xpn = 0xB0DF459C, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE,
		0x86, 0xA2, 0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0x69, 0x1D, 0x3E, 0xE9, 0x09, 0xD7, 0xF5, 0x41,
			0x67, 0xFD, 0x1C, 0xA0, 0xB5, 0xD7, 0x69, 0x08,
			0x1F, 0x2B, 0xDE, 0x1A, 0xEE, 0x65, 0x5F, 0xDB,
			0xAB, 0x80, 0xBD, 0x52, 0x95, 0xAE, 0x6B, 0xE7,
		},
		.len = 32,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x00, 0x04,
		},
		.len = 54,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x4C,
			/* SL */
			0x2A,
			/* PN */
			0x76, 0xD4, 0x57, 0xED,
			/* Secure Data */
			0x88, 0xD9, 0xF7, 0xD1, 0xF1, 0x57, 0x8E, 0xE3,
			0x4B, 0xA7, 0xB1, 0xAB, 0xC8, 0x98, 0x93, 0xEF,
			0x1D, 0x33, 0x98, 0xC9, 0xF1, 0xDD, 0x3E, 0x47,
			0xFB, 0xD8, 0x55, 0x3E, 0x0F, 0xF7, 0x86, 0xEF,
			0x56, 0x99, 0xEB, 0x01, 0xEA, 0x10, 0x42, 0x0D,
			0x0E, 0xBD,
			/* ICV */
			0x39, 0xA0, 0xE2, 0x73, 0xC4, 0xC7, 0xF9, 0x5E,
			0xD8, 0x43, 0x20, 0x7D, 0x7A, 0x49, 0x7D, 0xFA,
		},
		.len = 78,
	},
},
/* gcm_128_60B_cipher */
{
	.test_idx = 5,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.xpn = 0x0, /* Most significant 32 bits */
	.salt = {0},
	.sa_key = {
		.data = {
			0xAD, 0x7A, 0x2B, 0xD0, 0x3E, 0xAC, 0x83, 0x5A,
			0x6F, 0x62, 0x0F, 0xDC, 0xB5, 0x06, 0xB3, 0x45,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xD6, 0x09, 0xB1, 0xF0, 0x56, 0x63,
			/* MAC SA */
			0x7A, 0x0D, 0x46, 0xDF, 0x99, 0x8D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x00, 0x02,
		},
		.len = 60,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xD6, 0x09, 0xB1, 0xF0, 0x56, 0x63,
			/* MAC SA */
			0x7A, 0x0D, 0x46, 0xDF, 0x99, 0x8D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x2E,
			/* SL */
			0x00,
			/* PN */
			0xB2, 0xC2, 0x84, 0x65,
			/* SCI */
			0x12, 0x15, 0x35, 0x24, 0xC0, 0x89, 0x5E, 0x81,
			/* Secure Data */
			0x70, 0x1A, 0xFA, 0x1C, 0xC0, 0x39, 0xC0, 0xD7,
			0x65, 0x12, 0x8A, 0x66, 0x5D, 0xAB, 0x69, 0x24,
			0x38, 0x99, 0xBF, 0x73, 0x18, 0xCC, 0xDC, 0x81,
			0xC9, 0x93, 0x1D, 0xA1, 0x7F, 0xBE, 0x8E, 0xDD,
			0x7D, 0x17, 0xCB, 0x8B, 0x4C, 0x26, 0xFC, 0x81,
			0xE3, 0x28, 0x4F, 0x2B, 0x7F, 0xBA, 0x71, 0x3D,
			/* ICV */
			0x4F, 0x8D, 0x55, 0xE7, 0xD3, 0xF0, 0x6F, 0xD5,
			0xA1, 0x3C, 0x0C, 0x29, 0xB9, 0xD5, 0xB8, 0x80,
		},
		.len = 92,
	},
},
/* gcm_256_60B_cipher */
{
	.test_idx = 6,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_256,
	.ssci = 0x0,
	.xpn = 0x0, /* Most significant 32 bits */
	.salt = {0},
	.sa_key = {
		.data = {
			0xE3, 0xC0, 0x8A, 0x8F, 0x06, 0xC6, 0xE3, 0xAD,
			0x95, 0xA7, 0x05, 0x57, 0xB2, 0x3F, 0x75, 0x48,
			0x3C, 0xE3, 0x30, 0x21, 0xA9, 0xC7, 0x2B, 0x70,
			0x25, 0x66, 0x62, 0x04, 0xC6, 0x9C, 0x0B, 0x72,
		},
		.len = 32,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xD6, 0x09, 0xB1, 0xF0, 0x56, 0x63,
			/* MAC SA */
			0x7A, 0x0D, 0x46, 0xDF, 0x99, 0x8D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x00, 0x02,
		},
		.len = 60,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xD6, 0x09, 0xB1, 0xF0, 0x56, 0x63,
			/* MAC SA */
			0x7A, 0x0D, 0x46, 0xDF, 0x99, 0x8D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x2E,
			/* SL */
			0x00,
			/* PN */
			0xB2, 0xC2, 0x84, 0x65,
			/* SCI */
			0x12, 0x15, 0x35, 0x24, 0xC0, 0x89, 0x5E, 0x81,
			/* Secure Data */
			0xE2, 0x00, 0x6E, 0xB4, 0x2F, 0x52, 0x77, 0x02,
			0x2D, 0x9B, 0x19, 0x92, 0x5B, 0xC4, 0x19, 0xD7,
			0xA5, 0x92, 0x66, 0x6C, 0x92, 0x5F, 0xE2, 0xEF,
			0x71, 0x8E, 0xB4, 0xE3, 0x08, 0xEF, 0xEA, 0xA7,
			0xC5, 0x27, 0x3B, 0x39, 0x41, 0x18, 0x86, 0x0A,
			0x5B, 0xE2, 0xA9, 0x7F, 0x56, 0xAB, 0x78, 0x36,
			/* ICV */
			0x5C, 0xA5, 0x97, 0xCD, 0xBB, 0x3E, 0xDB, 0x8D,
			0x1A, 0x11, 0x51, 0xEA, 0x0A, 0xF7, 0xB4, 0x36,
		},
		.len = 92,
	},
},
/* gcm_128_xpn_60B_cipher */
{
	.test_idx = 7,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_128,
	.ssci = 0x7A30C118,
	.xpn = 0xB0DF459C, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE,
		0x86, 0xA2, 0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0xAD, 0x7A, 0x2B, 0xD0, 0x3E, 0xAC, 0x83, 0x5A,
			0x6F, 0x62, 0x0F, 0xDC, 0xB5, 0x06, 0xB3, 0x45,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xD6, 0x09, 0xB1, 0xF0, 0x56, 0x63,
			/* MAC SA */
			0x7A, 0x0D, 0x46, 0xDF, 0x99, 0x8D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x00, 0x02,
		},
		.len = 60,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xD6, 0x09, 0xB1, 0xF0, 0x56, 0x63,
			/* MAC SA */
			0x7A, 0x0D, 0x46, 0xDF, 0x99, 0x8D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x2E,
			/* SL */
			0x00,
			/* PN */
			0xB2, 0xC2, 0x84, 0x65,
			/* SCI */
			0x12, 0x15, 0x35, 0x24, 0xC0, 0x89, 0x5E, 0x81,
			/* Secure Data */
			0x07, 0x12, 0xD9, 0x80, 0xCA, 0x50, 0xBB, 0xED,
			0x35, 0xA0, 0xFA, 0x56, 0x63, 0x38, 0x72, 0x9F,
			0xFA, 0x16, 0xD1, 0x9F, 0xFC, 0xF0, 0x7B, 0x3A,
			0x1E, 0x79, 0x19, 0xB3, 0x77, 0x6A, 0xAC, 0xEC,
			0x8A, 0x59, 0x37, 0x20, 0x8B, 0x48, 0x3A, 0x76,
			0x91, 0x98, 0x4D, 0x38, 0x07, 0x92, 0xE0, 0x7F,
			/* ICV */
			0xC2, 0xC3, 0xC7, 0x9F, 0x26, 0x3F, 0xA6, 0xBF,
			0xF8, 0xE7, 0x58, 0x1E, 0x2C, 0xE4, 0x5A, 0xF8,
		},
		.len = 92,
	},
},
/* gcm_256_xpn_60B_cipher */
{
	.test_idx = 8,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_256,
	.ssci = 0x7A30C118,
	.xpn = 0xB0DF459C, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE,
		0x86, 0xA2, 0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0xE3, 0xC0, 0x8A, 0x8F, 0x06, 0xC6, 0xE3, 0xAD,
			0x95, 0xA7, 0x05, 0x57, 0xB2, 0x3F, 0x75, 0x48,
			0x3C, 0xE3, 0x30, 0x21, 0xA9, 0xC7, 0x2B, 0x70,
			0x25, 0x66, 0x62, 0x04, 0xC6, 0x9C, 0x0B, 0x72,
		},
		.len = 32,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xD6, 0x09, 0xB1, 0xF0, 0x56, 0x63,
			/* MAC SA */
			0x7A, 0x0D, 0x46, 0xDF, 0x99, 0x8D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x00, 0x02,
		},
		.len = 60,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xD6, 0x09, 0xB1, 0xF0, 0x56, 0x63,
			/* MAC SA */
			0x7A, 0x0D, 0x46, 0xDF, 0x99, 0x8D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x2E,
			/* SL */
			0x00,
			/* PN */
			0xB2, 0xC2, 0x84, 0x65,
			/* SCI */
			0x12, 0x15, 0x35, 0x24, 0xC0, 0x89, 0x5E, 0x81,
			/* Secure Data */
			0x3E, 0xB0, 0x4A, 0x4B, 0xBF, 0x54, 0xC6, 0xEB,
			0x12, 0x22, 0xA9, 0xAE, 0xA0, 0x0C, 0x38, 0x68,
			0x7F, 0x6C, 0x35, 0x20, 0xD9, 0x76, 0xA3, 0xB6,
			0x94, 0x80, 0x06, 0x50, 0xCE, 0x65, 0x85, 0xE6,
			0x20, 0xA4, 0x19, 0x19, 0x17, 0xD2, 0xA6, 0x05,
			0xD8, 0x70, 0xC7, 0x8D, 0x27, 0x52, 0xCE, 0x49,
			/* ICV */
			0x3B, 0x44, 0x2A, 0xC0, 0xC8, 0x16, 0xD7, 0xAB,
			0xD7, 0x0A, 0xD6, 0x5C, 0x25, 0xD4, 0x64, 0x13,
		},
		.len = 92,
	},
},
/* gcm_128_61B_cipher */
{
	.test_idx = 9,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.xpn = 0x0, /* Most significant 32 bits */
	.salt = {0},
	.sa_key = {
		.data = {
			0x01, 0x3F, 0xE0, 0x0B, 0x5F, 0x11, 0xBE, 0x7F,
			0x86, 0x6D, 0x0C, 0xBB, 0xC5, 0x5A, 0x7A, 0x90,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0x84, 0xC5, 0xD5, 0x13, 0xD2, 0xAA,
			/* MAC SA */
			0xF6, 0xE5, 0xBB, 0xD2, 0x72, 0x77,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x00,
			0x06,
		},
		.len = 61,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0x84, 0xC5, 0xD5, 0x13, 0xD2, 0xAA,
			/* MAC SA */
			0xF6, 0xE5, 0xBB, 0xD2, 0x72, 0x77,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x2F,
			/* SL */
			0x00,
			/* PN */
			0x89, 0x32, 0xD6, 0x12,
			/* SCI */
			0x7C, 0xFD, 0xE9, 0xF9, 0xE3, 0x37, 0x24, 0xC6,
			/* Secure Data */
			0x3A, 0x4D, 0xE6, 0xFA, 0x32, 0x19, 0x10, 0x14,
			0xDB, 0xB3, 0x03, 0xD9, 0x2E, 0xE3, 0xA9, 0xE8,
			0xA1, 0xB5, 0x99, 0xC1, 0x4D, 0x22, 0xFB, 0x08,
			0x00, 0x96, 0xE1, 0x38, 0x11, 0x81, 0x6A, 0x3C,
			0x9C, 0x9B, 0xCF, 0x7C, 0x1B, 0x9B, 0x96, 0xDA,
			0x80, 0x92, 0x04, 0xE2, 0x9D, 0x0E, 0x2A, 0x76,
			0x42,
			/* ICV */
			0xBF, 0xD3, 0x10, 0xA4, 0x83, 0x7C, 0x81, 0x6C,
			0xCF, 0xA5, 0xAC, 0x23, 0xAB, 0x00, 0x39, 0x88,
		},
		.len = 93,
	},
},
/* gcm_256_61B_cipher */
{
	.test_idx = 10,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_256,
	.ssci = 0x0,
	.xpn = 0x0, /* Most significant 32 bits */
	.salt = {0},
	.sa_key = {
		.data = {
			0x83, 0xC0, 0x93, 0xB5, 0x8D, 0xE7, 0xFF, 0xE1,
			0xC0, 0xDA, 0x92, 0x6A, 0xC4, 0x3F, 0xB3, 0x60,
			0x9A, 0xC1, 0xC8, 0x0F, 0xEE, 0x1B, 0x62, 0x44,
			0x97, 0xEF, 0x94, 0x2E, 0x2F, 0x79, 0xA8, 0x23,
		},
		.len = 32,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0x84, 0xC5, 0xD5, 0x13, 0xD2, 0xAA,
			/* MAC SA */
			0xF6, 0xE5, 0xBB, 0xD2, 0x72, 0x77,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x00,
			0x06,
		},
		.len = 61,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0x84, 0xC5, 0xD5, 0x13, 0xD2, 0xAA,
			/* MAC SA */
			0xF6, 0xE5, 0xBB, 0xD2, 0x72, 0x77,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x2F,
			/* SL */
			0x00,
			/* PN */
			0x89, 0x32, 0xD6, 0x12,
			/* SCI */
			0x7C, 0xFD, 0xE9, 0xF9, 0xE3, 0x37, 0x24, 0xC6,
			/* Secure Data */
			0x11, 0x02, 0x22, 0xFF, 0x80, 0x50, 0xCB, 0xEC,
			0xE6, 0x6A, 0x81, 0x3A, 0xD0, 0x9A, 0x73, 0xED,
			0x7A, 0x9A, 0x08, 0x9C, 0x10, 0x6B, 0x95, 0x93,
			0x89, 0x16, 0x8E, 0xD6, 0xE8, 0x69, 0x8E, 0xA9,
			0x02, 0xEB, 0x12, 0x77, 0xDB, 0xEC, 0x2E, 0x68,
			0xE4, 0x73, 0x15, 0x5A, 0x15, 0xA7, 0xDA, 0xEE,
			0xD4,
			/* ICV */
			0xA1, 0x0F, 0x4E, 0x05, 0x13, 0x9C, 0x23, 0xDF,
			0x00, 0xB3, 0xAA, 0xDC, 0x71, 0xF0, 0x59, 0x6A,
		},
		.len = 93,
	},
},
/* gcm_128_xpn_61B_cipher */
{
	.test_idx = 11,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_128,
	.ssci = 0x7A30C118,
	.xpn = 0xB0DF459C, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE,
		0x86, 0xA2, 0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0x01, 0x3F, 0xE0, 0x0B, 0x5F, 0x11, 0xBE, 0x7F,
			0x86, 0x6D, 0x0C, 0xBB, 0xC5, 0x5A, 0x7A, 0x90,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0x84, 0xC5, 0xD5, 0x13, 0xD2, 0xAA,
			/* MAC SA */
			0xF6, 0xE5, 0xBB, 0xD2, 0x72, 0x77,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x00,
			0x06,
		},
		.len = 61,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0x84, 0xC5, 0xD5, 0x13, 0xD2, 0xAA,
			/* MAC SA */
			0xF6, 0xE5, 0xBB, 0xD2, 0x72, 0x77,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x2F,
			/* SL */
			0x00,
			/* PN */
			0x89, 0x32, 0xD6, 0x12,
			/* SCI */
			0x7C, 0xFD, 0xE9, 0xF9, 0xE3, 0x37, 0x24, 0xC6,
			/* Secure Data */
			0x14, 0xC1, 0x76, 0x93, 0xBC, 0x82, 0x97, 0xEE,
			0x6C, 0x47, 0xC5, 0x65, 0xCB, 0xE0, 0x67, 0x9E,
			0x80, 0xF0, 0x0F, 0xCA, 0xF5, 0x92, 0xC9, 0xAA,
			0x04, 0x73, 0x92, 0x8E, 0x7F, 0x2F, 0x21, 0x6F,
			0xF5, 0xA0, 0x33, 0xDE, 0xC7, 0x51, 0x3F, 0x45,
			0xD3, 0x4C, 0xBB, 0x98, 0x1C, 0x5B, 0xD6, 0x4E,
			0x8B,
			/* ICV */
			0xD8, 0x4B, 0x8E, 0x2A, 0x78, 0xE7, 0x4D, 0xAF,
			0xEA, 0xA0, 0x38, 0x46, 0xFE, 0x93, 0x0C, 0x0E,
		},
		.len = 93,
	},
},
/* gcm_256_xpn_61B_cipher */
{
	.test_idx = 12,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_256,
	.ssci = 0x7A30C118,
	.xpn = 0xB0DF459C, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE,
		0x86, 0xA2, 0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0x83, 0xC0, 0x93, 0xB5, 0x8D, 0xE7, 0xFF, 0xE1,
			0xC0, 0xDA, 0x92, 0x6A, 0xC4, 0x3F, 0xB3, 0x60,
			0x9A, 0xC1, 0xC8, 0x0F, 0xEE, 0x1B, 0x62, 0x44,
			0x97, 0xEF, 0x94, 0x2E, 0x2F, 0x79, 0xA8, 0x23,
		},
		.len = 32,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0x84, 0xC5, 0xD5, 0x13, 0xD2, 0xAA,
			/* MAC SA */
			0xF6, 0xE5, 0xBB, 0xD2, 0x72, 0x77,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x00,
			0x06,
		},
		.len = 61,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0x84, 0xC5, 0xD5, 0x13, 0xD2, 0xAA,
			/* MAC SA */
			0xF6, 0xE5, 0xBB, 0xD2, 0x72, 0x77,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x2F,
			/* SL */
			0x00,
			/* PN */
			0x89, 0x32, 0xD6, 0x12,
			/* SCI */
			0x7C, 0xFD, 0xE9, 0xF9, 0xE3, 0x37, 0x24, 0xC6,
			/* Secure Data */
			0x09, 0x96, 0xE0, 0xC9, 0xA5, 0x57, 0x74, 0xE0,
			0xA7, 0x92, 0x30, 0x4E, 0x7D, 0xC1, 0x50, 0xBD,
			0x67, 0xFD, 0x74, 0x7D, 0xD1, 0xB9, 0x41, 0x95,
			0x94, 0xBF, 0x37, 0x3D, 0x4A, 0xCE, 0x8F, 0x87,
			0xF5, 0xC1, 0x34, 0x9A, 0xFA, 0xC4, 0x91, 0xAA,
			0x0A, 0x40, 0xD3, 0x19, 0x90, 0x87, 0xB2, 0x9F,
			0xDF,
			/* ICV */
			0x80, 0x2F, 0x05, 0x0E, 0x69, 0x1F, 0x11, 0xA2,
			0xD9, 0xB3, 0x58, 0xF6, 0x99, 0x41, 0x84, 0xF5,
		},
		.len = 93,
	},
},
/* gcm_128_75B_cipher */
{
	.test_idx = 13,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0,
	.xpn = 0, /* Most significant 32 bits */
	.salt = {0},
	.sa_key = {
		.data = {
			0x88, 0xEE, 0x08, 0x7F, 0xD9, 0x5D, 0xA9, 0xFB,
			0xF6, 0x72, 0x5A, 0xA9, 0xD7, 0x57, 0xB0, 0xCD,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0x68, 0xF2, 0xE7, 0x76, 0x96, 0xCE,
			/* MAC SA */
			0x7A, 0xE8, 0xE2, 0xCA, 0x4E, 0xC5,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C,
			0x3D, 0x3E, 0x3F, 0x40, 0x41, 0x42, 0x43, 0x44,
			0x45, 0x46, 0x47, 0x48, 0x49, 0x00, 0x08,
		},
		.len = 75,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0x68, 0xF2, 0xE7, 0x76, 0x96, 0xCE,
			/* MAC SA */
			0x7A, 0xE8, 0xE2, 0xCA, 0x4E, 0xC5,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x4D,
			/* SL */
			0x00,
			/* PN */
			0x2E, 0x58, 0x49, 0x5C,
			/* SCI */
			/* Secure Data */
			0xC3, 0x1F, 0x53, 0xD9, 0x9E, 0x56, 0x87, 0xF7,
			0x36, 0x51, 0x19, 0xB8, 0x32, 0xD2, 0xAA, 0xE7,
			0x07, 0x41, 0xD5, 0x93, 0xF1, 0xF9, 0xE2, 0xAB,
			0x34, 0x55, 0x77, 0x9B, 0x07, 0x8E, 0xB8, 0xFE,
			0xAC, 0xDF, 0xEC, 0x1F, 0x8E, 0x3E, 0x52, 0x77,
			0xF8, 0x18, 0x0B, 0x43, 0x36, 0x1F, 0x65, 0x12,
			0xAD, 0xB1, 0x6D, 0x2E, 0x38, 0x54, 0x8A, 0x2C,
			0x71, 0x9D, 0xBA, 0x72, 0x28, 0xD8, 0x40,
			/* ICV */
			0x88, 0xF8, 0x75, 0x7A, 0xDB, 0x8A, 0xA7, 0x88,
			0xD8, 0xF6, 0x5A, 0xD6, 0x68, 0xBE, 0x70, 0xE7,
		},
		.len = 99,
	},
},
/* gcm_256_75B_cipher */
{
	.test_idx = 14,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_256,
	.ssci = 0,
	.xpn = 0, /* Most significant 32 bits */
	.salt = {0},
	.sa_key = {
		.data = {
			0x4C, 0x97, 0x3D, 0xBC, 0x73, 0x64, 0x62, 0x16,
			0x74, 0xF8, 0xB5, 0xB8, 0x9E, 0x5C, 0x15, 0x51,
			0x1F, 0xCE, 0xD9, 0x21, 0x64, 0x90, 0xFB, 0x1C,
			0x1A, 0x2C, 0xAA, 0x0F, 0xFE, 0x04, 0x07, 0xE5,
		},
		.len = 32,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0x68, 0xF2, 0xE7, 0x76, 0x96, 0xCE,
			/* MAC SA */
			0x7A, 0xE8, 0xE2, 0xCA, 0x4E, 0xC5,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C,
			0x3D, 0x3E, 0x3F, 0x40, 0x41, 0x42, 0x43, 0x44,
			0x45, 0x46, 0x47, 0x48, 0x49, 0x00, 0x08,
		},
		.len = 75,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0x68, 0xF2, 0xE7, 0x76, 0x96, 0xCE,
			/* MAC SA */
			0x7A, 0xE8, 0xE2, 0xCA, 0x4E, 0xC5,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x4D,
			/* SL */
			0x00,
			/* PN */
			0x2E, 0x58, 0x49, 0x5C,
			/* SCI */
			/* Secure Data */
			0xBA, 0x8A, 0xE3, 0x1B, 0xC5, 0x06, 0x48, 0x6D,
			0x68, 0x73, 0xE4, 0xFC, 0xE4, 0x60, 0xE7, 0xDC,
			0x57, 0x59, 0x1F, 0xF0, 0x06, 0x11, 0xF3, 0x1C,
			0x38, 0x34, 0xFE, 0x1C, 0x04, 0xAD, 0x80, 0xB6,
			0x68, 0x03, 0xAF, 0xCF, 0x5B, 0x27, 0xE6, 0x33,
			0x3F, 0xA6, 0x7C, 0x99, 0xDA, 0x47, 0xC2, 0xF0,
			0xCE, 0xD6, 0x8D, 0x53, 0x1B, 0xD7, 0x41, 0xA9,
			0x43, 0xCF, 0xF7, 0xA6, 0x71, 0x3B, 0xD0,
			/* ICV */
			0x26, 0x11, 0xCD, 0x7D, 0xAA, 0x01, 0xD6, 0x1C,
			0x5C, 0x88, 0x6D, 0xC1, 0xA8, 0x17, 0x01, 0x07,
		},
		.len = 99,
	},
},
/* gcm_128_xpn_75B_cipher */
{
	.test_idx = 15,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_128,
	.ssci = 0x7A30C118,
	.xpn = 0xB0DF459C, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE, 0x86, 0xA2,
		0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0x88, 0xEE, 0x08, 0x7F, 0xD9, 0x5D, 0xA9, 0xFB,
			0xF6, 0x72, 0x5A, 0xA9, 0xD7, 0x57, 0xB0, 0xCD,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0x68, 0xF2, 0xE7, 0x76, 0x96, 0xCE,
			/* MAC SA */
			0x7A, 0xE8, 0xE2, 0xCA, 0x4E, 0xC5,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C,
			0x3D, 0x3E, 0x3F, 0x40, 0x41, 0x42, 0x43, 0x44,
			0x45, 0x46, 0x47, 0x48, 0x49, 0x00, 0x08,
		},
		.len = 75,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0x68, 0xF2, 0xE7, 0x76, 0x96, 0xCE,
			/* MAC SA */
			0x7A, 0xE8, 0xE2, 0xCA, 0x4E, 0xC5,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x4D,
			/* SL */
			0x00,
			/* PN */
			0x2E, 0x58, 0x49, 0x5C,
			/* SCI */
			/* Secure Data */
			0xEA, 0xEC, 0xC6, 0xAF, 0x65, 0x12, 0xFC, 0x8B,
			0x6C, 0x8C, 0x43, 0xBC, 0x55, 0xB1, 0x90, 0xB2,
			0x62, 0x6D, 0x07, 0xD3, 0xD2, 0x18, 0xFA, 0xF5,
			0xDA, 0xA7, 0xD8, 0xF8, 0x00, 0xA5, 0x73, 0x31,
			0xEB, 0x43, 0xB5, 0xA1, 0x7A, 0x37, 0xE5, 0xB1,
			0xD6, 0x0D, 0x27, 0x5C, 0xCA, 0xF7, 0xAC, 0xD7,
			0x04, 0xCC, 0x9A, 0xCE, 0x2B, 0xF8, 0xBC, 0x8B,
			0x9B, 0x23, 0xB9, 0xAD, 0xF0, 0x2F, 0x87,
			/* ICV */
			0x34, 0x6B, 0x96, 0xD1, 0x13, 0x6A, 0x75, 0x4D,
			0xF0, 0xA6, 0xCD, 0xE1, 0x26, 0xC1, 0x07, 0xF8,
		},
		.len = 99,
	},
},
/* gcm_256_xpn_75B_cipher */
{
	.test_idx = 16,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_256,
	.ssci = 0x7A30C118,
	.xpn = 0xB0DF459C, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE, 0x86, 0xA2,
		0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0x4C, 0x97, 0x3D, 0xBC, 0x73, 0x64, 0x62, 0x16,
			0x74, 0xF8, 0xB5, 0xB8, 0x9E, 0x5C, 0x15, 0x51,
			0x1F, 0xCE, 0xD9, 0x21, 0x64, 0x90, 0xFB, 0x1C,
			0x1A, 0x2C, 0xAA, 0x0F, 0xFE, 0x04, 0x07, 0xE5,
		},
		.len = 32,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0x68, 0xF2, 0xE7, 0x76, 0x96, 0xCE,
			/* MAC SA */
			0x7A, 0xE8, 0xE2, 0xCA, 0x4E, 0xC5,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C,
			0x3D, 0x3E, 0x3F, 0x40, 0x41, 0x42, 0x43, 0x44,
			0x45, 0x46, 0x47, 0x48, 0x49, 0x00, 0x08,
		},
		.len = 75,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0x68, 0xF2, 0xE7, 0x76, 0x96, 0xCE,
			/* MAC SA */
			0x7A, 0xE8, 0xE2, 0xCA, 0x4E, 0xC5,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x4D,
			/* SL */
			0x00,
			/* PN */
			0x2E, 0x58, 0x49, 0x5C,
			/* SCI */
			/* Secure Data */
			0xB0, 0xFE, 0xA3, 0x63, 0x18, 0xB9, 0xB3, 0x64,
			0x66, 0xC4, 0x6E, 0x9E, 0x1B, 0xDA, 0x1A, 0x26,
			0x68, 0x58, 0x19, 0x6E, 0x7E, 0x70, 0xD8, 0x82,
			0xAE, 0x70, 0x47, 0x56, 0x68, 0xCD, 0xE4, 0xEC,
			0x88, 0x3F, 0x6A, 0xC2, 0x36, 0x9F, 0x28, 0x4B,
			0xED, 0x1F, 0xE3, 0x2F, 0x42, 0x09, 0x2F, 0xDF,
			0xF5, 0x86, 0x8A, 0x3C, 0x64, 0xE5, 0x61, 0x51,
			0x92, 0xA7, 0xA3, 0x76, 0x0B, 0x34, 0xBC,
			/* ICV */
			0x85, 0x69, 0x2C, 0xD8, 0x15, 0xB6, 0x64, 0x71,
			0x1A, 0xEF, 0x91, 0x1D, 0xF7, 0x8D, 0x7F, 0x46,
		},
		.len = 99,
	},
},
};

static const struct mcs_test_vector list_mcs_integrity_vectors[] = {
/* gcm_128_54B_integrity */
{
	.test_idx = 1,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0,
	.xpn = 0, /* Most significant 32 bits */
	.salt = {0},
	.sa_key = {
		.data = {
			0xAD, 0x7A, 0x2B, 0xD0, 0x3E, 0xAC, 0x83, 0x5A,
			0x6F, 0x62, 0x0F, 0xDC, 0xB5, 0x06, 0xB3, 0x45,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xD6, 0x09, 0xB1, 0xF0, 0x56, 0x63,
			/* MAC SA */
			0x7A, 0x0D, 0x46, 0xDF, 0x99, 0x8D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x00, 0x01,
		},
		.len = 54,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xD6, 0x09, 0xB1, 0xF0, 0x56, 0x63,
			/* MAC SA */
			0x7A, 0x0D, 0x46, 0xDF, 0x99, 0x8D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x22,
			/* SL */
			0x2A,
			/* PN */
			0xB2, 0xC2, 0x84, 0x65,
			/* SCI */
			0x12, 0x15, 0x35, 0x24, 0xC0, 0x89, 0x5E, 0x81,
			/* Secure Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x00, 0x01,
			/* ICV */
			0xF0, 0x94, 0x78, 0xA9, 0xB0, 0x90, 0x07, 0xD0,
			0x6F, 0x46, 0xE9, 0xB6, 0xA1, 0xDA, 0x25, 0xDD,
		},
		.len = 86,
	},
},
/* gcm_256_54B_integrity */
{
	.test_idx = 2,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_256,
	.ssci = 0,
	.xpn = 0, /* Most significant 32 bits */
	.salt = {0},
	.sa_key = {
		.data = {
			0xE3, 0xC0, 0x8A, 0x8F, 0x06, 0xC6, 0xE3, 0xAD,
			0x95, 0xA7, 0x05, 0x57, 0xB2, 0x3F, 0x75, 0x48,
			0x3C, 0xE3, 0x30, 0x21, 0xA9, 0xC7, 0x2B, 0x70,
			0x25, 0x66, 0x62, 0x04, 0xC6, 0x9C, 0x0B, 0x72,
		},
		.len = 32,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xD6, 0x09, 0xB1, 0xF0, 0x56, 0x63,
			/* MAC SA */
			0x7A, 0x0D, 0x46, 0xDF, 0x99, 0x8D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x00, 0x01,
		},
		.len = 54,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xD6, 0x09, 0xB1, 0xF0, 0x56, 0x63,
			/* MAC SA */
			0x7A, 0x0D, 0x46, 0xDF, 0x99, 0x8D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x22,
			/* SL */
			0x2A,
			/* PN */
			0xB2, 0xC2, 0x84, 0x65,
			/* SCI */
			0x12, 0x15, 0x35, 0x24, 0xC0, 0x89, 0x5E, 0x81,
			/* Secure Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x00, 0x01,
			/* ICV */
			0x2F, 0x0B, 0xC5, 0xAF, 0x40, 0x9E, 0x06, 0xD6,
			0x09, 0xEA, 0x8B, 0x7D, 0x0F, 0xA5, 0xEA, 0x50,
		},
		.len = 86,
	},
},
/* gcm_128_xpn_54B_integrity */
{
	.test_idx = 3,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_128,
	.ssci = 0x7A30C118,
	.xpn = 0xB0DF459C, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE, 0x86, 0xA2,
		0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0xAD, 0x7A, 0x2B, 0xD0, 0x3E, 0xAC, 0x83, 0x5A,
			0x6F, 0x62, 0x0F, 0xDC, 0xB5, 0x06, 0xB3, 0x45,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xD6, 0x09, 0xB1, 0xF0, 0x56, 0x63,
			/* MAC SA */
			0x7A, 0x0D, 0x46, 0xDF, 0x99, 0x8D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x00, 0x01,
		},
		.len = 54,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xD6, 0x09, 0xB1, 0xF0, 0x56, 0x63,
			/* MAC SA */
			0x7A, 0x0D, 0x46, 0xDF, 0x99, 0x8D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x22,
			/* SL */
			0x2A,
			/* PN */
			0xB2, 0xC2, 0x84, 0x65,
			/* SCI */
			0x12, 0x15, 0x35, 0x24, 0xC0, 0x89, 0x5E, 0x81,
			/* Secure Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x00, 0x01,
			/* ICV */
			0x17, 0xFE, 0x19, 0x81, 0xEB, 0xDD, 0x4A, 0xFC,
			0x50, 0x62, 0x69, 0x7E, 0x8B, 0xAA, 0x0C, 0x23,
		},
		.len = 86,
	},
},
/* gcm_256_xpn_54B_integrity */
{
	.test_idx = 4,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_256,
	.ssci = 0x7A30C118,
	.xpn = 0xB0DF459C, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE, 0x86, 0xA2,
		0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0xE3, 0xC0, 0x8A, 0x8F, 0x06, 0xC6, 0xE3, 0xAD,
			0x95, 0xA7, 0x05, 0x57, 0xB2, 0x3F, 0x75, 0x48,
			0x3C, 0xE3, 0x30, 0x21, 0xA9, 0xC7, 0x2B, 0x70,
			0x25, 0x66, 0x62, 0x04, 0xC6, 0x9C, 0x0B, 0x72,
		},
		.len = 32,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xD6, 0x09, 0xB1, 0xF0, 0x56, 0x63,
			/* MAC SA */
			0x7A, 0x0D, 0x46, 0xDF, 0x99, 0x8D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x00, 0x01,
		},
		.len = 54,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xD6, 0x09, 0xB1, 0xF0, 0x56, 0x63,
			/* MAC SA */
			0x7A, 0x0D, 0x46, 0xDF, 0x99, 0x8D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x22,
			/* SL */
			0x2A,
			/* PN */
			0xB2, 0xC2, 0x84, 0x65,
			/* SCI */
			0x12, 0x15, 0x35, 0x24, 0xC0, 0x89, 0x5E, 0x81,
			/* Secure Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x00, 0x01,
			/* ICV */
			0x4D, 0xBD, 0x2F, 0x6A, 0x75, 0x4A, 0x6C, 0xF7,
			0x28, 0xCC, 0x12, 0x9B, 0xA6, 0x93, 0x15, 0x77,
		},
		.len = 86,
	},
},
/* gcm_128_60B_integrity */
{
	.test_idx = 5,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0,
	.xpn = 0, /* Most significant 32 bits */
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x00, 0x03,
		},
		.len = 60,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x40,
			/* SL */
			0x00,
			/* PN */
			0x76, 0xD4, 0x57, 0xED,
			/* SCI */
			/* Secure Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x00, 0x03,
			/* ICV */
			0x0C, 0x01, 0x7B, 0xC7, 0x3B, 0x22, 0x7D, 0xFC,
			0xC9, 0xBA, 0xFA, 0x1C, 0x41, 0xAC, 0xC3, 0x53,
		},
		.len = 84,
	},
},
/* gcm_256_60B_integrity */
{
	.test_idx = 6,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_256,
	.ssci = 0,
	.xpn = 0, /* Most significant 32 bits */
	.salt = {0},
	.sa_key = {
		.data = {
			0x69, 0x1D, 0x3E, 0xE9, 0x09, 0xD7, 0xF5, 0x41,
			0x67, 0xFD, 0x1C, 0xA0, 0xB5, 0xD7, 0x69, 0x08,
			0x1F, 0x2B, 0xDE, 0x1A, 0xEE, 0x65, 0x5F, 0xDB,
			0xAB, 0x80, 0xBD, 0x52, 0x95, 0xAE, 0x6B, 0xE7,
		},
		.len = 32,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x00, 0x03,
		},
		.len = 60,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x40,
			/* SL */
			0x00,
			/* PN */
			0x76, 0xD4, 0x57, 0xED,
			/* SCI */
			/* Secure Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x00, 0x03,
			/* ICV */
			0x35, 0x21, 0x7C, 0x77, 0x4B, 0xBC, 0x31, 0xB6,
			0x31, 0x66, 0xBC, 0xF9, 0xD4, 0xAB, 0xED, 0x07,
		},
		.len = 84,
	},
},
/* gcm_128_xpn_60B_integrity */
{
	.test_idx = 7,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_128,
	.ssci = 0x7A30C118,
	.xpn = 0xB0DF459C, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE, 0x86, 0xA2,
		0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x00, 0x03,
		},
		.len = 60,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x40,
			/* SL */
			0x00,
			/* PN */
			0x76, 0xD4, 0x57, 0xED,
			/* SCI */
			/* Secure Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x00, 0x03,
			/* ICV */
			0xAB, 0xC4, 0x06, 0x85, 0xA3, 0xCF, 0x91, 0x1D,
			0x37, 0x87, 0xE4, 0x9D, 0xB6, 0xA7, 0x26, 0x5E,
		},
		.len = 84,
	},
},
/* gcm_256_xpn_60B_integrity */
{
	.test_idx = 8,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_256,
	.ssci = 0x7A30C118,
	.xpn = 0xB0DF459C, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE, 0x86, 0xA2,
		0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0x69, 0x1D, 0x3E, 0xE9, 0x09, 0xD7, 0xF5, 0x41,
			0x67, 0xFD, 0x1C, 0xA0, 0xB5, 0xD7, 0x69, 0x08,
			0x1F, 0x2B, 0xDE, 0x1A, 0xEE, 0x65, 0x5F, 0xDB,
			0xAB, 0x80, 0xBD, 0x52, 0x95, 0xAE, 0x6B, 0xE7,
		},
		.len = 32,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x00, 0x03,
		},
		.len = 60,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x40,
			/* SL */
			0x00,
			/* PN */
			0x76, 0xD4, 0x57, 0xED,
			/* SCI */
			/* Secure Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x00, 0x03,
			/* ICV */
			0xAC, 0x21, 0x95, 0x7B, 0x83, 0x12, 0xAB, 0x3C,
			0x99, 0xAB, 0x46, 0x84, 0x98, 0x79, 0xC3, 0xF3,
		},
		.len = 84,
	},
},
/* gcm_128_65B_integrity */
{
	.test_idx = 9,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0,
	.xpn = 0, /* Most significant 32 bits */
	.salt = {0},
	.sa_key = {
		.data = {
			0x01, 0x3F, 0xE0, 0x0B, 0x5F, 0x11, 0xBE, 0x7F,
			0x86, 0x6D, 0x0C, 0xBB, 0xC5, 0x5A, 0x7A, 0x90,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0x84, 0xC5, 0xD5, 0x13, 0xD2, 0xAA,
			/* MAC SA */
			0xF6, 0xE5, 0xBB, 0xD2, 0x72, 0x77,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C,
			0x3D, 0x3E, 0x3F, 0x00, 0x05,
		},
		.len = 65,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0x84, 0xC5, 0xD5, 0x13, 0xD2, 0xAA,
			/* MAC SA */
			0xF6, 0xE5, 0xBB, 0xD2, 0x72, 0x77,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x23,
			/* SL */
			0x00,
			/* PN */
			0x89, 0x32, 0xD6, 0x12,
			/* SCI */
			0x7C, 0xFD, 0xE9, 0xF9, 0xE3, 0x37, 0x24, 0xC6,
			/* Secure Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C,
			0x3D, 0x3E, 0x3F, 0x00, 0x05,
			/* ICV */
			0x21, 0x78, 0x67, 0xE5, 0x0C, 0x2D, 0xAD, 0x74,
			0xC2, 0x8C, 0x3B, 0x50, 0xAB, 0xDF, 0x69, 0x5A,
		},
		.len = 97,
	},
},
/* gcm_256_65B_integrity */
{
	.test_idx = 10,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_256,
	.ssci = 0,
	.xpn = 0, /* Most significant 32 bits */
	.salt = {0},
	.sa_key = {
		.data = {
			0x83, 0xC0, 0x93, 0xB5, 0x8D, 0xE7, 0xFF, 0xE1,
			0xC0, 0xDA, 0x92, 0x6A, 0xC4, 0x3F, 0xB3, 0x60,
			0x9A, 0xC1, 0xC8, 0x0F, 0xEE, 0x1B, 0x62, 0x44,
			0x97, 0xEF, 0x94, 0x2E, 0x2F, 0x79, 0xA8, 0x23,
		},
		.len = 32,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0x84, 0xC5, 0xD5, 0x13, 0xD2, 0xAA,
			/* MAC SA */
			0xF6, 0xE5, 0xBB, 0xD2, 0x72, 0x77,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C,
			0x3D, 0x3E, 0x3F, 0x00, 0x05,
		},
		.len = 65,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0x84, 0xC5, 0xD5, 0x13, 0xD2, 0xAA,
			/* MAC SA */
			0xF6, 0xE5, 0xBB, 0xD2, 0x72, 0x77,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x23,
			/* SL */
			0x00,
			/* PN */
			0x89, 0x32, 0xD6, 0x12,
			/* SCI */
			0x7C, 0xFD, 0xE9, 0xF9, 0xE3, 0x37, 0x24, 0xC6,
			/* Secure Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C,
			0x3D, 0x3E, 0x3F, 0x00, 0x05,
			/* ICV */
			0x6E, 0xE1, 0x60, 0xE8, 0xFA, 0xEC, 0xA4, 0xB3,
			0x6C, 0x86, 0xB2, 0x34, 0x92, 0x0C, 0xA9, 0x75,
		},
		.len = 97,
	},
},
/* gcm_128_xpn_65B_integrity */
{
	.test_idx = 11,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_128,
	.ssci = 0x7A30C118,
	.xpn = 0xB0DF459C, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE, 0x86, 0xA2,
		0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0x01, 0x3F, 0xE0, 0x0B, 0x5F, 0x11, 0xBE, 0x7F,
			0x86, 0x6D, 0x0C, 0xBB, 0xC5, 0x5A, 0x7A, 0x90,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0x84, 0xC5, 0xD5, 0x13, 0xD2, 0xAA,
			/* MAC SA */
			0xF6, 0xE5, 0xBB, 0xD2, 0x72, 0x77,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C,
			0x3D, 0x3E, 0x3F, 0x00, 0x05,
		},
		.len = 65,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0x84, 0xC5, 0xD5, 0x13, 0xD2, 0xAA,
			/* MAC SA */
			0xF6, 0xE5, 0xBB, 0xD2, 0x72, 0x77,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x23,
			/* SL */
			0x00,
			/* PN */
			0x89, 0x32, 0xD6, 0x12,
			/* SCI */
			0x7C, 0xFD, 0xE9, 0xF9, 0xE3, 0x37, 0x24, 0xC6,
			/* Secure Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C,
			0x3D, 0x3E, 0x3F, 0x00, 0x05,
			/* ICV */
			0x67, 0x85, 0x59, 0xB7, 0xE5, 0x2D, 0xB0, 0x06,
			0x82, 0xE3, 0xB8, 0x30, 0x34, 0xCE, 0xBE, 0x59,
		},
		.len = 97,
	},
},
/* gcm_256_xpn_65B_integrity */
{
	.test_idx = 12,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_256,
	.ssci = 0x7A30C118,
	.xpn = 0xB0DF459C, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE, 0x86, 0xA2,
		0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0x83, 0xC0, 0x93, 0xB5, 0x8D, 0xE7, 0xFF, 0xE1,
			0xC0, 0xDA, 0x92, 0x6A, 0xC4, 0x3F, 0xB3, 0x60,
			0x9A, 0xC1, 0xC8, 0x0F, 0xEE, 0x1B, 0x62, 0x44,
			0x97, 0xEF, 0x94, 0x2E, 0x2F, 0x79, 0xA8, 0x23,
		},
		.len = 32,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0x84, 0xC5, 0xD5, 0x13, 0xD2, 0xAA,
			/* MAC SA */
			0xF6, 0xE5, 0xBB, 0xD2, 0x72, 0x77,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C,
			0x3D, 0x3E, 0x3F, 0x00, 0x05,
		},
		.len = 65,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0x84, 0xC5, 0xD5, 0x13, 0xD2, 0xAA,
			/* MAC SA */
			0xF6, 0xE5, 0xBB, 0xD2, 0x72, 0x77,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x23,
			/* SL */
			0x00,
			/* PN */
			0x89, 0x32, 0xD6, 0x12,
			/* SCI */
			0x7C, 0xFD, 0xE9, 0xF9, 0xE3, 0x37, 0x24, 0xC6,
			/* Secure Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C,
			0x3D, 0x3E, 0x3F, 0x00, 0x05,
			/* ICV */
			0x84, 0xBA, 0xC8, 0xE5, 0x3D, 0x1E, 0xA3, 0x55,
			0xA5, 0xC7, 0xD3, 0x34, 0x84, 0x0A, 0xE9, 0x62,
		},
		.len = 97,
	},
},
/* gcm_128_79B_integrity */
{
	.test_idx = 13,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0,
	.xpn = 0, /* Most significant 32 bits */
	.salt = {0},
	.sa_key = {
		.data = {
			0x88, 0xEE, 0x08, 0x7F, 0xD9, 0x5D, 0xA9, 0xFB,
			0xF6, 0x72, 0x5A, 0xA9, 0xD7, 0x57, 0xB0, 0xCD,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0x68, 0xF2, 0xE7, 0x76, 0x96, 0xCE,
			/* MAC SA */
			0x7A, 0xE8, 0xE2, 0xCA, 0x4E, 0xC5,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C,
			0x3D, 0x3E, 0x3F, 0x40, 0x41, 0x42, 0x43, 0x44,
			0x45, 0x46, 0x47, 0x48, 0x49, 0x4A, 0x4B, 0x4C,
			0x4D, 0x00, 0x07,
		},
		.len = 79,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0x68, 0xF2, 0xE7, 0x76, 0x96, 0xCE,
			/* MAC SA */
			0x7A, 0xE8, 0xE2, 0xCA, 0x4E, 0xC5,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x41,
			/* SL */
			0x00,
			/* PN */
			0x2E, 0x58, 0x49, 0x5C,
			/* SCI */
			/* Secure Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C,
			0x3D, 0x3E, 0x3F, 0x40, 0x41, 0x42, 0x43, 0x44,
			0x45, 0x46, 0x47, 0x48, 0x49, 0x4A, 0x4B, 0x4C,
			0x4D, 0x00, 0x07,
			/* ICV */
			0x07, 0x92, 0x2B, 0x8E, 0xBC, 0xF1, 0x0B, 0xB2,
			0x29, 0x75, 0x88, 0xCA, 0x4C, 0x61, 0x45, 0x23,
		},
		.len = 103,
	},
},
/* gcm_256_79B_integrity */
{
	.test_idx = 14,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_256,
	.ssci = 0,
	.xpn = 0, /* Most significant 32 bits */
	.salt = {0},
	.sa_key = {
		.data = {
			0x4C, 0x97, 0x3D, 0xBC, 0x73, 0x64, 0x62, 0x16,
			0x74, 0xF8, 0xB5, 0xB8, 0x9E, 0x5C, 0x15, 0x51,
			0x1F, 0xCE, 0xD9, 0x21, 0x64, 0x90, 0xFB, 0x1C,
			0x1A, 0x2C, 0xAA, 0x0F, 0xFE, 0x04, 0x07, 0xE5,
		},
		.len = 32,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0x68, 0xF2, 0xE7, 0x76, 0x96, 0xCE,
			/* MAC SA */
			0x7A, 0xE8, 0xE2, 0xCA, 0x4E, 0xC5,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C,
			0x3D, 0x3E, 0x3F, 0x40, 0x41, 0x42, 0x43, 0x44,
			0x45, 0x46, 0x47, 0x48, 0x49, 0x4A, 0x4B, 0x4C,
			0x4D, 0x00, 0x07,
		},
		.len = 79,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0x68, 0xF2, 0xE7, 0x76, 0x96, 0xCE,
			/* MAC SA */
			0x7A, 0xE8, 0xE2, 0xCA, 0x4E, 0xC5,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x41,
			/* SL */
			0x00,
			/* PN */
			0x2E, 0x58, 0x49, 0x5C,
			/* SCI */
			/* Secure Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C,
			0x3D, 0x3E, 0x3F, 0x40, 0x41, 0x42, 0x43, 0x44,
			0x45, 0x46, 0x47, 0x48, 0x49, 0x4A, 0x4B, 0x4C,
			0x4D, 0x00, 0x07,
			/* ICV */
			0x00, 0xBD, 0xA1, 0xB7, 0xE8, 0x76, 0x08, 0xBC,
			0xBF, 0x47, 0x0F, 0x12, 0x15, 0x7F, 0x4C, 0x07,
		},
		.len = 103,
	},
},
/* gcm_128_xpn_79B_integrity */
{
	.test_idx = 15,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_128,
	.ssci = 0x7A30C118,
	.xpn = 0xB0DF459C, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE, 0x86, 0xA2,
		0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0x88, 0xEE, 0x08, 0x7F, 0xD9, 0x5D, 0xA9, 0xFB,
			0xF6, 0x72, 0x5A, 0xA9, 0xD7, 0x57, 0xB0, 0xCD,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0x68, 0xF2, 0xE7, 0x76, 0x96, 0xCE,
			/* MAC SA */
			0x7A, 0xE8, 0xE2, 0xCA, 0x4E, 0xC5,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C,
			0x3D, 0x3E, 0x3F, 0x40, 0x41, 0x42, 0x43, 0x44,
			0x45, 0x46, 0x47, 0x48, 0x49, 0x4A, 0x4B, 0x4C,
			0x4D, 0x00, 0x07,
		},
		.len = 79,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0x68, 0xF2, 0xE7, 0x76, 0x96, 0xCE,
			/* MAC SA */
			0x7A, 0xE8, 0xE2, 0xCA, 0x4E, 0xC5,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x41,
			/* SL */
			0x00,
			/* PN */
			0x2E, 0x58, 0x49, 0x5C,
			/* SCI */
			/* Secure Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C,
			0x3D, 0x3E, 0x3F, 0x40, 0x41, 0x42, 0x43, 0x44,
			0x45, 0x46, 0x47, 0x48, 0x49, 0x4A, 0x4B, 0x4C,
			0x4D, 0x00, 0x07,
			/* ICV */
			0xD0, 0xDC, 0x89, 0x6D, 0xC8, 0x37, 0x98, 0xA7,
			0x9F, 0x3C, 0x5A, 0x95, 0xBA, 0x3C, 0xDF, 0x9A,
		},
		.len = 103,
	},
},
/* gcm_256_xpn_79B_integrity */
{
	.test_idx = 16,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_256,
	.ssci = 0x7A30C118,
	.xpn = 0xB0DF459C, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE, 0x86, 0xA2,
		0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0x4C, 0x97, 0x3D, 0xBC, 0x73, 0x64, 0x62, 0x16,
			0x74, 0xF8, 0xB5, 0xB8, 0x9E, 0x5C, 0x15, 0x51,
			0x1F, 0xCE, 0xD9, 0x21, 0x64, 0x90, 0xFB, 0x1C,
			0x1A, 0x2C, 0xAA, 0x0F, 0xFE, 0x04, 0x07, 0xE5,
		},
		.len = 32,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0x68, 0xF2, 0xE7, 0x76, 0x96, 0xCE,
			/* MAC SA */
			0x7A, 0xE8, 0xE2, 0xCA, 0x4E, 0xC5,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C,
			0x3D, 0x3E, 0x3F, 0x40, 0x41, 0x42, 0x43, 0x44,
			0x45, 0x46, 0x47, 0x48, 0x49, 0x4A, 0x4B, 0x4C,
			0x4D, 0x00, 0x07,
		},
		.len = 79,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0x68, 0xF2, 0xE7, 0x76, 0x96, 0xCE,
			/* MAC SA */
			0x7A, 0xE8, 0xE2, 0xCA, 0x4E, 0xC5,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x41,
			/* SL */
			0x00,
			/* PN */
			0x2E, 0x58, 0x49, 0x5C,
			/* SCI */
			/* Secure Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C,
			0x3D, 0x3E, 0x3F, 0x40, 0x41, 0x42, 0x43, 0x44,
			0x45, 0x46, 0x47, 0x48, 0x49, 0x4A, 0x4B, 0x4C,
			0x4D, 0x00, 0x07,
			/* ICV */
			0x04, 0x24, 0x9A, 0x20, 0x8A, 0x65, 0xB9, 0x6B,
			0x3F, 0x32, 0x63, 0x00, 0x4C, 0xFD, 0x86, 0x7D,
		},
		.len = 103,
	},
},
};

static const struct mcs_test_vector list_mcs_err_cipher_vectors[] = {
/* gcm_128_64B_cipher */
{
	.test_idx = 0,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42,
			0x43, 0x44, 0x45, 0x46,
		},
		.len = 64,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x2C,
			/* SL */
			0x0,
			/* PN */
			0x0, 0x0, 0x0, 0x2,
			/* SCI */
			0xFE, 0x2F, 0xCD, 0x14, 0x24, 0x1B, 0x88, 0x2C,
			/* Secure Data */
			0x38, 0x38, 0x97, 0x44, 0xA2, 0x6D, 0x71, 0x3D,
			0x14, 0x27, 0xC7, 0x3E, 0x02, 0x96, 0x81, 0xAD,
			0x47, 0x82, 0x2A, 0xCF, 0x19, 0x79, 0x12, 0x49,
			0x0F, 0x93, 0x5A, 0x32, 0x43, 0x79, 0xEF, 0x9D,
			0x70, 0xF8, 0xA9, 0xBE, 0x3D, 0x00, 0x5D, 0x22,
			0xDA, 0x87, 0x3D, 0xC1, 0xBE, 0x1B, 0x13, 0xD9,
			0x99, 0xDB, 0xF1, 0xC8,
			/* ICV */
			0x4B, 0xC4, 0xF8, 0xC6,	0x09, 0x78, 0xB9, 0xBB,
			0x5D, 0xC0, 0x04, 0xF3,	0x20, 0x7D, 0x14, 0x87,
		},
		.len = 96,
	}
}
};

static const struct mcs_test_vector list_mcs_untagged_cipher_vectors[] = {
{
	.test_idx = 0,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42,
			0x43, 0x44, 0x45, 0x46,
		},
		.len = 64,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* Wrong MACsec EtherType */
			0x88, 0xD7,
			/* TCI and AN */
			0x2C,
			/* SL */
			0x0,
			/* PN */
			0x0, 0x0, 0x0, 0x2,
			/* SCI */
			0xFE, 0x2F, 0xCD, 0x14, 0x24, 0x1B, 0x88, 0x2C,
			/* Secure Data */
			0x39, 0x38, 0x97, 0x44, 0xA2, 0x6D, 0x71, 0x3D,
			0x14, 0x27, 0xC7, 0x3E, 0x02, 0x96, 0x81, 0xAD,
			0x47, 0x82, 0x2A, 0xCF, 0x19, 0x79, 0x12, 0x49,
			0x0F, 0x93, 0x5A, 0x32, 0x43, 0x79, 0xEF, 0x9D,
			0x70, 0xF8, 0xA9, 0xBE, 0x3D, 0x00, 0x5D, 0x22,
			0xDA, 0x87, 0x3D, 0xC1, 0xBE, 0x1B, 0x13, 0xD9,
			0x99, 0xDB, 0xF1, 0xC8,
			/* ICV */
			0x4B, 0xC4, 0xF8, 0xC6,	0x09, 0x78, 0xB9, 0xBB,
			0x5D, 0xC0, 0x04, 0xF3,	0x20, 0x7D, 0x14, 0x87,
		},
		.len = 96,
	},
},
};

static const struct mcs_test_vector list_mcs_bad_tag_vectors[] = {
{
	.test_idx = 0,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42,
			0x43, 0x44, 0x45, 0x46,
		},
		.len = 64,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI{V = 1} and AN */
			0xAC,
			/* SL */
			0x0,
			/* PN */
			0x0, 0x0, 0x0, 0x2,
			/* SCI */
			0xFE, 0x2F, 0xCD, 0x14, 0x24, 0x1B, 0x88, 0x2C,
			/* Secure Data */
			0x39, 0x38, 0x97, 0x44, 0xA2, 0x6D, 0x71, 0x3D,
			0x14, 0x27, 0xC7, 0x3E, 0x02, 0x96, 0x81, 0xAD,
			0x47, 0x82, 0x2A, 0xCF, 0x19, 0x79, 0x12, 0x49,
			0x0F, 0x93, 0x5A, 0x32, 0x43, 0x79, 0xEF, 0x9D,
			0x70, 0xF8, 0xA9, 0xBE, 0x3D, 0x00, 0x5D, 0x22,
			0xDA, 0x87, 0x3D, 0xC1, 0xBE, 0x1B, 0x13, 0xD9,
			0x99, 0xDB, 0xF1, 0xC8,
			/* ICV */
			0x4B, 0xC4, 0xF8, 0xC6,	0x09, 0x78, 0xB9, 0xBB,
			0x5D, 0xC0, 0x04, 0xF3,	0x20, 0x7D, 0x14, 0x87,
		},
		.len = 96,
	},
},
{
	.test_idx = 1,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42,
			0x43, 0x44, 0x45, 0x46,
		},
		.len = 64,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI{E,C = 2'b01} and AN */
			0x24,
			/* SL */
			0x0,
			/* PN */
			0x0, 0x0, 0x0, 0x2,
			/* SCI */
			0xFE, 0x2F, 0xCD, 0x14, 0x24, 0x1B, 0x88, 0x2C,
			/* Secure Data */
			0x39, 0x38, 0x97, 0x44, 0xA2, 0x6D, 0x71, 0x3D,
			0x14, 0x27, 0xC7, 0x3E, 0x02, 0x96, 0x81, 0xAD,
			0x47, 0x82, 0x2A, 0xCF, 0x19, 0x79, 0x12, 0x49,
			0x0F, 0x93, 0x5A, 0x32, 0x43, 0x79, 0xEF, 0x9D,
			0x70, 0xF8, 0xA9, 0xBE, 0x3D, 0x00, 0x5D, 0x22,
			0xDA, 0x87, 0x3D, 0xC1, 0xBE, 0x1B, 0x13, 0xD9,
			0x99, 0xDB, 0xF1, 0xC8,
			/* ICV */
			0x4B, 0xC4, 0xF8, 0xC6,	0x09, 0x78, 0xB9, 0xBB,
			0x5D, 0xC0, 0x04, 0xF3,	0x20, 0x7D, 0x14, 0x87,
		},
		.len = 96,
	},
},
{
	.test_idx = 2,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42,
			0x43, 0x44, 0x45, 0x46,
		},
		.len = 64,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI{ES = 1 && SC = 1} and AN */
			0x6C,
			/* SL */
			0x0,
			/* PN */
			0x0, 0x0, 0x0, 0x2,
			/* SCI */
			0xFE, 0x2F, 0xCD, 0x14, 0x24, 0x1B, 0x88, 0x2C,
			/* Secure Data */
			0x39, 0x38, 0x97, 0x44, 0xA2, 0x6D, 0x71, 0x3D,
			0x14, 0x27, 0xC7, 0x3E, 0x02, 0x96, 0x81, 0xAD,
			0x47, 0x82, 0x2A, 0xCF, 0x19, 0x79, 0x12, 0x49,
			0x0F, 0x93, 0x5A, 0x32, 0x43, 0x79, 0xEF, 0x9D,
			0x70, 0xF8, 0xA9, 0xBE, 0x3D, 0x00, 0x5D, 0x22,
			0xDA, 0x87, 0x3D, 0xC1, 0xBE, 0x1B, 0x13, 0xD9,
			0x99, 0xDB, 0xF1, 0xC8,
			/* ICV */
			0x4B, 0xC4, 0xF8, 0xC6,	0x09, 0x78, 0xB9, 0xBB,
			0x5D, 0xC0, 0x04, 0xF3,	0x20, 0x7D, 0x14, 0x87,
		},
		.len = 96,
	},
},
{
	.test_idx = 3,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42,
			0x43, 0x44, 0x45, 0x46,
		},
		.len = 64,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI{SCB = 1 && SC = 1} and AN */
			0x3C,
			/* SL */
			0x0,
			/* PN */
			0x0, 0x0, 0x0, 0x2,
			/* SCI */
			0xFE, 0x2F, 0xCD, 0x14, 0x24, 0x1B, 0x88, 0x2C,
			/* Secure Data */
			0x39, 0x38, 0x97, 0x44, 0xA2, 0x6D, 0x71, 0x3D,
			0x14, 0x27, 0xC7, 0x3E, 0x02, 0x96, 0x81, 0xAD,
			0x47, 0x82, 0x2A, 0xCF, 0x19, 0x79, 0x12, 0x49,
			0x0F, 0x93, 0x5A, 0x32, 0x43, 0x79, 0xEF, 0x9D,
			0x70, 0xF8, 0xA9, 0xBE, 0x3D, 0x00, 0x5D, 0x22,
			0xDA, 0x87, 0x3D, 0xC1, 0xBE, 0x1B, 0x13, 0xD9,
			0x99, 0xDB, 0xF1, 0xC8,
			/* ICV */
			0x4B, 0xC4, 0xF8, 0xC6,	0x09, 0x78, 0xB9, 0xBB,
			0x5D, 0xC0, 0x04, 0xF3,	0x20, 0x7D, 0x14, 0x87,
		},
		.len = 96,
	},
},
{
	.test_idx = 4,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.xpn = 0x0, /* Most significant 32 bits */
	.ssci = 0x0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42,
			0x43, 0x44, 0x45, 0x46,
		},
		.len = 64,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x2C,
			/* SL */
			0x0,
			/* PN = 0 */
			0x0, 0x0, 0x0, 0x0,
			/* SCI */
			0xFE, 0x2F, 0xCD, 0x14, 0x24, 0x1B, 0x88, 0x2C,
			/* Secure Data */
			0x39, 0x38, 0x97, 0x44, 0xA2, 0x6D, 0x71, 0x3D,
			0x14, 0x27, 0xC7, 0x3E, 0x02, 0x96, 0x81, 0xAD,
			0x47, 0x82, 0x2A, 0xCF, 0x19, 0x79, 0x12, 0x49,
			0x0F, 0x93, 0x5A, 0x32, 0x43, 0x79, 0xEF, 0x9D,
			0x70, 0xF8, 0xA9, 0xBE, 0x3D, 0x00, 0x5D, 0x22,
			0xDA, 0x87, 0x3D, 0xC1, 0xBE, 0x1B, 0x13, 0xD9,
			0x99, 0xDB, 0xF1, 0xC8,
			/* ICV */
			0x4B, 0xC4, 0xF8, 0xC6,	0x09, 0x78, 0xB9, 0xBB,
			0x5D, 0xC0, 0x04, 0xF3,	0x20, 0x7D, 0x14, 0x87,
		},
		.len = 96,
	},
},
{
	.test_idx = 5,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42,
			0x43, 0x44, 0x45, 0x46,
		},
		.len = 64,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x2C,
			/* SL */
			0x80,
			/* PN = 0 */
			0x0, 0x0, 0x0, 0x2,
			/* SCI */
			0xFE, 0x2F, 0xCD, 0x14, 0x24, 0x1B, 0x88, 0x2C,
			/* Secure Data */
			0x39, 0x38, 0x97, 0x44, 0xA2, 0x6D, 0x71, 0x3D,
			0x14, 0x27, 0xC7, 0x3E, 0x02, 0x96, 0x81, 0xAD,
			0x47, 0x82, 0x2A, 0xCF, 0x19, 0x79, 0x12, 0x49,
			0x0F, 0x93, 0x5A, 0x32, 0x43, 0x79, 0xEF, 0x9D,
			0x70, 0xF8, 0xA9, 0xBE, 0x3D, 0x00, 0x5D, 0x22,
			0xDA, 0x87, 0x3D, 0xC1, 0xBE, 0x1B, 0x13, 0xD9,
			0x99, 0xDB, 0xF1, 0xC8,
			/* ICV */
			0x4B, 0xC4, 0xF8, 0xC6,	0x09, 0x78, 0xB9, 0xBB,
			0x5D, 0xC0, 0x04, 0xF3,	0x20, 0x7D, 0x14, 0x87,
		},
		.len = 96,
	},
},
};

#define MCS_MULTI_FLOW_TD_KEY_SZ		16
#define MCS_MULTI_FLOW_TD_PLAIN_DATA_SZ		42
#define MCS_MULTI_FLOW_TD_SECURE_DATA_SZ	66
#define MCS_MULTI_FLOW_TD_KEY_SZ		16
#define MCS_MAX_FLOWS				63

uint8_t sa_key[MCS_MULTI_FLOW_TD_KEY_SZ] = {
		0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
		0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82,
};

uint8_t eth_addrs[MCS_MAX_FLOWS][2 * RTE_ETHER_ADDR_LEN] = {
		{0xE2, 0x00, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x02, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x03, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x04, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x05, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x06, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x07, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x08, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x09, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x0A, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x0B, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x0C, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x0D, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x0E, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x0F, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x10, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x11, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x12, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x13, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x14, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x15, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x16, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x17, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x18, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x19, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x1A, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x1B, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x1C, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x1D, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x1E, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x1F, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x20, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x21, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x22, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x23, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x24, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x25, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x26, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x27, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x28, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x29, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x2A, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x2B, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x2C, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x2D, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x2E, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x2F, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x30, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x31, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x32, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x33, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x34, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x35, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x36, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x37, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x38, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x39, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x3A, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x3B, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x3C, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x3D, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
		{0xE2, 0x3E, 0x06, 0xD7, 0xCD, 0x0D, 0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,},
};

uint8_t plain_user_data[MCS_MULTI_FLOW_TD_PLAIN_DATA_SZ] = {
		/* User Data with Ethertype */
		0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
		0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
		0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
		0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
		0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
		0x00, 0x04,
};

uint8_t secure_user_data[MCS_MULTI_FLOW_TD_SECURE_DATA_SZ] = {
		/* MACsec EtherType */
		0x88, 0xE5,
		/* TCI and AN */
		0x4C,
		/* SL */
		0x2A,
		/* PN */
		0x76, 0xD4, 0x57, 0xED,
		/* Secure Data */
		0x13, 0xB4, 0xC7, 0x2B, 0x38, 0x9D, 0xC5, 0x01,
		0x8E, 0x72, 0xA1, 0x71, 0xDD, 0x85, 0xA5, 0xD3,
		0x75, 0x22, 0x74, 0xD3, 0xA0, 0x19, 0xFB, 0xCA,
		0xED, 0x09, 0xA4, 0x25, 0xCD, 0x9B, 0x2E, 0x1C,
		0x9B, 0x72, 0xEE, 0xE7, 0xC9, 0xDE, 0x7D, 0x52,
		0xB3, 0xF3,
		/* ICV */
		0xD6, 0xA5, 0x28, 0x4F, 0x4A, 0x6D, 0x3F, 0xE2,
		0x2A, 0x5D, 0x6C, 0x2B, 0x96, 0x04, 0x94, 0xC3,
};

static const struct mcs_test_vector list_mcs_vlan_vectors[] = {
/* No clear tag, VLAN after macsec header */
{
	.test_idx = 1,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0,
	.xpn = 0, /* Most significant 32 bits */
	.salt = {0},
	.sa_key = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xCA, 0xCB, 0xCD, 0x41, 0x42, 0x43,
			/* MAC SA */
			0xCA, 0xCB, 0xCD, 0x21, 0x22, 0x23,
			/* User Data with VLAN Tag */
			0x81, 0x00, 0x00, 0x02, 0x08, 0x00, 0x45, 0x00,
			0x00, 0x54, 0xF2, 0xFA, 0x40, 0x00, 0x40, 0x01,
			0xF7, 0x83, 0x14, 0x14, 0x14, 0x02, 0x14, 0x14,
			0x14, 0x01, 0x08, 0x00, 0xE9, 0xC5, 0x02, 0xAF,
			0x00, 0x01, 0xCB, 0x51, 0x6D, 0x38, 0x00, 0x00,
			0x00, 0x00, 0x13, 0x2D, 0x01, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
			0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D,
			0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
			0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C, 0x2D,
			0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
			0x36, 0x37,
		},
		.len = 102,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xCA, 0xCB, 0xCD, 0x41, 0x42, 0x43,
			/* MAC SA */
			0xCA, 0xCB, 0xCD, 0x21, 0x22, 0x23,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x20,
			/* SL */
			0x00,
			/* PN */
			0x00, 0x00, 0x00, 0x06,
			/* SCI */
			0xCA, 0xCB, 0xCD, 0x21, 0x22, 0x23, 0x00, 0x01,
			/* Secure Data */
			0x81, 0x00, 0x00, 0x02, 0x08, 0x00, 0x45, 0x00,
			0x00, 0x54, 0xF2, 0xFA, 0x40, 0x00, 0x40, 0x01,
			0xF7, 0x83, 0x14, 0x14, 0x14, 0x02, 0x14, 0x14,
			0x14, 0x01, 0x08, 0x00, 0xE9, 0xC5, 0x02, 0xAF,
			0x00, 0x01, 0xCB, 0x51, 0x6D, 0x38, 0x00, 0x00,
			0x00, 0x00, 0x13, 0x2D, 0x01, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
			0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D,
			0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
			0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C, 0x2D,
			0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
			0x36, 0x37,
			/* ICV */
			0x21, 0x68, 0xF1, 0x21, 0x19, 0xB7, 0xDF, 0x73,
			0x6F, 0x2A, 0x11, 0xEA, 0x8A, 0xBC, 0x8A, 0x79,
		},
		.len = 134,
	},
},
/* 1 vlan tag followed by MACsec */
{
	.test_idx = 2,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0,
	.xpn = 0, /* Most significant 32 bits */
	.salt = {0},
	.sa_key = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xCA, 0xCB, 0xCD, 0x41, 0x42, 0x43,
			/* MAC SA */
			0xCA, 0xCB, 0xCD, 0x21, 0x22, 0x23,
			/* User Data */
			0x81, 0x00, 0x00, 0x02,
			0x08, 0x00, 0x45, 0x00, 0x00, 0x54, 0x88, 0x71,
			0x40, 0x00, 0x40, 0x01, 0x62, 0x0D, 0x14, 0x14,
			0x14, 0x02, 0x14, 0x14, 0x14, 0x01, 0x08, 0x00,
			0x77, 0xA6, 0x02, 0xB3, 0x00, 0x01, 0xBE, 0x52,
			0x6D, 0x38, 0x00, 0x00, 0x00, 0x00, 0x8C, 0x47,
			0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x11,
			0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,
			0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F, 0x20, 0x21,
			0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,
			0x2A, 0x2B, 0x2C, 0x2D, 0x2E, 0x2F, 0x30, 0x31,
			0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
		},
		.len = 102,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xCA, 0xCB, 0xCD, 0x41, 0x42, 0x43,
			/* MAC SA */
			0xCA, 0xCB, 0xCD, 0x21, 0x22, 0x23,
			/* VLAN Tag before MACsec */
			0x81, 0x00, 0x00, 0x02,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x20,
			/* SL */
			0x00,
			/* PN */
			0x00, 0x00, 0x00, 0x07,
			/* SCI */
			0xCA, 0xCB, 0xCD, 0x21, 0x22, 0x23, 0x00, 0x01,
			/* Secure Data */
			0x08, 0x00, 0x45, 0x00, 0x00, 0x54, 0x88, 0x71,
			0x40, 0x00, 0x40, 0x01, 0x62, 0x0D, 0x14, 0x14,
			0x14, 0x02, 0x14, 0x14, 0x14, 0x01, 0x08, 0x00,
			0x77, 0xA6, 0x02, 0xB3, 0x00, 0x01, 0xBE, 0x52,
			0x6D, 0x38, 0x00, 0x00, 0x00, 0x00, 0x8C, 0x47,
			0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x11,
			0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,
			0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F, 0x20, 0x21,
			0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,
			0x2A, 0x2B, 0x2C, 0x2D, 0x2E, 0x2F, 0x30, 0x31,
			0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
			/* ICV */
			0xF1, 0xC0, 0xA2, 0x6E, 0x99, 0xE5, 0xAB, 0x97,
			0x78, 0x79, 0x7D, 0x13, 0x35, 0x5E, 0x39, 0x4F,
		},
		.len = 134,
	},
},
/* 2 vlan tag followed by MACsec */
{
	.test_idx = 3,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0,
	.xpn = 0, /* Most significant 32 bits */
	.salt = {0},
	.sa_key = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xCA, 0xCB, 0xCD, 0x41, 0x42, 0x43,
			/* MAC SA */
			0xCA, 0xCB, 0xCD, 0x21, 0x22, 0x23,
			/* User Data */
			0x88, 0xA8, 0x00, 0x04, 0x81, 0x00, 0x00, 0x02,
			0x08, 0x00, 0x45, 0x00, 0x00, 0x54, 0x70, 0x5B,
			0x40, 0x00, 0x40, 0x01, 0x29, 0xF9, 0x28, 0x28,
			0x28, 0x04, 0x28, 0x28, 0x28, 0x01, 0x08, 0x00,
			0x08, 0x02, 0x02, 0xE2, 0x00, 0x01, 0x60, 0x58,
			0x6D, 0x38, 0x00, 0x00, 0x00, 0x00, 0x5C, 0xB7,
			0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x11,
			0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,
			0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F, 0x20, 0x21,
			0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,
			0x2A, 0x2B, 0x2C, 0x2D, 0x2E, 0x2F, 0x30, 0x31,
			0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
		},
		.len = 106,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xCA, 0xCB, 0xCD, 0x41, 0x42, 0x43,
			/* MAC SA */
			0xCA, 0xCB, 0xCD, 0x21, 0x22, 0x23,
			/* VLAN Tags before MACsec */
			0x88, 0xA8, 0x00, 0x04,
			0x81, 0x00, 0x00, 0x02,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x20,
			/* SL */
			0x00,
			/* PN */
			0x00, 0x00, 0x00, 0x0E,
			/* SCI */
			0xCA, 0xCB, 0xCD, 0x21, 0x22, 0x23, 0x00, 0x01,
			/* Secure Data */
			0x08, 0x00, 0x45, 0x00, 0x00, 0x54, 0x70, 0x5B,
			0x40, 0x00, 0x40, 0x01, 0x29, 0xF9, 0x28, 0x28,
			0x28, 0x04, 0x28, 0x28, 0x28, 0x01, 0x08, 0x00,
			0x08, 0x02, 0x02, 0xE2, 0x00, 0x01, 0x60, 0x58,
			0x6D, 0x38, 0x00, 0x00, 0x00, 0x00, 0x5C, 0xB7,
			0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x11,
			0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,
			0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F, 0x20, 0x21,
			0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,
			0x2A, 0x2B, 0x2C, 0x2D, 0x2E, 0x2F, 0x30, 0x31,
			0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
			/* ICV */
			0xCC, 0x38, 0x21, 0x3A, 0xEE, 0x5F, 0xE3, 0x7F,
			0xA1, 0xBA, 0xBD, 0xBD, 0x65, 0x5B, 0xB3, 0xE5,
		},
		.len = 138,
	},
},
};

static const struct mcs_test_vector list_mcs_intr_test_vectors[] = {
/* gcm_128_64B_cipher */
/* SECTAG_V_EQ1 */
{
	.test_idx = 0,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42,
			0x43, 0x44, 0x45, 0x46,
		},
		.len = 64,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0xAC,
			/* SL */
			0x0,
			/* PN */
			0x0, 0x0, 0x0, 0x2,
			/* SCI */
			0xFE, 0x2F, 0xCD, 0x14, 0x24, 0x1B, 0x88, 0x2C,
			/* Secure Data */
			0x39, 0x38, 0x97, 0x44, 0xA2, 0x6D, 0x71, 0x3D,
			0x14, 0x27, 0xC7, 0x3E, 0x02, 0x96, 0x81, 0xAD,
			0x47, 0x82, 0x2A, 0xCF, 0x19, 0x79, 0x12, 0x49,
			0x0F, 0x93, 0x5A, 0x32, 0x43, 0x79, 0xEF, 0x9D,
			0x70, 0xF8, 0xA9, 0xBE, 0x3D, 0x00, 0x5D, 0x22,
			0xDA, 0x87, 0x3D, 0xC1, 0xBE, 0x1B, 0x13, 0xD9,
			0x99, 0xDB, 0xF1, 0xC8,
			/* ICV */
			0x4B, 0xC4, 0xF8, 0xC6,	0x09, 0x78, 0xB9, 0xBB,
			0x5D, 0xC0, 0x04, 0xF3,	0x20, 0x7D, 0x14, 0x87,
		},
		.len = 96,
	},
},
/* SECTAG_E_EQ0_C_EQ1 */
{
	.test_idx = 1,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42,
			0x43, 0x44, 0x45, 0x46,
		},
		.len = 64,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x24,
			/* SL */
			0x0,
			/* PN */
			0x0, 0x0, 0x0, 0x2,
			/* SCI */
			0xFE, 0x2F, 0xCD, 0x14, 0x24, 0x1B, 0x88, 0x2C,
			/* Secure Data */
			0x39, 0x38, 0x97, 0x44, 0xA2, 0x6D, 0x71, 0x3D,
			0x14, 0x27, 0xC7, 0x3E, 0x02, 0x96, 0x81, 0xAD,
			0x47, 0x82, 0x2A, 0xCF, 0x19, 0x79, 0x12, 0x49,
			0x0F, 0x93, 0x5A, 0x32, 0x43, 0x79, 0xEF, 0x9D,
			0x70, 0xF8, 0xA9, 0xBE, 0x3D, 0x00, 0x5D, 0x22,
			0xDA, 0x87, 0x3D, 0xC1, 0xBE, 0x1B, 0x13, 0xD9,
			0x99, 0xDB, 0xF1, 0xC8,
			/* ICV */
			0x4B, 0xC4, 0xF8, 0xC6,	0x09, 0x78, 0xB9, 0xBB,
			0x5D, 0xC0, 0x04, 0xF3,	0x20, 0x7D, 0x14, 0x87,
		},
		.len = 96,
	},
},
/* SECTAG_SL_GTE48 */
{
	.test_idx = 2,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42,
			0x43, 0x44, 0x45, 0x46,
		},
		.len = 64,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x2C,
			/* SL */
			0x31,
			/* PN */
			0x0, 0x0, 0x0, 0x2,
			/* SCI */
			0xFE, 0x2F, 0xCD, 0x14, 0x24, 0x1B, 0x88, 0x2C,
			/* Secure Data */
			0x39, 0x38, 0x97, 0x44, 0xA2, 0x6D, 0x71, 0x3D,
			0x14, 0x27, 0xC7, 0x3E, 0x02, 0x96, 0x81, 0xAD,
			0x47, 0x82, 0x2A, 0xCF, 0x19, 0x79, 0x12, 0x49,
			0x0F, 0x93, 0x5A, 0x32, 0x43, 0x79, 0xEF, 0x9D,
			0x70, 0xF8, 0xA9, 0xBE, 0x3D, 0x00, 0x5D, 0x22,
			0xDA, 0x87, 0x3D, 0xC1, 0xBE, 0x1B, 0x13, 0xD9,
			0x99, 0xDB, 0xF1, 0xC8,
			/* ICV */
			0x4B, 0xC4, 0xF8, 0xC6,	0x09, 0x78, 0xB9, 0xBB,
			0x5D, 0xC0, 0x04, 0xF3,	0x20, 0x7D, 0x14, 0x87,
		},
		.len = 96,
	},
},
/* SECTAG_ES_EQ1_SC_EQ1 */
{
	.test_idx = 3,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42,
			0x43, 0x44, 0x45, 0x46,
		},
		.len = 64,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x6C,
			/* SL */
			0x0,
			/* PN */
			0x0, 0x0, 0x0, 0x2,
			/* SCI */
			0xFE, 0x2F, 0xCD, 0x14, 0x24, 0x1B, 0x88, 0x2C,
			/* Secure Data */
			0x39, 0x38, 0x97, 0x44, 0xA2, 0x6D, 0x71, 0x3D,
			0x14, 0x27, 0xC7, 0x3E, 0x02, 0x96, 0x81, 0xAD,
			0x47, 0x82, 0x2A, 0xCF, 0x19, 0x79, 0x12, 0x49,
			0x0F, 0x93, 0x5A, 0x32, 0x43, 0x79, 0xEF, 0x9D,
			0x70, 0xF8, 0xA9, 0xBE, 0x3D, 0x00, 0x5D, 0x22,
			0xDA, 0x87, 0x3D, 0xC1, 0xBE, 0x1B, 0x13, 0xD9,
			0x99, 0xDB, 0xF1, 0xC8,
			/* ICV */
			0x4B, 0xC4, 0xF8, 0xC6,	0x09, 0x78, 0xB9, 0xBB,
			0x5D, 0xC0, 0x04, 0xF3,	0x20, 0x7D, 0x14, 0x87,
		},
		.len = 96,
	},
},
/* SECTAG_SC_EQ1_SCB_EQ1 */
{
	.test_idx = 4,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42,
			0x43, 0x44, 0x45, 0x46,
		},
		.len = 64,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x3C,
			/* SL */
			0x0,
			/* PN */
			0x0, 0x0, 0x0, 0x2,
			/* SCI */
			0xFE, 0x2F, 0xCD, 0x14, 0x24, 0x1B, 0x88, 0x2C,
			/* Secure Data */
			0x39, 0x38, 0x97, 0x44, 0xA2, 0x6D, 0x71, 0x3D,
			0x14, 0x27, 0xC7, 0x3E, 0x02, 0x96, 0x81, 0xAD,
			0x47, 0x82, 0x2A, 0xCF, 0x19, 0x79, 0x12, 0x49,
			0x0F, 0x93, 0x5A, 0x32, 0x43, 0x79, 0xEF, 0x9D,
			0x70, 0xF8, 0xA9, 0xBE, 0x3D, 0x00, 0x5D, 0x22,
			0xDA, 0x87, 0x3D, 0xC1, 0xBE, 0x1B, 0x13, 0xD9,
			0x99, 0xDB, 0xF1, 0xC8,
			/* ICV */
			0x4B, 0xC4, 0xF8, 0xC6,	0x09, 0x78, 0xB9, 0xBB,
			0x5D, 0xC0, 0x04, 0xF3,	0x20, 0x7D, 0x14, 0x87,
		},
		.len = 96,
	},
},
};

static const struct mcs_test_vector list_mcs_rekey_vectors[] = {
/* Initial SA, AN = 0 and PN = 2 */
{
	.test_idx = 0,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42,
			0x43, 0x44, 0x45, 0x46,
		},
		.len = 64,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x2C,
			/* SL */
			0x0,
			/* PN */
			0x0, 0x0, 0x0, 0x2,
			/* SCI */
			0xFE, 0x2F, 0xCD, 0x14, 0x24, 0x1B, 0x88, 0x2C,
			/* Secure Data */
			0x39, 0x38, 0x97, 0x44, 0xA2, 0x6D, 0x71, 0x3D,
			0x14, 0x27, 0xC7, 0x3E, 0x02, 0x96, 0x81, 0xAD,
			0x47, 0x82, 0x2A, 0xCF, 0x19, 0x79, 0x12, 0x49,
			0x0F, 0x93, 0x5A, 0x32, 0x43, 0x79, 0xEF, 0x9D,
			0x70, 0xF8, 0xA9, 0xBE, 0x3D, 0x00, 0x5D, 0x22,
			0xDA, 0x87, 0x3D, 0xC1, 0xBE, 0x1B, 0x13, 0xD9,
			0x99, 0xDB, 0xF1, 0xC8,
			/* ICV */
			0x4B, 0xC4, 0xF8, 0xC6,	0x09, 0x78, 0xB9, 0xBB,
			0x5D, 0xC0, 0x04, 0xF3,	0x20, 0x7D, 0x14, 0x87,
		},
		.len = 96,
	},
},
/* Rekeyed SA. sa_key is different from the initial sa.
 * Also, AN = 1 and PN = 1.
 */
{
	.test_idx = 1,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.salt = {0},
	.sa_key = {
		.data = {
			0xAD, 0x7A, 0x2B, 0xD0, 0x3E, 0xAC, 0x83, 0x5A,
			0x6F, 0x62, 0x0F, 0xDC, 0xB5, 0x06, 0xB3, 0x45,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42,
			0x43, 0x44, 0x45, 0x46,
		},
		.len = 64,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x2D,
			/* SL */
			0x00,
			/* PN */
			0x00, 0x00, 0x00, 0x01,
			/* SCI */
			0xFE, 0x2F, 0xCD, 0x14, 0x24, 0x1B, 0x88, 0x2C,
			/* Secure Data */
			0x17, 0x66, 0xEF, 0xD9, 0x06, 0xDC, 0x15, 0xAF,
			0xE9, 0x06, 0xB1, 0xE6, 0x26, 0x22, 0xC8, 0x78,
			0x27, 0xE1, 0xED, 0x76, 0xF5, 0xC8, 0x16, 0xA1,
			0x6B, 0x0D, 0xA0, 0x8E, 0x24, 0x2A, 0x9D, 0x34,
			0xD0, 0xE0, 0x5F, 0xBA, 0x08, 0xF0, 0xE3, 0x7D,
			0x17, 0xC0, 0x2C, 0xCD, 0x8A, 0x44, 0xC9, 0xB9,
			0x28, 0xC0, 0xE8, 0x22,
			/* ICV */
			0x1B, 0x16, 0x68, 0x5F, 0x14, 0x8A, 0x51, 0x29,
			0xB5, 0x3D, 0x61, 0x0E, 0x49, 0x20, 0x60, 0x09,
		},
		.len = 96,
	},
},
{
	.test_idx = 2,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_128,
	.ssci = 0x7A30C118,
	.xpn = 0xB0DF459C, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE,
		0x86, 0xA2, 0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x00, 0x04,
		},
		.len = 54,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x4C,
			/* SL */
			0x2A,
			/* PN */
			0x76, 0xD4, 0x57, 0xED,
			/* Secure Data */
			0x9C, 0xA4, 0x69, 0x84, 0x43, 0x02, 0x03, 0xED,
			0x41, 0x6E, 0xBD, 0xC2, 0xFE, 0x26, 0x22, 0xBA,
			0x3E, 0x5E, 0xAB, 0x69, 0x61, 0xC3, 0x63, 0x83,
			0x00, 0x9E, 0x18, 0x7E, 0x9B, 0x0C, 0x88, 0x56,
			0x46, 0x53, 0xB9, 0xAB, 0xD2, 0x16, 0x44, 0x1C,
			0x6A, 0xB6,
			/* ICV */
			0xF0, 0xA2, 0x32, 0xE9, 0xE4, 0x4C, 0x97, 0x8C,
			0xF7, 0xCD, 0x84, 0xD4, 0x34, 0x84, 0xD1, 0x01,
		},
		.len = 78,
	},
},
/* Rekeyed SA. sa_key is different from the initial sa.
 * Also, AN = 1, XPN = 0 and PN = 1.
 */
{
	.test_idx = 3,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_128,
	.ssci = 0x7A30C118,
	.xpn = 0x0, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE,
		0x86, 0xA2, 0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0xAD, 0x7A, 0x2B, 0xD0, 0x3E, 0xAC, 0x83, 0x5A,
			0x6F, 0x62, 0x0F, 0xDC, 0xB5, 0x06, 0xB3, 0x45,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x00, 0x04,
		},
		.len = 54,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x4D,
			/* SL */
			0x2A,
			/* PN */
			0x0, 0x0, 0x0, 0x1,
			/* Secure Data */
			0x91, 0x00, 0xC0, 0xE4, 0xB9, 0x4E, 0x2C, 0x1C,
			0x86, 0xDF, 0xE1, 0x8F, 0xDD, 0xB6, 0xE6, 0x79,
			0x65, 0x87, 0x80, 0xE7, 0x9C, 0x5D, 0x8A, 0xB7,
			0x68, 0xFD, 0xE1, 0x6E, 0x3F, 0xF1, 0xDE, 0x20,
			0x4A, 0xF6, 0xBA, 0xE6, 0x14, 0xDB, 0x6A, 0x05,
			0xE9, 0xB6,
			/* ICV */
			0x2D, 0xDF, 0x59, 0x27, 0x25, 0x41, 0x68, 0x1D,
			0x74, 0x1A, 0xAA, 0xC4, 0x18, 0x49, 0xB4, 0x22,
		},
		.len = 78,
	},
},
};

static const struct mcs_test_vector list_mcs_anti_replay_vectors[] = {
{
	.test_idx = 0,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42,
			0x43, 0x44, 0x45, 0x46,
		},
		.len = 64,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x2C,
			/* SL */
			0x0,
			/* PN */
			0x0, 0x0, 0x0, 0x2,
			/* SCI */
			0xFE, 0x2F, 0xCD, 0x14, 0x24, 0x1B, 0x88, 0x2C,
			/* Secure Data */
			0x39, 0x38, 0x97, 0x44, 0xA2, 0x6D, 0x71, 0x3D,
			0x14, 0x27, 0xC7, 0x3E, 0x02, 0x96, 0x81, 0xAD,
			0x47, 0x82, 0x2A, 0xCF, 0x19, 0x79, 0x12, 0x49,
			0x0F, 0x93, 0x5A, 0x32, 0x43, 0x79, 0xEF, 0x9D,
			0x70, 0xF8, 0xA9, 0xBE, 0x3D, 0x00, 0x5D, 0x22,
			0xDA, 0x87, 0x3D, 0xC1, 0xBE, 0x1B, 0x13, 0xD9,
			0x99, 0xDB, 0xF1, 0xC8,
			/* ICV */
			0x4B, 0xC4, 0xF8, 0xC6,	0x09, 0x78, 0xB9, 0xBB,
			0x5D, 0xC0, 0x04, 0xF3,	0x20, 0x7D, 0x14, 0x87,
		},
		.len = 96,
	},
},
{
	.test_idx = 1,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42,
			0x43, 0x44, 0x45, 0x46,
		},
		.len = 64,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x2C,
			/* SL */
			0x0,
			/* PN */
			0x0, 0x0, 0x0, 0x4B,
			/* SCI */
			0xFE, 0x2F, 0xCD, 0x14, 0x24, 0x1B, 0x88, 0x2C,
			/* Secure Data */
			0x51, 0xC9, 0xBB, 0xF3, 0x24, 0x38, 0xF9, 0x06,
			0x76, 0x9E, 0x61, 0xCE, 0xB8, 0x65, 0xA7, 0xE4,
			0x1F, 0x16, 0x5D, 0x59, 0xB8, 0x44, 0x0F, 0x94,
			0x50, 0xF0, 0x4C, 0x35, 0x7D, 0x91, 0x53, 0xC6,
			0x28, 0x4D, 0xA8, 0xAB, 0x13, 0x3B, 0xC0, 0x2D,
			0x11, 0x8E, 0xCC, 0x75, 0xC9, 0xD8, 0x8F, 0x60,
			0x67, 0xE1, 0x03, 0x2C,
			/* ICV */
			0xA5, 0xF1, 0x2C, 0x85, 0x10, 0xEE, 0x67, 0x7E,
			0xDB, 0x4E, 0xF6, 0x0A, 0xA1, 0x0F, 0x15, 0x69,
		},
		.len = 96,
	},
},
{
	.test_idx = 2,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42,
			0x43, 0x44, 0x45, 0x46,
		},
		.len = 64,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x2C,
			/* SL */
			0x0,
			/* PN */
			0x0, 0x0, 0x0, 0x32,
			/* SCI */
			0xFE, 0x2F, 0xCD, 0x14, 0x24, 0x1B, 0x88, 0x2C,
			/* Secure Data */
			0x6F, 0xB6, 0xF8, 0x54, 0x67, 0x23, 0x3C, 0xE8,
			0x67, 0x54, 0x8B, 0xAD, 0x31, 0xC3, 0x2B, 0xAA,
			0x70, 0x1A, 0xC8, 0x0D, 0x3C, 0x31, 0x54, 0x0F,
			0xDD, 0x8F, 0x23, 0x0F, 0x86, 0xF3, 0x80, 0x31,
			0x8B, 0x30, 0xD9, 0x15, 0xF9, 0x3B, 0xD6, 0x00,
			0x95, 0xBD, 0xF3, 0x7F, 0xD2, 0x41, 0x28, 0xFC,
			0x52, 0x27, 0xB5, 0x88,
			/* ICV */
			0x64, 0x3C, 0x67, 0xD7, 0xB8, 0xC1, 0xAF, 0x15,
			0x82, 0x5F, 0x06, 0x4F, 0x5A, 0xED, 0x47, 0xC1,
		},
		.len = 96,
	},
},
{
	.test_idx = 3,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_128,
	.ssci = 0x0,
	.salt = {0},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x35, 0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42,
			0x43, 0x44, 0x45, 0x46,
		},
		.len = 64,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x2C,
			/* SL */
			0x0,
			/* PN */
			0x0, 0x0, 0x0, 0x3,
			/* SCI */
			0xFE, 0x2F, 0xCD, 0x14, 0x24, 0x1B, 0x88, 0x2C,
			/* Secure Data */
			0x16, 0x6E, 0x74, 0xE5, 0xF7, 0x49, 0xCC, 0x42,
			0x06, 0x30, 0x99, 0x60, 0x10, 0xAA, 0xB3, 0xEC,
			0x3C, 0xEF, 0x6C, 0x7D, 0x72, 0x93, 0x61, 0x28,
			0x39, 0x8E, 0x6B, 0x5C, 0x6C, 0x9E, 0xCA, 0x86,
			0x70, 0x5A, 0x95, 0x98, 0x0F, 0xB2, 0xC8, 0x05,
			0xD6, 0xC9, 0xBA, 0x9A, 0xCF, 0x7B, 0x5F, 0xD0,
			0xAE, 0x50, 0x66, 0x7D,
			/* ICV */
			0xC8, 0xF1, 0x4A, 0x10, 0x8A, 0xFF, 0x64, 0x6C,
			0xC7, 0x18, 0xC2, 0x7A, 0x16, 0x1A, 0x0D, 0xCA,
		},
		.len = 96,
	},
},
{
	.test_idx = 4,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_128,
	.ssci = 0x7A30C118,
	.xpn = 0x0, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE,
		0x86, 0xA2, 0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x00, 0x04,
		},
		.len = 54,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x4C,
			/* SL */
			0x2A,
			/* PN */
			0xFF, 0xFF, 0xFF, 0xFE,
			/* Secure Data */
			0xA4, 0x80, 0xA4, 0x24, 0xD3, 0xCB, 0x3B, 0x05,
			0xD5, 0x5B, 0x48, 0xE0, 0x23, 0xEA, 0x8C, 0x11,
			0xE2, 0xB6, 0xE9, 0x69, 0x39, 0x40, 0xA6, 0xEA,
			0xC9, 0xCD, 0xF9, 0xD8, 0x85, 0x8C, 0xD6, 0xFA,
			0xB6, 0x9A, 0xE2, 0x37, 0xAA, 0x0C, 0x02, 0x2C,
			0xB8, 0xC1,
			/* ICV */
			0xE3, 0x36, 0x34, 0x7A, 0x7C, 0x00, 0x71, 0x1F,
			0xAC, 0x04, 0x48, 0x82, 0x64, 0xD2, 0xDF, 0x58,
		},
		.len = 78,
	},
},
{
	.test_idx = 5,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_128,
	.ssci = 0x7A30C118,
	.xpn = 0x1, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE,
		0x86, 0xA2, 0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x00, 0x04,
		},
		.len = 54,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x4C,
			/* SL */
			0x2A,
			/* PN */
			0x00, 0x00, 0x00, 0x62,
			/* Secure Data */
			0x62, 0x62, 0x9E, 0x43, 0x59, 0x0C, 0xC6, 0x33,
			0x26, 0x3C, 0xBF, 0x93, 0x5D, 0xE2, 0x8A, 0x7F,
			0x96, 0xB4, 0xF7, 0x08, 0xEA, 0x9A, 0xA8, 0x88,
			0xB4, 0xE8, 0xBE, 0x8D, 0x28, 0x84, 0xE0, 0x16,
			0x08, 0x92, 0xB0, 0xAB, 0x76, 0x60, 0xEA, 0x05,
			0x74, 0x79,
			/* ICV */
			0x8E, 0x5D, 0x81, 0xA6, 0x3F, 0xDF, 0x39, 0xB8,
			0x19, 0x33, 0x73, 0x09, 0xCE, 0xC1, 0xAF, 0x85,
		},
		.len = 78,
	},
},
{
	.test_idx = 6,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_128,
	.ssci = 0x7A30C118,
	.xpn = 0x1, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE,
		0x86, 0xA2, 0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x00, 0x04,
		},
		.len = 54,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x4C,
			/* SL */
			0x2A,
			/* PN */
			0x00, 0x00, 0x00, 0x58,
			/* Secure Data */
			0xC7, 0xDC, 0xF4, 0xC9, 0x8C, 0x59, 0x6E, 0x96,
			0x3D, 0x4B, 0x89, 0xB3, 0xF3, 0x8D, 0x5D, 0x99,
			0x4E, 0xDF, 0x48, 0x74, 0x02, 0x25, 0x93, 0xB4,
			0x12, 0xFB, 0x0F, 0x28, 0xA5, 0x02, 0x78, 0xAC,
			0x0B, 0x14, 0xF1, 0xAC, 0x1C, 0x0C, 0x80, 0x37,
			0x6B, 0x44,
			/* ICV */
			0x47, 0x5A, 0xEE, 0x37, 0xFC, 0x6E, 0xDE, 0xB9,
			0x14, 0x0E, 0xBD, 0x22, 0x05, 0x12, 0x00, 0x52,
		},
		.len = 78,
	},
},
{
	.test_idx = 7,
	.alg = RTE_SECURITY_MACSEC_ALG_GCM_XPN_128,
	.ssci = 0x7A30C118,
	.xpn = 0x1, /* Most significant 32 bits */
	.salt = {
		0xE6, 0x30, 0xE8, 0x1A, 0x48, 0xDE,
		0x86, 0xA2, 0x1C, 0x66, 0xFA, 0x6D,
	},
	.sa_key = {
		.data = {
			0x07, 0x1B, 0x11, 0x3B, 0x0C, 0xA7, 0x43, 0xFE,
			0xCC, 0xCF, 0x3D, 0x05, 0x1F, 0x73, 0x73, 0x82,
		},
		.len = 16,
	},
	.plain_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* User Data */
			0x08, 0x00, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14,
			0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
			0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23, 0x24,
			0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C,
			0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34,
			0x00, 0x04,
		},
		.len = 54,
	},
	.secure_pkt = {
		.data = {/* MAC DA */
			0xE2, 0x01, 0x06, 0xD7, 0xCD, 0x0D,
			/* MAC SA */
			0xF0, 0x76, 0x1E, 0x8D, 0xCD, 0x3D,
			/* MACsec EtherType */
			0x88, 0xE5,
			/* TCI and AN */
			0x4C,
			/* SL */
			0x2A,
			/* PN */
			0x00, 0x00, 0x00, 0x02,
			/* Secure Data */
			0xDD, 0x86, 0x37, 0x48, 0x11, 0xF3, 0xA8, 0x96,
			0x25, 0x3A, 0xD9, 0xBE, 0x7C, 0x62, 0x72, 0xD6,
			0x43, 0x70, 0xB6, 0x92, 0x04, 0x25, 0x46, 0xC1,
			0x17, 0xBC, 0x14, 0xE1, 0x09, 0x4C, 0x04, 0x94,
			0x51, 0x1F, 0x6E, 0x89, 0x32, 0x13, 0x4B, 0xAC,
			0x2A, 0x60,
			/* ICV */
			0x96, 0xC0, 0xB4, 0xA4, 0xC7, 0xEC, 0xF5, 0xEF,
			0x5E, 0x51, 0x22, 0x14, 0xF8, 0x70, 0xA0, 0x22,
		},
		.len = 78,
	},
},
};

#endif /* TEST_INLINE_MACSEC_VECTORS_H */
