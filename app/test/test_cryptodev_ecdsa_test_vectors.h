/* SPDX-License-Identifier: BSD-3-Clause
 * Copyright (C) 2020 Marvell International Ltd.
 */

#ifndef __TEST_CRYPTODEV_ECDSA_TEST_VECTORS_H__
#define __TEST_CRYPTODEV_ECDSA_TEST_VECTORS_H__

#include "rte_crypto_asym.h"

/* EC curve id */
enum curve {
	SECP192R1,
	SECP224R1,
	SECP256R1,
	SECP384R1,
	SECP521R1,
	SECP521R1_UA,
	ED25519,
	ED448,
	END_OF_CURVE_LIST
};

const char *curve[] = {"SECP192R1",
		       "SECP224R1",
		       "SECP256R1",
		       "SECP384R1",
		       "SECP521R1",
		       "SECP521R1(unaligned)",
		       "ED25519",
		       "ED448",
};

struct crypto_testsuite_ecdsa_params {
	rte_crypto_param pubkey_qx;
	rte_crypto_param pubkey_qy;
	rte_crypto_param scalar;
	rte_crypto_param digest;
	rte_crypto_param sign_r;
	rte_crypto_param sign_s;
	rte_crypto_param pkey;
	int curve;
};

/*
 * Test vector reference:
 * https://csrc.nist.gov/CSRC/media/Projects/
 * Cryptographic-Algorithm-Validation-Program/
 * documents/components/186-********************************.zip
 */

/* SECP192R1 (P-192 NIST) test vector */

static uint8_t digest_secp192r1[] = {
	0x5a, 0xe8, 0x31, 0x7d, 0x34, 0xd1, 0xe5, 0x95,
	0xe3, 0xfa, 0x72, 0x47, 0xdb, 0x80, 0xc0, 0xaf,
	0x43, 0x20, 0xcc, 0xe1, 0x11, 0x6d, 0xe1, 0x87,
	0xf8, 0xf7, 0xe2, 0xe0, 0x99, 0xc0, 0xd8, 0xd0
};

static uint8_t pkey_secp192r1[] = {
	0x24, 0xed, 0xd2, 0x2f, 0x7d, 0xdd, 0x6f, 0xa5,
	0xbc, 0x61, 0xfc, 0x06, 0x53, 0x47, 0x9a, 0xa4,
	0x08, 0x09, 0xef, 0x86, 0x5c, 0xf2, 0x7a, 0x47
};

static uint8_t scalar_secp192r1[] = {
	0xa5, 0xc8, 0x17, 0xa2, 0x36, 0xa5, 0xf7, 0xfa,
	0xa3, 0x29, 0xb8, 0xec, 0xc3, 0xc5, 0x96, 0x68,
	0x7c, 0x71, 0xaa, 0xaf, 0x86, 0xc7, 0x70, 0x3e
};

static uint8_t pubkey_qx_secp192r1[] = {
	0x9b, 0xf1, 0x2d, 0x71, 0x74, 0xb7, 0x70, 0x8a,
	0x07, 0x6a, 0x38, 0xbc, 0x80, 0xaa, 0x28, 0x66,
	0x2f, 0x25, 0x1e, 0x2e, 0xd8, 0xd4, 0x14, 0xdc
};

static uint8_t pubkey_qy_secp192r1[] = {
	0x48, 0x54, 0xc8, 0xd0, 0x7d, 0xfc, 0x08, 0x82,
	0x4e, 0x9e, 0x47, 0x1c, 0xa2, 0xfe, 0xdc, 0xfc,
	0xff, 0x3d, 0xdc, 0xb0, 0x11, 0x57, 0x34, 0x98
};

static uint8_t sign_secp192r1_r[] = {
	0x35, 0x4a, 0xba, 0xec, 0xf4, 0x36, 0x1f, 0xea,
	0x90, 0xc2, 0x9b, 0x91, 0x99, 0x88, 0x2e, 0xdf,
	0x85, 0x73, 0xe6, 0x86, 0xa8, 0x13, 0xef, 0xf8
};

static uint8_t sign_secp192r1_s[] = {
	0x80, 0xf5, 0x00, 0x00, 0xac, 0x86, 0x11, 0x1c,
	0x9b, 0x30, 0x47, 0x38, 0x5a, 0x15, 0xd7, 0x8e,
	0x63, 0x2c, 0x58, 0xb7, 0x94, 0x9e, 0x82, 0xc1
};

/** ECDSA SECP192R1 elliptic curve param */

struct crypto_testsuite_ecdsa_params ecdsa_param_secp192r1 = {
	.pubkey_qx = {
		.data = pubkey_qx_secp192r1,
		.length = sizeof(pubkey_qx_secp192r1),
	},
	.pubkey_qy = {
		.data = pubkey_qy_secp192r1,
		.length = sizeof(pubkey_qy_secp192r1),
	},
	.scalar = {
		.data = scalar_secp192r1,
		.length = sizeof(scalar_secp192r1),
	},
	.digest = {
		.data = digest_secp192r1,
		.length = sizeof(digest_secp192r1),
	},
	.sign_r = {
		.data = sign_secp192r1_r,
		.length = sizeof(sign_secp192r1_r),
	},
	.sign_s = {
		.data = sign_secp192r1_s,
		.length = sizeof(sign_secp192r1_s),
	},
	.pkey = {
		.data = pkey_secp192r1,
		.length = sizeof(pkey_secp192r1),
	},
	.curve = RTE_CRYPTO_EC_GROUP_SECP192R1
};

/* SECP224R1 (P-224 NIST) test vectors */

static uint8_t digest_secp224r1[] = {
	0x00, 0xc6, 0xfc, 0x53, 0xc1, 0x98, 0x6d, 0x19,
	0xa8, 0xa8, 0xb5, 0x80, 0xee, 0x55, 0x3d, 0xc1,
	0x24, 0x07, 0x45, 0xd7, 0x60, 0x64, 0x7d, 0x1c,
	0x0a, 0xdf, 0x44, 0x2c, 0x13, 0x3c, 0x7f, 0x56
};

static uint8_t pkey_secp224r1[] = {
	0x88, 0x8f, 0xc9, 0x92, 0x89, 0x3b, 0xdd, 0x8a,
	0xa0, 0x2c, 0x80, 0x76, 0x88, 0x32, 0x60, 0x5d,
	0x02, 0x0b, 0x81, 0xae, 0x0b, 0x25, 0x47, 0x41,
	0x54, 0xec, 0x89, 0xaa
};

static uint8_t scalar_secp224r1[] = {
	0x06, 0xf7, 0xa5, 0x60, 0x07, 0x82, 0x54, 0x33,
	0xc4, 0xc6, 0x11, 0x53, 0xdf, 0x1a, 0x13, 0x5e,
	0xee, 0x2f, 0x38, 0xec, 0x68, 0x7b, 0x49, 0x2e,
	0xd4, 0x0d, 0x9c, 0x90
};

static uint8_t pubkey_qx_secp224r1[] = {
	0x4c, 0x74, 0x1e, 0x4d, 0x20, 0x10, 0x36, 0x70,
	0xb7, 0x16, 0x1a, 0xe7, 0x22, 0x71, 0x08, 0x21,
	0x55, 0x83, 0x84, 0x18, 0x08, 0x43, 0x35, 0x33,
	0x8a, 0xc3, 0x8f, 0xa4
};

static uint8_t pubkey_qy_secp224r1[] = {
	0xdb, 0x79, 0x19, 0x15, 0x1a, 0xc2, 0x85, 0x87,
	0xb7, 0x2b, 0xad, 0x7a, 0xb1, 0x80, 0xec, 0x8e,
	0x95, 0xab, 0x9e, 0x2c, 0x8d, 0x81, 0xd9, 0xb9,
	0xd7, 0xe2, 0xe3, 0x83
};

static uint8_t sign_secp224r1_r[] = {
	0x09, 0x09, 0xc9, 0xb9, 0xca, 0xe8, 0xd2, 0x79,
	0x0e, 0x29, 0xdb, 0x6a, 0xfd, 0xb4, 0x5c, 0x04,
	0xf5, 0xb0, 0x72, 0xc4, 0xc2, 0x04, 0x10, 0xc7,
	0xdc, 0x9b, 0x67, 0x72
};

static uint8_t sign_secp224r1_s[] = {
	0x29, 0x8f, 0x4f, 0xca, 0xe1, 0xfe, 0x27, 0x1d,
	0xa1, 0xe0, 0x34, 0x5d, 0x11, 0xd0, 0x7a, 0x1f,
	0xca, 0x43, 0xf5, 0x8a, 0xf4, 0xc1, 0x13, 0xb9,
	0x09, 0xee, 0xde, 0xa0
};

/** ECDSA SECP224R1 elliptic curve param */

struct crypto_testsuite_ecdsa_params ecdsa_param_secp224r1 = {
	.pubkey_qx = {
		.data = pubkey_qx_secp224r1,
		.length = sizeof(pubkey_qx_secp224r1),
	},
	.pubkey_qy = {
		.data = pubkey_qy_secp224r1,
		.length = sizeof(pubkey_qy_secp224r1),
	},
	.scalar = {
		.data = scalar_secp224r1,
		.length = sizeof(scalar_secp224r1),
	},
	.digest = {
		.data = digest_secp224r1,
		.length = sizeof(digest_secp224r1),
	},
	.sign_r = {
		.data = sign_secp224r1_r,
		.length = sizeof(sign_secp224r1_r),
	},
	.sign_s = {
		.data = sign_secp224r1_s,
		.length = sizeof(sign_secp224r1_s),
	},
	.pkey = {
		.data = pkey_secp224r1,
		.length = sizeof(pkey_secp224r1),
	},
	.curve = RTE_CRYPTO_EC_GROUP_SECP224R1
};

/* SECP256R1 (P-256 NIST) test vectors */

static uint8_t digest_secp256r1[] = {
	0x44, 0xac, 0xf6, 0xb7, 0xe3, 0x6c, 0x13, 0x42,
	0xc2, 0xc5, 0x89, 0x72, 0x04, 0xfe, 0x09, 0x50,
	0x4e, 0x1e, 0x2e, 0xfb, 0x1a, 0x90, 0x03, 0x77,
	0xdb, 0xc4, 0xe7, 0xa6, 0xa1, 0x33, 0xec, 0x56
};

static uint8_t pkey_secp256r1[] = {
	0x51, 0x9b, 0x42, 0x3d, 0x71, 0x5f, 0x8b, 0x58,
	0x1f, 0x4f, 0xa8, 0xee, 0x59, 0xf4, 0x77, 0x1a,
	0x5b, 0x44, 0xc8, 0x13, 0x0b, 0x4e, 0x3e, 0xac,
	0xca, 0x54, 0xa5, 0x6d, 0xda, 0x72, 0xb4, 0x64
};

static uint8_t scalar_secp256r1[] = {
	0x94, 0xa1, 0xbb, 0xb1, 0x4b, 0x90, 0x6a, 0x61,
	0xa2, 0x80, 0xf2, 0x45, 0xf9, 0xe9, 0x3c, 0x7f,
	0x3b, 0x4a, 0x62, 0x47, 0x82, 0x4f, 0x5d, 0x33,
	0xb9, 0x67, 0x07, 0x87, 0x64, 0x2a, 0x68, 0xde
};

static uint8_t pubkey_qx_secp256r1[] = {
	0x1c, 0xcb, 0xe9, 0x1c, 0x07, 0x5f, 0xc7, 0xf4,
	0xf0, 0x33, 0xbf, 0xa2, 0x48, 0xdb, 0x8f, 0xcc,
	0xd3, 0x56, 0x5d, 0xe9, 0x4b, 0xbf, 0xb1, 0x2f,
	0x3c, 0x59, 0xff, 0x46, 0xc2, 0x71, 0xbf, 0x83
};

static uint8_t pubkey_qy_secp256r1[] = {
	0xce, 0x40, 0x14, 0xc6, 0x88, 0x11, 0xf9, 0xa2,
	0x1a, 0x1f, 0xdb, 0x2c, 0x0e, 0x61, 0x13, 0xe0,
	0x6d, 0xb7, 0xca, 0x93, 0xb7, 0x40, 0x4e, 0x78,
	0xdc, 0x7c, 0xcd, 0x5c, 0xa8, 0x9a, 0x4c, 0xa9
};

static uint8_t sign_secp256r1_r[] = {
	0xf3, 0xac, 0x80, 0x61, 0xb5, 0x14, 0x79, 0x5b,
	0x88, 0x43, 0xe3, 0xd6, 0x62, 0x95, 0x27, 0xed,
	0x2a, 0xfd, 0x6b, 0x1f, 0x6a, 0x55, 0x5a, 0x7a,
	0xca, 0xbb, 0x5e, 0x6f, 0x79, 0xc8, 0xc2, 0xac
};

static uint8_t sign_secp256r1_s[] = {
	0x8b, 0xf7, 0x78, 0x19, 0xca, 0x05, 0xa6, 0xb2,
	0x78, 0x6c, 0x76, 0x26, 0x2b, 0xf7, 0x37, 0x1c,
	0xef, 0x97, 0xb2, 0x18, 0xe9, 0x6f, 0x17, 0x5a,
	0x3c, 0xcd, 0xda, 0x2a, 0xcc, 0x05, 0x89, 0x03
};

/** ECDSA SECP256R1 elliptic curve param */

struct crypto_testsuite_ecdsa_params ecdsa_param_secp256r1 = {
	.pubkey_qx = {
		.data = pubkey_qx_secp256r1,
		.length = sizeof(pubkey_qx_secp256r1),
	},
	.pubkey_qy = {
		.data = pubkey_qy_secp256r1,
		.length = sizeof(pubkey_qy_secp256r1),
	},
	.scalar = {
		.data = scalar_secp256r1,
		.length = sizeof(scalar_secp256r1),
	},
	.digest = {
		.data = digest_secp256r1,
		.length = sizeof(digest_secp256r1),
	},
	.sign_r = {
		.data = sign_secp256r1_r,
		.length = sizeof(sign_secp256r1_r),
	},
	.sign_s = {
		.data = sign_secp256r1_s,
		.length = sizeof(sign_secp256r1_s),
	},
	.pkey = {
		.data = pkey_secp256r1,
		.length = sizeof(pkey_secp256r1),
	},
	.curve = RTE_CRYPTO_EC_GROUP_SECP256R1
};

/* SECP384R1 (P-384 NIST) test vectors */

static uint8_t digest_secp384r1[] = {
	0xbb, 0xbd, 0x0a, 0x5f, 0x64, 0x5d, 0x3f, 0xda,
	0x10, 0xe2, 0x88, 0xd1, 0x72, 0xb2, 0x99, 0x45,
	0x5f, 0x9d, 0xff, 0x00, 0xe0, 0xfb, 0xc2, 0x83,
	0x3e, 0x18, 0xcd, 0x01, 0x7d, 0x7f, 0x3e, 0xd1
};

static uint8_t pkey_secp384r1[] = {
	0xc6, 0x02, 0xbc, 0x74, 0xa3, 0x45, 0x92, 0xc3,
	0x11, 0xa6, 0x56, 0x96, 0x61, 0xe0, 0x83, 0x2c,
	0x84, 0xf7, 0x20, 0x72, 0x74, 0x67, 0x6c, 0xc4,
	0x2a, 0x89, 0xf0, 0x58, 0x16, 0x26, 0x30, 0x18,
	0x4b, 0x52, 0xf0, 0xd9, 0x9b, 0x85, 0x5a, 0x77,
	0x83, 0xc9, 0x87, 0x47, 0x6d, 0x7f, 0x9e, 0x6b
};

static uint8_t scalar_secp384r1[] = {
	0xc1, 0x0b, 0x5c, 0x25, 0xc4, 0x68, 0x3d, 0x0b,
	0x78, 0x27, 0xd0, 0xd8, 0x86, 0x97, 0xcd, 0xc0,
	0x93, 0x24, 0x96, 0xb5, 0x29, 0x9b, 0x79, 0x8c,
	0x0d, 0xd1, 0xe7, 0xaf, 0x6c, 0xc7, 0x57, 0xcc,
	0xb3, 0x0f, 0xcd, 0x3d, 0x36, 0xea, 0xd4, 0xa8,
	0x04, 0x87, 0x7e, 0x24, 0xf3, 0xa3, 0x24, 0x43
};

static uint8_t pubkey_qx_secp384r1[] = {
	0x04, 0x00, 0x19, 0x3b, 0x21, 0xf0, 0x7c, 0xd0,
	0x59, 0x82, 0x6e, 0x94, 0x53, 0xd3, 0xe9, 0x6d,
	0xd1, 0x45, 0x04, 0x1c, 0x97, 0xd4, 0x9f, 0xf6,
	0xb7, 0x04, 0x7f, 0x86, 0xbb, 0x0b, 0x04, 0x39,
	0xe9, 0x09, 0x27, 0x4c, 0xb9, 0xc2, 0x82, 0xbf,
	0xab, 0x88, 0x67, 0x4c, 0x07, 0x65, 0xbc, 0x75
};

static uint8_t pubkey_qy_secp384r1[] = {
	0xf7, 0x0d, 0x89, 0xc5, 0x2a, 0xcb, 0xc7, 0x04,
	0x68, 0xd2, 0xc5, 0xae, 0x75, 0xc7, 0x6d, 0x7f,
	0x69, 0xb7, 0x6a, 0xf6, 0x2d, 0xcf, 0x95, 0xe9,
	0x9e, 0xba, 0x5d, 0xd1, 0x1a, 0xdf, 0x8f, 0x42,
	0xec, 0x9a, 0x42, 0x5b, 0x0c, 0x5e, 0xc9, 0x8e,
	0x2f, 0x23, 0x4a, 0x92, 0x6b, 0x82, 0xa1, 0x47
};

static uint8_t sign_secp384r1_r[] = {
	0xb1, 0x1d, 0xb0, 0x0c, 0xda, 0xf5, 0x32, 0x86,
	0xd4, 0x48, 0x3f, 0x38, 0xcd, 0x02, 0x78, 0x59,
	0x48, 0x47, 0x7e, 0xd7, 0xeb, 0xc2, 0xad, 0x60,
	0x90, 0x54, 0x55, 0x1d, 0xa0, 0xab, 0x03, 0x59,
	0x97, 0x8c, 0x61, 0x85, 0x17, 0x88, 0xaa, 0x2e,
	0xc3, 0x26, 0x79, 0x46, 0xd4, 0x40, 0xe8, 0x78
};

static uint8_t sign_secp384r1_s[] = {
	0x16, 0x00, 0x78, 0x73, 0xc5, 0xb0, 0x60, 0x4c,
	0xe6, 0x81, 0x12, 0xa8, 0xfe, 0xe9, 0x73, 0xe8,
	0xe2, 0xb6, 0xe3, 0x31, 0x9c, 0x68, 0x3a, 0x76,
	0x2f, 0xf5, 0x06, 0x5a, 0x07, 0x65, 0x12, 0xd7,
	0xc9, 0x8b, 0x27, 0xe7, 0x4b, 0x78, 0x87, 0x67,
	0x10, 0x48, 0xac, 0x02, 0x7d, 0xf8, 0xcb, 0xf2
};

/** ECDSA SECP384R1 elliptic curve param */

struct crypto_testsuite_ecdsa_params ecdsa_param_secp384r1 = {
	.pubkey_qx = {
		.data = pubkey_qx_secp384r1,
		.length = sizeof(pubkey_qx_secp384r1),
	},
	.pubkey_qy = {
		.data = pubkey_qy_secp384r1,
		.length = sizeof(pubkey_qy_secp384r1),
	},
	.scalar = {
		.data = scalar_secp384r1,
		.length = sizeof(scalar_secp384r1),
	},
	.digest = {
		.data = digest_secp384r1,
		.length = sizeof(digest_secp384r1),
	},
	.sign_r = {
		.data = sign_secp384r1_r,
		.length = sizeof(sign_secp384r1_r),
	},
	.sign_s = {
		.data = sign_secp384r1_s,
		.length = sizeof(sign_secp384r1_s),
	},
	.pkey = {
		.data = pkey_secp384r1,
		.length = sizeof(pkey_secp384r1),
	},
	.curve = RTE_CRYPTO_EC_GROUP_SECP384R1
};

/* SECP521R1 (P-521 NIST) test vectors */

static uint8_t digest_secp521r1[] = {
	0x53, 0xe6, 0x53, 0x7c, 0xb6, 0xea, 0x68, 0xae,
	0x47, 0xa8, 0x16, 0x11, 0xc2, 0x27, 0x56, 0xd7,
	0x70, 0xd7, 0xa3, 0x7e, 0x33, 0x6c, 0x3a, 0xf0,
	0xb0, 0x81, 0x4b, 0x04, 0xfa, 0x39, 0x43, 0x4b
};

static uint8_t pkey_secp521r1[] = {
	0x01, 0xe8, 0xc0, 0x59, 0x96, 0xb8, 0x5e, 0x6f,
	0x3f, 0x87, 0x57, 0x12, 0xa0, 0x9c, 0x1b, 0x40,
	0x67, 0x2b, 0x5e, 0x7a, 0x78, 0xd5, 0x85, 0x2d,
	0xe0, 0x15, 0x85, 0xc5, 0xfb, 0x99, 0x0b, 0xf3,
	0x81, 0x2c, 0x32, 0x45, 0x53, 0x4a, 0x71, 0x43,
	0x89, 0xae, 0x90, 0x14, 0xd6, 0x77, 0xa4, 0x49,
	0xef, 0xd6, 0x58, 0x25, 0x4e, 0x61, 0x0d, 0xa8,
	0xe6, 0xca, 0xd3, 0x34, 0x14, 0xb9, 0xd3, 0x3e,
	0x0d, 0x7a
};

static uint8_t scalar_secp521r1[] = {
	0x00, 0xdc, 0x8d, 0xaa, 0xac, 0xdd, 0xb8, 0xfd,
	0x2f, 0xf5, 0xc3, 0x4a, 0x5c, 0xe1, 0x83, 0xa4,
	0x22, 0x61, 0xad, 0x3c, 0x64, 0xdb, 0xfc, 0x09,
	0x5e, 0x58, 0x92, 0x43, 0x64, 0xdc, 0x47, 0xea,
	0x1c, 0x05, 0xe2, 0x59, 0x9a, 0xae, 0x91, 0x7c,
	0x2c, 0x95, 0xf4, 0x7d, 0x6b, 0xb3, 0x7d, 0xa0,
	0x08, 0xaf, 0x9f, 0x55, 0x73, 0x0d, 0xdb, 0xe4,
	0xd8, 0xde, 0xd2, 0x4f, 0x9e, 0x8d, 0xaa, 0x46,
	0xdb, 0x6a
};

static uint8_t pubkey_qx_secp521r1[] = {
	0x00, 0x7d, 0x04, 0x2c, 0xa1, 0x94, 0x08, 0x52,
	0x4e, 0x68, 0xb9, 0x81, 0xf1, 0x41, 0x93, 0x51,
	0xe3, 0xb8, 0x47, 0x36, 0xc7, 0x7f, 0xe5, 0x8f,
	0xee, 0x7d, 0x11, 0x31, 0x7d, 0xf2, 0xe8, 0x50,
	0xd9, 0x60, 0xc7, 0xdd, 0x10, 0xd1, 0x0b, 0xa7,
	0x14, 0xc8, 0xa6, 0x09, 0xd1, 0x63, 0x50, 0x2b,
	0x79, 0xd6, 0x82, 0xe8, 0xbb, 0xec, 0xd4, 0xf5,
	0x25, 0x91, 0xd2, 0x74, 0x85, 0x33, 0xe4, 0x5a,
	0x86, 0x7a
};

static uint8_t pubkey_qy_secp521r1[] = {
	0x01, 0x97, 0xac, 0x64, 0x16, 0x11, 0x1c, 0xcf,
	0x98, 0x7d, 0x29, 0x04, 0x59, 0xeb, 0xc8, 0xad,
	0x9e, 0xc5, 0x6e, 0x49, 0x05, 0x9c, 0x99, 0x21,
	0x55, 0x53, 0x9a, 0x36, 0xa6, 0x26, 0x63, 0x1f,
	0x4a, 0x2d, 0x89, 0x16, 0x4b, 0x98, 0x51, 0x54,
	0xf2, 0xdd, 0xdc, 0x02, 0x81, 0xee, 0x5b, 0x51,
	0x78, 0x27, 0x1f, 0x3a, 0x76, 0xa0, 0x91, 0x4c,
	0x3f, 0xcd, 0x1f, 0x97, 0xbe, 0x8e, 0x83, 0x76,
	0xef, 0xb3
};

static uint8_t sign_secp521r1_r[] = {
	0x00, 0x9d, 0xd1, 0xf2, 0xa7, 0x16, 0x84, 0x3e,
	0xed, 0xec, 0x7a, 0x66, 0x45, 0xac, 0x83, 0x4d,
	0x43, 0x36, 0xe7, 0xb1, 0x8e, 0x35, 0x70, 0x1f,
	0x06, 0xca, 0xe9, 0xd6, 0xb2, 0x90, 0xd4, 0x14,
	0x91, 0x42, 0x47, 0x35, 0xf3, 0xb5, 0x7e, 0x82,
	0x9a, 0xd5, 0xde, 0x05, 0x5e, 0xae, 0xef, 0x17,
	0x78, 0xf0, 0x51, 0xc1, 0xee, 0x15, 0x2b, 0xf2,
	0x13, 0x1a, 0x08, 0x1e, 0x53, 0xdf, 0x2a, 0x56,
	0x7a, 0x8a
};

static uint8_t sign_secp521r1_s[] = {
	0x00, 0x21, 0x48, 0xe8, 0x42, 0x8d, 0x70, 0xa7,
	0x2b, 0xc9, 0xfa, 0x98, 0x6c, 0x38, 0xc2, 0xc9,
	0x7d, 0xed, 0xa0, 0x42, 0x0f, 0x22, 0x2f, 0x9d,
	0xc9, 0x9d, 0x32, 0xc0, 0xac, 0xba, 0x69, 0x9d,
	0xc7, 0xba, 0x0a, 0x2b, 0x79, 0xce, 0x59, 0x99,
	0xff, 0x61, 0xbd, 0x0b, 0x23, 0x3c, 0x74, 0x4a,
	0x89, 0x3b, 0xc1, 0x05, 0xbc, 0xa5, 0xc2, 0x35,
	0x42, 0x3e, 0x53, 0x16, 0x12, 0xda, 0x65, 0xd7,
	0x2e, 0x62
};

/** ECDSA SECP521R1 elliptic curve param */

struct crypto_testsuite_ecdsa_params ecdsa_param_secp521r1 = {
	.pubkey_qx = {
		.data = pubkey_qx_secp521r1,
		.length = sizeof(pubkey_qx_secp521r1),
	},
	.pubkey_qy = {
		.data = pubkey_qy_secp521r1,
		.length = sizeof(pubkey_qy_secp521r1),
	},
	.scalar = {
		.data = scalar_secp521r1,
		.length = sizeof(scalar_secp521r1),
	},
	.digest = {
		.data = digest_secp521r1,
		.length = sizeof(digest_secp521r1),
	},
	.sign_r = {
		.data = sign_secp521r1_r,
		.length = sizeof(sign_secp521r1_r),
	},
	.sign_s = {
		.data = sign_secp521r1_s,
		.length = sizeof(sign_secp521r1_s),
	},
	.pkey = {
		.data = pkey_secp521r1,
		.length = sizeof(pkey_secp521r1),
	},
	.curve = RTE_CRYPTO_EC_GROUP_SECP521R1
};

/* SECP521R1 (P-521 NIST) test vectors (unaligned) */

static uint8_t ua_digest_secp521r1[] = {
	0x7b, 0xec, 0xf5, 0x96, 0xa8, 0x12, 0x04, 0x4c,
	0x07, 0x96, 0x98, 0x4b, 0xe2, 0x3e, 0x9c, 0x02,
	0xbf, 0xc5, 0x90, 0x96, 0xf4, 0x2f, 0xfc, 0x8a,
	0x3f, 0x9a, 0x65, 0x0e
};

static uint8_t ua_pkey_secp521r1[] = {
	0x00, 0x70, 0xa8, 0x4d, 0x30, 0xfd, 0xc9, 0x01,
	0x1c, 0xc6, 0xc3, 0x38, 0xd4, 0x75, 0x6f, 0x3e,
	0x59, 0xd8, 0x91, 0xaa, 0xb4, 0x18, 0x3e, 0x3c,
	0xa5, 0x3d, 0x3f, 0x23, 0xd8, 0xe6, 0xfb, 0x3c,
	0x54, 0x5a, 0xa1, 0xdd, 0x40, 0xec, 0xc5, 0xa0,
	0x40, 0xa7, 0xb1, 0xb1, 0xbc, 0xfe, 0x34, 0xe4,
	0xbf, 0xdb, 0x40, 0x89, 0x45, 0xb5, 0xf7, 0x45,
	0x69, 0xca, 0xa7, 0xc1, 0x9e, 0x4a, 0x76, 0xa8,
	0x05, 0x58
};

static uint8_t ua_scalar_secp521r1[] = {
	0x00, 0x70, 0xa8, 0x4d, 0x30, 0xfd, 0xc9, 0x01,
	0x1c, 0xc6, 0xc3, 0x38, 0xd4, 0x75, 0x6f, 0x3e,
	0x59, 0xd8, 0x91, 0xaa, 0xb4, 0x18, 0x3e, 0x3c,
	0xa5, 0x3d, 0x3f, 0x23, 0xd8, 0xe6, 0xfb, 0x3c,
	0x54, 0x5a, 0xa1, 0xdd, 0x40, 0xec, 0xc5, 0xa0,
	0x40, 0xa7, 0xb1, 0xb1, 0xbc, 0xfe, 0x34, 0xe4,
	0xbf, 0xdb, 0x40, 0x89, 0x45, 0xb5, 0xf7, 0x45,
	0x69, 0xca, 0xa7, 0xc1, 0x9e, 0x4a, 0x76, 0xa8,
	0x05, 0x57
};

static uint8_t ua_pubkey_qx_secp521r1[] = {
	0x01, 0x29, 0x15, 0x13, 0xa6, 0x45, 0x98, 0x5c,
	0x5e, 0x2b, 0xc3, 0x99, 0xc5, 0x25, 0x64, 0x29,
	0x14, 0x91, 0x12, 0xcc, 0x58, 0x3a, 0x9d, 0x91,
	0x95, 0x64, 0x10, 0x9e, 0xc3, 0x2d, 0xde, 0xe2,
	0xb1, 0xac, 0x44, 0xb7, 0x90, 0x70, 0xbf, 0xb5,
	0x50, 0x3b, 0x06, 0x78, 0x36, 0x05, 0x7e, 0x48,
	0xe7, 0x31, 0x6e, 0x3f, 0x78, 0x3b, 0x37, 0xbc,
	0xa8, 0xcd, 0xc0, 0x34, 0xb6, 0x4f, 0xf8, 0x73,
	0xd0, 0xb3
};

static uint8_t ua_pubkey_qy_secp521r1[] = {
	0x00, 0xc1, 0x46, 0x92, 0x6e, 0x1a, 0xb5, 0xe6,
	0xee, 0x25, 0xe3, 0x62, 0x68, 0x30, 0x38, 0xef,
	0x44, 0x2a, 0xb0, 0xb8, 0xa9, 0xbc, 0x4b, 0x4b,
	0x55, 0x4c, 0x35, 0xde, 0x50, 0xcc, 0xc6, 0x9e,
	0xf9, 0x9d, 0x8d, 0xe9, 0x0f, 0x84, 0x95, 0xcb,
	0x41, 0xa2, 0xc7, 0xf3, 0x7d, 0xea, 0xb1, 0x8b,
	0x52, 0x5d, 0x58, 0x45, 0xac, 0xa0, 0xb4, 0x64,
	0x60, 0x74, 0x1f, 0x59, 0x71, 0x97, 0xe8, 0x6b,
	0x9f, 0x5d
};

static uint8_t ua_sign_secp521r1_r[] = {
	0x00, 0xf1, 0xea, 0x3b, 0x7b, 0xfb, 0x49, 0x60,
	0xf3, 0x93, 0x66, 0x8d, 0x81, 0x28, 0x7f, 0x40,
	0xe9, 0x35, 0xd6, 0x13, 0xe1, 0x51, 0x1a, 0xee,
	0xc8, 0x98, 0xa1, 0xf9, 0x62, 0xb6, 0x9f, 0xf3,
	0x18, 0xdd, 0x45, 0x3c, 0xbb, 0x9d, 0xee, 0x89,
	0xf1, 0x91, 0xf3, 0xd9, 0xe7, 0x08, 0xc8, 0xe6,
	0xd3, 0x61, 0x19, 0x28, 0x19, 0x17, 0x23, 0x2b,
	0x69, 0x49, 0x32, 0x9c, 0x98, 0x19, 0x52, 0x5c,
	0x73, 0xbf
};

static uint8_t ua_sign_secp521r1_s[] = {
	0x00, 0xa5, 0xab, 0x72, 0xc5, 0x71, 0x2a, 0x21,
	0x47, 0x6a, 0x45, 0xc8, 0xc2, 0xe3, 0x45, 0x40,
	0x8e, 0x79, 0xf8, 0x19, 0x65, 0xd4, 0xfd, 0xf2,
	0xd7, 0x28, 0xae, 0x70, 0xb8, 0xc1, 0x6d, 0x09,
	0x16, 0x31, 0x6e, 0xa0, 0x9c, 0xbf, 0x86, 0x19,
	0x9c, 0x04, 0x46, 0x48, 0xc1, 0x3f, 0x89, 0xb1,
	0xdd, 0xa2, 0x71, 0xb7, 0xf7, 0x0e, 0xa6, 0x66,
	0x15, 0x26, 0x66, 0x9b, 0xed, 0xda, 0x00, 0x1b,
	0x2b, 0xc5
};

/** ECDSA SECP521R1 elliptic curve param (unaligned) */

struct crypto_testsuite_ecdsa_params ecdsa_param_secp521r1_ua = {
	.pubkey_qx = {
		.data = ua_pubkey_qx_secp521r1,
		.length = sizeof(ua_pubkey_qx_secp521r1),
	},
	.pubkey_qy = {
		.data = ua_pubkey_qy_secp521r1,
		.length = sizeof(ua_pubkey_qy_secp521r1),
	},
	.scalar = {
		.data = ua_scalar_secp521r1,
		.length = sizeof(ua_scalar_secp521r1),
	},
	.digest = {
		.data = ua_digest_secp521r1,
		.length = sizeof(ua_digest_secp521r1),
	},
	.sign_r = {
		.data = ua_sign_secp521r1_r,
		.length = sizeof(ua_sign_secp521r1_r),
	},
	.sign_s = {
		.data = ua_sign_secp521r1_s,
		.length = sizeof(ua_sign_secp521r1_s),
	},
	.pkey = {
		.data = ua_pkey_secp521r1,
		.length = sizeof(ua_pkey_secp521r1),
	},
	.curve = RTE_CRYPTO_EC_GROUP_SECP521R1
};

#endif /* __TEST_CRYPTODEV_ECDSA_TEST_VECTORS_H__ */
