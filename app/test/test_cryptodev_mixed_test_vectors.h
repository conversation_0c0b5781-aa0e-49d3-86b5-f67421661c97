/* SPDX-License-Identifier: BSD-3-Clause
 * Copyright(c) 2019 Intel Corporation
 */

#ifndef TEST_CRYPTODEV_MIXED_TEST_VECTORS_H_
#define TEST_CRYPTODEV_MIXED_TEST_VECTORS_H_

struct mixed_cipher_auth_test_data {

	enum rte_crypto_auth_algorithm auth_algo;

	struct {
		uint8_t data[64];
		unsigned int len; /* length must be in Bytes */
	} auth_key;

	struct {
		uint8_t data[64];
		unsigned int len; /* length must be in Bytes */
	} auth_iv;

	struct {
		unsigned int len_bits;
		unsigned int offset_bits;
	} auth;

	enum rte_crypto_cipher_algorithm cipher_algo;

	struct {
		uint8_t data[64];
		unsigned int len; /* length must be in Bytes */
	} cipher_key;

	struct {
		alignas(16) uint8_t data[64];
		unsigned int len; /* length must be in Bytes */
	} cipher_iv;

	struct {
		unsigned int len_bits;
		unsigned int offset_bits;
	} cipher;

	struct {
		uint8_t data[1024];
		unsigned int len_bits;
	} plaintext;

	struct {
		uint8_t data[1024];
		unsigned int len_bits;
	} ciphertext;

	struct {
		uint8_t data[64];
		unsigned int len; /* length must be in Bytes */
		unsigned int offset; /* offset must be in Bytes */
	} digest_enc;

	struct {
		unsigned int len_bits;
	} validDataLen;

	struct {
		unsigned int len_bits;
	} validCipherLen;

	struct {
		unsigned int len_bits;
	} validAuthLen;
};

struct mixed_cipher_auth_test_data auth_aes_cmac_cipher_aes_ctr_test_case_1 = {
	.auth_algo = RTE_CRYPTO_AUTH_AES_CMAC,
	.auth_key = {
		.data = {
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
			0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10,
		},
		.len = 16,
	},
	.auth_iv = {
		.data = {
			 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
			 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		},
		.len = 16,
	},
	.auth = {
		.len_bits = 124 << 3,
		.offset_bits = 0,
	},
	.cipher_algo = RTE_CRYPTO_CIPHER_AES_CTR,
	.cipher_key = {
		.data = {
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
			0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10,
		},
		.len = 16,
	},
	.cipher_iv = {
		.data = {
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		},
		.len = 16,
	},
	.cipher = {
		.len_bits = 126 << 3,
		.offset_bits = 2 << 3,
	},
	.plaintext = {
		.data = {
			0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,
			0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,
			0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,
			0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,
			0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,
			0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,
			0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,
			0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,
			0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,
			0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,
			0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,
			0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,
			0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,
			0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,
			0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,
			0x5A, 0x5A, 0x5A, 0x5A
		},
		.len_bits = 124 << 3,
	},
	.ciphertext = {
		.data = {
			0x5A, 0x5A, 0xC9, 0x79, 0xCA, 0xCB, 0xDF, 0x96,
			0x97, 0xA5, 0xB0, 0xFC, 0x56, 0xE9, 0xC4, 0xB4,
			0x32, 0x5F, 0x55, 0x59, 0xCF, 0x1A, 0xA5, 0xB5,
			0x63, 0xAC, 0x94, 0x87, 0x6F, 0xFB, 0x5A, 0x54,
			0x4E, 0xE2, 0x2B, 0x32, 0x04, 0x0C, 0xDF, 0x7D,
			0x58, 0x73, 0xCE, 0x64, 0x71, 0xEA, 0x31, 0x19,
			0xE1, 0x37, 0x9E, 0x89, 0x15, 0x1F, 0xDE, 0x99,
			0x17, 0x80, 0x2A, 0x95, 0xAB, 0x37, 0xDF, 0x01,
			0x7F, 0x3B, 0x17, 0x07, 0xFA, 0x93, 0xC9, 0xC8,
			0x6A, 0x1C, 0xE2, 0x34, 0x2F, 0x33, 0x59, 0xE4,
			0xE2, 0x56, 0x1A, 0x23, 0xCC, 0xCE, 0x29, 0x33,
			0xC7, 0xAF, 0x92, 0x28, 0x86, 0x5B, 0x91, 0x5B,
			0x0E, 0x4E, 0xFB, 0x3B, 0x9D, 0x9F, 0xDF, 0x16,
			0x79, 0x90, 0x86, 0xBA, 0x70, 0xD9, 0xF7, 0x9B,
			0xEC, 0x46, 0xA9, 0x98, 0x5A, 0xF1, 0x5A, 0x05,
			0x3E, 0xD9, 0xE5, 0x03, 0x5D, 0x41, 0xB4, 0xDF
		},
		.len_bits = 128 << 3,
	},
	.digest_enc = {
		.data = {
			0x5D, 0x41, 0xB4, 0xDF,
		},
		.len = 4,
		.offset = 124,
	},
	.validDataLen = {
		.len_bits = 128 << 3,
	},
	.validCipherLen = {
		.len_bits = 126 << 3,
	},
	.validAuthLen = {
		.len_bits = 124 << 3,
	}
};

struct mixed_cipher_auth_test_data auth_aes_cmac_cipher_aes_ctr_test_case_2 = {
	.auth_algo = RTE_CRYPTO_AUTH_AES_CMAC,
	.auth_key = {
		.data = {
			0x2B, 0x7E, 0x15, 0x16, 0x28, 0xAE, 0xD2, 0xA6,
			0xAB, 0xF7, 0x15, 0x88, 0x09, 0xCF, 0x4F, 0x3C
		},
		.len = 16,
	},
	.auth_iv = {
		.data = {
		},
		.len = 0,
	},
	.auth = {
		.len_bits = 512 << 3,
		.offset_bits = 0,
	},
	.cipher_algo = RTE_CRYPTO_CIPHER_AES_CTR,
	.cipher_key = {
		.data = {
			0x2B, 0x7E, 0x15, 0x16, 0x28, 0xAE, 0xD2, 0xA6,
			0xAB, 0xF7, 0x15, 0x88, 0x09, 0xCF, 0x4F, 0x3C
		},
		.len = 16,
	},
	.cipher_iv = {
		.data = {
			0xa9, 0x40, 0x59, 0xda, 0x50, 0x00, 0x00, 0x00,
			0x29, 0x40, 0x59, 0xda, 0x50, 0x00, 0x80, 0x00
		},
		.len = 16,
	},
	.cipher = {
		.len_bits = 516 << 3,
		.offset_bits = 0,
	},
	.plaintext = {
		.data = {
			0x57, 0x68, 0x61, 0x74, 0x20, 0x61, 0x20, 0x6C,
			0x6F, 0x75, 0x73, 0x79, 0x20, 0x65, 0x61, 0x72,
			0x74, 0x68, 0x21, 0x20, 0x48, 0x65, 0x20, 0x77,
			0x6F, 0x6E, 0x64, 0x65, 0x72, 0x65, 0x64, 0x20,
			0x68, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E, 0x79,
			0x20, 0x70, 0x65, 0x6F, 0x70, 0x6C, 0x65, 0x20,
			0x77, 0x65, 0x72, 0x65, 0x20, 0x64, 0x65, 0x73,
			0x74, 0x69, 0x74, 0x75, 0x74, 0x65, 0x20, 0x74,
			0x68, 0x61, 0x74, 0x20, 0x73, 0x61, 0x6D, 0x65,
			0x20, 0x6E, 0x69, 0x67, 0x68, 0x74, 0x20, 0x65,
			0x76, 0x65, 0x6E, 0x20, 0x69, 0x6E, 0x20, 0x68,
			0x69, 0x73, 0x20, 0x6F, 0x77, 0x6E, 0x20, 0x70,
			0x72, 0x6F, 0x73, 0x70, 0x65, 0x72, 0x6F, 0x75,
			0x73, 0x20, 0x63, 0x6F, 0x75, 0x6E, 0x74, 0x72,
			0x79, 0x2C, 0x20, 0x68, 0x6F, 0x77, 0x20, 0x6D,
			0x61, 0x6E, 0x79, 0x20, 0x68, 0x6F, 0x6D, 0x65,
			0x73, 0x20, 0x77, 0x65, 0x72, 0x65, 0x20, 0x73,
			0x68, 0x61, 0x6E, 0x74, 0x69, 0x65, 0x73, 0x2C,
			0x20, 0x68, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E,
			0x79, 0x20, 0x68, 0x75, 0x73, 0x62, 0x61, 0x6E,
			0x64, 0x73, 0x20, 0x77, 0x65, 0x72, 0x65, 0x20,
			0x64, 0x72, 0x75, 0x6E, 0x6B, 0x20, 0x61, 0x6E,
			0x64, 0x20, 0x77, 0x69, 0x76, 0x65, 0x73, 0x20,
			0x73, 0x6F, 0x63, 0x6B, 0x65, 0x64, 0x2C, 0x20,
			0x61, 0x6E, 0x64, 0x20, 0x68, 0x6F, 0x77, 0x20,
			0x6D, 0x61, 0x6E, 0x79, 0x20, 0x63, 0x68, 0x69,
			0x6C, 0x64, 0x72, 0x65, 0x6E, 0x20, 0x77, 0x65,
			0x72, 0x65, 0x20, 0x62, 0x75, 0x6C, 0x6C, 0x69,
			0x65, 0x64, 0x2C, 0x20, 0x61, 0x62, 0x75, 0x73,
			0x65, 0x64, 0x2C, 0x20, 0x6F, 0x72, 0x20, 0x61,
			0x62, 0x61, 0x6E, 0x64, 0x6F, 0x6E, 0x65, 0x64,
			0x2E, 0x20, 0x48, 0x6F, 0x77, 0x20, 0x6D, 0x61,
			0x6E, 0x79, 0x20, 0x66, 0x61, 0x6D, 0x69, 0x6C,
			0x69, 0x65, 0x73, 0x20, 0x68, 0x75, 0x6E, 0x67,
			0x65, 0x72, 0x65, 0x64, 0x20, 0x66, 0x6F, 0x72,
			0x20, 0x66, 0x6F, 0x6F, 0x64, 0x20, 0x74, 0x68,
			0x65, 0x79, 0x20, 0x63, 0x6F, 0x75, 0x6C, 0x64,
			0x20, 0x6E, 0x6F, 0x74, 0x20, 0x61, 0x66, 0x66,
			0x6F, 0x72, 0x64, 0x20, 0x74, 0x6F, 0x20, 0x62,
			0x75, 0x79, 0x3F, 0x20, 0x48, 0x6F, 0x77, 0x20,
			0x6D, 0x61, 0x6E, 0x79, 0x20, 0x68, 0x65, 0x61,
			0x72, 0x74, 0x73, 0x20, 0x77, 0x65, 0x72, 0x65,
			0x20, 0x62, 0x72, 0x6F, 0x6B, 0x65, 0x6E, 0x3F,
			0x20, 0x48, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E,
			0x79, 0x20, 0x73, 0x75, 0x69, 0x63, 0x69, 0x64,
			0x65, 0x73, 0x20, 0x77, 0x6F, 0x75, 0x6C, 0x64,
			0x20, 0x74, 0x61, 0x6B, 0x65, 0x20, 0x70, 0x6C,
			0x61, 0x63, 0x65, 0x20, 0x74, 0x68, 0x61, 0x74,
			0x20, 0x73, 0x61, 0x6D, 0x65, 0x20, 0x6E, 0x69,
			0x67, 0x68, 0x74, 0x2C, 0x20, 0x68, 0x6F, 0x77,
			0x20, 0x6D, 0x61, 0x6E, 0x79, 0x20, 0x70, 0x65,
			0x6F, 0x70, 0x6C, 0x65, 0x20, 0x77, 0x6F, 0x75,
			0x6C, 0x64, 0x20, 0x67, 0x6F, 0x20, 0x69, 0x6E,
			0x73, 0x61, 0x6E, 0x65, 0x3F, 0x20, 0x48, 0x6F,
			0x77, 0x20, 0x6D, 0x61, 0x6E, 0x79, 0x20, 0x63,
			0x6F, 0x63, 0x6B, 0x72, 0x6F, 0x61, 0x63, 0x68,
			0x65, 0x73, 0x20, 0x61, 0x6E, 0x64, 0x20, 0x6C,
			0x61, 0x6E, 0x64, 0x6C, 0x6F, 0x72, 0x64, 0x73,
			0x20, 0x77, 0x6F, 0x75, 0x6C, 0x64, 0x20, 0x74,
			0x72, 0x69, 0x75, 0x6D, 0x70, 0x68, 0x3F, 0x20,
			0x48, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E, 0x79,
			0x20, 0x77, 0x69, 0x6E, 0x6E, 0x65, 0x72, 0x73,
			0x20, 0x77, 0x65, 0x72, 0x65, 0x20, 0x6C, 0x6F,
			0x73, 0x65, 0x72, 0x73, 0x2C, 0x20, 0x73, 0x75
		},
		.len_bits = 512 << 3,
	},
	.ciphertext = {
		.data = {
			0xA5, 0xC3, 0xEC, 0xC5, 0x14, 0x36, 0xC8, 0x00,
			0x6E, 0xA6, 0xAD, 0xFB, 0x1C, 0x4F, 0x60, 0x6C,
			0x60, 0xC0, 0x9C, 0x00, 0xED, 0xC0, 0x2A, 0xD8,
			0x41, 0xD3, 0x44, 0x27, 0xE6, 0x1A, 0x6E, 0xB3,
			0x9C, 0x5E, 0x3C, 0x71, 0x19, 0x96, 0x13, 0xED,
			0x06, 0xCA, 0xFB, 0x74, 0x7C, 0x23, 0x3E, 0x54,
			0x49, 0xF1, 0x32, 0xA4, 0xCD, 0xF3, 0x92, 0x33,
			0x22, 0xDB, 0xE0, 0x0D, 0x3F, 0x31, 0xA3, 0x2B,
			0x85, 0x11, 0x41, 0x11, 0x02, 0xE0, 0x2B, 0x0E,
			0x35, 0xDF, 0xE4, 0x03, 0x6B, 0x48, 0x80, 0x4A,
			0xB8, 0x2D, 0xD4, 0xB3, 0x5B, 0xF1, 0xB4, 0xF9,
			0xC9, 0x83, 0x4E, 0x2A, 0x53, 0x92, 0xB2, 0xF6,
			0x0C, 0xBA, 0xC4, 0x94, 0x8E, 0x96, 0x60, 0x26,
			0x4F, 0xA1, 0x85, 0xC6, 0x31, 0x57, 0xAE, 0xC3,
			0x8C, 0x09, 0x1B, 0xDB, 0xC1, 0x0D, 0x0E, 0xCE,
			0xB9, 0xE9, 0x30, 0x5E, 0x51, 0x9C, 0xE4, 0xC5,
			0xDC, 0xC0, 0x4A, 0xD9, 0x1B, 0xBE, 0xCF, 0x0A,
			0x95, 0xA1, 0x78, 0xDD, 0xE0, 0x46, 0x90, 0xC8,
			0xFF, 0xAE, 0x38, 0x1E, 0xAF, 0x5C, 0x57, 0xED,
			0x20, 0x49, 0xED, 0x54, 0x57, 0xBA, 0x65, 0xAD,
			0xC1, 0x2B, 0xD3, 0xEF, 0x97, 0x1F, 0x27, 0xBE,
			0x8E, 0x98, 0x2C, 0x4B, 0xDF, 0x11, 0x77, 0xB9,
			0x7D, 0x89, 0x0D, 0x28, 0xE3, 0x8B, 0x09, 0xAF,
			0x85, 0xCE, 0x0F, 0x49, 0xD5, 0x8E, 0xF2, 0xF2,
			0xF9, 0x4C, 0xE0, 0xF0, 0x9B, 0xD0, 0x42, 0x07,
			0xBC, 0xD4, 0x8E, 0x78, 0xCD, 0x4D, 0x6D, 0x12,
			0xB2, 0x8D, 0x4C, 0xCB, 0x44, 0x2A, 0xA2, 0x4C,
			0xD7, 0x15, 0xFA, 0xB1, 0x05, 0xB9, 0xFF, 0x95,
			0x29, 0xF7, 0xA6, 0xFC, 0x25, 0x74, 0xF6, 0x9E,
			0x01, 0xFE, 0x4C, 0xFD, 0x50, 0xD8, 0xF6, 0x4C,
			0xDF, 0x75, 0xEF, 0xAF, 0x04, 0x84, 0x95, 0x15,
			0x33, 0x6D, 0x8E, 0x0A, 0x89, 0xE0, 0x20, 0x2E,
			0xC2, 0x0E, 0xE1, 0x91, 0xB9, 0x06, 0x34, 0x14,
			0xD4, 0x65, 0x68, 0x72, 0x5C, 0x60, 0x09, 0x2C,
			0x8A, 0x82, 0x4A, 0x04, 0x71, 0xB8, 0xDF, 0x45,
			0x3C, 0x21, 0x24, 0x06, 0x34, 0xD0, 0xE3, 0xA7,
			0x82, 0x25, 0xAB, 0x7B, 0xDD, 0x6D, 0xCC, 0xAE,
			0x01, 0xB1, 0xD6, 0x2A, 0xE2, 0x96, 0x6C, 0x65,
			0x94, 0xDB, 0xB5, 0x0F, 0xCD, 0xC9, 0xA6, 0x01,
			0x5C, 0x89, 0x59, 0x6C, 0x10, 0x4C, 0xA3, 0xBC,
			0x9A, 0x4F, 0x11, 0xEB, 0x54, 0xE0, 0x3B, 0x83,
			0x9C, 0x5E, 0x16, 0x0D, 0xD6, 0x2C, 0x4B, 0x6F,
			0xE6, 0xB9, 0xF6, 0x02, 0x6C, 0x34, 0x0C, 0x4D,
			0xEE, 0xF0, 0x42, 0xC3, 0x0C, 0xDE, 0x93, 0x8A,
			0xC1, 0xB5, 0xEF, 0xCF, 0x54, 0x9C, 0x3B, 0x4B,
			0x70, 0x6C, 0x06, 0x97, 0x3E, 0x26, 0x8C, 0x61,
			0x52, 0x47, 0xF6, 0x9C, 0x28, 0x5C, 0xD1, 0x4A,
			0x67, 0x4B, 0xCD, 0xD9, 0xA1, 0x8C, 0x29, 0x31,
			0x10, 0x71, 0x45, 0x68, 0x63, 0xCE, 0xC9, 0xBC,
			0x46, 0x6A, 0x81, 0x02, 0x4D, 0x66, 0xA4, 0x65,
			0xCC, 0x33, 0xEC, 0x99, 0x14, 0x4D, 0x28, 0xAB,
			0xF6, 0xCB, 0x46, 0x13, 0x0C, 0x13, 0x74, 0x22,
			0x4A, 0x7B, 0x53, 0x2F, 0x11, 0x54, 0xAF, 0xD9,
			0x2D, 0x66, 0x98, 0x7E, 0x20, 0x57, 0x01, 0x4C,
			0xF7, 0x0C, 0xCB, 0x70, 0xFF, 0x3D, 0xC4, 0x5F,
			0x3A, 0xAE, 0xD9, 0xC6, 0x93, 0xFC, 0x2C, 0x23,
			0x4C, 0x43, 0x92, 0x9B, 0x92, 0x3E, 0x35, 0xB7,
			0x1E, 0x96, 0xED, 0xCC, 0x60, 0xDE, 0x2E, 0x19,
			0xB8, 0x24, 0x55, 0x1E, 0x52, 0xB9, 0x52, 0x3C,
			0xC0, 0x06, 0x74, 0xC0, 0xEE, 0xD1, 0xA1, 0x80,
			0x98, 0xE8, 0xDF, 0x0D, 0x8A, 0x44, 0xC2, 0x36,
			0x16, 0xCC, 0x03, 0x68, 0xD9, 0xC2, 0xCF, 0xE2,
			0x78, 0xBD, 0x1A, 0x31, 0x19, 0x5D, 0xE0, 0x6E,
			0x09, 0x6E, 0xD5, 0x17, 0xB8, 0xA8, 0xF4, 0xBE,
			0x2C, 0xD6, 0xD1, 0x04
		},
		.len_bits = 516 << 3,
	},
	.digest_enc = {
		.data = {
			0x2C, 0xD6, 0xD1, 0x04
		},
		.len = 4,
		.offset = 512,
	},
	.validDataLen = {
		.len_bits = 516 << 3,
	},
	.validCipherLen = {
		.len_bits = 516 << 3,
	},
	.validAuthLen = {
		.len_bits = 512 << 3,
	}
};

struct mixed_cipher_auth_test_data auth_zuc_cipher_snow_test_case_1 = {
	.auth_algo = RTE_CRYPTO_AUTH_ZUC_EIA3,
	.auth_key = {
		.data = {
			0xc9, 0xe6, 0xce, 0xc4, 0x60, 0x7c, 0x72, 0xdb,
			0x00, 0x0a, 0xef, 0xa8, 0x83, 0x85, 0xab, 0x0a
		},
		.len = 16,
	},
	.auth_iv = {
		.data = {
			0xa9, 0x40, 0x59, 0xda, 0x50, 0x00, 0x00, 0x00,
			0x29, 0x40, 0x59, 0xda, 0x50, 0x00, 0x80, 0x00
		},
		.len = 16,
	},
	.auth = {
		.len_bits = 73 << 3,
		.offset_bits = 0,
	},
	.cipher_algo = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	.cipher_key = {
		.data = {
			0xc9, 0xe6, 0xce, 0xc4, 0x60, 0x7c, 0x72, 0xdb,
			0x00, 0x0a, 0xef, 0xa8, 0x83, 0x85, 0xab, 0x0a
		},
		.len = 16,
	},
	.cipher_iv = {
		.data = {
			0xa9, 0x40, 0x59, 0xda, 0x50, 0x00, 0x00, 0x00,
			0x29, 0x40, 0x59, 0xda, 0x50, 0x00, 0x80, 0x00
		},
		.len = 16,
	},
	.cipher = {
		.len_bits = 77 << 3,
		.offset_bits = 0,
	},
	.plaintext = {
		.data = {
			0x98, 0x3b, 0x41, 0xd4, 0x7d, 0x78, 0x0c, 0x9e,
			0x1a, 0xd1, 0x1d, 0x7e, 0xb7, 0x03, 0x91, 0xb1,
			0xde, 0x0b, 0x35, 0xda, 0x2d, 0xc6, 0x2f, 0x83,
			0xe7, 0xb7, 0x8d, 0x63, 0x06, 0xca, 0x0e, 0xa0,
			0x7e, 0x94, 0x1b, 0x7b, 0xe9, 0x13, 0x48, 0xf9,
			0xfc, 0xb1, 0x70, 0xe2, 0x21, 0x7f, 0xec, 0xd9,
			0x7f, 0x9f, 0x68, 0xad, 0xb1, 0x6e, 0x5d, 0x7d,
			0x21, 0xe5, 0x69, 0xd2, 0x80, 0xed, 0x77, 0x5c,
			0xeb, 0xde, 0x3f, 0x40, 0x93, 0xc5, 0x38, 0x81,
			0x00
		},
		.len_bits = 73 << 3,
	},
	.ciphertext = {
		.data = {
			0x18, 0x46, 0xE1, 0xC5, 0x2C, 0x85, 0x93, 0x22,
			0x84, 0x80, 0xD6, 0x84, 0x5C, 0x99, 0x55, 0xE0,
			0xD5, 0x02, 0x41, 0x74, 0x4A, 0xD2, 0x8E, 0x7E,
			0xB9, 0x79, 0xD3, 0xE5, 0x76, 0x75, 0xD5, 0x59,
			0x26, 0xD7, 0x06, 0x2D, 0xF4, 0x71, 0x26, 0x40,
			0xAC, 0x77, 0x62, 0xAC, 0x35, 0x0D, 0xC5, 0x35,
			0xF8, 0x03, 0x54, 0x52, 0x2E, 0xCA, 0x14, 0xD8,
			0x2E, 0x6C, 0x0E, 0x7A, 0x09, 0xE7, 0x20, 0xDD,
			0x7C, 0xE3, 0x28, 0x77, 0x53, 0x65, 0xBA, 0x54,
			0xE8, 0x25, 0x04, 0x52, 0xFD
		},
		.len_bits = 77 << 3,
	},
	.digest_enc = {
		.data = {
			0x25, 0x04, 0x52, 0xFD
		},
		.len = 4,
		.offset = 73,
	},
	.validDataLen = {
		.len_bits = 77 << 3,
	},
	.validCipherLen = {
		.len_bits = 77 << 3,
	},
	.validAuthLen = {
		.len_bits = 73 << 3,
	}
};

struct mixed_cipher_auth_test_data auth_aes_cmac_cipher_snow_test_case_1 = {
	.auth_algo = RTE_CRYPTO_AUTH_AES_CMAC,
	.auth_key = {
		.data = {
			0x2B, 0x7E, 0x15, 0x16, 0x28, 0xAE, 0xD2, 0xA6,
			0xAB, 0xF7, 0x15, 0x88, 0x09, 0xCF, 0x4F, 0x3C
		},
		.len = 16,
	},
	.auth_iv = {
		.data = {
		},
		.len = 0,
	},
	.auth = {
		.len_bits = 512 << 3,
		.offset_bits = 0,
	},
	.cipher_algo = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	.cipher_key = {
		.data = {
			0x2B, 0x7E, 0x15, 0x16, 0x28, 0xAE, 0xD2, 0xA6,
			0xAB, 0xF7, 0x15, 0x88, 0x09, 0xCF, 0x4F, 0x3C
		},
		.len = 16,
	},
	.cipher_iv = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
		},
		.len = 16,
	},
	.cipher = {
		.len_bits = 516 << 3,
		.offset_bits = 0,
	},
	.plaintext = {
		.data = {
			0x57, 0x68, 0x61, 0x74, 0x20, 0x61, 0x20, 0x6C,
			0x6F, 0x75, 0x73, 0x79, 0x20, 0x65, 0x61, 0x72,
			0x74, 0x68, 0x21, 0x20, 0x48, 0x65, 0x20, 0x77,
			0x6F, 0x6E, 0x64, 0x65, 0x72, 0x65, 0x64, 0x20,
			0x68, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E, 0x79,
			0x20, 0x70, 0x65, 0x6F, 0x70, 0x6C, 0x65, 0x20,
			0x77, 0x65, 0x72, 0x65, 0x20, 0x64, 0x65, 0x73,
			0x74, 0x69, 0x74, 0x75, 0x74, 0x65, 0x20, 0x74,
			0x68, 0x61, 0x74, 0x20, 0x73, 0x61, 0x6D, 0x65,
			0x20, 0x6E, 0x69, 0x67, 0x68, 0x74, 0x20, 0x65,
			0x76, 0x65, 0x6E, 0x20, 0x69, 0x6E, 0x20, 0x68,
			0x69, 0x73, 0x20, 0x6F, 0x77, 0x6E, 0x20, 0x70,
			0x72, 0x6F, 0x73, 0x70, 0x65, 0x72, 0x6F, 0x75,
			0x73, 0x20, 0x63, 0x6F, 0x75, 0x6E, 0x74, 0x72,
			0x79, 0x2C, 0x20, 0x68, 0x6F, 0x77, 0x20, 0x6D,
			0x61, 0x6E, 0x79, 0x20, 0x68, 0x6F, 0x6D, 0x65,
			0x73, 0x20, 0x77, 0x65, 0x72, 0x65, 0x20, 0x73,
			0x68, 0x61, 0x6E, 0x74, 0x69, 0x65, 0x73, 0x2C,
			0x20, 0x68, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E,
			0x79, 0x20, 0x68, 0x75, 0x73, 0x62, 0x61, 0x6E,
			0x64, 0x73, 0x20, 0x77, 0x65, 0x72, 0x65, 0x20,
			0x64, 0x72, 0x75, 0x6E, 0x6B, 0x20, 0x61, 0x6E,
			0x64, 0x20, 0x77, 0x69, 0x76, 0x65, 0x73, 0x20,
			0x73, 0x6F, 0x63, 0x6B, 0x65, 0x64, 0x2C, 0x20,
			0x61, 0x6E, 0x64, 0x20, 0x68, 0x6F, 0x77, 0x20,
			0x6D, 0x61, 0x6E, 0x79, 0x20, 0x63, 0x68, 0x69,
			0x6C, 0x64, 0x72, 0x65, 0x6E, 0x20, 0x77, 0x65,
			0x72, 0x65, 0x20, 0x62, 0x75, 0x6C, 0x6C, 0x69,
			0x65, 0x64, 0x2C, 0x20, 0x61, 0x62, 0x75, 0x73,
			0x65, 0x64, 0x2C, 0x20, 0x6F, 0x72, 0x20, 0x61,
			0x62, 0x61, 0x6E, 0x64, 0x6F, 0x6E, 0x65, 0x64,
			0x2E, 0x20, 0x48, 0x6F, 0x77, 0x20, 0x6D, 0x61,
			0x6E, 0x79, 0x20, 0x66, 0x61, 0x6D, 0x69, 0x6C,
			0x69, 0x65, 0x73, 0x20, 0x68, 0x75, 0x6E, 0x67,
			0x65, 0x72, 0x65, 0x64, 0x20, 0x66, 0x6F, 0x72,
			0x20, 0x66, 0x6F, 0x6F, 0x64, 0x20, 0x74, 0x68,
			0x65, 0x79, 0x20, 0x63, 0x6F, 0x75, 0x6C, 0x64,
			0x20, 0x6E, 0x6F, 0x74, 0x20, 0x61, 0x66, 0x66,
			0x6F, 0x72, 0x64, 0x20, 0x74, 0x6F, 0x20, 0x62,
			0x75, 0x79, 0x3F, 0x20, 0x48, 0x6F, 0x77, 0x20,
			0x6D, 0x61, 0x6E, 0x79, 0x20, 0x68, 0x65, 0x61,
			0x72, 0x74, 0x73, 0x20, 0x77, 0x65, 0x72, 0x65,
			0x20, 0x62, 0x72, 0x6F, 0x6B, 0x65, 0x6E, 0x3F,
			0x20, 0x48, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E,
			0x79, 0x20, 0x73, 0x75, 0x69, 0x63, 0x69, 0x64,
			0x65, 0x73, 0x20, 0x77, 0x6F, 0x75, 0x6C, 0x64,
			0x20, 0x74, 0x61, 0x6B, 0x65, 0x20, 0x70, 0x6C,
			0x61, 0x63, 0x65, 0x20, 0x74, 0x68, 0x61, 0x74,
			0x20, 0x73, 0x61, 0x6D, 0x65, 0x20, 0x6E, 0x69,
			0x67, 0x68, 0x74, 0x2C, 0x20, 0x68, 0x6F, 0x77,
			0x20, 0x6D, 0x61, 0x6E, 0x79, 0x20, 0x70, 0x65,
			0x6F, 0x70, 0x6C, 0x65, 0x20, 0x77, 0x6F, 0x75,
			0x6C, 0x64, 0x20, 0x67, 0x6F, 0x20, 0x69, 0x6E,
			0x73, 0x61, 0x6E, 0x65, 0x3F, 0x20, 0x48, 0x6F,
			0x77, 0x20, 0x6D, 0x61, 0x6E, 0x79, 0x20, 0x63,
			0x6F, 0x63, 0x6B, 0x72, 0x6F, 0x61, 0x63, 0x68,
			0x65, 0x73, 0x20, 0x61, 0x6E, 0x64, 0x20, 0x6C,
			0x61, 0x6E, 0x64, 0x6C, 0x6F, 0x72, 0x64, 0x73,
			0x20, 0x77, 0x6F, 0x75, 0x6C, 0x64, 0x20, 0x74,
			0x72, 0x69, 0x75, 0x6D, 0x70, 0x68, 0x3F, 0x20,
			0x48, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E, 0x79,
			0x20, 0x77, 0x69, 0x6E, 0x6E, 0x65, 0x72, 0x73,
			0x20, 0x77, 0x65, 0x72, 0x65, 0x20, 0x6C, 0x6F,
			0x73, 0x65, 0x72, 0x73, 0x2C, 0x20, 0x73, 0x75
		},
		.len_bits = 512 << 3,
	},
	.ciphertext = {
		.data = {
			0x8A, 0xA9, 0x74, 0x31, 0xB1, 0xF2, 0xAB, 0x00,
			0xD6, 0x3D, 0xFA, 0xBD, 0xD9, 0x65, 0x52, 0x80,
			0xB5, 0x98, 0x20, 0xFF, 0x8D, 0x1C, 0x0F, 0x53,
			0xDD, 0x79, 0xCC, 0x9D, 0x7A, 0x6D, 0x76, 0x06,
			0xB6, 0xF4, 0xAC, 0xDA, 0xF2, 0x24, 0x02, 0x58,
			0x5F, 0xE3, 0xD4, 0xF7, 0x0B, 0x3B, 0x1C, 0x4C,
			0x0B, 0x4C, 0xC7, 0x4D, 0x3D, 0xFA, 0x28, 0xD9,
			0xA0, 0x90, 0x3E, 0x91, 0xDC, 0xC4, 0xE1, 0x2E,
			0x7C, 0xB4, 0xBD, 0xE0, 0x9E, 0xC8, 0x33, 0x42,
			0x0E, 0x84, 0xEF, 0x3C, 0xF1, 0x8B, 0x2C, 0xBD,
			0x33, 0x70, 0x22, 0xBA, 0xD4, 0x0B, 0xB2, 0x83,
			0x7F, 0x27, 0x51, 0x92, 0xD1, 0x40, 0x1E, 0xCD,
			0x62, 0x0F, 0x61, 0x5F, 0xB4, 0xB1, 0x0D, 0x1A,
			0x16, 0x1B, 0xE8, 0xA8, 0x2B, 0x45, 0xBA, 0x56,
			0x30, 0xD0, 0xE3, 0xCA, 0x4D, 0x23, 0xA3, 0x38,
			0xD6, 0x2C, 0xE4, 0x8D, 0xFF, 0x23, 0x97, 0x9E,
			0xE9, 0xBD, 0x70, 0xAF, 0x6B, 0x68, 0xA7, 0x21,
			0x3C, 0xFB, 0xB2, 0x99, 0x4D, 0xE9, 0x70, 0x56,
			0x36, 0xB8, 0xD7, 0xE0, 0xEB, 0x62, 0xA1, 0x79,
			0xF9, 0xD6, 0xAD, 0x83, 0x75, 0x54, 0xF5, 0x45,
			0x82, 0xE8, 0xD6, 0xA9, 0x76, 0x11, 0xC7, 0x81,
			0x2C, 0xBA, 0x67, 0xB5, 0xDB, 0xE5, 0xF2, 0x6B,
			0x7D, 0x9F, 0x4E, 0xDC, 0xA1, 0x62, 0xF1, 0xF0,
			0xAD, 0xD4, 0x7A, 0xA3, 0xF3, 0x76, 0x29, 0xA4,
			0xB7, 0xF3, 0x31, 0x84, 0xE7, 0x1F, 0x0D, 0x01,
			0xBD, 0x46, 0x07, 0x51, 0x05, 0x76, 0xE2, 0x95,
			0xF8, 0x48, 0x18, 0x8A, 0x1E, 0x92, 0x8B, 0xBC,
			0x30, 0x05, 0xF5, 0xD6, 0x96, 0xEF, 0x78, 0xB6,
			0xF3, 0xEC, 0x4C, 0xB1, 0x88, 0x8B, 0x63, 0x40,
			0x07, 0x37, 0xB4, 0x1A, 0xBD, 0xE9, 0x38, 0xB4,
			0x31, 0x35, 0x9D, 0x0C, 0xF1, 0x24, 0x0E, 0xD2,
			0xAE, 0x39, 0xA6, 0x41, 0x3C, 0x91, 0x6A, 0x4B,
			0xEC, 0x46, 0x76, 0xB4, 0x15, 0xC3, 0x58, 0x96,
			0x69, 0x02, 0x21, 0x37, 0x65, 0xDF, 0xA6, 0x43,
			0x78, 0x81, 0x8B, 0x39, 0x37, 0xE3, 0xF3, 0xD9,
			0xA2, 0xAA, 0x3F, 0xA9, 0x21, 0x24, 0x93, 0x4A,
			0xB0, 0xDE, 0x22, 0x5F, 0xF8, 0xD3, 0xCC, 0x13,
			0x5C, 0xC2, 0x5C, 0x98, 0x6D, 0xFB, 0x34, 0x26,
			0xE2, 0xC9, 0x26, 0x23, 0x41, 0xAB, 0xC3, 0x8A,
			0xEC, 0x62, 0xA9, 0x5B, 0x51, 0xB9, 0x10, 0x9D,
			0xB1, 0xBB, 0xDE, 0x78, 0xDE, 0xE7, 0xF0, 0x9F,
			0x91, 0x6C, 0x4D, 0xFC, 0xB3, 0x9C, 0xFF, 0xA4,
			0x9D, 0xB8, 0xCD, 0xF6, 0xA8, 0x6A, 0xDB, 0x3B,
			0x82, 0xFE, 0xCD, 0x6B, 0x08, 0x0A, 0x5E, 0x76,
			0xE9, 0xB3, 0xA2, 0x78, 0x25, 0xDB, 0xB1, 0x76,
			0x42, 0x2C, 0xFB, 0x20, 0x87, 0x81, 0x76, 0x17,
			0x99, 0xFD, 0x56, 0x52, 0xE2, 0xB0, 0x8E, 0x1B,
			0x99, 0xB3, 0x6B, 0x16, 0xC5, 0x4F, 0x0D, 0xBB,
			0x0E, 0xB7, 0x54, 0x63, 0xD9, 0x67, 0xD9, 0x85,
			0x1F, 0xA8, 0xF0, 0xF0, 0xB0, 0x41, 0xDC, 0xBC,
			0x75, 0xEE, 0x23, 0x7D, 0x40, 0xCE, 0xB8, 0x0A,
			0x6D, 0xC1, 0xD7, 0xCB, 0xAE, 0xCE, 0x91, 0x9E,
			0x3E, 0x5A, 0x76, 0xF8, 0xC0, 0xF2, 0x7F, 0x0B,
			0xD2, 0x5F, 0x63, 0xBE, 0xB2, 0x81, 0x8E, 0x6D,
			0xB3, 0x6B, 0x67, 0x9D, 0xAC, 0xE2, 0xDB, 0x7C,
			0x11, 0x19, 0x55, 0x55, 0x11, 0xED, 0x7F, 0x4E,
			0x9E, 0x4B, 0x6E, 0x01, 0x74, 0x4A, 0xE8, 0x78,
			0xEC, 0xCD, 0xF7, 0xA2, 0x6E, 0xDB, 0xB6, 0x3B,
			0x4D, 0x2C, 0x09, 0x62, 0x57, 0x6E, 0x38, 0x8A,
			0x61, 0x17, 0x00, 0xE9, 0x86, 0x7F, 0x3D, 0x93,
			0xBC, 0xC3, 0x27, 0x90, 0x7E, 0x41, 0x81, 0xBA,
			0x74, 0x70, 0x19, 0xE8, 0xD2, 0x88, 0x61, 0xDF,
			0xB4, 0xED, 0xA4, 0x9D, 0x3D, 0xED, 0x95, 0x65,
			0xCA, 0xFF, 0x8D, 0x58, 0x63, 0x10, 0x9D, 0xBE,
			0x78, 0x81, 0x47, 0x38
		},
		.len_bits = 516 << 3,
	},
	.digest_enc = {
		.data = {
			0x78, 0x81, 0x47, 0x38
		},
		.len = 4,
		.offset = 512,
	},
	.validDataLen = {
		.len_bits = 516 << 3,
	},
	.validCipherLen = {
		.len_bits = 516 << 3,
	},
	.validAuthLen = {
		.len_bits = 512 << 3,
	}
};

struct mixed_cipher_auth_test_data auth_zuc_cipher_aes_ctr_test_case_1 = {
	.auth_algo = RTE_CRYPTO_AUTH_ZUC_EIA3,
	.auth_key = {
		.data = {
			0xc9, 0xe6, 0xce, 0xc4, 0x60, 0x7c, 0x72, 0xdb,
			0x00, 0x0a, 0xef, 0xa8, 0x83, 0x85, 0xab, 0x0a
		},
		.len = 16,
	},
	.auth_iv = {
		.data = {
			0xa9, 0x40, 0x59, 0xda, 0x50, 0x00, 0x00, 0x00,
			0x29, 0x40, 0x59, 0xda, 0x50, 0x00, 0x80, 0x00
		},
		.len = 16,
	},
	.auth = {
		.len_bits = 73 << 3,
		.offset_bits = 0,
	},
	.cipher_algo = RTE_CRYPTO_CIPHER_AES_CTR,
	.cipher_key = {
		.data = {
			0xc9, 0xe6, 0xce, 0xc4, 0x60, 0x7c, 0x72, 0xdb,
			0x00, 0x0a, 0xef, 0xa8, 0x83, 0x85, 0xab, 0x0a
		},
		.len = 16,
	},
	.cipher_iv = {
		.data = {
			0xa9, 0x40, 0x59, 0xda, 0x50, 0x00, 0x00, 0x00,
			0x29, 0x40, 0x59, 0xda, 0x50, 0x00, 0x80, 0x00
		},
		.len = 16,
	},
	.cipher = {
		.len_bits = 77 << 3,
		.offset_bits = 0,
	},
	.plaintext = {
		.data = {
			0x98, 0x3b, 0x41, 0xd4, 0x7d, 0x78, 0x0c, 0x9e,
			0x1a, 0xd1, 0x1d, 0x7e, 0xb7, 0x03, 0x91, 0xb1,
			0xde, 0x0b, 0x35, 0xda, 0x2d, 0xc6, 0x2f, 0x83,
			0xe7, 0xb7, 0x8d, 0x63, 0x06, 0xca, 0x0e, 0xa0,
			0x7e, 0x94, 0x1b, 0x7b, 0xe9, 0x13, 0x48, 0xf9,
			0xfc, 0xb1, 0x70, 0xe2, 0x21, 0x7f, 0xec, 0xd9,
			0x7f, 0x9f, 0x68, 0xad, 0xb1, 0x6e, 0x5d, 0x7d,
			0x21, 0xe5, 0x69, 0xd2, 0x80, 0xed, 0x77, 0x5c,
			0xeb, 0xde, 0x3f, 0x40, 0x93, 0xc5, 0x38, 0x81,
			0x00
		},
		.len_bits = 73 << 3,
	},
	.ciphertext = {
		.data = {
			0x53, 0x92, 0x9F, 0x88, 0x32, 0xA1, 0x6D, 0x66,
			0x00, 0x32, 0x29, 0xF9, 0x14, 0x75, 0x6D, 0xB3,
			0xEB, 0x64, 0x25, 0x09, 0xE1, 0x80, 0x31, 0x8C,
			0xF8, 0x47, 0x64, 0xAA, 0x07, 0x8E, 0x06, 0xBF,
			0x05, 0xD7, 0x43, 0xEE, 0xFF, 0x11, 0x33, 0x4A,
			0x82, 0xCF, 0x88, 0x6F, 0x33, 0xB2, 0xB5, 0x67,
			0x50, 0x0A, 0x74, 0x2D, 0xE4, 0x56, 0x40, 0x31,
			0xEE, 0xB3, 0x6C, 0x6E, 0x6A, 0x7B, 0x20, 0xBA,
			0x4E, 0x44, 0x34, 0xC8, 0x62, 0x21, 0x8C, 0x45,
			0xD7, 0x85, 0x44, 0xF4, 0x7E
		},
		.len_bits = 77 << 3,
	},
	.digest_enc = {
		.data = {
			0x85, 0x44, 0xF4, 0x7E
		},
		.len = 4,
		.offset = 73,
	},
	.validDataLen = {
		.len_bits = 77 << 3,
	},
	.validCipherLen = {
		.len_bits = 77 << 3,
	},
	.validAuthLen = {
		.len_bits = 73 << 3,
	}
};

struct mixed_cipher_auth_test_data auth_snow_cipher_aes_ctr_test_case_1 = {
	.auth_algo = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	.auth_key = {
		.data = {
			0xC7, 0x36, 0xC6, 0xAA, 0xB2, 0x2B, 0xFF, 0xF9,
			0x1E, 0x26, 0x98, 0xD2, 0xE2, 0x2A, 0xD5, 0x7E
		},
		.len = 16,
	},
	.auth_iv = {
		.data = {
			0x14, 0x79, 0x3E, 0x41, 0x03, 0x97, 0xE8, 0xFD,
			0x94, 0x79, 0x3E, 0x41, 0x03, 0x97, 0x68, 0xFD
		},
		.len = 16,
	},
	.auth = {
		.len_bits = 48 << 3,
		.offset_bits = 0,
	},
	.cipher_algo = RTE_CRYPTO_CIPHER_AES_CTR,
	.cipher_key = {
		.data = {
			0xC7, 0x36, 0xC6, 0xAA, 0xB2, 0x2B, 0xFF, 0xF9,
			0x1E, 0x26, 0x98, 0xD2, 0xE2, 0x2A, 0xD5, 0x7E
		},
		.len = 16,
	},
	.cipher_iv = {
		.data = {
			0x14, 0x79, 0x3E, 0x41, 0x03, 0x97, 0xE8, 0xFD,
			0x94, 0x79, 0x3E, 0x41, 0x03, 0x97, 0x68, 0xFD
		},
		.len = 16,
	},
	.cipher = {
		.len_bits = 52 << 3,
		.offset_bits = 0,
	},
	.plaintext = {
		.data = {
			0xD0, 0xA7, 0xD4, 0x63, 0xDF, 0x9F, 0xB2, 0xB2,
			0x78, 0x83, 0x3F, 0xA0, 0x2E, 0x23, 0x5A, 0xA1,
			0x72, 0xBD, 0x97, 0x0C, 0x14, 0x73, 0xE1, 0x29,
			0x07, 0xFB, 0x64, 0x8B, 0x65, 0x99, 0xAA, 0xA0,
			0xB2, 0x4A, 0x03, 0x86, 0x65, 0x42, 0x2B, 0x20,
			0xA4, 0x99, 0x27, 0x6A, 0x50, 0x42, 0x70, 0x09
		},
		.len_bits = 48 << 3,
	},
	.ciphertext = {
		.data = {
			0x91, 0x96, 0x28, 0xB4, 0x89, 0x74, 0xF6, 0x5E,
			0x98, 0x58, 0xA1, 0xD3, 0x0E, 0xE3, 0xFC, 0x39,
			0xDB, 0x36, 0xE4, 0x97, 0x74, 0x5B, 0x5E, 0xD4,
			0x1B, 0x8A, 0xC5, 0x9D, 0xDF, 0x96, 0x97, 0x5F,
			0x58, 0x4A, 0x75, 0x74, 0x27, 0x07, 0xF3, 0x7F,
			0xCE, 0x2C, 0x4A, 0x6C, 0xE5, 0x19, 0xE7, 0x8B,
			0xF3, 0x21, 0x84, 0x6C
		},
		.len_bits = 52 << 3,
	},
	.digest_enc = {
		.data = {
			0xF3, 0x21, 0x84, 0x6C
		},
		.len = 4,
		.offset = 48,
	},
	.validDataLen = {
		.len_bits = 52 << 3,
	},
	.validCipherLen = {
		.len_bits = 52 << 3,
	},
	.validAuthLen = {
		.len_bits = 48 << 3,
	}
};

struct mixed_cipher_auth_test_data auth_snow_cipher_zuc_test_case_1 = {
	.auth_algo = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	.auth_key = {
		.data = {
			0xC7, 0x36, 0xC6, 0xAA, 0xB2, 0x2B, 0xFF, 0xF9,
			0x1E, 0x26, 0x98, 0xD2, 0xE2, 0x2A, 0xD5, 0x7E
		},
		.len = 16,
	},
	.auth_iv = {
		.data = {
			0x14, 0x79, 0x3E, 0x41, 0x03, 0x97, 0xE8, 0xFD,
			0x94, 0x79, 0x3E, 0x41, 0x03, 0x97, 0x68, 0xFD
		},
		.len = 16,
	},
	.auth = {
		.len_bits = 48 << 3,
		.offset_bits = 0,
	},
	.cipher_algo = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	.cipher_key = {
		.data = {
			0xC7, 0x36, 0xC6, 0xAA, 0xB2, 0x2B, 0xFF, 0xF9,
			0x1E, 0x26, 0x98, 0xD2, 0xE2, 0x2A, 0xD5, 0x7E
		},
		.len = 16,
	},
	.cipher_iv = {
		.data = {
			0x14, 0x79, 0x3E, 0x41, 0x03, 0x97, 0xE8, 0xFD,
			0x94, 0x79, 0x3E, 0x41, 0x03, 0x97, 0x68, 0xFD
		},
		.len = 16,
	},
	.cipher = {
		.len_bits = 52 << 3,
		.offset_bits = 0,
	},
	.plaintext = {
		.data = {
			0xD0, 0xA7, 0xD4, 0x63, 0xDF, 0x9F, 0xB2, 0xB2,
			0x78, 0x83, 0x3F, 0xA0, 0x2E, 0x23, 0x5A, 0xA1,
			0x72, 0xBD, 0x97, 0x0C, 0x14, 0x73, 0xE1, 0x29,
			0x07, 0xFB, 0x64, 0x8B, 0x65, 0x99, 0xAA, 0xA0,
			0xB2, 0x4A, 0x03, 0x86, 0x65, 0x42, 0x2B, 0x20,
			0xA4, 0x99, 0x27, 0x6A, 0x50, 0x42, 0x70, 0x09
		},
		.len_bits = 48 << 3,
	},
	.ciphertext = {
		.data = {
			0x52, 0x11, 0xCD, 0xFF, 0xF8, 0x88, 0x61, 0x1E,
			0xF5, 0xD2, 0x8E, 0xEB, 0x2A, 0x49, 0x18, 0x1F,
			0xF4, 0xDA, 0x8B, 0x19, 0x60, 0x0B, 0x92, 0x9E,
			0x79, 0x2A, 0x5B, 0x0B, 0x7E, 0xC6, 0x22, 0x36,
			0x74, 0xA4, 0x6C, 0xBC, 0xF5, 0x25, 0x69, 0xAE,
			0xDA, 0x04, 0xB9, 0xAF, 0x16, 0x42, 0x0F, 0xCB,
			0x3E, 0xC9, 0x49, 0xE9
		},
		.len_bits = 52 << 3,
	},
	.digest_enc = {
		.data = {
			0x3E, 0xC9, 0x49, 0xE9
		},
		.len = 4,
		.offset = 48,
	},
	.validDataLen = {
		.len_bits = 52 << 3,
	},
	.validCipherLen = {
		.len_bits = 52 << 3,
	},
	.validAuthLen = {
		.len_bits = 48 << 3,
	}
};

struct mixed_cipher_auth_test_data auth_aes_cmac_cipher_zuc_test_case_1 = {
	.auth_algo = RTE_CRYPTO_AUTH_AES_CMAC,
	.auth_key = {
		.data = {
			0x2B, 0x7E, 0x15, 0x16, 0x28, 0xAE, 0xD2, 0xA6,
			0xAB, 0xF7, 0x15, 0x88, 0x09, 0xCF, 0x4F, 0x3C
		},
		.len = 16,
	},
	.auth_iv = {
		.data = {
		},
		.len = 0,
	},
	.auth = {
		.len_bits = 512 << 3,
		.offset_bits = 0,
	},
	.cipher_algo = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	.cipher_key = {
		.data = {
			0x2B, 0x7E, 0x15, 0x16, 0x28, 0xAE, 0xD2, 0xA6,
			0xAB, 0xF7, 0x15, 0x88, 0x09, 0xCF, 0x4F, 0x3C
		},
		.len = 16,
	},
	.cipher_iv = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
		},
		.len = 16,
	},
	.cipher = {
		.len_bits = 516 << 3,
		.offset_bits = 0,
	},
	.plaintext = {
		.data = {
			0x57, 0x68, 0x61, 0x74, 0x20, 0x61, 0x20, 0x6C,
			0x6F, 0x75, 0x73, 0x79, 0x20, 0x65, 0x61, 0x72,
			0x74, 0x68, 0x21, 0x20, 0x48, 0x65, 0x20, 0x77,
			0x6F, 0x6E, 0x64, 0x65, 0x72, 0x65, 0x64, 0x20,
			0x68, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E, 0x79,
			0x20, 0x70, 0x65, 0x6F, 0x70, 0x6C, 0x65, 0x20,
			0x77, 0x65, 0x72, 0x65, 0x20, 0x64, 0x65, 0x73,
			0x74, 0x69, 0x74, 0x75, 0x74, 0x65, 0x20, 0x74,
			0x68, 0x61, 0x74, 0x20, 0x73, 0x61, 0x6D, 0x65,
			0x20, 0x6E, 0x69, 0x67, 0x68, 0x74, 0x20, 0x65,
			0x76, 0x65, 0x6E, 0x20, 0x69, 0x6E, 0x20, 0x68,
			0x69, 0x73, 0x20, 0x6F, 0x77, 0x6E, 0x20, 0x70,
			0x72, 0x6F, 0x73, 0x70, 0x65, 0x72, 0x6F, 0x75,
			0x73, 0x20, 0x63, 0x6F, 0x75, 0x6E, 0x74, 0x72,
			0x79, 0x2C, 0x20, 0x68, 0x6F, 0x77, 0x20, 0x6D,
			0x61, 0x6E, 0x79, 0x20, 0x68, 0x6F, 0x6D, 0x65,
			0x73, 0x20, 0x77, 0x65, 0x72, 0x65, 0x20, 0x73,
			0x68, 0x61, 0x6E, 0x74, 0x69, 0x65, 0x73, 0x2C,
			0x20, 0x68, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E,
			0x79, 0x20, 0x68, 0x75, 0x73, 0x62, 0x61, 0x6E,
			0x64, 0x73, 0x20, 0x77, 0x65, 0x72, 0x65, 0x20,
			0x64, 0x72, 0x75, 0x6E, 0x6B, 0x20, 0x61, 0x6E,
			0x64, 0x20, 0x77, 0x69, 0x76, 0x65, 0x73, 0x20,
			0x73, 0x6F, 0x63, 0x6B, 0x65, 0x64, 0x2C, 0x20,
			0x61, 0x6E, 0x64, 0x20, 0x68, 0x6F, 0x77, 0x20,
			0x6D, 0x61, 0x6E, 0x79, 0x20, 0x63, 0x68, 0x69,
			0x6C, 0x64, 0x72, 0x65, 0x6E, 0x20, 0x77, 0x65,
			0x72, 0x65, 0x20, 0x62, 0x75, 0x6C, 0x6C, 0x69,
			0x65, 0x64, 0x2C, 0x20, 0x61, 0x62, 0x75, 0x73,
			0x65, 0x64, 0x2C, 0x20, 0x6F, 0x72, 0x20, 0x61,
			0x62, 0x61, 0x6E, 0x64, 0x6F, 0x6E, 0x65, 0x64,
			0x2E, 0x20, 0x48, 0x6F, 0x77, 0x20, 0x6D, 0x61,
			0x6E, 0x79, 0x20, 0x66, 0x61, 0x6D, 0x69, 0x6C,
			0x69, 0x65, 0x73, 0x20, 0x68, 0x75, 0x6E, 0x67,
			0x65, 0x72, 0x65, 0x64, 0x20, 0x66, 0x6F, 0x72,
			0x20, 0x66, 0x6F, 0x6F, 0x64, 0x20, 0x74, 0x68,
			0x65, 0x79, 0x20, 0x63, 0x6F, 0x75, 0x6C, 0x64,
			0x20, 0x6E, 0x6F, 0x74, 0x20, 0x61, 0x66, 0x66,
			0x6F, 0x72, 0x64, 0x20, 0x74, 0x6F, 0x20, 0x62,
			0x75, 0x79, 0x3F, 0x20, 0x48, 0x6F, 0x77, 0x20,
			0x6D, 0x61, 0x6E, 0x79, 0x20, 0x68, 0x65, 0x61,
			0x72, 0x74, 0x73, 0x20, 0x77, 0x65, 0x72, 0x65,
			0x20, 0x62, 0x72, 0x6F, 0x6B, 0x65, 0x6E, 0x3F,
			0x20, 0x48, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E,
			0x79, 0x20, 0x73, 0x75, 0x69, 0x63, 0x69, 0x64,
			0x65, 0x73, 0x20, 0x77, 0x6F, 0x75, 0x6C, 0x64,
			0x20, 0x74, 0x61, 0x6B, 0x65, 0x20, 0x70, 0x6C,
			0x61, 0x63, 0x65, 0x20, 0x74, 0x68, 0x61, 0x74,
			0x20, 0x73, 0x61, 0x6D, 0x65, 0x20, 0x6E, 0x69,
			0x67, 0x68, 0x74, 0x2C, 0x20, 0x68, 0x6F, 0x77,
			0x20, 0x6D, 0x61, 0x6E, 0x79, 0x20, 0x70, 0x65,
			0x6F, 0x70, 0x6C, 0x65, 0x20, 0x77, 0x6F, 0x75,
			0x6C, 0x64, 0x20, 0x67, 0x6F, 0x20, 0x69, 0x6E,
			0x73, 0x61, 0x6E, 0x65, 0x3F, 0x20, 0x48, 0x6F,
			0x77, 0x20, 0x6D, 0x61, 0x6E, 0x79, 0x20, 0x63,
			0x6F, 0x63, 0x6B, 0x72, 0x6F, 0x61, 0x63, 0x68,
			0x65, 0x73, 0x20, 0x61, 0x6E, 0x64, 0x20, 0x6C,
			0x61, 0x6E, 0x64, 0x6C, 0x6F, 0x72, 0x64, 0x73,
			0x20, 0x77, 0x6F, 0x75, 0x6C, 0x64, 0x20, 0x74,
			0x72, 0x69, 0x75, 0x6D, 0x70, 0x68, 0x3F, 0x20,
			0x48, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E, 0x79,
			0x20, 0x77, 0x69, 0x6E, 0x6E, 0x65, 0x72, 0x73,
			0x20, 0x77, 0x65, 0x72, 0x65, 0x20, 0x6C, 0x6F,
			0x73, 0x65, 0x72, 0x73, 0x2C, 0x20, 0x73, 0x75
		},
		.len_bits = 512 << 3,
	},
	.ciphertext = {
		.data = {
			0x3C, 0x89, 0x1C, 0xE5, 0xB7, 0xDE, 0x61, 0x4D,
			0x05, 0x37, 0x3F, 0x40, 0xC9, 0xCF, 0x10, 0x07,
			0x7F, 0x18, 0xC5, 0x96, 0x21, 0xA9, 0xCF, 0xF5,
			0xBB, 0x9C, 0x22, 0x72, 0x00, 0xBE, 0xAC, 0x4B,
			0x55, 0x02, 0x19, 0x2B, 0x37, 0x64, 0x15, 0x6B,
			0x54, 0x74, 0xAE, 0x0F, 0xE7, 0x68, 0xB3, 0x92,
			0x17, 0x26, 0x75, 0xEE, 0x0B, 0xE9, 0x46, 0x3C,
			0x6E, 0x76, 0x52, 0x14, 0x2B, 0xD0, 0xB6, 0xD0,
			0x09, 0x07, 0x17, 0x12, 0x58, 0x61, 0xE8, 0x2A,
			0x7C, 0x55, 0x67, 0x66, 0x49, 0xD1, 0x4E, 0x2F,
			0x06, 0x96, 0x3A, 0xF7, 0x05, 0xE3, 0x65, 0x47,
			0x7C, 0xBB, 0x66, 0x25, 0xC4, 0x73, 0xB3, 0x7B,
			0x3D, 0x1D, 0x59, 0x54, 0x4E, 0x38, 0x9C, 0x4D,
			0x10, 0x4B, 0x49, 0xA4, 0x92, 0xC7, 0xD7, 0x17,
			0x6F, 0xC0, 0xEE, 0x8D, 0xBE, 0xA5, 0xE3, 0xB9,
			0xBA, 0x5E, 0x88, 0x36, 0x06, 0x19, 0xB7, 0x86,
			0x66, 0x19, 0x90, 0xC4, 0xAE, 0xB3, 0xFE, 0xA7,
			0xCF, 0x2A, 0xD8, 0x6C, 0x0E, 0xD5, 0x24, 0x2A,
			0x92, 0x93, 0xB9, 0x12, 0xCB, 0x50, 0x0A, 0x22,
			0xB0, 0x09, 0x06, 0x17, 0x85, 0xC9, 0x03, 0x70,
			0x18, 0xF2, 0xD5, 0x6A, 0x66, 0xC2, 0xB6, 0xC6,
			0xA5, 0xA3, 0x24, 0xEC, 0xB9, 0x07, 0xD5, 0x8A,
			0xA0, 0x44, 0x54, 0xD7, 0x21, 0x9F, 0x02, 0x83,
			0x78, 0x7B, 0x78, 0x9C, 0x97, 0x2A, 0x36, 0x51,
			0xAF, 0xE1, 0x79, 0x81, 0x07, 0x53, 0xE4, 0xA0,
			0xC7, 0xCF, 0x10, 0x7C, 0xB2, 0xE6, 0xA1, 0xFD,
			0x81, 0x0B, 0x96, 0x50, 0x5D, 0xFE, 0xB3, 0xC6,
			0x75, 0x00, 0x0C, 0x56, 0x83, 0x9B, 0x7B, 0xF4,
			0xE0, 0x3A, 0xC0, 0xE1, 0xA9, 0xEC, 0xAC, 0x47,
			0x24, 0xF5, 0x12, 0x1B, 0xD0, 0x28, 0x32, 0xE2,
			0x3B, 0x42, 0xC1, 0x5B, 0x98, 0x98, 0x78, 0x2D,
			0xC1, 0x69, 0x05, 0x37, 0x24, 0xF0, 0x73, 0xBA,
			0xBE, 0x57, 0xAC, 0x40, 0x9A, 0x91, 0x42, 0x49,
			0x31, 0x0F, 0xED, 0x45, 0xA8, 0x25, 0xFF, 0x1B,
			0xF4, 0x2F, 0x61, 0x7A, 0xB0, 0x60, 0xC6, 0x5E,
			0x0E, 0xF6, 0x96, 0x35, 0x90, 0xAF, 0x3B, 0x9D,
			0x4D, 0x6C, 0xE7, 0xF2, 0x4F, 0xC0, 0xBA, 0x57,
			0x92, 0x18, 0xB7, 0xF5, 0x1D, 0x06, 0x81, 0xF6,
			0xE3, 0xF4, 0x66, 0x8C, 0x33, 0x74, 0xBE, 0x64,
			0x8C, 0x18, 0xED, 0x7F, 0x68, 0x2A, 0xE4, 0xAF,
			0xF1, 0x02, 0x07, 0x51, 0x22, 0x96, 0xC8, 0x9E,
			0x23, 0x7F, 0x6A, 0xD7, 0x80, 0x0F, 0x2D, 0xFC,
			0xCC, 0xD0, 0x95, 0x86, 0x00, 0x2A, 0x77, 0xDD,
			0xA2, 0x60, 0x1E, 0x0F, 0x8E, 0x42, 0x44, 0x37,
			0x7E, 0x33, 0xC4, 0xE0, 0x04, 0x53, 0xF6, 0x3F,
			0xDD, 0x1D, 0x5E, 0x24, 0xDA, 0xAE, 0xEF, 0x06,
			0x06, 0x05, 0x13, 0x3A, 0x1E, 0xFF, 0xAD, 0xAD,
			0xEE, 0x0F, 0x6F, 0x05, 0xA5, 0xFB, 0x3B, 0xC3,
			0xDB, 0xA0, 0x20, 0xC1, 0x65, 0x8B, 0x39, 0xAB,
			0xC9, 0xEC, 0xA8, 0x31, 0x85, 0x6C, 0xD2, 0xE4,
			0x76, 0x77, 0x76, 0xD5, 0x81, 0x01, 0x73, 0x36,
			0x08, 0x8C, 0xC3, 0xD4, 0x70, 0x7A, 0xA3, 0xDF,
			0xAD, 0x3A, 0x00, 0x46, 0x88, 0x65, 0x10, 0xBE,
			0xD8, 0x1C, 0x19, 0x98, 0xE9, 0x29, 0xDD, 0x58,
			0x46, 0x31, 0xEB, 0x3D, 0xD0, 0x12, 0x02, 0x83,
			0x15, 0xDD, 0x70, 0x27, 0x0D, 0xB5, 0xBB, 0x0C,
			0xE3, 0xF1, 0x02, 0xF2, 0xD7, 0x1D, 0x17, 0x6D,
			0xDF, 0x2A, 0x42, 0x1F, 0x01, 0x5C, 0x68, 0xB1,
			0x64, 0x74, 0xCE, 0x74, 0xB1, 0x3C, 0x2F, 0x43,
			0x5F, 0xB7, 0x7E, 0x3E, 0x6F, 0xE3, 0xDC, 0x03,
			0xD9, 0x0C, 0xDD, 0x42, 0x65, 0x7F, 0xEA, 0x69,
			0x6F, 0xDB, 0xD7, 0xFB, 0xFF, 0x4D, 0xB4, 0x48,
			0xFE, 0x0F, 0x59, 0x24, 0x8F, 0x13, 0xA8, 0x60,
			0xF7, 0x13, 0xE5, 0xB1, 0x8D, 0xB7, 0x70, 0xEE,
			0x82, 0x8F, 0xCF, 0x7E
		},
		.len_bits = 516 << 3,
	},
	.digest_enc = {
		.data = {
			0x82, 0x8F, 0xCF, 0x7E
		},
		.len = 4,
		.offset = 512,
	},
	.validDataLen = {
		.len_bits = 516 << 3,
	},
	.validCipherLen = {
		.len_bits = 516 << 3,
	},
	.validAuthLen = {
		.len_bits = 512 << 3,
	}
};

struct mixed_cipher_auth_test_data auth_null_cipher_snow_test_case_1 = {
	.auth_algo = RTE_CRYPTO_AUTH_NULL,
	.auth_key = {
		.data = { 0x0 },
		.len = 0,
	},
	.auth_iv = {
		.data = { 0x0 },
		.len = 0,
	},
	.auth = {
		.len_bits = 0,
		.offset_bits = 0,
	},
	.cipher_algo = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	.cipher_key = {
		.data = {
			0xC7, 0x36, 0xC6, 0xAA, 0xB2, 0x2B, 0xFF, 0xF9,
			0x1E, 0x26, 0x98, 0xD2, 0xE2, 0x2A, 0xD5, 0x7E
		},
		.len = 16,
	},
	.cipher_iv = {
		.data = {
			0x14, 0x79, 0x3E, 0x41, 0x03, 0x97, 0xE8, 0xFD,
			0x94, 0x79, 0x3E, 0x41, 0x03, 0x97, 0x68, 0xFD
		},
		.len = 16,
	},
	.cipher = {
		.len_bits = 48 << 3,
		.offset_bits = 0,
	},
	.plaintext = {
		.data = {
			0xD0, 0xA7, 0xD4, 0x63, 0xDF, 0x9F, 0xB2, 0xB2,
			0x78, 0x83, 0x3F, 0xA0, 0x2E, 0x23, 0x5A, 0xA1,
			0x72, 0xBD, 0x97, 0x0C, 0x14, 0x73, 0xE1, 0x29,
			0x07, 0xFB, 0x64, 0x8B, 0x65, 0x99, 0xAA, 0xA0,
			0xB2, 0x4A, 0x03, 0x86, 0x65, 0x42, 0x2B, 0x20,
			0xA4, 0x99, 0x27, 0x6A,
		},
		.len_bits = 44 << 3,
	},
	.ciphertext = {
		.data = {
			0x95, 0x2E, 0x5A, 0xE1, 0x50, 0xB8, 0x59, 0x2A,
			0x9B, 0xA0, 0x38, 0xA9, 0x8E, 0x2F, 0xED, 0xAB,
			0xFD, 0xC8, 0x3B, 0x47, 0x46, 0x0B, 0x50, 0x16,
			0xEC, 0x88, 0x45, 0xB6, 0x05, 0xC7, 0x54, 0xF8,
			0xBD, 0x91, 0xAA, 0xB6, 0xA4, 0xDC, 0x64, 0xB4,
			0xCB, 0xEB, 0x97, 0x06, 0x1C, 0xB5, 0x72, 0x34
		},
		.len_bits = 48 << 3,
	},
	.digest_enc = {
		.data = {
			0x1C, 0xB5, 0x72, 0x34
		},
		.len = 4,
		.offset = 44,
	},
	.validDataLen = {
		.len_bits = 48 << 3,
	},
	.validCipherLen = {
		.len_bits = 48 << 3,
	},
	.validAuthLen = {
		.len_bits = 44 << 3,
	}
};

struct mixed_cipher_auth_test_data auth_null_cipher_zuc_test_case_1 = {
	.auth_algo = RTE_CRYPTO_AUTH_NULL,
	.auth_key = {
		.data = { 0x0 },
		.len = 0,
	},
	.auth_iv = {
		.data = { 0x0 },
		.len = 0,
	},
	.auth = {
		.len_bits = 0,
		.offset_bits = 0,
	},
	.cipher_algo = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	.cipher_key = {
		.data = {
			0xC7, 0x36, 0xC6, 0xAA, 0xB2, 0x2B, 0xFF, 0xF9,
			0x1E, 0x26, 0x98, 0xD2, 0xE2, 0x2A, 0xD5, 0x7E
		},
		.len = 16,
	},
	.cipher_iv = {
		.data = {
			0x14, 0x79, 0x3E, 0x41, 0x03, 0x97, 0xE8, 0xFD,
			0x94, 0x79, 0x3E, 0x41, 0x03, 0x97, 0x68, 0xFD
		},
		.len = 16,
	},
	.cipher = {
		.len_bits = 52 << 3,
		.offset_bits = 0,
	},
	.plaintext = {
		.data = {
			0xD0, 0xA7, 0xD4, 0x63, 0xDF, 0x9F, 0xB2, 0xB2,
			0x78, 0x83, 0x3F, 0xA0, 0x2E, 0x23, 0x5A, 0xA1,
			0x72, 0xBD, 0x97, 0x0C, 0x14, 0x73, 0xE1, 0x29,
			0x07, 0xFB, 0x64, 0x8B, 0x65, 0x99, 0xAA, 0xA0,
			0xB2, 0x4A, 0x03, 0x86, 0x65, 0x42, 0x2B, 0x20,
			0xA4, 0x99, 0x27, 0x6A, 0x50, 0x42, 0x70, 0x09
		},
		.len_bits = 48 << 3,
	},
	.ciphertext = {
		.data = {
			0x52, 0x11, 0xCD, 0xFF, 0xF8, 0x88, 0x61, 0x1E,
			0xF5, 0xD2, 0x8E, 0xEB, 0x2A, 0x49, 0x18, 0x1F,
			0xF4, 0xDA, 0x8B, 0x19, 0x60, 0x0B, 0x92, 0x9E,
			0x79, 0x2A, 0x5B, 0x0B, 0x7E, 0xC6, 0x22, 0x36,
			0x74, 0xA4, 0x6C, 0xBC, 0xF5, 0x25, 0x69, 0xAE,
			0xDA, 0x04, 0xB9, 0xAF, 0x16, 0x42, 0x0F, 0xCB,
			0x06, 0x7C, 0x1D, 0x29
		},
		.len_bits = 52 << 3,
	},
	.digest_enc = {
		.data = {
			0x06, 0x7C, 0x1D, 0x29
		},
		.len = 4,
		.offset = 48,
	},
	.validDataLen = {
		.len_bits = 52 << 3,
	},
	.validCipherLen = {
		.len_bits = 52 << 3,
	},
	.validAuthLen = {
		.len_bits = 48 << 3,
	}
};

struct mixed_cipher_auth_test_data auth_snow_cipher_null_test_case_1 = {
	.auth_algo = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	.auth_key = {
		.data = {
			0xC7, 0x36, 0xC6, 0xAA, 0xB2, 0x2B, 0xFF, 0xF9,
			0x1E, 0x26, 0x98, 0xD2, 0xE2, 0x2A, 0xD5, 0x7E
		},
		.len = 16,
	},
	.auth_iv = {
		.data = {
			0x14, 0x79, 0x3E, 0x41, 0x03, 0x97, 0xE8, 0xFD,
			0x94, 0x79, 0x3E, 0x41, 0x03, 0x97, 0x68, 0xFD
		},
		.len = 16,
	},
	.auth = {
		.len_bits = 48 << 3,
		.offset_bits = 0,
	},
	.cipher_algo = RTE_CRYPTO_CIPHER_NULL,
	.cipher_key = {
		.data = { 0x0 },
		.len = 0,
	},
	.cipher_iv = {
		.data = { 0x0 },
		.len = 0,
	},
	.cipher = {
		.len_bits = 0,
		.offset_bits = 0,
	},
	.plaintext = {
		.data = {
			0xD0, 0xA7, 0xD4, 0x63, 0xDF, 0x9F, 0xB2, 0xB2,
			0x78, 0x83, 0x3F, 0xA0, 0x2E, 0x23, 0x5A, 0xA1,
			0x72, 0xBD, 0x97, 0x0C, 0x14, 0x73, 0xE1, 0x29,
			0x07, 0xFB, 0x64, 0x8B, 0x65, 0x99, 0xAA, 0xA0,
			0xB2, 0x4A, 0x03, 0x86, 0x65, 0x42, 0x2B, 0x20,
			0xA4, 0x99, 0x27, 0x6A, 0x50, 0x42, 0x70, 0x09
		},
		.len_bits = 48 << 3,
	},
	.ciphertext = {
		.data = {
			0xD0, 0xA7, 0xD4, 0x63, 0xDF, 0x9F, 0xB2, 0xB2,
			0x78, 0x83, 0x3F, 0xA0, 0x2E, 0x23, 0x5A, 0xA1,
			0x72, 0xBD, 0x97, 0x0C, 0x14, 0x73, 0xE1, 0x29,
			0x07, 0xFB, 0x64, 0x8B, 0x65, 0x99, 0xAA, 0xA0,
			0xB2, 0x4A, 0x03, 0x86, 0x65, 0x42, 0x2B, 0x20,
			0xA4, 0x99, 0x27, 0x6A, 0x50, 0x42, 0x70, 0x09,
			0x38, 0xB5, 0x54, 0xC0
		},
		.len_bits = 52 << 3,
	},
	.digest_enc = {
		.data = {
			0x38, 0xB5, 0x54, 0xC0
		},
		.len = 4,
		.offset = 48,
	},
	.validDataLen = {
		.len_bits = 52 << 3,
	},
	.validCipherLen = {
		.len_bits = 52 << 3,
	},
	.validAuthLen = {
		.len_bits = 48 << 3,
	}
};

struct mixed_cipher_auth_test_data auth_zuc_cipher_null_test_case_1 = {
	.auth_algo = RTE_CRYPTO_AUTH_ZUC_EIA3,
	.auth_key = {
		.data = {
			0xc9, 0xe6, 0xce, 0xc4, 0x60, 0x7c, 0x72, 0xdb,
			0x00, 0x0a, 0xef, 0xa8, 0x83, 0x85, 0xab, 0x0a
		},
		.len = 16,
	},
	.auth_iv = {
		.data = {
			0xa9, 0x40, 0x59, 0xda, 0x50, 0x00, 0x00, 0x00,
			0x29, 0x40, 0x59, 0xda, 0x50, 0x00, 0x80, 0x00
		},
		.len = 16,
	},
	.auth = {
		.len_bits = 73 << 3,
		.offset_bits = 0,
	},
	.cipher_algo = RTE_CRYPTO_CIPHER_NULL,
	.cipher_key = {
		.data = { 0x0 },
		.len = 0,
	},
	.cipher_iv = {
		.data = { 0x0 },
		.len = 0,
	},
	.cipher = {
		.len_bits = 0,
		.offset_bits = 0,
	},
	.plaintext = {
		.data = {
			0x98, 0x3b, 0x41, 0xd4, 0x7d, 0x78, 0x0c, 0x9e,
			0x1a, 0xd1, 0x1d, 0x7e, 0xb7, 0x03, 0x91, 0xb1,
			0xde, 0x0b, 0x35, 0xda, 0x2d, 0xc6, 0x2f, 0x83,
			0xe7, 0xb7, 0x8d, 0x63, 0x06, 0xca, 0x0e, 0xa0,
			0x7e, 0x94, 0x1b, 0x7b, 0xe9, 0x13, 0x48, 0xf9,
			0xfc, 0xb1, 0x70, 0xe2, 0x21, 0x7f, 0xec, 0xd9,
			0x7f, 0x9f, 0x68, 0xad, 0xb1, 0x6e, 0x5d, 0x7d,
			0x21, 0xe5, 0x69, 0xd2, 0x80, 0xed, 0x77, 0x5c,
			0xeb, 0xde, 0x3f, 0x40, 0x93, 0xc5, 0x38, 0x81,
			0x00
		},
		.len_bits = 73 << 3,
	},
	.ciphertext = {
		.data = {
			0x98, 0x3b, 0x41, 0xd4, 0x7d, 0x78, 0x0c, 0x9e,
			0x1a, 0xd1, 0x1d, 0x7e, 0xb7, 0x03, 0x91, 0xb1,
			0xde, 0x0b, 0x35, 0xda, 0x2d, 0xc6, 0x2f, 0x83,
			0xe7, 0xb7, 0x8d, 0x63, 0x06, 0xca, 0x0e, 0xa0,
			0x7e, 0x94, 0x1b, 0x7b, 0xe9, 0x13, 0x48, 0xf9,
			0xfc, 0xb1, 0x70, 0xe2, 0x21, 0x7f, 0xec, 0xd9,
			0x7f, 0x9f, 0x68, 0xad, 0xb1, 0x6e, 0x5d, 0x7d,
			0x21, 0xe5, 0x69, 0xd2, 0x80, 0xed, 0x77, 0x5c,
			0xeb, 0xde, 0x3f, 0x40, 0x93, 0xc5, 0x38, 0x81,
			0x00, 0x24, 0xa8, 0x42, 0xb3
		},
		.len_bits = 77 << 3,
	},
	.digest_enc = {
		.data = {
			0x24, 0xa8, 0x42, 0xb3
		},
		.len = 4,
		.offset = 73,
	},
	.validDataLen = {
		.len_bits = 77 << 3,
	},
	.validCipherLen = {
		.len_bits = 77 << 3,
	},
	.validAuthLen = {
		.len_bits = 73 << 3,
	}
};

struct mixed_cipher_auth_test_data auth_null_cipher_aes_ctr_test_case_1 = {
	.auth_algo = RTE_CRYPTO_AUTH_NULL,
	.auth_key = {
		.data = { 0x0 },
		.len = 0,
	},
	.auth_iv = {
		.data = { 0x0 },
		.len = 0,
	},
	.auth = {
		.len_bits = 0,
		.offset_bits = 0,
	},
	.cipher_algo = RTE_CRYPTO_CIPHER_AES_CTR,
	.cipher_key = {
		.data = {
			0xC7, 0x36, 0xC6, 0xAA, 0xB2, 0x2B, 0xFF, 0xF9,
			0x1E, 0x26, 0x98, 0xD2, 0xE2, 0x2A, 0xD5, 0x7E
		},
		.len = 16,
	},
	.cipher_iv = {
		.data = {
			0x14, 0x79, 0x3E, 0x41, 0x03, 0x97, 0xE8, 0xFD,
			0x94, 0x79, 0x3E, 0x41, 0x03, 0x97, 0x68, 0xFD
		},
		.len = 16,
	},
	.cipher = {
		.len_bits = 52 << 3,
		.offset_bits = 0,
	},
	.plaintext = {
		.data = {
			0xD0, 0xA7, 0xD4, 0x63, 0xDF, 0x9F, 0xB2, 0xB2,
			0x78, 0x83, 0x3F, 0xA0, 0x2E, 0x23, 0x5A, 0xA1,
			0x72, 0xBD, 0x97, 0x0C, 0x14, 0x73, 0xE1, 0x29,
			0x07, 0xFB, 0x64, 0x8B, 0x65, 0x99, 0xAA, 0xA0,
			0xB2, 0x4A, 0x03, 0x86, 0x65, 0x42, 0x2B, 0x20,
			0xA4, 0x99, 0x27, 0x6A, 0x50, 0x42, 0x70, 0x09
		},
		.len_bits = 48 << 3,
	},
	.ciphertext = {
		.data = {
			0x91, 0x96, 0x28, 0xB4, 0x89, 0x74, 0xF6, 0x5E,
			0x98, 0x58, 0xA1, 0xD3, 0x0E, 0xE3, 0xFC, 0x39,
			0xDB, 0x36, 0xE4, 0x97, 0x74, 0x5B, 0x5E, 0xD4,
			0x1B, 0x8A, 0xC5, 0x9D, 0xDF, 0x96, 0x97, 0x5F,
			0x58, 0x4A, 0x75, 0x74, 0x27, 0x07, 0xF3, 0x7F,
			0xCE, 0x2C, 0x4A, 0x6C, 0xE5, 0x19, 0xE7, 0x8B,
			0xCB, 0x94, 0xD0, 0xAC
		},
		.len_bits = 52 << 3,
	},
	.digest_enc = {
		.data = {
			0xCB, 0x94, 0xD0, 0xAC
		},
		.len = 4,
		.offset = 48,
	},
	.validDataLen = {
		.len_bits = 52 << 3,
	},
	.validCipherLen = {
		.len_bits = 52 << 3,
	},
	.validAuthLen = {
		.len_bits = 48 << 3,
	}
};

struct mixed_cipher_auth_test_data auth_aes_cmac_cipher_null_test_case_1 = {
	.auth_algo = RTE_CRYPTO_AUTH_AES_CMAC,
	.auth_key = {
		.data = {
			0x2B, 0x7E, 0x15, 0x16, 0x28, 0xAE, 0xD2, 0xA6,
			0xAB, 0xF7, 0x15, 0x88, 0x09, 0xCF, 0x4F, 0x3C
		},
		.len = 16,
	},
	.auth_iv = {
		.data = {
		},
		.len = 0,
	},
	.auth = {
		.len_bits = 512 << 3,
		.offset_bits = 0,
	},
	.cipher_algo = RTE_CRYPTO_CIPHER_NULL,
	.cipher_key = {
		.data = { 0x0 },
		.len = 0,
	},
	.cipher_iv = {
		.data = { 0x0 },
		.len = 0,
	},
	.cipher = {
		.len_bits = 0,
		.offset_bits = 0,
	},
	.plaintext = {
		.data = {
			0x57, 0x68, 0x61, 0x74, 0x20, 0x61, 0x20, 0x6C,
			0x6F, 0x75, 0x73, 0x79, 0x20, 0x65, 0x61, 0x72,
			0x74, 0x68, 0x21, 0x20, 0x48, 0x65, 0x20, 0x77,
			0x6F, 0x6E, 0x64, 0x65, 0x72, 0x65, 0x64, 0x20,
			0x68, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E, 0x79,
			0x20, 0x70, 0x65, 0x6F, 0x70, 0x6C, 0x65, 0x20,
			0x77, 0x65, 0x72, 0x65, 0x20, 0x64, 0x65, 0x73,
			0x74, 0x69, 0x74, 0x75, 0x74, 0x65, 0x20, 0x74,
			0x68, 0x61, 0x74, 0x20, 0x73, 0x61, 0x6D, 0x65,
			0x20, 0x6E, 0x69, 0x67, 0x68, 0x74, 0x20, 0x65,
			0x76, 0x65, 0x6E, 0x20, 0x69, 0x6E, 0x20, 0x68,
			0x69, 0x73, 0x20, 0x6F, 0x77, 0x6E, 0x20, 0x70,
			0x72, 0x6F, 0x73, 0x70, 0x65, 0x72, 0x6F, 0x75,
			0x73, 0x20, 0x63, 0x6F, 0x75, 0x6E, 0x74, 0x72,
			0x79, 0x2C, 0x20, 0x68, 0x6F, 0x77, 0x20, 0x6D,
			0x61, 0x6E, 0x79, 0x20, 0x68, 0x6F, 0x6D, 0x65,
			0x73, 0x20, 0x77, 0x65, 0x72, 0x65, 0x20, 0x73,
			0x68, 0x61, 0x6E, 0x74, 0x69, 0x65, 0x73, 0x2C,
			0x20, 0x68, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E,
			0x79, 0x20, 0x68, 0x75, 0x73, 0x62, 0x61, 0x6E,
			0x64, 0x73, 0x20, 0x77, 0x65, 0x72, 0x65, 0x20,
			0x64, 0x72, 0x75, 0x6E, 0x6B, 0x20, 0x61, 0x6E,
			0x64, 0x20, 0x77, 0x69, 0x76, 0x65, 0x73, 0x20,
			0x73, 0x6F, 0x63, 0x6B, 0x65, 0x64, 0x2C, 0x20,
			0x61, 0x6E, 0x64, 0x20, 0x68, 0x6F, 0x77, 0x20,
			0x6D, 0x61, 0x6E, 0x79, 0x20, 0x63, 0x68, 0x69,
			0x6C, 0x64, 0x72, 0x65, 0x6E, 0x20, 0x77, 0x65,
			0x72, 0x65, 0x20, 0x62, 0x75, 0x6C, 0x6C, 0x69,
			0x65, 0x64, 0x2C, 0x20, 0x61, 0x62, 0x75, 0x73,
			0x65, 0x64, 0x2C, 0x20, 0x6F, 0x72, 0x20, 0x61,
			0x62, 0x61, 0x6E, 0x64, 0x6F, 0x6E, 0x65, 0x64,
			0x2E, 0x20, 0x48, 0x6F, 0x77, 0x20, 0x6D, 0x61,
			0x6E, 0x79, 0x20, 0x66, 0x61, 0x6D, 0x69, 0x6C,
			0x69, 0x65, 0x73, 0x20, 0x68, 0x75, 0x6E, 0x67,
			0x65, 0x72, 0x65, 0x64, 0x20, 0x66, 0x6F, 0x72,
			0x20, 0x66, 0x6F, 0x6F, 0x64, 0x20, 0x74, 0x68,
			0x65, 0x79, 0x20, 0x63, 0x6F, 0x75, 0x6C, 0x64,
			0x20, 0x6E, 0x6F, 0x74, 0x20, 0x61, 0x66, 0x66,
			0x6F, 0x72, 0x64, 0x20, 0x74, 0x6F, 0x20, 0x62,
			0x75, 0x79, 0x3F, 0x20, 0x48, 0x6F, 0x77, 0x20,
			0x6D, 0x61, 0x6E, 0x79, 0x20, 0x68, 0x65, 0x61,
			0x72, 0x74, 0x73, 0x20, 0x77, 0x65, 0x72, 0x65,
			0x20, 0x62, 0x72, 0x6F, 0x6B, 0x65, 0x6E, 0x3F,
			0x20, 0x48, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E,
			0x79, 0x20, 0x73, 0x75, 0x69, 0x63, 0x69, 0x64,
			0x65, 0x73, 0x20, 0x77, 0x6F, 0x75, 0x6C, 0x64,
			0x20, 0x74, 0x61, 0x6B, 0x65, 0x20, 0x70, 0x6C,
			0x61, 0x63, 0x65, 0x20, 0x74, 0x68, 0x61, 0x74,
			0x20, 0x73, 0x61, 0x6D, 0x65, 0x20, 0x6E, 0x69,
			0x67, 0x68, 0x74, 0x2C, 0x20, 0x68, 0x6F, 0x77,
			0x20, 0x6D, 0x61, 0x6E, 0x79, 0x20, 0x70, 0x65,
			0x6F, 0x70, 0x6C, 0x65, 0x20, 0x77, 0x6F, 0x75,
			0x6C, 0x64, 0x20, 0x67, 0x6F, 0x20, 0x69, 0x6E,
			0x73, 0x61, 0x6E, 0x65, 0x3F, 0x20, 0x48, 0x6F,
			0x77, 0x20, 0x6D, 0x61, 0x6E, 0x79, 0x20, 0x63,
			0x6F, 0x63, 0x6B, 0x72, 0x6F, 0x61, 0x63, 0x68,
			0x65, 0x73, 0x20, 0x61, 0x6E, 0x64, 0x20, 0x6C,
			0x61, 0x6E, 0x64, 0x6C, 0x6F, 0x72, 0x64, 0x73,
			0x20, 0x77, 0x6F, 0x75, 0x6C, 0x64, 0x20, 0x74,
			0x72, 0x69, 0x75, 0x6D, 0x70, 0x68, 0x3F, 0x20,
			0x48, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E, 0x79,
			0x20, 0x77, 0x69, 0x6E, 0x6E, 0x65, 0x72, 0x73,
			0x20, 0x77, 0x65, 0x72, 0x65, 0x20, 0x6C, 0x6F,
			0x73, 0x65, 0x72, 0x73, 0x2C, 0x20, 0x73, 0x75
		},
		.len_bits = 512 << 3,
	},
	.ciphertext = {
		.data = {
			0x57, 0x68, 0x61, 0x74, 0x20, 0x61, 0x20, 0x6C,
			0x6F, 0x75, 0x73, 0x79, 0x20, 0x65, 0x61, 0x72,
			0x74, 0x68, 0x21, 0x20, 0x48, 0x65, 0x20, 0x77,
			0x6F, 0x6E, 0x64, 0x65, 0x72, 0x65, 0x64, 0x20,
			0x68, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E, 0x79,
			0x20, 0x70, 0x65, 0x6F, 0x70, 0x6C, 0x65, 0x20,
			0x77, 0x65, 0x72, 0x65, 0x20, 0x64, 0x65, 0x73,
			0x74, 0x69, 0x74, 0x75, 0x74, 0x65, 0x20, 0x74,
			0x68, 0x61, 0x74, 0x20, 0x73, 0x61, 0x6D, 0x65,
			0x20, 0x6E, 0x69, 0x67, 0x68, 0x74, 0x20, 0x65,
			0x76, 0x65, 0x6E, 0x20, 0x69, 0x6E, 0x20, 0x68,
			0x69, 0x73, 0x20, 0x6F, 0x77, 0x6E, 0x20, 0x70,
			0x72, 0x6F, 0x73, 0x70, 0x65, 0x72, 0x6F, 0x75,
			0x73, 0x20, 0x63, 0x6F, 0x75, 0x6E, 0x74, 0x72,
			0x79, 0x2C, 0x20, 0x68, 0x6F, 0x77, 0x20, 0x6D,
			0x61, 0x6E, 0x79, 0x20, 0x68, 0x6F, 0x6D, 0x65,
			0x73, 0x20, 0x77, 0x65, 0x72, 0x65, 0x20, 0x73,
			0x68, 0x61, 0x6E, 0x74, 0x69, 0x65, 0x73, 0x2C,
			0x20, 0x68, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E,
			0x79, 0x20, 0x68, 0x75, 0x73, 0x62, 0x61, 0x6E,
			0x64, 0x73, 0x20, 0x77, 0x65, 0x72, 0x65, 0x20,
			0x64, 0x72, 0x75, 0x6E, 0x6B, 0x20, 0x61, 0x6E,
			0x64, 0x20, 0x77, 0x69, 0x76, 0x65, 0x73, 0x20,
			0x73, 0x6F, 0x63, 0x6B, 0x65, 0x64, 0x2C, 0x20,
			0x61, 0x6E, 0x64, 0x20, 0x68, 0x6F, 0x77, 0x20,
			0x6D, 0x61, 0x6E, 0x79, 0x20, 0x63, 0x68, 0x69,
			0x6C, 0x64, 0x72, 0x65, 0x6E, 0x20, 0x77, 0x65,
			0x72, 0x65, 0x20, 0x62, 0x75, 0x6C, 0x6C, 0x69,
			0x65, 0x64, 0x2C, 0x20, 0x61, 0x62, 0x75, 0x73,
			0x65, 0x64, 0x2C, 0x20, 0x6F, 0x72, 0x20, 0x61,
			0x62, 0x61, 0x6E, 0x64, 0x6F, 0x6E, 0x65, 0x64,
			0x2E, 0x20, 0x48, 0x6F, 0x77, 0x20, 0x6D, 0x61,
			0x6E, 0x79, 0x20, 0x66, 0x61, 0x6D, 0x69, 0x6C,
			0x69, 0x65, 0x73, 0x20, 0x68, 0x75, 0x6E, 0x67,
			0x65, 0x72, 0x65, 0x64, 0x20, 0x66, 0x6F, 0x72,
			0x20, 0x66, 0x6F, 0x6F, 0x64, 0x20, 0x74, 0x68,
			0x65, 0x79, 0x20, 0x63, 0x6F, 0x75, 0x6C, 0x64,
			0x20, 0x6E, 0x6F, 0x74, 0x20, 0x61, 0x66, 0x66,
			0x6F, 0x72, 0x64, 0x20, 0x74, 0x6F, 0x20, 0x62,
			0x75, 0x79, 0x3F, 0x20, 0x48, 0x6F, 0x77, 0x20,
			0x6D, 0x61, 0x6E, 0x79, 0x20, 0x68, 0x65, 0x61,
			0x72, 0x74, 0x73, 0x20, 0x77, 0x65, 0x72, 0x65,
			0x20, 0x62, 0x72, 0x6F, 0x6B, 0x65, 0x6E, 0x3F,
			0x20, 0x48, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E,
			0x79, 0x20, 0x73, 0x75, 0x69, 0x63, 0x69, 0x64,
			0x65, 0x73, 0x20, 0x77, 0x6F, 0x75, 0x6C, 0x64,
			0x20, 0x74, 0x61, 0x6B, 0x65, 0x20, 0x70, 0x6C,
			0x61, 0x63, 0x65, 0x20, 0x74, 0x68, 0x61, 0x74,
			0x20, 0x73, 0x61, 0x6D, 0x65, 0x20, 0x6E, 0x69,
			0x67, 0x68, 0x74, 0x2C, 0x20, 0x68, 0x6F, 0x77,
			0x20, 0x6D, 0x61, 0x6E, 0x79, 0x20, 0x70, 0x65,
			0x6F, 0x70, 0x6C, 0x65, 0x20, 0x77, 0x6F, 0x75,
			0x6C, 0x64, 0x20, 0x67, 0x6F, 0x20, 0x69, 0x6E,
			0x73, 0x61, 0x6E, 0x65, 0x3F, 0x20, 0x48, 0x6F,
			0x77, 0x20, 0x6D, 0x61, 0x6E, 0x79, 0x20, 0x63,
			0x6F, 0x63, 0x6B, 0x72, 0x6F, 0x61, 0x63, 0x68,
			0x65, 0x73, 0x20, 0x61, 0x6E, 0x64, 0x20, 0x6C,
			0x61, 0x6E, 0x64, 0x6C, 0x6F, 0x72, 0x64, 0x73,
			0x20, 0x77, 0x6F, 0x75, 0x6C, 0x64, 0x20, 0x74,
			0x72, 0x69, 0x75, 0x6D, 0x70, 0x68, 0x3F, 0x20,
			0x48, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E, 0x79,
			0x20, 0x77, 0x69, 0x6E, 0x6E, 0x65, 0x72, 0x73,
			0x20, 0x77, 0x65, 0x72, 0x65, 0x20, 0x6C, 0x6F,
			0x73, 0x65, 0x72, 0x73, 0x2C, 0x20, 0x73, 0x75,
			0x4C, 0x77, 0x87, 0xA0
		},
		.len_bits = 516 << 3,
	},
	.digest_enc = {
		.data = {
			0x4C, 0x77, 0x87, 0xA0
		},
		.len = 4,
		.offset = 512,
	},
	.validDataLen = {
		.len_bits = 516 << 3,
	},
	.validCipherLen = {
		.len_bits = 516 << 3,
	},
	.validAuthLen = {
		.len_bits = 512 << 3,
	}
};

#endif /* TEST_CRYPTODEV_MIXED_TEST_VECTORS_H_ */
