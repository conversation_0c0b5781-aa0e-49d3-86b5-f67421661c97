/* SPDX-License-Identifier: BSD-3-Clause
 * Copyright(c) 2020 Intel Corporation
 */

#ifndef TEST_CRYPTODEV_SECURITY_DOCSIS_TEST_VECTORS_H_
#define TEST_CRYPTODEV_SECURITY_DOCSIS_TEST_VECTORS_H_

/*
 * DOCSIS test data and cases
 * - encrypt direction: CRC-Crypto
 * - decrypt direction: Crypto-CRC
 */

struct docsis_test_data {
	const char test_descr_uplink[128];
	const char test_descr_downlink[128];

	struct {
		uint8_t data[32];
		unsigned int len;
	} key;

	struct {
		alignas(16) uint8_t data[16];
		unsigned int len;
	} iv;

	struct {
		uint8_t data[1024];
		unsigned int len;
		unsigned int cipher_offset;
		unsigned int crc_offset;
		bool no_cipher;
		bool no_crc;
	} plaintext;

	struct {
		uint8_t data[1024];
		unsigned int len;
		unsigned int cipher_offset;
		unsigned int crc_offset;
		bool no_cipher;
		bool no_crc;
	} ciphertext;
};

const struct docsis_test_data docsis_test_case_1 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-128 and CRC Verify (24-byte "
			"frame, Small offset and runt block decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-128 "
			"(24-byte frame, Small offset and runt block encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 24,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x7A, 0xF0,
			/* CRC */
			0x61, 0xF8, 0x63, 0x42
		},
		.len = 24,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	}
};

const struct docsis_test_data docsis_test_case_2 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-128 and CRC Verify (25-byte "
			"frame, Small offset and runt block decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-128 "
			"(25-byte frame, Small offset and runt block encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 25,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x7A, 0xF0, 0xDF,
			/* CRC */
			0xFE, 0x12, 0x99, 0xE5
		},
		.len = 25,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	}
};

const struct docsis_test_data docsis_test_case_3 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-128 and CRC Verify (34-byte "
			"frame, Small offset and full block decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-128 "
			"(34-byte frame, Small offset and full block encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 34,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0xD6, 0xE2, 0x70, 0x5C,
			0xE6, 0x4D, 0xCC, 0x8C, 0x47, 0xB7, 0x09, 0xD6,
			/* CRC */
			0x54, 0x85, 0xF8, 0x32
		},
		.len = 34,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	}
};

const struct docsis_test_data docsis_test_case_4 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-128 and CRC Verify (35-byte "
			"frame, Small offset and uneven decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-128 "
			"(35-byte frame, Small offset and uneven encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 35,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x92, 0x6A, 0xC2, 0xDC,
			0xEE, 0x3B, 0x31, 0xEC, 0x03, 0xDE, 0x95, 0x33,
			0x5E,
			/* CRC */
			0xFE, 0x47, 0x3E, 0x22
		},
		.len = 35,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	}
};

const struct docsis_test_data docsis_test_case_5 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-128 and CRC Verify (82-byte "
			"frame, Small offset and full block decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-128 "
			"(82-byte frame, Small offset and full block encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 82,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x77, 0x74, 0x56, 0x05,
			0xD1, 0x14, 0xA2, 0x8D, 0x2C, 0x9A, 0x11, 0xFC,
			0x7D, 0xB0, 0xE7, 0x18, 0xCE, 0x75, 0x7C, 0x89,
			0x14, 0x56, 0xE2, 0xF2, 0xB7, 0x47, 0x08, 0x27,
			0xF7, 0x08, 0x7A, 0x13, 0x90, 0x81, 0x75, 0xB0,
			0xC7, 0x91, 0x04, 0x83, 0xAD, 0x11, 0x46, 0x46,
			0xF8, 0x54, 0x87, 0xA0, 0x42, 0xF3, 0x71, 0xA9,
			0x8A, 0xCD, 0x59, 0x77, 0x67, 0x11, 0x1A, 0x87,
			/* CRC */
			0xAB, 0xED, 0x2C, 0x26
		},
		.len = 82,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	}
};

const struct docsis_test_data docsis_test_case_6 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-128 and CRC Verify (83-byte "
			"frame, Small offset and uneven decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-128 "
			"(83-byte frame, Small offset and uneven encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 83,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x77, 0x74, 0x56, 0x05,
			0xD1, 0x14, 0xA2, 0x8D, 0x2C, 0x9A, 0x11, 0xFC,
			0x7D, 0xB0, 0xE7, 0x18, 0xCE, 0x75, 0x7C, 0x89,
			0x14, 0x56, 0xE2, 0xF2, 0xB7, 0x47, 0x08, 0x27,
			0xF7, 0x08, 0x7A, 0x13, 0x90, 0x81, 0x75, 0xB0,
			0xC7, 0x91, 0x04, 0x83, 0xAD, 0x11, 0x46, 0x46,
			0xF8, 0x54, 0x87, 0xA0, 0xA4, 0x0C, 0xC2, 0xF0,
			0x81, 0x49, 0xA8, 0xA6, 0x6C, 0x48, 0xEB, 0x1F,
			0x4B,
			/* CRC */
			0x2F, 0xD4, 0x48, 0x18
		},
		.len = 83,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	}
};

const struct docsis_test_data docsis_test_case_7 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-128 and CRC Verify (83-byte "
			"frame, Big offset and uneven decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-128 "
			"(83-byte frame, Big offset and uneven encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 83,
		.cipher_offset = 40,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0x3B, 0x9F, 0x72, 0x4C, 0xB5, 0x72,
			0x3E, 0x56, 0x54, 0x49, 0x13, 0x53, 0xC4, 0xAA,
			0xCD, 0xEA, 0x6A, 0x88, 0x99, 0x07, 0x86, 0xF4,
			0xCF, 0x03, 0x4E, 0xDF, 0x65, 0x61, 0x47, 0x5B,
			0x2F, 0x81, 0x09, 0x12, 0x9A, 0xC2, 0x24, 0x8C,
			0x09,
			/* CRC */
			0x11, 0xB4, 0x06, 0x33
		},
		.len = 83,
		.cipher_offset = 40,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	}
};

const struct docsis_test_data docsis_test_case_8 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-128 and CRC Verify (24-byte "
			"frame, No CRC, Small offset and runt block decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-128 "
			"(24-byte frame, No CRC, Small offset and runt block encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 24,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = true
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x7A, 0xF0,
			/* CRC */
			0x8A, 0x0F, 0x74, 0xE8
		},
		.len = 24,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = true
	}
};

const struct docsis_test_data docsis_test_case_9 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-128 and CRC Verify (83-byte "
			"frame, No CRC, Big offset and uneven decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-128 "
			"(83-byte frame, No CRC, Big offset and uneven encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 83,
		.cipher_offset = 40,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = true
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0x3B, 0x9F, 0x72, 0x4C, 0xB5, 0x72,
			0x3E, 0x56, 0x54, 0x49, 0x13, 0x53, 0xC4, 0xAA,
			0xCD, 0xEA, 0x6A, 0x88, 0x99, 0x07, 0x86, 0xF4,
			0xCF, 0x03, 0x4E, 0xDF, 0x65, 0x61, 0x47, 0x5B,
			0x2F, 0x81, 0x09, 0x12, 0x9A, 0xC2, 0x24, 0x8C,
			0x09,
			/* CRC */
			0x5D, 0x2B, 0x12, 0xF4
		},
		.len = 83,
		.cipher_offset = 40,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = true
	}
};

const struct docsis_test_data docsis_test_case_10 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-128 and CRC Verify (24-byte "
			"frame, No decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-128 "
			"(24-byte frame, No encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 24,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = true,
		.no_crc = false
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00,
			/* CRC */
			0x14, 0x08, 0xE8, 0x55
		},
		.len = 24,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = true,
		.no_crc = false
	}
};

const struct docsis_test_data docsis_test_case_11 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-128 and CRC Verify (83-byte "
			"frame, No decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-128 "
			"(83-byte frame, No encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 83,
		.cipher_offset = 40,
		.crc_offset = 6,
		.no_cipher = true,
		.no_crc = false
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA,
			/* CRC */
			0xB3, 0x60, 0xEB, 0x38
		},
		.len = 83,
		.cipher_offset = 40,
		.crc_offset = 6,
		.no_cipher = true,
		.no_crc = false
	}
};

const struct docsis_test_data docsis_test_case_12 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-128 and CRC Verify (24-byte "
			"frame, No CRC, No decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-128 "
		"(24-byte frame, No CRC, No encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 24,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = true,
		.no_crc = true
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 24,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = true,
		.no_crc = true
	}
};

const struct docsis_test_data docsis_test_case_13 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-128 and CRC Verify (83-byte "
			"frame, No CRC, No decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-128 "
			"(83-byte frame, No CRC, No encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 83,
		.cipher_offset = 40,
		.crc_offset = 6,
		.no_cipher = true,
		.no_crc = true
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 83,
		.cipher_offset = 40,
		.crc_offset = 6,
		.no_cipher = true,
		.no_crc = true
	}
};

const struct docsis_test_data docsis_test_case_14 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-256 and CRC Verify (24-byte "
			"frame, Small offset and runt block decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-256 "
			"(24-byte frame, Small offset and runt block encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55,
			0x66, 0x77, 0x88, 0x99, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 24,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x6A, 0x86,
			/* CRC */
			0x9B, 0xB3, 0x1A, 0x26
		},
		.len = 24,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	}
};

const struct docsis_test_data docsis_test_case_15 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-256 and CRC Verify (25-byte "
			"frame, Small offset and runt block decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-256 "
			"(25-byte frame, Small offset and runt block encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55,
			0x66, 0x77, 0x88, 0x99, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 25,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x6A, 0x86, 0x25,
			/* CRC */
			0xB5, 0x6B, 0xFD, 0xCB
		},
		.len = 25,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	}
};

const struct docsis_test_data docsis_test_case_16 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-256 and CRC Verify (34-byte "
			"frame, Small offset and full block decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-256 "
			"(34-byte frame, Small offset and full block encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55,
			0x66, 0x77, 0x88, 0x99, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 34,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0xF6, 0xA1, 0x2E, 0x0A,
			0xBB, 0x27, 0x82, 0x4F, 0x99, 0x0A, 0xE2, 0x3F,
			/* CRC */
			0xEB, 0xB7, 0x89, 0xB0
		},
		.len = 34,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	}
};

const struct docsis_test_data docsis_test_case_17 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-256 and CRC Verify (35-byte "
			"frame, Small offset and uneven decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-256 "
			"(35-byte frame, Small offset and uneven encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55,
			0x66, 0x77, 0x88, 0x99, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 35,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0xE1, 0x30, 0x38, 0xC8,
			0xC4, 0x59, 0x8D, 0x43, 0x9A, 0xBE, 0xBE, 0x73,
			0xC3,
			/*CRC */
			0x8C, 0xE1, 0x89, 0x8B
		},
		.len = 35,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	}
};

const struct docsis_test_data docsis_test_case_18 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-256 and CRC Verify (82-byte "
			"frame, Small offset and full block decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-256 "
			"(82-byte frame, Small offset and full block encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55,
			0x66, 0x77, 0x88, 0x99, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 82,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0xE9, 0x12, 0x3B, 0x12,
			0x36, 0x56, 0x95, 0xA6, 0x97, 0xF1, 0x74, 0x68,
			0xBA, 0x58, 0x77, 0xEA, 0x43, 0x11, 0x85, 0xD4,
			0x7A, 0xF8, 0x1C, 0x11, 0x50, 0xD1, 0xF1, 0xBD,
			0x15, 0x4D, 0x99, 0xB5, 0x39, 0x74, 0x84, 0xDF,
			0xD4, 0x8B, 0xDC, 0xB7, 0x58, 0x1B, 0x22, 0xAB,
			0xF3, 0x29, 0xC6, 0xCB, 0x26, 0x07, 0x36, 0x6B,
			0x8C, 0xAC, 0x6E, 0x99, 0x37, 0x94, 0xDF, 0x31,
			/* CRC */
			0xA1, 0x7D, 0x70, 0xBB
		},
		.len = 82,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	}
};

const struct docsis_test_data docsis_test_case_19 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-256 and CRC Verify (83-byte "
			"frame, Small offset and uneven decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-256 "
			"(83-byte frame, Small offset and uneven encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55,
			0x66, 0x77, 0x88, 0x99, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 83,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0xE9, 0x12, 0x3B, 0x12,
			0x36, 0x56, 0x95, 0xA6, 0x97, 0xF1, 0x74, 0x68,
			0xBA, 0x58, 0x77, 0xEA, 0x43, 0x11, 0x85, 0xD4,
			0x7A, 0xF8, 0x1C, 0x11, 0x50, 0xD1, 0xF1, 0xBD,
			0x15, 0x4D, 0x99, 0xB5, 0x39, 0x74, 0x84, 0xDF,
			0xD4, 0x8B, 0xDC, 0xB7, 0x58, 0x1B, 0x22, 0xAB,
			0xF3, 0x29, 0xC6, 0xCB, 0x13, 0xED, 0x08, 0xF5,
			0x1B, 0x4B, 0xD8, 0x79, 0x93, 0x26, 0x69, 0x03,
			0x23,
			/* CRC */
			0xC8, 0x8E, 0x02, 0x3A
		},
		.len = 83,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	}
};

const struct docsis_test_data docsis_test_case_20 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-256 and CRC Verify (83-byte "
			"frame, Big offset and uneven decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-256 "
			"(83-byte frame, Big offset and uneven encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55,
			0x66, 0x77, 0x88, 0x99, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 83,
		.cipher_offset = 40,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0x54, 0xB4, 0x55, 0x68, 0x06, 0xBF,
			0x00, 0x8B, 0x5F, 0x2C, 0x10, 0x4A, 0xBF, 0x5A,
			0xF2, 0x20, 0xD9, 0x77, 0x7F, 0x2D, 0x2B, 0x11,
			0xAC, 0xAF, 0x21, 0x36, 0xD2, 0xD4, 0x80, 0xF2,
			0x4F, 0x14, 0xA0, 0x3A, 0x66, 0xE5, 0xC5, 0xE2,
			0x15,
			/* CRC */
			0x0C, 0x89, 0x76, 0x26
		},
		.len = 83,
		.cipher_offset = 40,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = false
	}
};

const struct docsis_test_data docsis_test_case_21 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-256 and CRC Verify (24-byte "
			"frame, No CRC, Small offset and runt block decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-256 "
			"(24-byte frame, No CRC, Small offset and runt block encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55,
			0x66, 0x77, 0x88, 0x99, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 24,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = true
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x6A, 0x86,
			/* CRC */
			0x70, 0x44, 0x0D, 0x8C
		},
		.len = 24,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = true
	}
};

const struct docsis_test_data docsis_test_case_22 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-256 and CRC Verify (83-byte "
			"frame, No CRC, Big offset and uneven decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-256 "
			"(83-byte frame, No CRC, Big offset and uneven encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55,
			0x66, 0x77, 0x88, 0x99, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 83,
		.cipher_offset = 40,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = true
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0x54, 0xB4, 0x55, 0x68, 0x06, 0xBF,
			0x00, 0x8B, 0x5F, 0x2C, 0x10, 0x4A, 0xBF, 0x5A,
			0xF2, 0x20, 0xD9, 0x77, 0x7F, 0x2D, 0x2B, 0x11,
			0xAC, 0xAF, 0x21, 0x36, 0xD2, 0xD4, 0x80, 0xF2,
			0x4F, 0x14, 0xA0, 0x3A, 0x66, 0xE5, 0xC5, 0xE2,
			0x15,
			/* CRC */
			0x40, 0x16, 0x62, 0xE1
		},
		.len = 83,
		.cipher_offset = 40,
		.crc_offset = 6,
		.no_cipher = false,
		.no_crc = true
	}
};

const struct docsis_test_data docsis_test_case_23 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-256 and CRC Verify (24-byte "
			"frame, No decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-256 "
			"(24-byte frame, No encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55,
			0x66, 0x77, 0x88, 0x99, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 24,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = true,
		.no_crc = false
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00,
			/* CRC */
			0x14, 0x08, 0xE8, 0x55
		},
		.len = 24,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = true,
		.no_crc = false
	}
};

const struct docsis_test_data docsis_test_case_24 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-256 and CRC Verify (83-byte "
			"frame, No decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-256 "
			"(83-byte frame, No encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55,
			0x66, 0x77, 0x88, 0x99, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 83,
		.cipher_offset = 40,
		.crc_offset = 6,
		.no_cipher = true,
		.no_crc = false
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA,
			/* CRC */
			0xB3, 0x60, 0xEB, 0x38
		},
		.len = 83,
		.cipher_offset = 40,
		.crc_offset = 6,
		.no_cipher = true,
		.no_crc = false
	}
};

const struct docsis_test_data docsis_test_case_25 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-256 and CRC Verify (24-byte "
			"frame, No CRC, No decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-256 "
			"(24-byte frame, No CRC, No encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55,
			0x66, 0x77, 0x88, 0x99, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 24,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = true,
		.no_crc = true
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 24,
		.cipher_offset = 18,
		.crc_offset = 6,
		.no_cipher = true,
		.no_crc = true
	}
};

const struct docsis_test_data docsis_test_case_26 = {
	.test_descr_uplink = {"Uplink AES-DOCSIS-BPI-256 and CRC Verify (83-byte "
			"frame, No CRC, No decryption)"},
	.test_descr_downlink = {"Downlink CRC Generate and AES-DOCSIS-BPI-256 "
			"(83-byte frame, No CRC, No encryption)"},
	.key = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55,
			0x66, 0x77, 0x88, 0x99, 0xAA, 0xBB, 0xCC, 0xDD,
			0xEE, 0xFF, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
			0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
		},
		.len = 16
	},
	.plaintext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 83,
		.cipher_offset = 40,
		.crc_offset = 6,
		.no_cipher = true,
		.no_crc = true
	},
	.ciphertext = {
		.data = {
			/* DOCSIS header */
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			/* Ethernet frame */
			0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x06, 0x05,
			0x04, 0x03, 0x02, 0x01, 0x08, 0x00, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA,
			/* CRC */
			0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 83,
		.cipher_offset = 40,
		.crc_offset = 6,
		.no_cipher = true,
		.no_crc = true
	}
};

#endif /* TEST_CRYPTODEV_SECURITY_DOCSIS_TEST_VECTORS_H_ */
