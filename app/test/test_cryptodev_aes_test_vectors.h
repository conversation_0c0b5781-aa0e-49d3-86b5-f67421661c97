/* SPDX-License-Identifier: BSD-3-Clause
 * Copyright(c) 2016-2019 Intel Corporation
 */

#ifndef TEST_CRYPTODEV_AES_TEST_VECTORS_H_
#define TEST_CRYPTODEV_AES_TEST_VECTORS_H_

/* test vectors */
static const uint8_t plaintext_aes128ctr[] = {
	0x6B, 0xC1, 0xBE, 0xE2, 0x2E, 0x40, 0x9F, 0x96,
	0xE9, 0x3D, 0x7E, 0x11, 0x73, 0x93, 0x17, 0x2A,
	0xAE, 0x2D, 0x8A, 0x57, 0x1E, 0x03, 0xAC, 0x9C,
	0x9E, 0xB7, 0x6F, 0xAC, 0x45, 0xAF, 0x8E, 0x51,
	0x30, 0xC8, 0x1C, 0x46, 0xA3, 0x5C, 0xE4, 0x11,
	0xE5, 0xFB, 0xC1, 0x19, 0x1A, 0x0A, 0x52, 0xEF,
	0xF6, 0x9F, 0x24, 0x45, 0xDF, 0x4F, 0x9B, 0x17,
	0xAD, 0x2B, 0x41, 0x7B, 0xE6, 0x6C, 0x37, 0x10
};

static const uint8_t ciphertext64_aes128ctr[] = {
	0x87, 0x4D, 0x61, 0x91, 0xB6, 0x20, 0xE3, 0x26,
	0x1B, 0xEF, 0x68, 0x64, 0x99, 0x0D, 0xB6, 0xCE,
	0x98, 0x06, 0xF6, 0x6B, 0x79, 0x70, 0xFD, 0xFF,
	0x86, 0x17, 0x18, 0x7B, 0xB9, 0xFF, 0xFD, 0xFF,
	0x5A, 0xE4, 0xDF, 0x3E, 0xDB, 0xD5, 0xD3, 0x5E,
	0x5B, 0x4F, 0x09, 0x02, 0x0D, 0xB0, 0x3E, 0xAB,
	0x1E, 0x03, 0x1D, 0xDA, 0x2F, 0xBE, 0x03, 0xD1,
	0x79, 0x21, 0x70, 0xA0, 0xF3, 0x00, 0x9C, 0xEE
};

static const uint8_t ciphertext64_aes128ctr_IV_12bytes[] = {
	0x28, 0x80, 0x28, 0xC7, 0x15, 0x99, 0xC5, 0xA8,
	0xDD, 0x53, 0xC2, 0x67, 0x1B, 0x86, 0xB8, 0x13,
	0xAB, 0x25, 0x39, 0x7A, 0xD2, 0x1F, 0x8B, 0x4B,
	0x94, 0x89, 0x2B, 0x65, 0xCF, 0x89, 0x1E, 0xDD,
	0xD4, 0x7C, 0xFD, 0x8D, 0x0E, 0xCD, 0x23, 0xA4,
	0xEB, 0x8C, 0x05, 0x58, 0x45, 0x4A, 0x63, 0x44,
	0x11, 0x42, 0x07, 0x17, 0xB4, 0xD2, 0xCC, 0x75,
	0xB7, 0x23, 0x99, 0xA9, 0xC5, 0x89, 0x7F, 0x66
};

static const uint8_t plaintext_aes_docsis_bpi_cfb[] = {
	0x00, 0x01, 0x02, 0x88, 0xEE, 0x59, 0x7E
};

static const uint8_t ciphertext_aes_docsis_bpi_cfb[] = {
	0xFC, 0x68, 0xA3, 0x55, 0x60, 0x37, 0xDC
};

static const uint8_t ciphertext_aes256_docsis_bpi_cfb[] = {
	0xE3, 0x75, 0xF2, 0x30, 0x1F, 0x75, 0x9A
};

static const uint8_t plaintext_aes_docsis_bpi_cbc_cfb[] = {
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
	0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x91,
	0xD2, 0xD1, 0x9F
};

static const uint8_t ciphertext_aes_docsis_bpi_cbc_cfb[] = {
	0x9D, 0xD1, 0x67, 0x4B, 0xBA, 0x61, 0x10, 0x1B,
	0x56, 0x75, 0x64, 0x74, 0x36, 0x4F, 0x10, 0x1D,
	0x44, 0xD4, 0x73
};

static const uint8_t ciphertext_aes256_docsis_bpi_cbc_cfb[] = {
	0xD1, 0x28, 0x73, 0x1F, 0xB5, 0x28, 0xB5, 0x18,
	0xAB, 0x51, 0xAB, 0xC8, 0x98, 0x3D, 0xD1, 0xEE,
	0xE4, 0x43, 0x59
};

static const uint8_t plaintext_aes192ctr[] = {
	0x01, 0x0F, 0x10, 0x1F, 0x20, 0x1C, 0x0E, 0xB8,
	0xFB, 0x5C, 0xCD, 0xCC, 0x1F, 0xF9, 0xAF, 0x0B,
	0x95, 0x03, 0x74, 0x99, 0x49, 0xE7, 0x62, 0x55,
	0xDA, 0xEA, 0x13, 0x20, 0x1D, 0xC6, 0xCC, 0xCC,
	0xD1, 0x70, 0x75, 0x47, 0x02, 0x2F, 0xFB, 0x86,
	0xBB, 0x6B, 0x23, 0xD2, 0xC9, 0x74, 0xD7, 0x7B,
	0x08, 0x03, 0x3B, 0x79, 0x39, 0xBB, 0x91, 0x29,
	0xDA, 0x14, 0x39, 0x8D, 0xFF, 0x81, 0x50, 0x96,
};

static const uint8_t ciphertext64_aes192ctr[] = {
	0x4A, 0x6C, 0xC8, 0xCC, 0x96, 0x2A, 0x13, 0x84,
	0x1C, 0x36, 0x88, 0xE9, 0xE5, 0x94, 0x70, 0xB2,
	0x14, 0x5B, 0x13, 0x80, 0xEA, 0xD8, 0x8D, 0x37,
	0xFD, 0x70, 0xA8, 0x83, 0xE8, 0x2B, 0x88, 0x1E,
	0xBA, 0x94, 0x3F, 0xF6, 0xB3, 0x1F, 0xDE, 0x34,
	0xF3, 0x5B, 0x80, 0xE9, 0xAB, 0xF5, 0x1C, 0x29,
	0xB6, 0xD9, 0x76, 0x2B, 0x06, 0xC6, 0x74, 0xF1,
	0x59, 0x5E, 0x9E, 0xA5, 0x7B, 0x2D, 0xD7, 0xF0
};

static const uint8_t ciphertext64_aes192ctr_IV_12bytes[] = {
	0x67, 0x65, 0xa9, 0xee, 0xfd, 0x31, 0x62, 0xfc,
	0xad, 0xfd, 0xc7, 0x25, 0xb7, 0x25, 0x16, 0xbe,
	0x25, 0xce, 0xc0, 0x1d, 0xda, 0xa9, 0xd3, 0xda,
	0x1b, 0x7d, 0x68, 0x6a, 0x6f, 0x06, 0xea, 0x47,
	0xa0, 0xe0, 0x15, 0xf4, 0xbd, 0x1b, 0x70, 0x34,
	0xd4, 0x6d, 0x1c, 0x84, 0x17, 0x91, 0x46, 0x0c,
	0xe8, 0xbc, 0x7a, 0xfb, 0x9f, 0x2a, 0x8f, 0xb4,
	0xd4, 0xf3, 0x6e, 0x5b, 0x75, 0xa0, 0xce, 0x32
};

static const uint8_t plaintext_aes256ctr[] = {
	0x6B, 0xC1, 0xBE, 0xE2, 0x2E, 0x40, 0x9F, 0x96,
	0xE9, 0x3D, 0x7E, 0x11, 0x73, 0x93, 0x17, 0x2A,
	0xAE, 0x2D, 0x8A, 0x57, 0x1E, 0x03, 0xAC, 0x9C,
	0x9E, 0xB7, 0x6F, 0xAC, 0x45, 0xAF, 0x8E, 0x51,
	0x30, 0xC8, 0x1C, 0x46, 0xA3, 0x5C, 0xE4, 0x11,
	0xE5, 0xFB, 0xC1, 0x19, 0x1A, 0x0A, 0x52, 0xEF,
	0xF6, 0x9F, 0x24, 0x45, 0xDF, 0x4F, 0x9B, 0x17,
	0xAD, 0x2B, 0x41, 0x7B, 0xE6, 0x6C, 0x37, 0x10
};

static const uint8_t ciphertext64_aes256ctr[] = {
	0x60, 0x1E, 0xC3, 0x13, 0x77, 0x57, 0x89, 0xA5,
	0xB7, 0xA7, 0xF5, 0x04, 0xBB, 0xF3, 0xD2, 0x28,
	0xF4, 0x43, 0xE3, 0xCA, 0x4D, 0x62, 0xB5, 0x9A,
	0xCA, 0x84, 0xE9, 0x90, 0xCA, 0xCA, 0xF5, 0xC5,
	0x2B, 0x09, 0x30, 0xDA, 0xA2, 0x3D, 0xE9, 0x4C,
	0xE8, 0x70, 0x17, 0xBA, 0x2D, 0x84, 0x98, 0x8D,
	0xDF, 0xC9, 0xC5, 0x8D, 0xB6, 0x7A, 0xAD, 0xA6,
	0x13, 0xC2, 0xDD, 0x08, 0x45, 0x79, 0x41, 0xA6
};

static const uint8_t ciphertext64_aes256ctr_IV_12bytes[] = {
	0x7B, 0x7A, 0x7D, 0x83, 0x85, 0xF8, 0x81, 0xF3,
	0x32, 0x33, 0xD9, 0xFB, 0x04, 0x73, 0xD4, 0x2F,
	0x70, 0xDE, 0x90, 0x3E, 0xD0, 0xA9, 0x93, 0x8A,
	0x91, 0xF3, 0xB5, 0x29, 0x4D, 0x2A, 0x74, 0xD0,
	0xDC, 0x4E, 0x5C, 0x9B, 0x97, 0x24, 0xD8, 0x02,
	0xFE, 0xAB, 0x38, 0xE8, 0x73, 0x51, 0x29, 0x7E,
	0xF1, 0xF9, 0x40, 0x78, 0xB1, 0x04, 0x7A, 0x78,
	0x61, 0x07, 0x47, 0xE6, 0x8C, 0x0F, 0xA8, 0x76
};

static const uint8_t plaintext_aes_common[] = {
	"What a lousy earth! He wondered how many people "
	"were destitute that same night even in his own "
	"prosperous country, how many homes were "
	"shanties, how many husbands were drunk and "
	"wives socked, and how many children were "
	"bullied, abused, or abandoned. How many "
	"families hungered for food they could not "
	"afford to buy? How many hearts were broken? How "
	"many suicides would take place that same night, "
	"how many people would go insane? How many "
	"cockroaches and landlords would triumph? How "
	"many winners were losers, successes failures, "
	"and rich men poor men? How many wise guys were "
	"stupid? How many happy endings were unhappy "
	"endings? How many honest men were liars, brave "
	"men cowards, loyal men traitors, how many "
	"sainted men were corrupt, how many people in "
	"positions of trust had sold their souls to "
	"bodyguards, how many had never had souls? How "
	"many straight-and-narrow paths were crooked "
	"paths? How many best families were worst "
	"families and how many good people were bad "
	"people? When you added them all up and then "
	"subtracted, you might be left with only the "
	"children, and perhaps with Albert Einstein and "
	"an old violinist or sculptor somewhere."
};

static const uint8_t ciphertext512_aes128cbc[] = {
	0x8B, 0x4D, 0xDA, 0x1B, 0xCF, 0x04, 0xA0, 0x31,
	0xB4, 0xBF, 0xBD, 0x68, 0x43, 0x20, 0x7E, 0x76,
	0xB1, 0x96, 0x8B, 0xA2, 0x7C, 0xA2, 0x83, 0x9E,
	0x39, 0x5A, 0x2F, 0x7E, 0x92, 0xB4, 0x48, 0x1A,
	0x3F, 0x6B, 0x5D, 0xDF, 0x52, 0x85, 0x5F, 0x8E,
	0x42, 0x3C, 0xFB, 0xE9, 0x1A, 0x24, 0xD6, 0x08,
	0xDD, 0xFD, 0x16, 0xFB, 0xE9, 0x55, 0xEF, 0xF0,
	0xA0, 0x8D, 0x13, 0xAB, 0x81, 0xC6, 0x90, 0x01,
	0xB5, 0x18, 0x84, 0xB3, 0xF6, 0xE6, 0x11, 0x57,
	0xD6, 0x71, 0xC6, 0x3C, 0x3F, 0x2F, 0x33, 0xEE,
	0x24, 0x42, 0x6E, 0xAC, 0x0B, 0xCA, 0xEC, 0xF9,
	0x84, 0xF8, 0x22, 0xAA, 0x60, 0xF0, 0x32, 0xA9,
	0x75, 0x75, 0x3B, 0xCB, 0x70, 0x21, 0x0A, 0x8D,
	0x0F, 0xE0, 0xC4, 0x78, 0x2B, 0xF8, 0x97, 0xE3,
	0xE4, 0x26, 0x4B, 0x29, 0xDA, 0x88, 0xCD, 0x46,
	0xEC, 0xAA, 0xF9, 0x7F, 0xF1, 0x15, 0xEA, 0xC3,
	0x87, 0xE6, 0x31, 0xF2, 0xCF, 0xDE, 0x4D, 0x80,
	0x70, 0x91, 0x7E, 0x0C, 0xF7, 0x26, 0x3A, 0x92,
	0x4F, 0x18, 0x83, 0xC0, 0x8F, 0x59, 0x01, 0xA5,
	0x88, 0xD1, 0xDB, 0x26, 0x71, 0x27, 0x16, 0xF5,
	0xEE, 0x10, 0x82, 0xAC, 0x68, 0x26, 0x9B, 0xE2,
	0x6D, 0xD8, 0x9A, 0x80, 0xDF, 0x04, 0x31, 0xD5,
	0xF1, 0x35, 0x5C, 0x3B, 0xDD, 0x9A, 0x65, 0xBA,
	0x58, 0x34, 0x85, 0x61, 0x1C, 0x42, 0x10, 0x76,
	0x73, 0x02, 0x42, 0xC9, 0x23, 0x18, 0x8E, 0xB4,
	0x6F, 0xB4, 0xA3, 0x54, 0x6E, 0x88, 0x3B, 0x62,
	0x7C, 0x02, 0x8D, 0x4C, 0x9F, 0xC8, 0x45, 0xF4,
	0xC9, 0xDE, 0x4F, 0xEB, 0x22, 0x83, 0x1B, 0xE4,
	0x49, 0x37, 0xE4, 0xAD, 0xE7, 0xCD, 0x21, 0x54,
	0xBC, 0x1C, 0xC2, 0x04, 0x97, 0xB4, 0x10, 0x61,
	0xF0, 0xE4, 0xEF, 0x27, 0x63, 0x3A, 0xDA, 0x91,
	0x41, 0x25, 0x62, 0x1C, 0x5C, 0xB6, 0x38, 0x4A,
	0x88, 0x71, 0x59, 0x5A, 0x8D, 0xA0, 0x09, 0xAF,
	0x72, 0x94, 0xD7, 0x79, 0x5C, 0x60, 0x7C, 0x8F,
	0x4C, 0xF5, 0xD9, 0xA1, 0x39, 0x6D, 0x81, 0x28,
	0xEF, 0x13, 0x28, 0xDF, 0xF5, 0x3E, 0xF7, 0x8E,
	0x09, 0x9C, 0x78, 0x18, 0x79, 0xB8, 0x68, 0xD7,
	0xA8, 0x29, 0x62, 0xAD, 0xDE, 0xE1, 0x61, 0x76,
	0x1B, 0x05, 0x16, 0xCD, 0xBF, 0x02, 0x8E, 0xA6,
	0x43, 0x6E, 0x92, 0x55, 0x4F, 0x60, 0x9C, 0x03,
	0xB8, 0x4F, 0xA3, 0x02, 0xAC, 0xA8, 0xA7, 0x0C,
	0x1E, 0xB5, 0x6B, 0xF8, 0xC8, 0x4D, 0xDE, 0xD2,
	0xB0, 0x29, 0x6E, 0x40, 0xE6, 0xD6, 0xC9, 0xE6,
	0xB9, 0x0F, 0xB6, 0x63, 0xF5, 0xAA, 0x2B, 0x96,
	0xA7, 0x16, 0xAC, 0x4E, 0x0A, 0x33, 0x1C, 0xA6,
	0xE6, 0xBD, 0x8A, 0xCF, 0x40, 0xA9, 0xB2, 0xFA,
	0x63, 0x27, 0xFD, 0x9B, 0xD9, 0xFC, 0xD5, 0x87,
	0x8D, 0x4C, 0xB6, 0xA4, 0xCB, 0xE7, 0x74, 0x55,
	0xF4, 0xFB, 0x41, 0x25, 0xB5, 0x4B, 0x0A, 0x1B,
	0xB1, 0xD6, 0xB7, 0xD9, 0x47, 0x2A, 0xC3, 0x98,
	0x6A, 0xC4, 0x03, 0x73, 0x1F, 0x93, 0x6E, 0x53,
	0x19, 0x25, 0x64, 0x15, 0x83, 0xF9, 0x73, 0x2A,
	0x74, 0xB4, 0x93, 0x69, 0xC4, 0x72, 0xFC, 0x26,
	0xA2, 0x9F, 0x43, 0x45, 0xDD, 0xB9, 0xEF, 0x36,
	0xC8, 0x3A, 0xCD, 0x99, 0x9B, 0x54, 0x1A, 0x36,
	0xC1, 0x59, 0xF8, 0x98, 0xA8, 0xCC, 0x28, 0x0D,
	0x73, 0x4C, 0xEE, 0x98, 0xCB, 0x7C, 0x58, 0x7E,
	0x20, 0x75, 0x1E, 0xB7, 0xC9, 0xF8, 0xF2, 0x0E,
	0x63, 0x9E, 0x05, 0x78, 0x1A, 0xB6, 0xA8, 0x7A,
	0xF9, 0x98, 0x6A, 0xA6, 0x46, 0x84, 0x2E, 0xF6,
	0x4B, 0xDC, 0x9B, 0x8F, 0x9B, 0x8F, 0xEE, 0xB4,
	0xAA, 0x3F, 0xEE, 0xC0, 0x37, 0x27, 0x76, 0xC7,
	0x95, 0xBB, 0x26, 0x74, 0x69, 0x12, 0x7F, 0xF1,
	0xBB, 0xFF, 0xAE, 0xB5, 0x99, 0x6E, 0xCB, 0x0C
};

static const uint8_t plaintext_aes128ecb[] = {
	0x6b, 0xc1, 0xbe, 0xe2, 0x2e, 0x40, 0x9f, 0x96,
	0xe9, 0x3d, 0x7e, 0x11, 0x73, 0x93, 0x17, 0x2a,
	0xae, 0x2d, 0x8a, 0x57, 0x1e, 0x03, 0xac, 0x9c,
	0x9e, 0xb7, 0x6f, 0xac, 0x45, 0xaf, 0x8e, 0x51,
	0x30, 0xc8, 0x1c, 0x46, 0xa3, 0x5c, 0xe4, 0x11,
	0xe5, 0xfb, 0xc1, 0x19, 0x1a, 0x0a, 0x52, 0xef,
	0xf6, 0x9f, 0x24, 0x45, 0xdf, 0x4f, 0x9b, 0x17,
	0xad, 0x2b, 0x41, 0x7b, 0xe6, 0x6c, 0x37, 0x10
};

static const uint8_t ciphertext_aes128ecb[] = {
	0x3a, 0xd7, 0x7b, 0xb4, 0x0d, 0x7a, 0x36, 0x60,
	0xa8, 0x9e, 0xca, 0xf3, 0x24, 0x66, 0xef, 0x97,
	0xf5, 0xd3, 0xd5, 0x85, 0x03, 0xb9, 0x69, 0x9d,
	0xe7, 0x85, 0x89, 0x5a, 0x96, 0xfd, 0xba, 0xaf,
	0x43, 0xb1, 0xcd, 0x7f, 0x59, 0x8e, 0xce, 0x23,
	0x88, 0x1b, 0x00, 0xe3, 0xed, 0x03, 0x06, 0x88,
	0x7b, 0x0c, 0x78, 0x5e, 0x27, 0xe8, 0xad, 0x3f,
	0x82, 0x23, 0x20, 0x71, 0x04, 0x72, 0x5d, 0xd4
};

static const uint8_t plaintext_aes192ecb[] = {
	0x6b, 0xc1, 0xbe, 0xe2, 0x2e, 0x40, 0x9f, 0x96,
	0xe9, 0x3d, 0x7e, 0x11, 0x73, 0x93, 0x17, 0x2a,
	0xae, 0x2d, 0x8a, 0x57, 0x1e, 0x03, 0xac, 0x9c,
	0x9e, 0xb7, 0x6f, 0xac, 0x45, 0xaf, 0x8e, 0x51,
	0x30, 0xc8, 0x1c, 0x46, 0xa3, 0x5c, 0xe4, 0x11,
	0xe5, 0xfb, 0xc1, 0x19, 0x1a, 0x0a, 0x52, 0xef,
	0xf6, 0x9f, 0x24, 0x45, 0xdf, 0x4f, 0x9b, 0x17,
	0xad, 0x2b, 0x41, 0x7b, 0xe6, 0x6c, 0x37, 0x10
};

static const uint8_t ciphertext_aes192ecb[] = {
	0xbd, 0x33, 0x4f, 0x1d, 0x6e, 0x45, 0xf2, 0x5f,
	0xf7, 0x12, 0xa2, 0x14, 0x57, 0x1f, 0xa5, 0xcc,
	0x97, 0x41, 0x04, 0x84, 0x6d, 0x0a, 0xd3, 0xad,
	0x77, 0x34, 0xec, 0xb3, 0xec, 0xee, 0x4e, 0xef,
	0xef, 0x7a, 0xfd, 0x22, 0x70, 0xe2, 0xe6, 0x0a,
	0xdc, 0xe0, 0xba, 0x2f, 0xac, 0xe6, 0x44, 0x4e,
	0x9a, 0x4b, 0x41, 0xba, 0x73, 0x8d, 0x6c, 0x72,
	0xfb, 0x16, 0x69, 0x16, 0x03, 0xc1, 0x8e, 0x0e
};

static const uint8_t plaintext_aes256ecb[] = {
	0x6b, 0xc1, 0xbe, 0xe2, 0x2e, 0x40, 0x9f, 0x96,
	0xe9, 0x3d, 0x7e, 0x11, 0x73, 0x93, 0x17, 0x2a,
	0xae, 0x2d, 0x8a, 0x57, 0x1e, 0x03, 0xac, 0x9c,
	0x9e, 0xb7, 0x6f, 0xac, 0x45, 0xaf, 0x8e, 0x51,
	0x30, 0xc8, 0x1c, 0x46, 0xa3, 0x5c, 0xe4, 0x11,
	0xe5, 0xfb, 0xc1, 0x19, 0x1a, 0x0a, 0x52, 0xef,
	0xf6, 0x9f, 0x24, 0x45, 0xdf, 0x4f, 0x9b, 0x17,
	0xad, 0x2b, 0x41, 0x7b, 0xe6, 0x6c, 0x37, 0x10
};

static const uint8_t ciphertext_aes256ecb[] = {
	0xf3, 0xee, 0xd1, 0xbd, 0xb5, 0xd2, 0xa0, 0x3c,
	0x06, 0x4b, 0x5a, 0x7e, 0x3d, 0xb1, 0x81, 0xf8,
	0x59, 0x1c, 0xcb, 0x10, 0xd4, 0x10, 0xed, 0x26,
	0xdc, 0x5b, 0xa7, 0x4a, 0x31, 0x36, 0x28, 0x70,
	0xb6, 0xed, 0x21, 0xb9, 0x9c, 0xa6, 0xf4, 0xf9,
	0xf1, 0x53, 0xe7, 0xb1, 0xbe, 0xaf, 0xed, 0x1d,
	0x23, 0x30, 0x4b, 0x7a, 0x39, 0xf9, 0xf3, 0xff,
	0x06, 0x7d, 0x8d, 0x8f, 0x9e, 0x24, 0xec, 0xc7
};

static const uint8_t plaintext_aes128ecb_128bytes[] = {
	0xf7, 0xcd, 0x12, 0xfb, 0x4f, 0x8e, 0x50, 0xab,
	0x35, 0x8e, 0x56, 0xf9, 0x83, 0x53, 0x9a, 0x1a,
	0xfc, 0x47, 0x3c, 0x96, 0x01, 0xfe, 0x01, 0x87,
	0xd5, 0xde, 0x46, 0x24, 0x5c, 0x62, 0x8f, 0xba,
	0xba, 0x91, 0x17, 0x8d, 0xba, 0x5a, 0x79, 0xb1,
	0x57, 0x05, 0x4d, 0x08, 0xba, 0x1f, 0x30, 0xd3,
	0x80, 0x40, 0xe9, 0x37, 0xb0, 0xd6, 0x34, 0x87,
	0x33, 0xdd, 0xc0, 0x5b, 0x2d, 0x58, 0x1d, 0x2a,
	0x7b, 0xb6, 0xe3, 0xd0, 0xc8, 0xa0, 0x7a, 0x69,
	0xc8, 0x5d, 0x10, 0xa2, 0xc3, 0x39, 0xca, 0xaf,
	0x40, 0xdc, 0xc7, 0xcb, 0xff, 0x18, 0x7d, 0x51,
	0x06, 0x28, 0x28, 0x1f, 0x3a, 0x9c, 0x18, 0x7d,
	0x5b, 0xb5, 0xe9, 0x20, 0xc2, 0xae, 0x17, 0x7f,
	0xd1, 0x65, 0x7a, 0x75, 0xcf, 0x21, 0xa0, 0x1e,
	0x17, 0x1b, 0xf7, 0xe8, 0x62, 0x5f, 0xaf, 0x34,
	0x7f, 0xd8, 0x18, 0x4a, 0x94, 0xf2, 0x33, 0x90
};

static const uint8_t ciphertext_aes128ecb_128bytes[] = {
	0x48, 0xa0, 0xe8, 0x0a, 0x89, 0x99, 0xab, 0xb5,
	0x66, 0x6d, 0x68, 0x23, 0x43, 0x40, 0x1f, 0x26,
	0xac, 0x52, 0xc4, 0x7b, 0x09, 0x0a, 0x8f, 0xc0,
	0x38, 0x00, 0xf5, 0x48, 0x3a, 0xfd, 0xcd, 0x7e,
	0x21, 0xe7, 0xf8, 0xf6, 0xc2, 0xa7, 0x4c, 0x1c,
	0x6e, 0x83, 0x57, 0xf4, 0xa4, 0xb0, 0xc0, 0x5f,
	0x36, 0x73, 0x22, 0xff, 0x33, 0x44, 0xab, 0xeb,
	0x96, 0xa8, 0xe0, 0x37, 0x65, 0x81, 0x6b, 0x82,
	0x89, 0xcd, 0xcc, 0xac, 0x33, 0x18, 0x7d, 0x43,
	0x0e, 0x79, 0x53, 0x30, 0x21, 0x4c, 0x95, 0x18,
	0xb6, 0xc9, 0xea, 0x5c, 0x6f, 0xa1, 0x10, 0xa3,
	0x51, 0x0e, 0x67, 0x8c, 0x1c, 0x9d, 0xf1, 0x57,
	0xeb, 0xf6, 0xad, 0x4f, 0xf2, 0x55, 0xe8, 0x11,
	0x6f, 0xaa, 0x4d, 0xe5, 0x18, 0x3d, 0xc3, 0x14,
	0xf9, 0x40, 0xfa, 0x86, 0x9d, 0xaf, 0xff, 0xfc,
	0x78, 0xba, 0xbe, 0x61, 0xf8, 0xd1, 0x00, 0x8d
};

static const uint8_t plaintext_aes192ecb_192bytes[] = {
	0x19, 0x08, 0xa3, 0x58, 0x17, 0x14, 0x70, 0x5a,
	0xb8, 0xab, 0x4f, 0x5f, 0xa4, 0x25, 0x2b, 0xec,
	0xb6, 0x74, 0x0b, 0x9d, 0x56, 0x3b, 0xaf, 0xa3,
	0xa4, 0x2d, 0x3e, 0x1f, 0x18, 0x84, 0x3b, 0x4f,
	0x48, 0xd9, 0xa3, 0xfe, 0x59, 0x1e, 0x80, 0x67,
	0x44, 0x35, 0x26, 0x00, 0x78, 0xda, 0x68, 0xfa,
	0x61, 0x9c, 0xd8, 0x8e, 0x5c, 0xc1, 0xff, 0xeb,
	0x9c, 0x7d, 0xe7, 0xa9, 0x38, 0xeb, 0x66, 0xf8,
	0x6a, 0x46, 0x71, 0x51, 0x02, 0xba, 0x8d, 0x70,
	0x55, 0x5b, 0x60, 0xc6, 0x4c, 0xae, 0xda, 0x2e,
	0x17, 0xbb, 0x65, 0xef, 0x60, 0x85, 0x9e, 0x77,
	0xe5, 0x83, 0xef, 0x30, 0x08, 0x3a, 0xba, 0x80,
	0x28, 0xc0, 0xa1, 0x93, 0x4c, 0x2a, 0x0b, 0xe1,
	0xcb, 0xd0, 0xac, 0x72, 0x72, 0x1d, 0x96, 0x76,
	0x0e, 0xc0, 0xec, 0x7d, 0x84, 0xfd, 0xee, 0x08,
	0xa1, 0x11, 0x20, 0x0d, 0x59, 0x5c, 0x06, 0x3f,
	0xa3, 0xf1, 0xd7, 0xa3, 0x1d, 0x29, 0xc3, 0xaa,
	0x05, 0x2b, 0x74, 0x8c, 0x73, 0x60, 0x65, 0x43,
	0x76, 0xd4, 0xd7, 0x7b, 0x5f, 0x40, 0xf4, 0x77,
	0xe1, 0xcc, 0x85, 0x37, 0x1c, 0xd8, 0xda, 0x91,
	0xf0, 0x40, 0xb2, 0x43, 0x2d, 0x87, 0x51, 0xd0,
	0xce, 0x27, 0xa6, 0x60, 0xac, 0x67, 0xea, 0x8b,
	0xae, 0x46, 0x2e, 0x78, 0x06, 0x09, 0x8a, 0x82,
	0xb0, 0x0d, 0x57, 0x56, 0x82, 0xfe, 0x89, 0xd2
};

static const uint8_t ciphertext_aes192ecb_192bytes[] = {
	0xcc, 0xe2, 0x3f, 0xc3, 0x12, 0x41, 0x31, 0x63,
	0x03, 0x3a, 0x3c, 0xfe, 0x76, 0x55, 0xd2, 0x26,
	0xf0, 0xc9, 0xb5, 0xc6, 0xf0, 0x1e, 0xc3, 0x72,
	0xfb, 0x64, 0x94, 0x7d, 0xf1, 0x5e, 0x2a, 0x9e,
	0x0d, 0x9a, 0x7a, 0xe0, 0xbc, 0x7b, 0xa6, 0x65,
	0x41, 0xc0, 0xa0, 0x9d, 0xb1, 0xb1, 0x09, 0x99,
	0x6e, 0xe7, 0x25, 0x5e, 0x64, 0x2b, 0x74, 0xfa,
	0xa1, 0x9a, 0x03, 0x33, 0x88, 0x81, 0x27, 0x48,
	0xdd, 0x53, 0x77, 0x0b, 0xef, 0xd9, 0x2f, 0xfa,
	0xc8, 0x50, 0x0e, 0x08, 0xa1, 0x45, 0x12, 0x82,
	0x2b, 0xfb, 0x85, 0x5a, 0x39, 0x8c, 0x71, 0x32,
	0x59, 0x27, 0x37, 0x53, 0xce, 0x3e, 0xae, 0x00,
	0x45, 0x53, 0xfd, 0xaf, 0xa5, 0xd1, 0x1a, 0xe9,
	0xa4, 0x1b, 0xe3, 0x99, 0xde, 0xcd, 0x03, 0x36,
	0x6b, 0x72, 0x43, 0x76, 0x04, 0xa8, 0xf9, 0x83,
	0xef, 0x9e, 0x57, 0x75, 0x36, 0x0e, 0x99, 0xe1,
	0x79, 0x2b, 0x2b, 0x96, 0x01, 0x10, 0xb8, 0xf6,
	0x4a, 0xa6, 0x13, 0xab, 0x7f, 0x55, 0x60, 0xf0,
	0xc9, 0x5c, 0x81, 0xa7, 0x96, 0x99, 0xb4, 0x55,
	0x41, 0x48, 0xf1, 0xd4, 0xa1, 0xb4, 0x76, 0xb5,
	0x35, 0xe1, 0x02, 0x8e, 0x09, 0xb2, 0x6c, 0x11,
	0x3f, 0xfb, 0x04, 0x47, 0x98, 0xab, 0x9b, 0x55,
	0xc3, 0xa9, 0x2a, 0x64, 0x32, 0x5a, 0x69, 0x96,
	0x28, 0x8c, 0x5b, 0xe3, 0xb2, 0x60, 0x82, 0xec
};

static const uint8_t plaintext_aes256ecb_256bytes[] = {
	0x0b, 0xe5, 0x48, 0xa6, 0xa1, 0xbc, 0xac, 0x81,
	0x80, 0x06, 0x5f, 0xae, 0x1e, 0x3f, 0x55, 0x73,
	0x6d, 0x36, 0x7f, 0x57, 0x3d, 0xa4, 0x4a, 0x6b,
	0xb6, 0x65, 0x2f, 0xb7, 0xe8, 0x85, 0x47, 0xe2,
	0x41, 0x42, 0xc2, 0x4e, 0x58, 0xf1, 0xde, 0x42,
	0x9f, 0x15, 0x4c, 0xaf, 0xea, 0x04, 0x20, 0xd0,
	0x1a, 0x19, 0x36, 0x74, 0x71, 0x12, 0x72, 0x1b,
	0xdb, 0x18, 0xf9, 0x0b, 0xb3, 0xf3, 0x63, 0xd4,
	0x62, 0x52, 0x8b, 0x63, 0x0f, 0x6b, 0x4d, 0xb9,
	0x70, 0xd6, 0x91, 0xa0, 0x43, 0x3f, 0x46, 0xfe,
	0x43, 0xbb, 0xb8, 0xdc, 0x5e, 0xdb, 0xd4, 0x1f,
	0xf0, 0x17, 0x94, 0x25, 0xee, 0x55, 0x67, 0xbf,
	0x4d, 0xda, 0x9d, 0xe7, 0x4b, 0xc6, 0x7a, 0xcf,
	0x8f, 0xd7, 0xbb, 0x29, 0x6e, 0x26, 0xd4, 0xc3,
	0x08, 0x9b, 0x67, 0x15, 0xe9, 0x2d, 0x9f, 0x2d,
	0x3c, 0x76, 0x26, 0xd3, 0xda, 0xfe, 0x6e, 0x73,
	0x9d, 0x09, 0x60, 0x4b, 0x35, 0x60, 0xdb, 0x77,
	0xb6, 0xc0, 0x45, 0x91, 0xf9, 0x14, 0x8a, 0x7a,
	0xdd, 0xe2, 0xf1, 0xdf, 0x8f, 0x12, 0x4f, 0xd7,
	0x75, 0xd6, 0x9a, 0x17, 0xda, 0x76, 0x88, 0xf0,
	0xfa, 0x44, 0x27, 0xbe, 0x61, 0xaf, 0x55, 0x9f,
	0xc7, 0xf0, 0x76, 0x77, 0xde, 0xca, 0xd1, 0x47,
	0x51, 0x55, 0xb1, 0xbf, 0xfa, 0x1e, 0xca, 0x28,
	0x17, 0x70, 0xf3, 0xb5, 0xd4, 0x32, 0x47, 0x04,
	0xe0, 0x92, 0xd8, 0xa5, 0x03, 0x69, 0x46, 0x99,
	0x7f, 0x1e, 0x3f, 0xb2, 0x93, 0x36, 0xa3, 0x88,
	0x75, 0x07, 0x68, 0xb8, 0x33, 0xce, 0x17, 0x3f,
	0x5c, 0xb7, 0x1e, 0x93, 0x38, 0xc5, 0x1d, 0x79,
	0x86, 0x7c, 0x9d, 0x9e, 0x2f, 0x69, 0x38, 0x0f,
	0x97, 0x5c, 0x67, 0xbf, 0xa0, 0x8d, 0x37, 0x0b,
	0xd3, 0xb1, 0x04, 0x87, 0x1d, 0x74, 0xfe, 0x30,
	0xfb, 0xd0, 0x22, 0x92, 0xf9, 0xf3, 0x23, 0xc9
};

static const uint8_t ciphertext_aes256ecb_256bytes[] = {
	0x4b, 0xc0, 0x1f, 0x80, 0xf5, 0xc7, 0xe8, 0xf5,
	0xc9, 0xd0, 0x3c, 0x86, 0x50, 0x78, 0x21, 0xce,
	0x01, 0xec, 0x91, 0x00, 0xc9, 0xf8, 0x73, 0x43,
	0x2f, 0x73, 0x8a, 0x6d, 0xee, 0xed, 0x2d, 0x40,
	0x17, 0x16, 0x93, 0x15, 0xac, 0xed, 0x28, 0x61,
	0xb0, 0x0f, 0xa2, 0xe1, 0xd3, 0x80, 0x51, 0xdf,
	0x73, 0xce, 0x48, 0x4c, 0x1c, 0xc1, 0x8b, 0xc9,
	0x9e, 0x5c, 0x48, 0x07, 0xa0, 0xf6, 0x29, 0xf8,
	0x63, 0x87, 0xe4, 0xe7, 0x8b, 0xf8, 0xcf, 0x58,
	0xda, 0x57, 0x62, 0x11, 0x2e, 0x6e, 0x91, 0x7e,
	0xc7, 0x73, 0xdb, 0x27, 0x3c, 0x64, 0x72, 0x52,
	0xe3, 0x27, 0x84, 0x1f, 0x73, 0x3f, 0xf4, 0x94,
	0xd2, 0xdd, 0x93, 0x33, 0x65, 0x91, 0x98, 0x89,
	0x13, 0xa9, 0x2b, 0x0d, 0x6f, 0x56, 0x51, 0x15,
	0x07, 0xc6, 0xa7, 0x36, 0x8f, 0x0c, 0xd6, 0xc2,
	0x07, 0x06, 0x65, 0x7a, 0xf8, 0x94, 0xa6, 0x75,
	0x48, 0x4c, 0xcc, 0xa5, 0xa9, 0x91, 0x04, 0x2f,
	0x7b, 0x89, 0x46, 0xd2, 0x87, 0xcb, 0xd6, 0x1b,
	0xf3, 0x1e, 0xa7, 0xe5, 0x09, 0xcf, 0x75, 0x05,
	0x9f, 0xc9, 0xac, 0xcc, 0x61, 0x15, 0x2d, 0x2e,
	0x2c, 0x0a, 0x57, 0x4d, 0x33, 0x17, 0x6b, 0x22,
	0x9e, 0x92, 0xc5, 0x81, 0xce, 0x9d, 0x52, 0x68,
	0x7d, 0x98, 0xe1, 0x23, 0x70, 0xc5, 0x19, 0x3e,
	0x91, 0xfc, 0xc6, 0xd7, 0x67, 0x5f, 0xbb, 0x57,
	0x20, 0x96, 0x3f, 0x1f, 0x9f, 0x64, 0xe9, 0xb1,
	0x51, 0xfd, 0x8c, 0xc1, 0x0f, 0x50, 0xbe, 0x43,
	0x5f, 0x90, 0xb4, 0xd1, 0xb6, 0x41, 0x7c, 0x37,
	0x92, 0x71, 0xda, 0x9d, 0xfd, 0xee, 0x69, 0x8c,
	0x24, 0x18, 0xe8, 0x81, 0x60, 0xe2, 0x89, 0x33,
	0x42, 0xd4, 0x1b, 0x6a, 0xcb, 0x4a, 0x5b, 0x00,
	0x01, 0x4f, 0x11, 0x47, 0x0f, 0x57, 0xb0, 0x90,
	0xf0, 0xed, 0xb0, 0x34, 0x2e, 0x9f, 0x81, 0x6c
};

static const uint8_t plaintext_aes128ecb_1008bytes[] = {
	0xdd, 0x14, 0xde, 0x30, 0xe0, 0xfd, 0x7b, 0x2a,
	0x94, 0x8e, 0x28, 0xa0, 0xf6, 0x93, 0x6e, 0xf5,
	0x92, 0x65, 0x1d, 0x5e, 0x78, 0x2a, 0x9d, 0x39,
	0xfc, 0xb8, 0x6d, 0x8b, 0xa5, 0xf4, 0x4b, 0x21,
	0xdd, 0x4e, 0xe9, 0xeb, 0xd7, 0xa7, 0xa1, 0x59,
	0xdc, 0x4c, 0x5e, 0xcc, 0x83, 0xab, 0xd3, 0x45,
	0xfe, 0x2c, 0x73, 0x23, 0xea, 0x45, 0xcb, 0x0c,
	0x12, 0x67, 0x28, 0xcd, 0xef, 0x4e, 0xca, 0xe2,
	0x1d, 0x92, 0x82, 0xd8, 0x0f, 0xa9, 0x36, 0x23,
	0x6d, 0x38, 0x68, 0xac, 0xa0, 0xeb, 0xdc, 0xcc,
	0xdf, 0xb8, 0x3a, 0x53, 0x04, 0x1a, 0x55, 0x27,
	0x8e, 0x22, 0x86, 0x8c, 0xbd, 0xdc, 0x6b, 0x12,
	0x9c, 0x69, 0xd2, 0x7a, 0x4b, 0x52, 0x5d, 0x76,
	0x34, 0xb9, 0x5e, 0x30, 0x0a, 0x8d, 0x1e, 0xf1,
	0x27, 0xda, 0x5b, 0xb9, 0x5e, 0xbf, 0x65, 0x34,
	0x00, 0xb6, 0xd2, 0xb0, 0x89, 0x12, 0xb6, 0x35,
	0xae, 0x27, 0x7f, 0x11, 0xe9, 0xf9, 0x1c, 0x71,
	0xc9, 0x50, 0xfe, 0xd4, 0x76, 0x50, 0x95, 0xf7,
	0xe1, 0x1c, 0x14, 0xcd, 0x67, 0x0f, 0xf0, 0x6d,
	0xa2, 0x93, 0x7b, 0x2c, 0x8d, 0x83, 0x5c, 0xff,
	0xe4, 0x95, 0xf3, 0xa1, 0xfd, 0x00, 0x77, 0x68,
	0x41, 0xb4, 0xfb, 0x81, 0xf4, 0x61, 0x1a, 0x84,
	0x5a, 0x53, 0xc3, 0xdc, 0xba, 0x0d, 0x67, 0x2e,
	0xcf, 0xf2, 0x30, 0xf5, 0x1d, 0xe9, 0xc4, 0x2c,
	0xac, 0x1f, 0xa7, 0x9c, 0x64, 0xfd, 0x45, 0x30,
	0x1b, 0xa1, 0x3b, 0x3d, 0xc7, 0xf5, 0xf9, 0xbb,
	0xba, 0x99, 0xa4, 0x12, 0x6e, 0x4e, 0xea, 0x0b,
	0x29, 0x7f, 0xcd, 0x84, 0x64, 0x50, 0x40, 0xb7,
	0x6a, 0x24, 0x29, 0xa4, 0xa7, 0xa1, 0xef, 0xa9,
	0xcf, 0xdf, 0x09, 0xff, 0xaa, 0x17, 0x5d, 0x82,
	0x74, 0xf5, 0xae, 0xd0, 0xe9, 0xec, 0xad, 0x5e,
	0xa7, 0x84, 0xda, 0xe7, 0x33, 0x58, 0x7e, 0x00,
	0x45, 0x5f, 0xbb, 0x15, 0xa3, 0x65, 0x0e, 0xf5,
	0x7e, 0x27, 0xe7, 0x04, 0x52, 0x58, 0x81, 0xd0,
	0xee, 0x8f, 0xaf, 0xe2, 0x3c, 0xbe, 0x08, 0x97,
	0x8a, 0x97, 0x12, 0xb0, 0x09, 0xfe, 0xa5, 0xeb,
	0xd1, 0x9c, 0x30, 0xe8, 0x9a, 0x3f, 0xe0, 0x38,
	0x34, 0x2b, 0xad, 0xb7, 0xc4, 0xda, 0x54, 0xab,
	0x97, 0x9c, 0x46, 0x2b, 0x2c, 0x0b, 0xb3, 0x49,
	0xcd, 0x9d, 0x32, 0x38, 0x3c, 0x1a, 0x49, 0xdc,
	0x2f, 0xe7, 0xcd, 0x8a, 0xb0, 0x76, 0xcf, 0x30,
	0xea, 0x0b, 0xb0, 0xb7, 0x63, 0xed, 0xb2, 0x8c,
	0xc9, 0x2c, 0xb7, 0x75, 0xa8, 0xf6, 0x63, 0xb6,
	0xcd, 0xb5, 0x63, 0xfb, 0x5f, 0x89, 0xae, 0x3d,
	0x33, 0x73, 0xaf, 0xde, 0xcb, 0x37, 0x0a, 0x50,
	0x6f, 0xae, 0xf3, 0xa6, 0x79, 0x85, 0xdd, 0xc5,
	0x24, 0xc5, 0x29, 0x23, 0x64, 0xef, 0x43, 0xd7,
	0xc4, 0xab, 0xd8, 0xb0, 0x84, 0x26, 0x6b, 0xe8,
	0xb1, 0x5d, 0xb5, 0x69, 0xfb, 0x97, 0x0e, 0x20,
	0xb3, 0xc1, 0x60, 0xad, 0x1a, 0xd2, 0xd6, 0x3a,
	0x73, 0x08, 0xf0, 0x47, 0x5f, 0xcf, 0x15, 0xf7,
	0x7b, 0xf3, 0x69, 0x08, 0x5a, 0x6b, 0x9f, 0xc7,
	0x12, 0xa1, 0xf0, 0xfb, 0x91, 0xc9, 0x07, 0x61,
	0x21, 0xa0, 0x30, 0x4c, 0x16, 0x81, 0xcd, 0x3c,
	0x61, 0xe8, 0x96, 0x91, 0x30, 0xdd, 0x0c, 0x0e,
	0x0b, 0xa1, 0x33, 0x95, 0x67, 0x99, 0xd6, 0x1e,
	0x1a, 0xb3, 0x12, 0xfd, 0xad, 0x46, 0x48, 0x87,
	0x5e, 0xe8, 0xd4, 0xf5, 0xac, 0xdf, 0xa7, 0x37,
	0xb8, 0xa1, 0x62, 0x8c, 0xb8, 0xb6, 0xb0, 0x69,
	0x63, 0x29, 0x60, 0x64, 0x26, 0xc3, 0xf8, 0x18,
	0x8e, 0x46, 0xa0, 0xc5, 0x45, 0x5c, 0x08, 0x2a,
	0xed, 0x29, 0x84, 0x11, 0xea, 0x59, 0xc0, 0x16,
	0xe2, 0x04, 0x30, 0x63, 0x22, 0x87, 0xb6, 0xc7,
	0x81, 0xa6, 0x58, 0xc0, 0xb2, 0xb0, 0x7d, 0xbc,
	0x16, 0x44, 0x6e, 0x5d, 0x6d, 0xce, 0x2a, 0xe0,
	0x20, 0x69, 0x35, 0xa1, 0x5d, 0x17, 0x48, 0x55,
	0x88, 0xfe, 0xde, 0x34, 0xe7, 0x18, 0xbf, 0x7e,
	0x0a, 0x1c, 0x32, 0x88, 0xab, 0xde, 0xe1, 0x02,
	0x61, 0x09, 0x58, 0x96, 0xef, 0x16, 0x73, 0xac,
	0xc0, 0x5c, 0x15, 0xca, 0x9b, 0xea, 0x0e, 0x05,
	0x97, 0x88, 0x09, 0xc5, 0xd0, 0x95, 0x90, 0xae,
	0xa5, 0xb5, 0x28, 0xc6, 0x5a, 0x7b, 0xb3, 0xcc,
	0xae, 0x57, 0x71, 0x83, 0x56, 0x57, 0xca, 0xe8,
	0x8b, 0x21, 0x0c, 0x37, 0x1d, 0xde, 0x85, 0xe2,
	0x1b, 0xa2, 0x38, 0xa0, 0xc5, 0xc7, 0x98, 0x7b,
	0xf9, 0x5e, 0x6a, 0x68, 0xb3, 0xed, 0x49, 0x5e,
	0x46, 0xb9, 0xc9, 0xf6, 0x34, 0xa6, 0x0e, 0xac,
	0x90, 0x72, 0xcf, 0xf8, 0x5b, 0x48, 0x13, 0x40,
	0x7a, 0xce, 0xfd, 0x3c, 0x16, 0xff, 0xb5, 0xea,
	0xb2, 0x56, 0x47, 0xcc, 0x9f, 0xbc, 0xae, 0x4a,
	0xc8, 0xa5, 0x59, 0x57, 0x01, 0xd7, 0x9f, 0xd7,
	0xbf, 0x13, 0xb1, 0xbf, 0xb7, 0x9a, 0xa0, 0xa1,
	0xc6, 0x66, 0x61, 0x96, 0xf2, 0xcd, 0x8c, 0xcb,
	0x3c, 0x67, 0xb5, 0xed, 0xb7, 0xa2, 0x54, 0x84,
	0x3c, 0xcb, 0x7e, 0xb3, 0x97, 0x05, 0xcb, 0x8f,
	0xa9, 0xc6, 0x3c, 0xa2, 0xbd, 0xbf, 0x3a, 0xb8,
	0x92, 0x08, 0x01, 0xea, 0xfd, 0x55, 0x2f, 0x27,
	0x2a, 0x82, 0x38, 0x26, 0x1d, 0x81, 0x19, 0x33,
	0x75, 0x3c, 0xa2, 0x13, 0x1e, 0x58, 0x9f, 0x0b,
	0x08, 0x5d, 0x7a, 0x2c, 0x9a, 0xd1, 0xa5, 0x4c,
	0x41, 0xb4, 0x1d, 0xf8, 0x42, 0x08, 0x87, 0xdd,
	0x8e, 0xc9, 0x05, 0xd2, 0x8c, 0xba, 0x93, 0x28,
	0xbe, 0x4a, 0x14, 0x13, 0x2a, 0x58, 0xf0, 0x1c,
	0xac, 0xc1, 0xc4, 0x49, 0xbc, 0xe1, 0xda, 0xb6,
	0x2d, 0x06, 0x98, 0x32, 0xea, 0xa3, 0x89, 0x11,
	0xca, 0x5f, 0x3e, 0xda, 0x24, 0xe2, 0xdb, 0x1e,
	0xca, 0xf3, 0xc0, 0xc7, 0x64, 0xee, 0x4b, 0x3d,
	0xa2, 0xee, 0x69, 0xb0, 0x3f, 0x2c, 0xd5, 0x49,
	0xba, 0x2d, 0x45, 0x7d, 0xdd, 0xb0, 0x0d, 0xc5,
	0xe0, 0x57, 0x95, 0xbe, 0xf8, 0x4a, 0x11, 0x46,
	0x4c, 0xbb, 0xdf, 0xa8, 0x5a, 0xf9, 0xff, 0x0e,
	0x31, 0xa9, 0x50, 0x5d, 0xc4, 0xb3, 0x3d, 0x09,
	0x46, 0x33, 0x39, 0x31, 0xd5, 0xb3, 0xe5, 0x91,
	0xcf, 0xca, 0x8a, 0xe0, 0xc2, 0x8e, 0xea, 0xbe,
	0x54, 0x64, 0x78, 0x0c, 0x25, 0x1c, 0x17, 0xbc,
	0x49, 0xf9, 0xc0, 0x30, 0x5f, 0x08, 0x04, 0x9d,
	0xb5, 0xe4, 0xeb, 0x9e, 0xe5, 0x1e, 0x6d, 0xbc,
	0x7b, 0xe7, 0xf0, 0xd1, 0xa0, 0x01, 0x18, 0x51,
	0x4f, 0x64, 0xc3, 0x9c, 0x70, 0x25, 0x4f, 0xed,
	0xc7, 0xbc, 0x19, 0x00, 0x09, 0x22, 0x97, 0x5d,
	0x6f, 0xe4, 0x47, 0x98, 0x05, 0xcd, 0xcc, 0xde,
	0xd5, 0xe3, 0xaf, 0xa3, 0xde, 0x69, 0x99, 0x2a,
	0xd1, 0x28, 0x4d, 0x7c, 0x89, 0xa0, 0xdb, 0xae,
	0xf9, 0xf1, 0x4a, 0x46, 0xdf, 0xbe, 0x1d, 0x37,
	0xf2, 0xd5, 0x36, 0x4a, 0x54, 0xe8, 0xc4, 0xfb,
	0x57, 0x77, 0x09, 0x05, 0x31, 0x99, 0xaf, 0x9a,
	0x17, 0xd1, 0x20, 0x93, 0x31, 0x89, 0xff, 0xed,
	0x0f, 0xf8, 0xed, 0xb3, 0xcf, 0x4c, 0x9a, 0x74,
	0xbb, 0x00, 0x36, 0x41, 0xd1, 0x13, 0x68, 0x73,
	0x78, 0x63, 0x42, 0xdd, 0x99, 0x15, 0x9a, 0xf4,
	0xe1, 0xad, 0x6d, 0xf6, 0x5e, 0xca, 0x20, 0x24,
	0xd7, 0x9d, 0x2f, 0x58, 0x97, 0xf7, 0xde, 0x31,
	0x51, 0xa3, 0x1c, 0xe2, 0x66, 0x24, 0x4b, 0xa1,
	0x56, 0x02, 0x32, 0xf4, 0x89, 0xf3, 0x86, 0x9a,
	0x85, 0xda, 0x95, 0xa8, 0x7f, 0x6a, 0x77, 0x02,
	0x3a, 0xba, 0xe0, 0xbe, 0x34, 0x5c, 0x9a, 0x1a
};

static const uint8_t ciphertext_aes128ecb_1008bytes[] = {
	0x62, 0xa1, 0xcc, 0x1e, 0x1b, 0xc3, 0xb1, 0x11,
	0xb5, 0x11, 0x4c, 0x37, 0xbf, 0xd0, 0x0c, 0xef,
	0x36, 0x9f, 0x99, 0x49, 0x38, 0xc2, 0x62, 0xbd,
	0x3e, 0x03, 0xd1, 0x02, 0xa2, 0x18, 0xdc, 0x58,
	0x9c, 0x01, 0x99, 0xd8, 0x47, 0xeb, 0x27, 0xce,
	0x76, 0x84, 0xa5, 0xab, 0xb7, 0x9b, 0xbb, 0x98,
	0xc9, 0x84, 0x02, 0x6e, 0x32, 0x65, 0xc9, 0xcb,
	0xca, 0xc7, 0xa5, 0x95, 0x11, 0xcc, 0x0a, 0x9d,
	0x5e, 0xea, 0xba, 0x59, 0xef, 0x25, 0xc0, 0x2d,
	0x8b, 0xa2, 0xec, 0x2f, 0x34, 0xea, 0x7c, 0xef,
	0xee, 0x2a, 0x57, 0x80, 0xc4, 0xca, 0x5e, 0x08,
	0x8c, 0x12, 0x13, 0x39, 0xd1, 0xc7, 0x96, 0x93,
	0x41, 0x22, 0x97, 0x1c, 0x7d, 0xe0, 0x47, 0xab,
	0xfa, 0xd7, 0xc6, 0x38, 0x5a, 0x39, 0xdb, 0x4c,
	0xd4, 0x6d, 0x50, 0x2b, 0x8f, 0xb1, 0x92, 0x06,
	0x01, 0xbf, 0xdc, 0x14, 0x5c, 0x32, 0xee, 0xb0,
	0x6a, 0x36, 0xe8, 0xe9, 0xf3, 0x12, 0x9f, 0x1f,
	0x00, 0xe5, 0x25, 0x3b, 0x52, 0x74, 0xba, 0x50,
	0x17, 0x81, 0x60, 0x5c, 0x15, 0xec, 0x4d, 0xb0,
	0x6a, 0xa1, 0xdd, 0xb4, 0xa2, 0x71, 0x01, 0xb8,
	0x8b, 0x59, 0x93, 0x58, 0x23, 0xd6, 0x38, 0xbf,
	0x49, 0x94, 0xb7, 0x6e, 0x22, 0x75, 0x68, 0x1f,
	0x15, 0x2c, 0xc4, 0x46, 0x44, 0x35, 0xc8, 0x7a,
	0x40, 0x2e, 0x55, 0x3f, 0x67, 0x4d, 0x12, 0x21,
	0xf6, 0xb1, 0x20, 0x47, 0x4f, 0x35, 0xe4, 0x96,
	0xf9, 0xa2, 0xdc, 0x4c, 0xe3, 0xa2, 0x13, 0x41,
	0xed, 0x6d, 0x86, 0x80, 0x23, 0xe5, 0x2a, 0xd1,
	0xa0, 0x69, 0x8f, 0x7e, 0x22, 0x3f, 0xf1, 0x65,
	0x9f, 0xd7, 0x86, 0xa8, 0x78, 0x57, 0x49, 0x74,
	0x91, 0x52, 0x91, 0xe7, 0x1e, 0xe2, 0x14, 0xe9,
	0x88, 0xe1, 0x67, 0x12, 0x3d, 0x0a, 0x22, 0x31,
	0x56, 0x2e, 0x36, 0xd4, 0x45, 0xc9, 0x9b, 0x7b,
	0x09, 0x53, 0x55, 0x36, 0xed, 0xa3, 0xc2, 0x22,
	0xac, 0x00, 0x5e, 0x57, 0xc8, 0x40, 0x65, 0xd2,
	0x62, 0x61, 0x35, 0xf2, 0xe8, 0x4f, 0xb3, 0x9d,
	0x2c, 0xb2, 0x12, 0x5e, 0x15, 0x47, 0xd6, 0x1c,
	0x99, 0x80, 0xe0, 0x1c, 0x09, 0x28, 0xa0, 0x7e,
	0x6c, 0x96, 0xc9, 0x62, 0x33, 0xd3, 0xbe, 0x53,
	0x16, 0xa0, 0xf2, 0xa9, 0x42, 0x1c, 0x81, 0xa3,
	0x35, 0x9b, 0x93, 0x9e, 0xc6, 0xc0, 0x83, 0x03,
	0xb7, 0x39, 0x66, 0xc9, 0x86, 0xf8, 0x8d, 0xc0,
	0xe2, 0x88, 0xb4, 0x1f, 0x5d, 0x15, 0x80, 0x60,
	0x2d, 0x53, 0x1d, 0x60, 0x07, 0xbc, 0x72, 0x11,
	0xd0, 0x0e, 0xcb, 0x70, 0x9c, 0xa0, 0x48, 0x56,
	0x21, 0x5f, 0x18, 0xdd, 0xa3, 0x1d, 0xdb, 0xe0,
	0x41, 0x0c, 0x9e, 0xb9, 0xa2, 0x7e, 0x32, 0xb3,
	0x3e, 0x91, 0x9d, 0xf2, 0xa6, 0x0d, 0x8c, 0xea,
	0xae, 0x44, 0xb2, 0x0f, 0x11, 0x35, 0x27, 0x2e,
	0xb6, 0x3d, 0xe9, 0x63, 0x86, 0x2e, 0x81, 0xdc,
	0xfa, 0xb4, 0x52, 0x1d, 0x9c, 0xd5, 0x44, 0x95,
	0xc8, 0xd0, 0x66, 0x8a, 0xbd, 0xf6, 0xd1, 0xff,
	0xeb, 0x82, 0x68, 0x58, 0x7b, 0xec, 0x0e, 0x92,
	0x0e, 0x48, 0xd6, 0xff, 0x8d, 0xac, 0xc1, 0x41,
	0x84, 0x9e, 0x56, 0x54, 0xf9, 0xb5, 0x1c, 0xb0,
	0x9f, 0xde, 0xfe, 0x14, 0x42, 0x0d, 0x22, 0x12,
	0xf2, 0x7d, 0x7b, 0xc3, 0x2e, 0x72, 0x27, 0x76,
	0x12, 0xdf, 0x57, 0x2f, 0x97, 0x82, 0x9b, 0xcf,
	0x75, 0x1a, 0x4a, 0x0c, 0xad, 0x29, 0x56, 0x4c,
	0x74, 0xaf, 0x95, 0x03, 0xff, 0x9f, 0x9d, 0xc3,
	0x2e, 0x9c, 0x1a, 0x42, 0x75, 0xe1, 0x59, 0xc9,
	0x05, 0x12, 0x6c, 0xea, 0x2b, 0x2f, 0x89, 0xfc,
	0xa4, 0x73, 0xc8, 0xdc, 0xf6, 0xd5, 0x50, 0x19,
	0x22, 0x80, 0xbc, 0x08, 0x48, 0xb4, 0x45, 0x47,
	0x25, 0x01, 0xa9, 0x55, 0x7b, 0x66, 0xbd, 0x84,
	0x0f, 0x16, 0xfa, 0x44, 0x23, 0x51, 0x6f, 0xed,
	0x35, 0x0e, 0x88, 0x4d, 0xda, 0xe8, 0x27, 0x94,
	0xbd, 0x68, 0x46, 0x28, 0x79, 0x8c, 0x03, 0x03,
	0xf0, 0x81, 0xac, 0xbc, 0xc2, 0xdd, 0xa8, 0x98,
	0xdf, 0xe3, 0x1c, 0x1c, 0x4b, 0x43, 0x9e, 0x7b,
	0x26, 0x3c, 0xe9, 0xff, 0x3b, 0xee, 0x35, 0xe6,
	0x2a, 0xcf, 0xdc, 0x17, 0x85, 0x99, 0x9e, 0x88,
	0x5c, 0x38, 0x4c, 0x56, 0x4a, 0x06, 0xeb, 0x28,
	0xf7, 0xb5, 0x97, 0x04, 0xd4, 0x05, 0x85, 0xee,
	0x90, 0xd7, 0xe2, 0x10, 0x8a, 0x86, 0xb2, 0x3f,
	0xbf, 0x3f, 0x6a, 0xe6, 0xeb, 0xc1, 0x42, 0x97,
	0xcb, 0x30, 0x41, 0x44, 0x79, 0x44, 0x7e, 0x1e,
	0x3e, 0x55, 0xe5, 0xc8, 0xd5, 0xec, 0x64, 0x3d,
	0x09, 0x69, 0xea, 0xdb, 0xe5, 0x08, 0x33, 0x00,
	0x79, 0x1b, 0x31, 0xf2, 0x3d, 0xbd, 0x73, 0xe6,
	0x0e, 0xc1, 0xb9, 0x45, 0xbf, 0xa5, 0x52, 0x5a,
	0xcd, 0x71, 0x7a, 0x2e, 0x20, 0x1e, 0xbf, 0xff,
	0x42, 0x0a, 0x6a, 0x1b, 0xa4, 0xad, 0x79, 0x3d,
	0x34, 0x54, 0x73, 0xe2, 0xd6, 0x6f, 0xb0, 0xcc,
	0xc0, 0x8a, 0x56, 0x3d, 0x4d, 0x90, 0x35, 0xe3,
	0x4b, 0xcc, 0x40, 0x40, 0xbc, 0xcf, 0x93, 0xa0,
	0xbd, 0x5c, 0xed, 0x22, 0x57, 0x92, 0x5c, 0x8d,
	0xfb, 0x67, 0x9e, 0xab, 0x40, 0xc9, 0xed, 0x7c,
	0xa1, 0xb6, 0x36, 0xb2, 0xcb, 0xbc, 0xf2, 0x1a,
	0x46, 0x6c, 0x1f, 0xb3, 0xe4, 0xf6, 0x4c, 0x7a,
	0x10, 0x81, 0x16, 0x93, 0x77, 0xa3, 0xa1, 0x07,
	0xec, 0xc8, 0x01, 0x76, 0xf8, 0xe3, 0xe6, 0xae,
	0xaf, 0x90, 0x98, 0x3a, 0xbd, 0x7d, 0x28, 0x57,
	0xb4, 0xc5, 0xfe, 0x13, 0xab, 0x6c, 0x77, 0xc1,
	0xc3, 0x47, 0x1d, 0x34, 0x2f, 0xdd, 0xe1, 0x7b,
	0x8b, 0x65, 0xc4, 0xe3, 0x45, 0xda, 0x6e, 0xba,
	0x37, 0xb1, 0x37, 0xbf, 0x63, 0x1d, 0x39, 0x77,
	0xf0, 0xa8, 0xf8, 0xda, 0x91, 0xd3, 0x27, 0xb9,
	0x29, 0x70, 0xf7, 0xae, 0x11, 0x6d, 0x8a, 0x8f,
	0x2f, 0x3a, 0xe1, 0xb8, 0x9b, 0xb5, 0x2a, 0xa8,
	0x7b, 0x86, 0x49, 0xca, 0x0c, 0x95, 0x17, 0x1e,
	0xaf, 0x9c, 0x52, 0x6b, 0x68, 0xae, 0xe3, 0xc3,
	0xc9, 0x8c, 0x89, 0x4b, 0xf2, 0xfb, 0xb1, 0xae,
	0x2f, 0x80, 0xf9, 0xa3, 0xf4, 0x10, 0x09, 0x36,
	0x81, 0x27, 0x06, 0x6d, 0xe9, 0x79, 0x8e, 0xa4,
	0x8e, 0x12, 0xfa, 0x03, 0x8e, 0x69, 0x4c, 0x7e,
	0xc5, 0x10, 0xd5, 0x00, 0x64, 0x87, 0xf8, 0x10,
	0x8a, 0x8e, 0x96, 0x9e, 0xc8, 0xac, 0x42, 0x75,
	0x97, 0x6d, 0x62, 0x3f, 0xa3, 0x29, 0x11, 0xd2,
	0x73, 0xd3, 0x95, 0xef, 0xb4, 0x64, 0xa4, 0x37,
	0x09, 0x15, 0x42, 0x7f, 0xc4, 0x46, 0x8b, 0x80,
	0xa8, 0xd9, 0x2a, 0xfc, 0x38, 0x8f, 0xf9, 0xc1,
	0xc5, 0x95, 0xad, 0x62, 0xc9, 0x6c, 0x60, 0x0b,
	0x30, 0x04, 0x8c, 0x88, 0xb5, 0x0b, 0x73, 0x23,
	0xa4, 0xe0, 0xb7, 0x6e, 0x4c, 0x78, 0xe5, 0x0a,
	0xfb, 0xe1, 0xc4, 0xeb, 0x1a, 0xb4, 0xd8, 0x3c,
	0x06, 0xb0, 0x00, 0x23, 0x86, 0xb0, 0xb4, 0x9d,
	0x33, 0xe4, 0x21, 0xca, 0xf2, 0xad, 0x14, 0x07,
	0x82, 0x25, 0xde, 0x85, 0xe4, 0x58, 0x56, 0x93,
	0x09, 0x3a, 0xeb, 0xde, 0x46, 0x77, 0x76, 0xa2,
	0x35, 0x39, 0xd0, 0xf6, 0x10, 0x81, 0x73, 0x3f,
	0x22, 0x3b, 0xeb, 0xca, 0x00, 0x19, 0x38, 0x89,
	0x26, 0x29, 0x7d, 0x6f, 0x70, 0xa6, 0xbb, 0x52,
	0x58, 0xb1, 0x0a, 0x85, 0xe9, 0x0b, 0x74, 0x2f,
	0x08, 0xe8, 0xa4, 0x4d, 0xa1, 0xcf, 0xf2, 0x75,
	0xed, 0x05, 0xae, 0x7f, 0x10, 0xb1, 0x71, 0x26,
	0xc5, 0xc7, 0xdc, 0xb0, 0x2d, 0x26, 0xf1, 0xb4
};

/** AES-128-ECB test vector */
static const struct blockcipher_test_data aes_test_data_15 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_ECB,
	.cipher_key = {
		.data = {
			 0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
			 0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c
		},
		.len = 16
	},
	.iv = {
	       .data = { 0 },
	       .len = 0
	},
	.plaintext = {
		.data = plaintext_aes128ecb,
		.len = 64
	},
	.ciphertext = {
		.data = ciphertext_aes128ecb,
		.len = 64
	}
};

/** AES-192-ECB test vector */
static const struct blockcipher_test_data aes_test_data_16 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_ECB,
	.cipher_key = {
		.data = {
			 0x8e, 0x73, 0xb0, 0xf7, 0xda, 0x0e, 0x64, 0x52,
			 0xc8, 0x10, 0xf3, 0x2b, 0x80, 0x90, 0x79, 0xe5,
			 0x62, 0xf8, 0xea, 0xd2, 0x52, 0x2c, 0x6b, 0x7b
		},
		.len = 24
	},
	.iv = {
	       .data = { 0 },
	       .len = 0
	},
	.plaintext = {
		.data = plaintext_aes192ecb,
		.len = 64
	},
	.ciphertext = {
		.data = ciphertext_aes192ecb,
		.len = 64
	}
};

/** AES-256-ECB test vector */
static const struct blockcipher_test_data aes_test_data_17 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_ECB,
	.cipher_key = {
		.data = {
			 0x60, 0x3d, 0xeb, 0x10, 0x15, 0xca, 0x71, 0xbe,
			 0x2b, 0x73, 0xae, 0xf0, 0x85, 0x7d, 0x77, 0x81,
			 0x1f, 0x35, 0x2c, 0x07, 0x3b, 0x61, 0x08, 0xd7,
			 0x2d, 0x98, 0x10, 0xa3, 0x09, 0x14, 0xdf, 0xf4
		},
		.len = 32
	},
	.iv = {
	       .data = { 0 },
	       .len = 0
	},
	.plaintext = {
		.data = plaintext_aes256ecb,
		.len = 64
	},
	.ciphertext = {
		.data = ciphertext_aes256ecb,
		.len = 64
	}
};

/** AES-128-ECB 128 byte test vector */
static const struct blockcipher_test_data aes_test_data_18 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_ECB,
	.cipher_key = {
		.data = {
			 0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
			 0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c
		},
		.len = 16
	},
	.iv = {
	       .data = { 0 },
	       .len = 0
	},
	.plaintext = {
		.data = plaintext_aes128ecb_128bytes,
		.len = 128
	},
	.ciphertext = {
		.data = ciphertext_aes128ecb_128bytes,
		.len = 128
	}
};

/** AES-192-ECB 192 bytes test vector */
static const struct blockcipher_test_data aes_test_data_19 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_ECB,
	.cipher_key = {
		.data = {
			 0x8e, 0x73, 0xb0, 0xf7, 0xda, 0x0e, 0x64, 0x52,
			 0xc8, 0x10, 0xf3, 0x2b, 0x80, 0x90, 0x79, 0xe5,
			 0x62, 0xf8, 0xea, 0xd2, 0x52, 0x2c, 0x6b, 0x7b
		},
		.len = 24
	},
	.iv = {
	       .data = { 0 },
	       .len = 0
	},
	.plaintext = {
		.data = plaintext_aes192ecb_192bytes,
		.len = 192
	},
	.ciphertext = {
		.data = ciphertext_aes192ecb_192bytes,
		.len = 192
	}
};

/** AES-256-ECB 256 byte test vector */
static const struct blockcipher_test_data aes_test_data_20 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_ECB,
	.cipher_key = {
		.data = {
			 0x60, 0x3d, 0xeb, 0x10, 0x15, 0xca, 0x71, 0xbe,
			 0x2b, 0x73, 0xae, 0xf0, 0x85, 0x7d, 0x77, 0x81,
			 0x1f, 0x35, 0x2c, 0x07, 0x3b, 0x61, 0x08, 0xd7,
			 0x2d, 0x98, 0x10, 0xa3, 0x09, 0x14, 0xdf, 0xf4
		},
		.len = 32
	},
	.iv = {
	       .data = { 0 },
	       .len = 0
	},
	.plaintext = {
		.data = plaintext_aes256ecb_256bytes,
		.len = 256
	},
	.ciphertext = {
		.data = ciphertext_aes256ecb_256bytes,
		.len = 256
	}
};

/** AES-128-ECB 1008 byte test vector */
static const struct blockcipher_test_data aes_test_data_21 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_ECB,
	.cipher_key = {
		.data = {
			 0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
			 0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c
		},
		.len = 16
	},
	.iv = {
	       .data = { 0 },
	       .len = 0
	},
	.plaintext = {
		.data = plaintext_aes128ecb_1008bytes,
		.len = 1008
	},
	.ciphertext = {
		.data = ciphertext_aes128ecb_1008bytes,
		.len = 1008
	}
};

/* NULL cipher NULL auth 8-byte multiple test vector */
static const struct blockcipher_test_data null_test_data_chain_x8_multiple = {
	.crypto_algo = RTE_CRYPTO_CIPHER_NULL,
	.cipher_key = {
		.data = { 0x0 },
		.len = 0
	},
	.iv = {
		.data = { 0x0 },
		.len = 0
	},
	.plaintext = {
		.data = plaintext_aes_common,
		.len = 512
	},
	.ciphertext = {
		.data = plaintext_aes_common,
		.len = 512
	},
	.auth_algo = RTE_CRYPTO_AUTH_NULL,
	.auth_key = {
		.data = { 0x0 },
		.len = 0
	},
	.digest = {
		.data = { 0x0 },
		.len = 0,
		.truncated_len = 0
	}
};

/* NULL cipher NULL auth 4-byte multiple test vector */
static const struct blockcipher_test_data null_test_data_chain_x4_multiple = {
	.crypto_algo = RTE_CRYPTO_CIPHER_NULL,
	.cipher_key = {
		.data = { 0x0 },
		.len = 0
	},
	.iv = {
		.data = { 0x0 },
		.len = 0
	},
	.plaintext = {
		.data = plaintext_aes128ctr,
		.len = 20
	},
	.ciphertext = {
		.data = plaintext_aes128ctr,
		.len = 20
	},
	.auth_algo = RTE_CRYPTO_AUTH_NULL,
	.auth_key = {
		.data = { 0x0 },
		.len = 0
	},
	.digest = {
		.data = { 0x0 },
		.len = 0,
		.truncated_len = 0
	}
};

/* NULL cipher NULL auth 1-byte multiple test vector */
static const struct blockcipher_test_data null_test_data_chain_x1_multiple = {
	.crypto_algo = RTE_CRYPTO_CIPHER_NULL,
	.cipher_key = {
		.data = { 0x0 },
		.len = 0
	},
	.iv = {
		.data = { 0x0 },
		.len = 0
	},
	.plaintext = {
		.data = plaintext_aes128ctr,
		.len = 21
	},
	.ciphertext = {
		.data = plaintext_aes128ctr,
		.len = 21
	},
	.auth_algo = RTE_CRYPTO_AUTH_NULL,
	.auth_key = {
		.data = { 0x0 },
		.len = 0
	},
	.digest = {
		.data = { 0x0 },
		.len = 0,
		.truncated_len = 0
	}
};

static const uint8_t ciphertext512_aes128cbc_aad[] = {
	0x57, 0x68, 0x61, 0x74, 0x20, 0x61, 0x20, 0x6C,
	0x1D, 0x7C, 0x76, 0xED, 0xC2, 0x10, 0x3C, 0xB5,
	0x14, 0x07, 0x3C, 0x33, 0x7B, 0xBE, 0x9E, 0xA9,
	0x01, 0xC5, 0xAA, 0xA6, 0xB6, 0x7A, 0xE1, 0xDB,
	0x39, 0xAA, 0xAA, 0xF4, 0xEE, 0xA7, 0x71, 0x71,
	0x78, 0x0D, 0x5A, 0xD4, 0xF9, 0xCD, 0x75, 0xD1,
	0x9C, 0x7F, 0xC8, 0x58, 0x46, 0x7A, 0xD1, 0x81,
	0xEA, 0xCC, 0x08, 0xDC, 0x82, 0x73, 0x22, 0x08,
	0x11, 0x73, 0x7C, 0xB1, 0x84, 0x6A, 0x8E, 0x67,
	0x3F, 0x5D, 0xDB, 0x0E, 0xE2, 0xC2, 0xCB, 0x6D,
	0x88, 0xEC, 0x3F, 0x50, 0x44, 0xD3, 0x47, 0x6E,
	0xDD, 0x42, 0xDC, 0x2A, 0x5E, 0x5C, 0x50, 0x24,
	0x57, 0x8A, 0xE7, 0xC5, 0x53, 0x6D, 0x89, 0x33,
	0x21, 0x65, 0x82, 0xD6, 0xE9, 0xE7, 0x77, 0x10,
	0xC2, 0x09, 0x91, 0xC1, 0x42, 0x62, 0x36, 0xF4,
	0x43, 0x37, 0x95, 0xB3, 0x7E, 0x21, 0xC5, 0x3E,
	0x65, 0xCB, 0xB6, 0xAA, 0xEC, 0xA5, 0xC6, 0x5C,
	0x4D, 0xBE, 0x14, 0xF1, 0x98, 0xBF, 0x6C, 0x8A,
	0x9E, 0x9F, 0xD4, 0xB4, 0xF2, 0x22, 0x96, 0x99,
	0x37, 0x32, 0xB6, 0xC1, 0x04, 0x66, 0x52, 0x37,
	0x5D, 0x5F, 0x58, 0x92, 0xC9, 0x97, 0xEA, 0x60,
	0x60, 0x27, 0x57, 0xF9, 0x47, 0x4F, 0xBC, 0xDF,
	0x05, 0xBD, 0x37, 0x87, 0xBB, 0x09, 0xA5, 0xBE,
	0xC1, 0xFC, 0x32, 0x86, 0x6A, 0xB7, 0x8B, 0x1E,
	0x6B, 0xCE, 0x8D, 0x81, 0x63, 0x4C, 0xF2, 0x7F,
	0xD1, 0x45, 0x82, 0xE8, 0x0D, 0x1C, 0x4D, 0xA8,
	0xBF, 0x2D, 0x2B, 0x52, 0xE5, 0xDB, 0xAB, 0xFD,
	0x04, 0xA2, 0xA1, 0x1E, 0x21, 0x1D, 0x06, 0x9A,
	0xC2, 0x7D, 0x99, 0xFC, 0xB4, 0x72, 0x89, 0x41,
	0x55, 0x69, 0xFA, 0x1F, 0x78, 0x2F, 0x35, 0x59,
	0xD7, 0x59, 0x6D, 0xA6, 0x45, 0xC9, 0x2B, 0x06,
	0x6C, 0xEC, 0x83, 0x34, 0xA5, 0x08, 0xDB, 0x6F,
	0xDE, 0x75, 0x21, 0x9B, 0xB0, 0xCB, 0x0A, 0xAE,
	0x22, 0x99, 0x74, 0x1C, 0x9D, 0x37, 0x0E, 0xC6,
	0x3A, 0x45, 0x49, 0xE5, 0xE3, 0x21, 0x11, 0xEA,
	0x34, 0x25, 0xD5, 0x76, 0xB0, 0x30, 0x19, 0x87,
	0x14, 0x3A, 0x10, 0x6F, 0x6D, 0xDD, 0xE9, 0x60,
	0x6A, 0x00, 0x6A, 0x4C, 0x5B, 0x85, 0x3E, 0x1A,
	0x41, 0xFA, 0xDE, 0x2D, 0x2F, 0x2E, 0x5B, 0x79,
	0x09, 0x66, 0x65, 0xD0, 0xDB, 0x32, 0x05, 0xB5,
	0xEA, 0xFB, 0x6A, 0xD5, 0x43, 0xF8, 0xBD, 0x98,
	0x7B, 0x8E, 0x3B, 0x85, 0x89, 0x5D, 0xC5, 0x59,
	0x54, 0x22, 0x75, 0xA8, 0x60, 0xDC, 0x0A, 0x37,
	0x8C, 0xD8, 0x05, 0xEA, 0x62, 0x62, 0x71, 0x98,
	0x0C, 0xCB, 0xCE, 0x0A, 0xD9, 0xE6, 0xE8, 0xA7,
	0xB3, 0x2D, 0x89, 0xA7, 0x60, 0xF0, 0x42, 0xA7,
	0x3D, 0x80, 0x44, 0xE7, 0xC1, 0xA6, 0x88, 0xB1,
	0x4F, 0xC0, 0xB1, 0xAF, 0x40, 0xF3, 0x54, 0x72,
	0x8F, 0xAF, 0x47, 0x96, 0x19, 0xEB, 0xA5, 0x5C,
	0x00, 0x3B, 0x36, 0xC8, 0x3F, 0x1E, 0x63, 0x54,
	0xF3, 0x3D, 0x85, 0x44, 0x9B, 0x9B, 0x20, 0xE3,
	0x9D, 0xEF, 0x62, 0x21, 0xA1, 0x0B, 0x78, 0xF4,
	0x2B, 0x89, 0x66, 0x5E, 0x97, 0xC6, 0xC4, 0x55,
	0x35, 0x32, 0xD7, 0x44, 0x95, 0x9A, 0xE7, 0xF2,
	0x57, 0x52, 0x5B, 0x92, 0x86, 0x8F, 0x8B, 0xCF,
	0x41, 0x89, 0xF6, 0x2A, 0xD3, 0x42, 0x87, 0x43,
	0x56, 0x1F, 0x0E, 0x49, 0xF1, 0x32, 0x6D, 0xA8,
	0x62, 0xDF, 0x47, 0xBB, 0xB6, 0x53, 0xF8, 0x5C,
	0x36, 0xDA, 0x34, 0x34, 0x2D, 0x2E, 0x1D, 0x33,
	0xAF, 0x6A, 0x1E, 0xF1, 0xC9, 0x72, 0xB5, 0x3C,
	0x64, 0x4C, 0x96, 0x12, 0x78, 0x67, 0x6A, 0xE5,
	0x8B, 0x05, 0x80, 0xAE, 0x7D, 0xE5, 0x9B, 0x24,
	0xDB, 0xFF, 0x1E, 0xB8, 0x36, 0x6D, 0x3D, 0x5D,
	0x73, 0x65, 0x72, 0x73, 0x2C, 0x20, 0x73, 0x75
};

static const uint8_t plaintext_aes_common_digest_enc[] = {
	0x57, 0x68, 0x61, 0x74, 0x20, 0x61, 0x20, 0x6C,
	0x6F, 0x75, 0x73, 0x79, 0x20, 0x65, 0x61, 0x72,
	0x74, 0x68, 0x21, 0x20, 0x48, 0x65, 0x20, 0x77,
	0x6F, 0x6E, 0x64, 0x65, 0x72, 0x65, 0x64, 0x20,
	0x68, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E, 0x79,
	0x20, 0x70, 0x65, 0x6F, 0x70, 0x6C, 0x65, 0x20,
	0x77, 0x65, 0x72, 0x65, 0x20, 0x64, 0x65, 0x73,
	0x74, 0x69, 0x74, 0x75, 0x74, 0x65, 0x20, 0x74,
	0x68, 0x61, 0x74, 0x20, 0x73, 0x61, 0x6D, 0x65,
	0x20, 0x6E, 0x69, 0x67, 0x68, 0x74, 0x20, 0x65,
	0x76, 0x65, 0x6E, 0x20, 0x69, 0x6E, 0x20, 0x68,
	0x69, 0x73, 0x20, 0x6F, 0x77, 0x6E, 0x20, 0x70,
	0x72, 0x6F, 0x73, 0x70, 0x65, 0x72, 0x6F, 0x75,
	0x73, 0x20, 0x63, 0x6F, 0x75, 0x6E, 0x74, 0x72,
	0x79, 0x2C, 0x20, 0x68, 0x6F, 0x77, 0x20, 0x6D,
	0x61, 0x6E, 0x79, 0x20, 0x68, 0x6F, 0x6D, 0x65,
	0x73, 0x20, 0x77, 0x65, 0x72, 0x65, 0x20, 0x73,
	0x68, 0x61, 0x6E, 0x74, 0x69, 0x65, 0x73, 0x2C,
	0x20, 0x68, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E,
	0x79, 0x20, 0x68, 0x75, 0x73, 0x62, 0x61, 0x6E,
	0x64, 0x73, 0x20, 0x77, 0x65, 0x72, 0x65, 0x20,
	0x64, 0x72, 0x75, 0x6E, 0x6B, 0x20, 0x61, 0x6E,
	0x64, 0x20, 0x77, 0x69, 0x76, 0x65, 0x73, 0x20,
	0x73, 0x6F, 0x63, 0x6B, 0x65, 0x64, 0x2C, 0x20,
	0x61, 0x6E, 0x64, 0x20, 0x68, 0x6F, 0x77, 0x20,
	0x6D, 0x61, 0x6E, 0x79, 0x20, 0x63, 0x68, 0x69,
	0x6C, 0x64, 0x72, 0x65, 0x6E, 0x20, 0x77, 0x65,
	0x72, 0x65, 0x20, 0x62, 0x75, 0x6C, 0x6C, 0x69,
	0x65, 0x64, 0x2C, 0x20, 0x61, 0x62, 0x75, 0x73,
	0x65, 0x64, 0x2C, 0x20, 0x6F, 0x72, 0x20, 0x61,
	0x62, 0x61, 0x6E, 0x64, 0x6F, 0x6E, 0x65, 0x64,
	0x2E, 0x20, 0x48, 0x6F, 0x77, 0x20, 0x6D, 0x61,
	0x6E, 0x79, 0x20, 0x66, 0x61, 0x6D, 0x69, 0x6C,
	0x69, 0x65, 0x73, 0x20, 0x68, 0x75, 0x6E, 0x67,
	0x65, 0x72, 0x65, 0x64, 0x20, 0x66, 0x6F, 0x72,
	0x20, 0x66, 0x6F, 0x6F, 0x64, 0x20, 0x74, 0x68,
	0x65, 0x79, 0x20, 0x63, 0x6F, 0x75, 0x6C, 0x64,
	0x20, 0x6E, 0x6F, 0x74, 0x20, 0x61, 0x66, 0x66,
	0x6F, 0x72, 0x64, 0x20, 0x74, 0x6F, 0x20, 0x62,
	0x75, 0x79, 0x3F, 0x20, 0x48, 0x6F, 0x77, 0x20,
	0x6D, 0x61, 0x6E, 0x79, 0x20, 0x68, 0x65, 0x61,
	0x72, 0x74, 0x73, 0x20, 0x77, 0x65, 0x72, 0x65,
	0x20, 0x62, 0x72, 0x6F, 0x6B, 0x65, 0x6E, 0x3F,
	0x20, 0x48, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E,
	0x79, 0x20, 0x73, 0x75, 0x69, 0x63, 0x69, 0x64,
	0x65, 0x73, 0x20, 0x77, 0x6F, 0x75, 0x6C, 0x64,
	0x20, 0x74, 0x61, 0x6B, 0x65, 0x20, 0x70, 0x6C,
	0x61, 0x63, 0x65, 0x20, 0x74, 0x68, 0x61, 0x74,
	0x20, 0x73, 0x61, 0x6D, 0x65, 0x20, 0x6E, 0x69,
	0x67, 0x68, 0x74, 0x2C, 0x20, 0x68, 0x6F, 0x77,
	0x20, 0x6D, 0x61, 0x6E, 0x79, 0x20, 0x70, 0x65,
	0x6F, 0x70, 0x6C, 0x65, 0x20, 0x77, 0x6F, 0x75,
	0x6C, 0x64, 0x20, 0x67, 0x6F, 0x20, 0x69, 0x6E,
	0x73, 0x61, 0x6E, 0x65, 0x3F, 0x20, 0x48, 0x6F,
	0x77, 0x20, 0x6D, 0x61, 0x6E, 0x79, 0x20, 0x63,
	0x6F, 0x63, 0x6B, 0x72, 0x6F, 0x61, 0x63, 0x68,
	0x65, 0x73, 0x20, 0x61, 0x6E, 0x64, 0x20, 0x6C,
	0x61, 0x6E, 0x64, 0x6C, 0x6F, 0x72, 0x64, 0x73,
	0x20, 0x77, 0x6F, 0x75, 0x6C, 0x64, 0x20, 0x74,
	0x72, 0x69, 0x75, 0x6D, 0x70, 0x68, 0x3F, 0x20,
	0x48, 0x6F, 0x77, 0x20, 0x6D, 0x61, 0x6E, 0x79,
	0x20, 0x77, 0x69, 0x6E, 0x6E, 0x65, 0x72, 0x73,
	0x20, 0x77, 0x65, 0x72, 0x65, 0x20, 0x6c, 0x6f,
	0x73, 0x65, 0x72, 0x73, 0x2c, 0x20, 0x73, 0x75,
	/* mac */
	0xC4, 0xB7, 0x0E, 0x6B, 0xDE, 0xD1, 0xE7, 0x77,
	0x7E, 0x2E, 0x8F, 0xFC, 0x48, 0x39, 0x46, 0x17,
	0x3F, 0x91, 0x64, 0x59
};

static const uint8_t ciphertext512_aes128cbc_digest_enc[] = {
	0x8B, 0x4D, 0xDA, 0x1B, 0xCF, 0x04, 0xA0, 0x31,
	0xB4, 0xBF, 0xBD, 0x68, 0x43, 0x20, 0x7E, 0x76,
	0xB1, 0x96, 0x8B, 0xA2, 0x7C, 0xA2, 0x83, 0x9E,
	0x39, 0x5A, 0x2F, 0x7E, 0x92, 0xB4, 0x48, 0x1A,
	0x3F, 0x6B, 0x5D, 0xDF, 0x52, 0x85, 0x5F, 0x8E,
	0x42, 0x3C, 0xFB, 0xE9, 0x1A, 0x24, 0xD6, 0x08,
	0xDD, 0xFD, 0x16, 0xFB, 0xE9, 0x55, 0xEF, 0xF0,
	0xA0, 0x8D, 0x13, 0xAB, 0x81, 0xC6, 0x90, 0x01,
	0xB5, 0x18, 0x84, 0xB3, 0xF6, 0xE6, 0x11, 0x57,
	0xD6, 0x71, 0xC6, 0x3C, 0x3F, 0x2F, 0x33, 0xEE,
	0x24, 0x42, 0x6E, 0xAC, 0x0B, 0xCA, 0xEC, 0xF9,
	0x84, 0xF8, 0x22, 0xAA, 0x60, 0xF0, 0x32, 0xA9,
	0x75, 0x75, 0x3B, 0xCB, 0x70, 0x21, 0x0A, 0x8D,
	0x0F, 0xE0, 0xC4, 0x78, 0x2B, 0xF8, 0x97, 0xE3,
	0xE4, 0x26, 0x4B, 0x29, 0xDA, 0x88, 0xCD, 0x46,
	0xEC, 0xAA, 0xF9, 0x7F, 0xF1, 0x15, 0xEA, 0xC3,
	0x87, 0xE6, 0x31, 0xF2, 0xCF, 0xDE, 0x4D, 0x80,
	0x70, 0x91, 0x7E, 0x0C, 0xF7, 0x26, 0x3A, 0x92,
	0x4F, 0x18, 0x83, 0xC0, 0x8F, 0x59, 0x01, 0xA5,
	0x88, 0xD1, 0xDB, 0x26, 0x71, 0x27, 0x16, 0xF5,
	0xEE, 0x10, 0x82, 0xAC, 0x68, 0x26, 0x9B, 0xE2,
	0x6D, 0xD8, 0x9A, 0x80, 0xDF, 0x04, 0x31, 0xD5,
	0xF1, 0x35, 0x5C, 0x3B, 0xDD, 0x9A, 0x65, 0xBA,
	0x58, 0x34, 0x85, 0x61, 0x1C, 0x42, 0x10, 0x76,
	0x73, 0x02, 0x42, 0xC9, 0x23, 0x18, 0x8E, 0xB4,
	0x6F, 0xB4, 0xA3, 0x54, 0x6E, 0x88, 0x3B, 0x62,
	0x7C, 0x02, 0x8D, 0x4C, 0x9F, 0xC8, 0x45, 0xF4,
	0xC9, 0xDE, 0x4F, 0xEB, 0x22, 0x83, 0x1B, 0xE4,
	0x49, 0x37, 0xE4, 0xAD, 0xE7, 0xCD, 0x21, 0x54,
	0xBC, 0x1C, 0xC2, 0x04, 0x97, 0xB4, 0x10, 0x61,
	0xF0, 0xE4, 0xEF, 0x27, 0x63, 0x3A, 0xDA, 0x91,
	0x41, 0x25, 0x62, 0x1C, 0x5C, 0xB6, 0x38, 0x4A,
	0x88, 0x71, 0x59, 0x5A, 0x8D, 0xA0, 0x09, 0xAF,
	0x72, 0x94, 0xD7, 0x79, 0x5C, 0x60, 0x7C, 0x8F,
	0x4C, 0xF5, 0xD9, 0xA1, 0x39, 0x6D, 0x81, 0x28,
	0xEF, 0x13, 0x28, 0xDF, 0xF5, 0x3E, 0xF7, 0x8E,
	0x09, 0x9C, 0x78, 0x18, 0x79, 0xB8, 0x68, 0xD7,
	0xA8, 0x29, 0x62, 0xAD, 0xDE, 0xE1, 0x61, 0x76,
	0x1B, 0x05, 0x16, 0xCD, 0xBF, 0x02, 0x8E, 0xA6,
	0x43, 0x6E, 0x92, 0x55, 0x4F, 0x60, 0x9C, 0x03,
	0xB8, 0x4F, 0xA3, 0x02, 0xAC, 0xA8, 0xA7, 0x0C,
	0x1E, 0xB5, 0x6B, 0xF8, 0xC8, 0x4D, 0xDE, 0xD2,
	0xB0, 0x29, 0x6E, 0x40, 0xE6, 0xD6, 0xC9, 0xE6,
	0xB9, 0x0F, 0xB6, 0x63, 0xF5, 0xAA, 0x2B, 0x96,
	0xA7, 0x16, 0xAC, 0x4E, 0x0A, 0x33, 0x1C, 0xA6,
	0xE6, 0xBD, 0x8A, 0xCF, 0x40, 0xA9, 0xB2, 0xFA,
	0x63, 0x27, 0xFD, 0x9B, 0xD9, 0xFC, 0xD5, 0x87,
	0x8D, 0x4C, 0xB6, 0xA4, 0xCB, 0xE7, 0x74, 0x55,
	0xF4, 0xFB, 0x41, 0x25, 0xB5, 0x4B, 0x0A, 0x1B,
	0xB1, 0xD6, 0xB7, 0xD9, 0x47, 0x2A, 0xC3, 0x98,
	0x6A, 0xC4, 0x03, 0x73, 0x1F, 0x93, 0x6E, 0x53,
	0x19, 0x25, 0x64, 0x15, 0x83, 0xF9, 0x73, 0x2A,
	0x74, 0xB4, 0x93, 0x69, 0xC4, 0x72, 0xFC, 0x26,
	0xA2, 0x9F, 0x43, 0x45, 0xDD, 0xB9, 0xEF, 0x36,
	0xC8, 0x3A, 0xCD, 0x99, 0x9B, 0x54, 0x1A, 0x36,
	0xC1, 0x59, 0xF8, 0x98, 0xA8, 0xCC, 0x28, 0x0D,
	0x73, 0x4C, 0xEE, 0x98, 0xCB, 0x7C, 0x58, 0x7E,
	0x20, 0x75, 0x1E, 0xB7, 0xC9, 0xF8, 0xF2, 0x0E,
	0x63, 0x9E, 0x05, 0x78, 0x1A, 0xB6, 0xA8, 0x7A,
	0xF9, 0x98, 0x6A, 0xA6, 0x46, 0x84, 0x2E, 0xF6,
	0x4B, 0xDC, 0x9B, 0x8F, 0x9B, 0x8F, 0xEE, 0xB4,
	0xAA, 0x3F, 0xEE, 0xC0, 0x37, 0x27, 0x76, 0xC7,
	0x95, 0xBB, 0x26, 0x74, 0x69, 0x12, 0x7F, 0xF1,
	0xBB, 0xFF, 0xAE, 0xB5, 0x99, 0x6E, 0xCB, 0x0C,
	/* encrypted mac */
	0x39, 0x34, 0x16, 0x84, 0xE7, 0x90, 0x02, 0xB1,
	0x3E, 0x92, 0xCB, 0x58, 0x98, 0xBA, 0x7C, 0x98,
	0x8B, 0x90, 0xFB, 0x5E, 0x9D, 0x74, 0xCC, 0x56,
	0xE3, 0x55, 0xB1, 0xC8, 0xC8, 0x7C, 0xB2, 0xE7
};

static const uint8_t plaintext_aes_common_short_digest_enc[] = {
	0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD,
	0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD,
	0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD,
	0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD,
	0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD,
	0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD,
	0xDD, 0xDD
};

static const uint8_t ciphertext512_aes128cbc_short_digest_enc[] = {
	0x6D, 0xB1, 0x23, 0x86, 0x58, 0x54, 0x83, 0xE7,
	0x3B, 0x27, 0x85, 0xA7, 0x06, 0xBC, 0x4A, 0xFB,
	0x16, 0xD8, 0x56, 0x7C, 0x39, 0xC6, 0xE7, 0x1A,
	0x67, 0xA8, 0x48, 0x22, 0xD6, 0xFF, 0x96, 0xB3,
	0xA1, 0x52, 0x52, 0x83, 0xE6, 0x2E, 0x6A, 0xC9,
	0x57, 0x8E, 0x34, 0x6F, 0xEE, 0x41, 0xF5, 0xBD,
	0x6B, 0x89, 0x31, 0x64, 0x89, 0xAB, 0xDE, 0xA7,
	0xBD, 0xFD, 0x46, 0x40, 0xE7, 0x13, 0x23, 0x84,
	0xDD, 0xCB, 0x35, 0x87, 0x4E, 0x57, 0xB7, 0x97,
	0x9E, 0xAB, 0xFB, 0xE7, 0x18, 0x82, 0x05, 0xDF
};

/* AES128-CTR-SHA1 test vector */
static const struct blockcipher_test_data aes_test_data_1 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CTR,
	.cipher_key = {
		.data = {
			0x2B, 0x7E, 0x15, 0x16, 0x28, 0xAE, 0xD2, 0xA6,
			0xAB, 0xF7, 0x15, 0x88, 0x09, 0xCF, 0x4F, 0x3C
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xF0, 0xF1, 0xF2, 0xF3, 0xF4, 0xF5, 0xF6, 0xF7,
			0xF8, 0xF9, 0xFA, 0xFB, 0xFC, 0xFD, 0xFE, 0xFF
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes128ctr,
		.len = 64
	},
	.ciphertext = {
		.data = ciphertext64_aes128ctr,
		.len = 64
	},
	.auth_algo = RTE_CRYPTO_AUTH_SHA1_HMAC,
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD
		},
		.len = 20
	},
	.digest = {
		.data = {
			0x9B, 0x6F, 0x0C, 0x43, 0xF5, 0xC1, 0x3E, 0xB0,
			0xB1, 0x70, 0xB8, 0x2B, 0x33, 0x09, 0xD2, 0xB2,
			0x56, 0x20, 0xFB, 0xFE
		},
		.len = 20,
		.truncated_len = 12
	}
};

/** AES-192-CTR XCBC test vector */
static const struct blockcipher_test_data aes_test_data_2 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CTR,
	.cipher_key = {
		.data = {
			0xCB, 0xC5, 0xED, 0x5B, 0xE7, 0x7C, 0xBD, 0x8C,
			0x50, 0xD9, 0x30, 0xF2, 0xB5, 0x6A, 0x0E, 0x5F,
			0xAA, 0xAE, 0xAD, 0xA2, 0x1F, 0x49, 0x52, 0xD4
		},
		.len = 24
	},
	.iv = {
		.data = {
			0x3F, 0x69, 0xA8, 0xCD, 0xE8, 0xF0, 0xEF, 0x40,
			0xB8, 0x7A, 0x4B, 0xED, 0x2B, 0xAF, 0xBF, 0x57
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes192ctr,
		.len = 64
	},
	.ciphertext = {
		.data = ciphertext64_aes192ctr,
		.len = 64
	},
	.auth_algo = RTE_CRYPTO_AUTH_AES_XCBC_MAC,
	.auth_key = {
		.data = {
			0x87, 0x61, 0x54, 0x53, 0xC4, 0x6D, 0xDD, 0x51,
			0xE1, 0x9F, 0x86, 0x64, 0x39, 0x0A, 0xE6, 0x59
		},
		.len = 16
	},
	.digest = {
		.data = {
			0xCA, 0x33, 0xB3, 0x3B, 0x16, 0x94, 0xAA, 0x55,
			0x36, 0x6B, 0x45, 0x46
		},
		.len = 12,
		.truncated_len = 12
	}
};

/** AES-256-CTR SHA1 test vector */
static const struct blockcipher_test_data aes_test_data_3 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CTR,
	.cipher_key = {
		.data = {
			0x60, 0x3D, 0xEB, 0x10, 0x15, 0xCA, 0x71, 0xBE,
			0x2B, 0x73, 0xAE, 0xF0, 0x85, 0x7D, 0x77, 0x81,
			0x1F, 0x35, 0x2C, 0x07, 0x3B, 0x61, 0x08, 0xD7,
			0x2D, 0x98, 0x10, 0xA3, 0x09, 0x14, 0xDF, 0xF4
		},
		.len = 32
	},
	.iv = {
		.data = {
			0xF0, 0xF1, 0xF2, 0xF3, 0xF4, 0xF5, 0xF6, 0xF7,
			0xF8, 0xF9, 0xFA, 0xFB, 0xFC, 0xFD, 0xFE, 0xFF
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes256ctr,
		.len = 64
	},
	.ciphertext = {
		.data = ciphertext64_aes256ctr,
		.len = 64
	},
	.auth_algo = RTE_CRYPTO_AUTH_SHA1_HMAC,
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD
		},
		.len = 20
	},
	.digest = {
		.data = {
			0x3B, 0x1A, 0x9D, 0x82, 0x35, 0xD5, 0xDD, 0x64,
			0xCC, 0x1B, 0xA9, 0xC0, 0xEB, 0xE9, 0x42, 0x16,
			0xE7, 0x87, 0xA3, 0xEF
		},
		.len = 20,
		.truncated_len = 12
	}
};

/* AES128-CTR-SHA1 test vector (12-byte IV) */
static const struct blockcipher_test_data aes_test_data_1_IV_12_bytes = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CTR,
	.cipher_key = {
		.data = {
			0x2B, 0x7E, 0x15, 0x16, 0x28, 0xAE, 0xD2, 0xA6,
			0xAB, 0xF7, 0x15, 0x88, 0x09, 0xCF, 0x4F, 0x3C
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xF0, 0xF1, 0xF2, 0xF3, 0xF4, 0xF5, 0xF6, 0xF7,
			0xF8, 0xF9, 0xFA, 0xFB
		},
		.len = 12
	},
	.plaintext = {
		.data = plaintext_aes128ctr,
		.len = 64
	},
	.ciphertext = {
		.data = ciphertext64_aes128ctr_IV_12bytes,
		.len = 64
	},
	.auth_algo = RTE_CRYPTO_AUTH_SHA1_HMAC,
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD
		},
		.len = 20
	},
	.digest = {
		.data = {
			0x5C, 0x34, 0x6B, 0xE4, 0x9A, 0x7F, 0x4A, 0xC3,
			0x82, 0xBE, 0xA0, 0x12, 0xD1, 0xF0, 0x15, 0xFA,
			0xCF, 0xC8, 0x7F, 0x60
		},
		.len = 20,
		.truncated_len = 12
	}
};

/** AES-192-CTR XCBC test vector (12-byte IV) */
static const struct blockcipher_test_data aes_test_data_2_IV_12_bytes = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CTR,
	.cipher_key = {
		.data = {
			0xCB, 0xC5, 0xED, 0x5B, 0xE7, 0x7C, 0xBD, 0x8C,
			0x50, 0xD9, 0x30, 0xF2, 0xB5, 0x6A, 0x0E, 0x5F,
			0xAA, 0xAE, 0xAD, 0xA2, 0x1F, 0x49, 0x52, 0xD4
		},
		.len = 24
	},
	.iv = {
		.data = {
			0x3F, 0x69, 0xA8, 0xCD, 0xE8, 0xF0, 0xEF, 0x40,
			0xB8, 0x7A, 0x4B, 0xED
		},
		.len = 12
	},
	.plaintext = {
		.data = plaintext_aes192ctr,
		.len = 64
	},
	.ciphertext = {
		.data = ciphertext64_aes192ctr_IV_12bytes,
		.len = 64
	},
	.auth_algo = RTE_CRYPTO_AUTH_AES_XCBC_MAC,
	.auth_key = {
		.data = {
			0x87, 0x61, 0x54, 0x53, 0xC4, 0x6D, 0xDD, 0x51,
			0xE1, 0x9F, 0x86, 0x64, 0x39, 0x0A, 0xE6, 0x59
		},
		.len = 16
	},
	.digest = {
		.data = {
			0x0C, 0xA1, 0xA5, 0xAF, 0x3E, 0x41, 0xD2, 0xF4,
			0x4C, 0x4C, 0xAB, 0x13
		},
		.len = 12,
		.truncated_len = 12
	}
};

/** AES-256-CTR SHA1 test vector (12-byte IV) */
static const struct blockcipher_test_data aes_test_data_3_IV_12_bytes = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CTR,
	.cipher_key = {
		.data = {
			0x60, 0x3D, 0xEB, 0x10, 0x15, 0xCA, 0x71, 0xBE,
			0x2B, 0x73, 0xAE, 0xF0, 0x85, 0x7D, 0x77, 0x81,
			0x1F, 0x35, 0x2C, 0x07, 0x3B, 0x61, 0x08, 0xD7,
			0x2D, 0x98, 0x10, 0xA3, 0x09, 0x14, 0xDF, 0xF4
		},
		.len = 32
	},
	.iv = {
		.data = {
			0xF0, 0xF1, 0xF2, 0xF3, 0xF4, 0xF5, 0xF6, 0xF7,
			0xF8, 0xF9, 0xFA, 0xFB
		},
		.len = 12
	},
	.plaintext = {
		.data = plaintext_aes256ctr,
		.len = 64
	},
	.ciphertext = {
		.data = ciphertext64_aes256ctr_IV_12bytes,
		.len = 64
	},
	.auth_algo = RTE_CRYPTO_AUTH_SHA1_HMAC,
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD
		},
		.len = 20
	},
	.digest = {
		.data = {
			0x57, 0x9A, 0x52, 0x6E, 0x31, 0x17, 0x57, 0x49,
			0xE7, 0xA1, 0x88, 0x6C, 0x2E, 0x36, 0x67, 0x63,
			0x3F, 0x2D, 0xA3, 0xEF
		},
		.len = 20,
		.truncated_len = 12
	}
};
/** AES-128-CBC SHA1 test vector */
static const struct blockcipher_test_data aes_test_data_4 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CBC,
	.cipher_key = {
		.data = {
			0xE4, 0x23, 0x33, 0x8A, 0x35, 0x64, 0x61, 0xE2,
			0x49, 0x03, 0xDD, 0xC6, 0xB8, 0xCA, 0x55, 0x7A
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
			0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_common,
		.len = 512
	},
	.ciphertext = {
		.data = ciphertext512_aes128cbc,
		.len = 512
	},
	.auth_algo = RTE_CRYPTO_AUTH_SHA1_HMAC,
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD
		},
		.len = 20
	},
	.digest = {
		.data = {
			0x9A, 0x4F, 0x88, 0x1B, 0xB6, 0x8F, 0xD8, 0x60,
			0x42, 0x1A, 0x7D, 0x3D, 0xF5, 0x82, 0x80, 0xF1,
			0x18, 0x8C, 0x1D, 0x32
		},
		.len = 20,
		.truncated_len = 12
	}
};

/** AES-128-CBC SHA1 test vector (Digest encrypted mode) */
static const struct blockcipher_test_data aes_test_data_4_digest_enc = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CBC,
	.cipher_key = {
		.data = {
			0xE4, 0x23, 0x33, 0x8A, 0x35, 0x64, 0x61, 0xE2,
			0x49, 0x03, 0xDD, 0xC6, 0xB8, 0xCA, 0x55, 0x7A
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
			0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_common_digest_enc,
		.len = 512
	},
	.ciphertext = {
		.data = ciphertext512_aes128cbc_digest_enc,
		.len = 512
	},
	.auth_algo = RTE_CRYPTO_AUTH_SHA1_HMAC,
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD
		},
		.len = 20
	},
	.digest = {
		.data = {
			0x9A, 0x4F, 0x88, 0x1B, 0xB6, 0x8F, 0xD8, 0x60,
			0x42, 0x1A, 0x7D, 0x3D, 0xF5, 0x82, 0x80, 0xF1,
			0x18, 0x8C, 0x1D, 0x32
		},
		.len = 20,
	}
};

/** AES-128-CBC SHA256 test vector */
static const struct blockcipher_test_data aes_test_data_5 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CBC,
	.cipher_key = {
		.data = {
			0xE4, 0x23, 0x33, 0x8A, 0x35, 0x64, 0x61, 0xE2,
			0x49, 0x03, 0xDD, 0xC6, 0xB8, 0xCA, 0x55, 0x7A
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
			0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_common,
		.len = 512
	},
	.ciphertext = {
		.data = ciphertext512_aes128cbc,
		.len = 512
	},
	.auth_algo = RTE_CRYPTO_AUTH_SHA256_HMAC,
	.auth_key = {
		.data = {
			0x42, 0x1A, 0x7D, 0x3D, 0xF5, 0x82, 0x80, 0xF1,
			0xF1, 0x35, 0x5C, 0x3B, 0xDD, 0x9A, 0x65, 0xBA,
			0x58, 0x34, 0x85, 0x61, 0x1C, 0x42, 0x10, 0x76,
			0x9A, 0x4F, 0x88, 0x1B, 0xB6, 0x8F, 0xD8, 0x60
		},
		.len = 32
	},
	.digest = {
		.data = {
			0xC8, 0x57, 0x57, 0x31, 0x03, 0xE0, 0x03, 0x55,
			0x07, 0xC8, 0x9E, 0x7F, 0x48, 0x9A, 0x61, 0x9A,
			0x68, 0xEE, 0x03, 0x0E, 0x71, 0x75, 0xC7, 0xF4,
			0x2E, 0x45, 0x26, 0x32, 0x7C, 0x12, 0x15, 0x15
		},
		.len = 32,
		.truncated_len = 16
	}
};

/** AES-128-CBC SHA512 test vector */
static const struct blockcipher_test_data aes_test_data_6 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CBC,
	.cipher_key = {
		.data = {
			0xE4, 0x23, 0x33, 0x8A, 0x35, 0x64, 0x61, 0xE2,
			0x49, 0x03, 0xDD, 0xC6, 0xB8, 0xCA, 0x55, 0x7A
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
			0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_common,
		.len = 512
	},
	.ciphertext = {
		.data = ciphertext512_aes128cbc,
		.len = 512
	},
	.auth_algo = RTE_CRYPTO_AUTH_SHA512_HMAC,
	.auth_key = {
		.data = {
			0x42, 0x1A, 0x7D, 0x3D, 0xF5, 0x82, 0x80, 0xF1,
			0xF1, 0x35, 0x5C, 0x3B, 0xDD, 0x9A, 0x65, 0xBA,
			0x58, 0x34, 0x85, 0x65, 0x1C, 0x42, 0x50, 0x76,
			0x9A, 0xAF, 0x88, 0x1B, 0xB6, 0x8F, 0xF8, 0x60,
			0xA2, 0x5A, 0x7F, 0x3F, 0xF4, 0x72, 0x70, 0xF1,
			0xF5, 0x35, 0x4C, 0x3B, 0xDD, 0x90, 0x65, 0xB0,
			0x47, 0x3A, 0x75, 0x61, 0x5C, 0xA2, 0x10, 0x76,
			0x9A, 0xAF, 0x77, 0x5B, 0xB6, 0x7F, 0xF7, 0x60
		},
		.len = 64
	},
	.digest = {
		.data = {
			0x5D, 0x54, 0x66, 0xC1, 0x6E, 0xBC, 0x04, 0xB8,
			0x46, 0xB8, 0x08, 0x6E, 0xE0, 0xF0, 0x43, 0x48,
			0x37, 0x96, 0x9C, 0xC6, 0x9C, 0xC2, 0x1E, 0xE8,
			0xF2, 0x0C, 0x0B, 0xEF, 0x86, 0xA2, 0xE3, 0x70,
			0x95, 0xC8, 0xB3, 0x06, 0x47, 0xA9, 0x90, 0xE8,
			0xA0, 0xC6, 0x72, 0x69, 0x05, 0xC0, 0x0D, 0x0E,
			0x21, 0x96, 0x65, 0x93, 0x74, 0x43, 0x2A, 0x1D,
			0x2E, 0xBF, 0xC2, 0xC2, 0xEE, 0xCC, 0x2F, 0x0A
		},
		.len = 64,
		.truncated_len = 32
	}
};

/** AES-128-CBC XCBC test vector */
static const struct blockcipher_test_data aes_test_data_7 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CBC,
	.cipher_key = {
		.data = {
			0xE4, 0x23, 0x33, 0x8A, 0x35, 0x64, 0x61, 0xE2,
			0x49, 0x03, 0xDD, 0xC6, 0xB8, 0xCA, 0x55, 0x7A
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
			0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_common,
		.len = 512
	},
	.ciphertext = {
		.data = ciphertext512_aes128cbc,
		.len = 512
	},
	.auth_algo = RTE_CRYPTO_AUTH_AES_XCBC_MAC,
	.auth_key = {
		.data = {
			0x87, 0x61, 0x54, 0x53, 0xC4, 0x6D, 0xDD, 0x51,
			0xE1, 0x9F, 0x86, 0x64, 0x39, 0x0A, 0xE6, 0x59
		},
		.len = 16
	},
	.digest = {
		.data = {
			0xE0, 0xAC, 0x9A, 0xC4, 0x22, 0x64, 0x35, 0x89,
			0x77, 0x1D, 0x8B, 0x75
		},
		.len = 12,
		.truncated_len = 12
	}
};

/** AES-128-CBC SHA224 test vector */
static const struct blockcipher_test_data aes_test_data_8 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CBC,
	.cipher_key = {
		.data = {
			0xE4, 0x23, 0x33, 0x8A, 0x35, 0x64, 0x61, 0xE2,
			0x49, 0x03, 0xDD, 0xC6, 0xB8, 0xCA, 0x55, 0x7A
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
			0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_common,
		.len = 512
	},
	.ciphertext = {
		.data = ciphertext512_aes128cbc,
		.len = 512
	},
	.auth_algo = RTE_CRYPTO_AUTH_SHA224_HMAC,
	.auth_key = {
		.data = {
			0x42, 0x1A, 0x7D, 0x3D, 0xF5, 0x82, 0x80, 0xF1,
			0xF1, 0x35, 0x5C, 0x3B, 0xDD, 0x9A, 0x65, 0xBA,
			0x58, 0x34, 0x85, 0x65, 0x1C, 0x42, 0x50, 0x76,
			0x9A, 0xAF, 0x88, 0x1B, 0xB6, 0x8F, 0xF8, 0x60,
			0xA2, 0x5A, 0x7F, 0x3F, 0xF4, 0x72, 0x70, 0xF1,
			0xF5, 0x35, 0x4C, 0x3B, 0xDD, 0x90, 0x65, 0xB0,
			0x47, 0x3A, 0x75, 0x61, 0x5C, 0xA2, 0x10, 0x76,
			0x9A, 0xAF, 0x77, 0x5B, 0xB6, 0x7F, 0xF7, 0x60
		},
		.len = 64
	},
	.digest = {
		.data = {
			0xA3, 0xCA, 0xC7, 0x1D, 0xA8, 0x61, 0x30, 0x98,
			0x3B, 0x8F, 0x01, 0x19, 0xAE, 0x8D, 0xBD, 0x34,
			0x40, 0x63, 0xA8, 0x2F, 0xDF, 0x85, 0x2B, 0x7F,
			0x63, 0x7C, 0xDD, 0xB7
		},
		.len = 28,
		.truncated_len = 14
	}
};

/** AES-128-CBC SHA384 test vector */
static const struct blockcipher_test_data aes_test_data_9 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CBC,
	.cipher_key = {
		.data = {
			0xE4, 0x23, 0x33, 0x8A, 0x35, 0x64, 0x61, 0xE2,
			0x49, 0x03, 0xDD, 0xC6, 0xB8, 0xCA, 0x55, 0x7A
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
			0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_common,
		.len = 512
	},
	.ciphertext = {
		.data = ciphertext512_aes128cbc,
		.len = 512
	},
	.auth_algo = RTE_CRYPTO_AUTH_SHA384_HMAC,
	.auth_key = {
		.data = {
			0x42, 0x1A, 0x7D, 0x3D, 0xF5, 0x82, 0x80, 0xF1,
			0xF1, 0x35, 0x5C, 0x3B, 0xDD, 0x9A, 0x65, 0xBA,
			0x58, 0x34, 0x85, 0x65, 0x1C, 0x42, 0x50, 0x76,
			0x9A, 0xAF, 0x88, 0x1B, 0xB6, 0x8F, 0xF8, 0x60,
			0xA2, 0x5A, 0x7F, 0x3F, 0xF4, 0x72, 0x70, 0xF1,
			0xF5, 0x35, 0x4C, 0x3B, 0xDD, 0x90, 0x65, 0xB0,
			0x47, 0x3A, 0x75, 0x61, 0x5C, 0xA2, 0x10, 0x76,
			0x9A, 0xAF, 0x77, 0x5B, 0xB6, 0x7F, 0xF7, 0x60,
			0x42, 0x1A, 0x7D, 0x3D, 0xF5, 0x82, 0x80, 0xF1,
			0xF1, 0x35, 0x5C, 0x3B, 0xDD, 0x9A, 0x65, 0xBA,
			0x58, 0x34, 0x85, 0x65, 0x1C, 0x42, 0x50, 0x76,
			0x9A, 0xAF, 0x88, 0x1B, 0xB6, 0x8F, 0xF8, 0x60,
			0xA2, 0x5A, 0x7F, 0x3F, 0xF4, 0x72, 0x70, 0xF1,
			0xF5, 0x35, 0x4C, 0x3B, 0xDD, 0x90, 0x65, 0xB0,
			0x47, 0x3A, 0x75, 0x61, 0x5C, 0xA2, 0x10, 0x76,
			0x9A, 0xAF, 0x77, 0x5B, 0xB6, 0x7F, 0xF7, 0x60
		},
		.len = 128
	},
	.digest = {
		.data = {
			0x23, 0x60, 0xC8, 0xB1, 0x2D, 0x6C, 0x1E, 0x72,
			0x25, 0xAB, 0xF9, 0xC3, 0x9A, 0xA9, 0x4F, 0x8C,
			0x56, 0x38, 0x65, 0x0E, 0x74, 0xD5, 0x45, 0x9D,
			0xA3, 0xFD, 0x7E, 0x6D, 0x9E, 0x74, 0x88, 0x9D,
			0xA7, 0x12, 0x9D, 0xD8, 0x81, 0x3C, 0x86, 0x2F,
			0x4D, 0xF9, 0x6F, 0x0A, 0xB0, 0xC9, 0xEB, 0x0B
		},
		.len = 48,
		.truncated_len = 24
	}
};

static const uint8_t ciphertext512_aes192cbc[] = {
	0x45, 0xEE, 0x9A, 0xEA, 0x3C, 0x03, 0xFC, 0x4C,
	0x84, 0x36, 0xB0, 0xDA, 0xB0, 0xDC, 0xF3, 0x5B,
	0x75, 0xA7, 0xBE, 0x0E, 0xC0, 0x8D, 0x6C, 0xF8,
	0xC1, 0x0F, 0xD0, 0x35, 0x1D, 0x82, 0xAE, 0x7C,
	0x57, 0xC5, 0x7A, 0x55, 0x87, 0x1B, 0xD4, 0x03,
	0x0A, 0x64, 0xC9, 0xE0, 0xF4, 0xC7, 0x6F, 0x57,
	0x52, 0xC6, 0x73, 0xBA, 0x84, 0x0B, 0x5B, 0x89,
	0x21, 0xD2, 0x9B, 0x88, 0x68, 0xF5, 0xA9, 0x7F,
	0x3F, 0x49, 0xEB, 0xF4, 0xD4, 0x52, 0xD2, 0x64,
	0x80, 0xB2, 0x53, 0xDA, 0x19, 0xF6, 0x10, 0x24,
	0x23, 0x26, 0x7A, 0x7C, 0x07, 0x57, 0x4B, 0x0E,
	0x58, 0x49, 0x61, 0xD1, 0xDC, 0x9A, 0x32, 0x6B,
	0x0F, 0x43, 0x9E, 0x4D, 0xB4, 0x07, 0x4E, 0xB3,
	0x51, 0x74, 0xDE, 0x29, 0xBC, 0x98, 0xF9, 0xDF,
	0x78, 0x9A, 0x18, 0x9C, 0xD6, 0x7A, 0x55, 0x7C,
	0xE6, 0x1D, 0x5C, 0x1A, 0x99, 0xD2, 0xC3, 0x7B,
	0x9F, 0x96, 0x74, 0x2D, 0xE0, 0xEF, 0xD1, 0xE3,
	0x08, 0x9F, 0xAF, 0xE6, 0xED, 0xCA, 0xE1, 0xEA,
	0x23, 0x6F, 0x7C, 0x81, 0xA8, 0xC0, 0x5B, 0x8B,
	0x53, 0x90, 0x51, 0x2D, 0x0F, 0xF6, 0x7D, 0xA7,
	0x1C, 0xBD, 0x83, 0x84, 0x54, 0xA4, 0x15, 0xFB,
	0x3E, 0x25, 0xA7, 0x3A, 0x0A, 0x73, 0xD9, 0x88,
	0x6F, 0x80, 0x78, 0x95, 0x7F, 0x60, 0xAA, 0x86,
	0x8A, 0xFC, 0xDF, 0xC1, 0xCB, 0xDE, 0xBB, 0x25,
	0x52, 0x20, 0xC6, 0x79, 0xD4, 0x0F, 0x25, 0xE7,
	0xDB, 0xB2, 0x17, 0xA4, 0x6F, 0x3C, 0x6F, 0x91,
	0xF6, 0x44, 0x1E, 0xB6, 0x85, 0xBC, 0x7A, 0x14,
	0x10, 0x72, 0xBD, 0x16, 0x63, 0x39, 0x9E, 0x7B,
	0x84, 0x5B, 0x17, 0x61, 0xB1, 0x5D, 0x82, 0x0B,
	0x6D, 0x37, 0xD7, 0x79, 0xB8, 0x24, 0x91, 0x30,
	0x82, 0x91, 0x02, 0xB1, 0x18, 0x4B, 0xE0, 0xF4,
	0x13, 0x1B, 0xB2, 0x4C, 0xDA, 0xB8, 0x99, 0x96,
	0x83, 0x2F, 0xBE, 0x53, 0x8D, 0xDE, 0xFA, 0xAD,
	0xF6, 0x5C, 0xDB, 0xE5, 0x66, 0x26, 0x8F, 0x13,
	0x2B, 0x76, 0x47, 0x73, 0xDE, 0x1A, 0x74, 0xA6,
	0x30, 0xAF, 0x42, 0xA0, 0xE5, 0xD2, 0x8F, 0xC2,
	0xED, 0x3E, 0x9E, 0x29, 0x54, 0x3C, 0xDE, 0x9F,
	0x5D, 0x30, 0x2B, 0x63, 0xFB, 0xE3, 0xB1, 0x07,
	0xEE, 0x74, 0x4A, 0xAF, 0xB1, 0x20, 0x8D, 0xEC,
	0xE6, 0x78, 0x16, 0x8D, 0xA4, 0x6E, 0x34, 0x7D,
	0x47, 0xFB, 0x0B, 0xC1, 0x32, 0xD7, 0x0D, 0x6C,
	0x6F, 0x93, 0x9C, 0x5E, 0xEF, 0x1F, 0x9C, 0x45,
	0x80, 0x6B, 0x74, 0xA6, 0x81, 0xF2, 0xF6, 0xFA,
	0xAA, 0x9D, 0x4F, 0xCA, 0xB5, 0x90, 0x59, 0xB0,
	0x3B, 0xF2, 0xF0, 0x75, 0xFD, 0x8A, 0xD8, 0x97,
	0x65, 0x88, 0x56, 0x4C, 0x44, 0xDF, 0x73, 0xF7,
	0x56, 0x9C, 0x48, 0x7E, 0xB0, 0x1F, 0x1D, 0x7D,
	0x6A, 0x11, 0xF5, 0xC2, 0xF4, 0x17, 0xEF, 0x58,
	0xD8, 0x2A, 0xAF, 0x56, 0x2F, 0xCF, 0xEC, 0xA4,
	0x58, 0x8B, 0x60, 0xCE, 0xD4, 0x0F, 0x9C, 0x21,
	0xEC, 0x3E, 0x74, 0x7B, 0x81, 0x3D, 0x69, 0xC6,
	0x5E, 0x12, 0x83, 0xE9, 0xEF, 0x81, 0x58, 0x36,
	0x6A, 0x60, 0x0F, 0x54, 0x28, 0x11, 0xF9, 0x64,
	0x36, 0xAD, 0x79, 0xF5, 0x1C, 0x74, 0xD0, 0xC3,
	0x7B, 0x61, 0xE1, 0x92, 0xB0, 0x13, 0x91, 0x87,
	0x32, 0x1F, 0xF2, 0x5A, 0xDA, 0x25, 0x69, 0xEB,
	0xD7, 0x32, 0x7F, 0xF5, 0x23, 0x21, 0x54, 0x47,
	0x7B, 0x1B, 0x33, 0xB0, 0x3D, 0xF6, 0xE2, 0x7E,
	0x3E, 0xA2, 0x9E, 0xCA, 0x48, 0x0B, 0x4A, 0x29,
	0x81, 0xD4, 0x4E, 0xD5, 0x69, 0xFB, 0xCD, 0x37,
	0x8A, 0xC1, 0x5B, 0x50, 0xFF, 0xB5, 0x7D, 0x43,
	0x0F, 0xAE, 0xA6, 0xC2, 0xE5, 0x8F, 0x45, 0xB2,
	0x85, 0x99, 0x02, 0xA2, 0x9B, 0xBE, 0x90, 0x43,
	0x4F, 0x2F, 0x50, 0xE2, 0x77, 0x62, 0xD9, 0xCC
};

static const uint8_t ciphertext512_aes192cbc_digest_enc[] = {
	0x45, 0xee, 0x9a, 0xEA, 0x3C, 0x03, 0xFC, 0x4C,
	0x84, 0x36, 0xB0, 0xDA, 0xB0, 0xDC, 0xF3, 0x5B,
	0x75, 0xA7, 0xBE, 0x0E, 0xC0, 0x8D, 0x6C, 0xF8,
	0xC1, 0x0F, 0xD0, 0x35, 0x1D, 0x82, 0xAE, 0x7C,
	0x57, 0xC5, 0x7A, 0x55, 0x87, 0x1B, 0xD4, 0x03,
	0x0A, 0x64, 0xC9, 0xE0, 0xF4, 0xC7, 0x6F, 0x57,
	0x52, 0xC6, 0x73, 0xBA, 0x84, 0x0B, 0x5B, 0x89,
	0x21, 0xD2, 0x9B, 0x88, 0x68, 0xF5, 0xA9, 0x7F,
	0x3F, 0x49, 0xEB, 0xF4, 0xD4, 0x52, 0xD2, 0x64,
	0x80, 0xB2, 0x53, 0xDA, 0x19, 0xF6, 0x10, 0x24,
	0x23, 0x26, 0x7A, 0x7C, 0x07, 0x57, 0x4B, 0x0E,
	0x58, 0x49, 0x61, 0xD1, 0xDC, 0x9A, 0x32, 0x6B,
	0x0F, 0x43, 0x9E, 0x4D, 0xB4, 0x07, 0x4E, 0xB3,
	0x51, 0x74, 0xDE, 0x29, 0xBC, 0x98, 0xF9, 0xDF,
	0x78, 0x9A, 0x18, 0x9C, 0xD6, 0x7A, 0x55, 0x7C,
	0xE6, 0x1D, 0x5C, 0x1A, 0x99, 0xD2, 0xC3, 0x7B,
	0x9F, 0x96, 0x74, 0x2D, 0xE0, 0xEF, 0xD1, 0xE3,
	0x08, 0x9F, 0xAF, 0xE6, 0xED, 0xCA, 0xE1, 0xEA,
	0x23, 0x6F, 0x7C, 0x81, 0xA8, 0xC0, 0x5B, 0x8B,
	0x53, 0x90, 0x51, 0x2D, 0x0F, 0xF6, 0x7D, 0xA7,
	0x1C, 0xBD, 0x83, 0x84, 0x54, 0xA4, 0x15, 0xFB,
	0x3E, 0x25, 0xA7, 0x3A, 0x0A, 0x73, 0xD9, 0x88,
	0x6F, 0x80, 0x78, 0x95, 0x7F, 0x60, 0xAA, 0x86,
	0x8A, 0xFC, 0xDF, 0xC1, 0xCB, 0xDE, 0xBB, 0x25,
	0x52, 0x20, 0xC6, 0x79, 0xD4, 0x0F, 0x25, 0xE7,
	0xDB, 0xB2, 0x17, 0xA4, 0x6F, 0x3C, 0x6F, 0x91,
	0xF6, 0x44, 0x1E, 0xB6, 0x85, 0xBC, 0x7A, 0x14,
	0x10, 0x72, 0xBD, 0x16, 0x63, 0x39, 0x9E, 0x7B,
	0x84, 0x5B, 0x17, 0x61, 0xB1, 0x5D, 0x82, 0x0B,
	0x6D, 0x37, 0xD7, 0x79, 0xB8, 0x24, 0x91, 0x30,
	0x82, 0x91, 0x02, 0xB1, 0x18, 0x4B, 0xE0, 0xF4,
	0x13, 0x1B, 0xB2, 0x4C, 0xDA, 0xB8, 0x99, 0x96,
	0x83, 0x2F, 0xBE, 0x53, 0x8D, 0xDE, 0xFA, 0xAD,
	0xF6, 0x5C, 0xDB, 0xE5, 0x66, 0x26, 0x8F, 0x13,
	0x2B, 0x76, 0x47, 0x73, 0xDE, 0x1A, 0x74, 0xA6,
	0x30, 0xAF, 0x42, 0xA0, 0xE5, 0xD2, 0x8F, 0xC2,
	0xED, 0x3E, 0x9E, 0x29, 0x54, 0x3C, 0xDE, 0x9F,
	0x5D, 0x30, 0x2B, 0x63, 0xFB, 0xE3, 0xB1, 0x07,
	0xEE, 0x74, 0x4A, 0xAF, 0xB1, 0x20, 0x8D, 0xEC,
	0xE6, 0x78, 0x16, 0x8D, 0xA4, 0x6E, 0x34, 0x7D,
	0x47, 0xFB, 0x0B, 0xC1, 0x32, 0xD7, 0x0D, 0x6C,
	0x6F, 0x93, 0x9C, 0x5E, 0xEF, 0x1F, 0x9C, 0x45,
	0x80, 0x6B, 0x74, 0xA6, 0x81, 0xF2, 0xF6, 0xFA,
	0xAA, 0x9D, 0x4F, 0xCA, 0xB5, 0x90, 0x59, 0xB0,
	0x3B, 0xF2, 0xF0, 0x75, 0xFD, 0x8A, 0xD8, 0x97,
	0x65, 0x88, 0x56, 0x4C, 0x44, 0xDF, 0x73, 0xF7,
	0x56, 0x9C, 0x48, 0x7E, 0xB0, 0x1F, 0x1D, 0x7D,
	0x6A, 0x11, 0xF5, 0xC2, 0xF4, 0x17, 0xEF, 0x58,
	0xD8, 0x2A, 0xAF, 0x56, 0x2F, 0xCF, 0xEC, 0xA4,
	0x58, 0x8B, 0x60, 0xCE, 0xD4, 0x0F, 0x9C, 0x21,
	0xEC, 0x3E, 0x74, 0x7B, 0x81, 0x3D, 0x69, 0xC6,
	0x5E, 0x12, 0x83, 0xE9, 0xEF, 0x81, 0x58, 0x36,
	0x6A, 0x60, 0x0F, 0x54, 0x28, 0x11, 0xF9, 0x64,
	0x36, 0xAD, 0x79, 0xF5, 0x1C, 0x74, 0xD0, 0xC3,
	0x7B, 0x61, 0xE1, 0x92, 0xB0, 0x13, 0x91, 0x87,
	0x32, 0x1F, 0xF2, 0x5A, 0xDA, 0x25, 0x69, 0xEB,
	0xD7, 0x32, 0x7F, 0xF5, 0x23, 0x21, 0x54, 0x47,
	0x7B, 0x1B, 0x33, 0xB0, 0x3D, 0xF6, 0xE2, 0x7E,
	0x3E, 0xA2, 0x9E, 0xCA, 0x48, 0x0B, 0x4A, 0x29,
	0x81, 0xD4, 0x4E, 0xD5, 0x69, 0xFB, 0xCD, 0x37,
	0x8A, 0xC1, 0x5B, 0x50, 0xFF, 0xB5, 0x7D, 0x43,
	0x0F, 0xAE, 0xA6, 0xC2, 0xE5, 0x8F, 0x45, 0xB2,
	0x85, 0x99, 0x02, 0xA2, 0x9B, 0xBE, 0x90, 0x43,
	0x4F, 0x2F, 0x50, 0xE2, 0x77, 0x62, 0xD9, 0xCC,
	0x5D, 0x93, 0x6A, 0x97, 0xFB, 0x1D, 0x01, 0x17,
	0x90, 0x61, 0x41, 0xD5, 0x5C, 0x43, 0x60, 0xBC,
	0x21, 0x04, 0x5B, 0xB9, 0xFF, 0x75, 0xF8, 0x1F,
	0x5C, 0x85, 0x91, 0x08, 0x02, 0xA2, 0xFD, 0x88,
};

/** AES-192-CBC test vector */
static const struct blockcipher_test_data aes_test_data_10 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CBC,
	.cipher_key = {
		.data = {
			0xE4, 0x23, 0x33, 0x8A, 0x35, 0x64, 0x61, 0xE2,
			0x49, 0x03, 0xDD, 0xC6, 0xB8, 0xCA, 0x55, 0x7A,
			0xD4, 0xC3, 0xA3, 0xAA, 0x33, 0x62, 0x61, 0xE0
		},
		.len = 24
	},
	.iv = {
		.data = {
			0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
			0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_common,
		.len = 512
	},
	.ciphertext = {
		.data = ciphertext512_aes192cbc,
		.len = 512
	}
};

/** AES-192-CBC SHA1 test vector (Digest encrypted mode) */
static const struct blockcipher_test_data aes_test_data_10_digest_enc = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CBC,
	.cipher_key = {
		.data = {
			0xE4, 0x23, 0x33, 0x8A, 0x35, 0x64, 0x61, 0xE2,
			0x49, 0x03, 0xDD, 0xC6, 0xB8, 0xCA, 0x55, 0x7A,
			0xD4, 0xC3, 0xA3, 0xAA, 0x33, 0x62, 0x61, 0xE0
		},
		.len = 24
	},
	.iv = {
		.data = {
			0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
			0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_common_digest_enc,
		.len = 512
	},
	.ciphertext = {
		.data = ciphertext512_aes192cbc_digest_enc,
		.len = 512
	},
	.auth_algo = RTE_CRYPTO_AUTH_SHA1_HMAC,
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD
		},
		.len = 20
	},
	.digest = {
		.data = {
			0x9A, 0x4F, 0x88, 0x1B, 0xB6, 0x8F, 0xD8, 0x60,
			0x42, 0x1A, 0x7D, 0x3D, 0xF5, 0x82, 0x80, 0xF1,
			0x18, 0x8C, 0x1D, 0x32
		},
		.len = 20,
	}
};

static const uint8_t ciphertext512_aes256cbc[] = {
	0xF3, 0xDD, 0xF0, 0x0B, 0xFF, 0xA2, 0x6A, 0x04,
	0xBE, 0xDA, 0x52, 0xA6, 0xFE, 0x6B, 0xA6, 0xA7,
	0x48, 0x1D, 0x7D, 0x98, 0x65, 0xDB, 0xEF, 0x06,
	0x26, 0xB5, 0x8E, 0xEB, 0x05, 0x0E, 0x77, 0x98,
	0x17, 0x8E, 0xD0, 0xD4, 0x7B, 0x92, 0x8F, 0x5C,
	0xD0, 0x74, 0x5C, 0xA8, 0x4B, 0x54, 0xB6, 0x2F,
	0x83, 0x72, 0x2C, 0xFF, 0x72, 0xE9, 0xE4, 0x15,
	0x4C, 0x32, 0xAF, 0xC8, 0xC9, 0x89, 0x3C, 0x6E,
	0x31, 0xD5, 0xC0, 0x16, 0xC0, 0x31, 0x7D, 0x11,
	0xAB, 0xCB, 0xDE, 0xD2, 0xD6, 0xAA, 0x76, 0x5E,
	0xBA, 0xF6, 0xE2, 0x92, 0xCB, 0x86, 0x07, 0xFA,
	0xD4, 0x9E, 0x83, 0xED, 0xFD, 0xB8, 0x70, 0x54,
	0x6B, 0xBE, 0xEC, 0x72, 0xDD, 0x28, 0x5E, 0x95,
	0x78, 0xA5, 0x28, 0x43, 0x3D, 0x6D, 0xB1, 0xD9,
	0x69, 0x1F, 0xC9, 0x66, 0x0E, 0x32, 0x44, 0x08,
	0xD2, 0xAE, 0x2C, 0x43, 0xF2, 0xD0, 0x7D, 0x26,
	0x70, 0xE5, 0xA1, 0xCA, 0x37, 0xE9, 0x7D, 0xC7,
	0xA3, 0xFA, 0x81, 0x91, 0x64, 0xAA, 0x64, 0x91,
	0x9A, 0x95, 0x2D, 0xC9, 0xF9, 0xCE, 0xFE, 0x9F,
	0xC4, 0xD8, 0x81, 0xBE, 0x57, 0x84, 0xC5, 0x02,
	0xDB, 0x30, 0xC1, 0xD9, 0x0E, 0xA0, 0xA6, 0x00,
	0xD6, 0xF3, 0x52, 0x7E, 0x0D, 0x23, 0x6B, 0x2B,
	0x34, 0x99, 0x1F, 0x70, 0x27, 0x6D, 0x58, 0x84,
	0x93, 0x77, 0xB8, 0x3E, 0xF1, 0x71, 0x58, 0x42,
	0x8B, 0x2B, 0xC8, 0x6D, 0x05, 0x84, 0xFF, 0x4E,
	0x85, 0xEF, 0x4A, 0x9D, 0x91, 0x6A, 0xD5, 0xE1,
	0xAF, 0x01, 0xEB, 0x83, 0x8F, 0x23, 0x7C, 0x7F,
	0x12, 0x91, 0x05, 0xF0, 0x4E, 0xD9, 0x17, 0x62,
	0x75, 0xBB, 0xAC, 0x97, 0xEE, 0x3B, 0x4E, 0xC7,
	0xE5, 0x92, 0xF8, 0x9D, 0x4C, 0xF9, 0xEE, 0x55,
	0x18, 0xBB, 0xCC, 0xB4, 0xF2, 0x59, 0xB9, 0xFC,
	0x7A, 0x0F, 0x98, 0xD4, 0x8B, 0xFE, 0xF7, 0x83,
	0x46, 0xE2, 0x83, 0x33, 0x3E, 0x95, 0x8D, 0x17,
	0x1E, 0x85, 0xF8, 0x8C, 0x51, 0xB0, 0x6C, 0xB5,
	0x5E, 0x95, 0xBA, 0x4B, 0x69, 0x1B, 0x48, 0x69,
	0x0B, 0x8F, 0xA5, 0x18, 0x13, 0xB9, 0x77, 0xD1,
	0x80, 0x32, 0x32, 0x6D, 0x53, 0xA1, 0x95, 0x40,
	0x96, 0x8A, 0xCC, 0xA3, 0x69, 0xF8, 0x9F, 0xB5,
	0x8E, 0xD2, 0x68, 0x07, 0x4F, 0xA7, 0xEC, 0xF8,
	0x20, 0x21, 0x58, 0xF8, 0xD8, 0x9E, 0x5F, 0x40,
	0xBA, 0xB9, 0x76, 0x57, 0x3B, 0x17, 0xAD, 0xEE,
	0xCB, 0xDF, 0x07, 0xC1, 0xDF, 0x66, 0xA8, 0x0D,
	0xC2, 0xCE, 0x8F, 0x79, 0xC3, 0x32, 0xE0, 0x8C,
	0xFE, 0x5A, 0xF3, 0x55, 0x27, 0x73, 0x6F, 0xA1,
	0x54, 0xC6, 0xFC, 0x28, 0x9D, 0xBE, 0x97, 0xB9,
	0x54, 0x97, 0x72, 0x3A, 0x61, 0xAF, 0x6F, 0xDE,
	0xF8, 0x0E, 0xBB, 0x6B, 0x96, 0x84, 0xDD, 0x9B,
	0x62, 0xBA, 0x47, 0xB5, 0xC9, 0x3B, 0x4E, 0x8C,
	0x78, 0x2A, 0xCC, 0x0A, 0x69, 0x54, 0x25, 0x5E,
	0x8B, 0xAC, 0x56, 0xD9, 0xFE, 0x48, 0xBA, 0xCE,
	0xA9, 0xCE, 0xA6, 0x1D, 0xBF, 0x3E, 0x3C, 0x66,
	0x40, 0x71, 0x79, 0xAD, 0x5B, 0x26, 0xAD, 0xBE,
	0x58, 0x13, 0x64, 0x60, 0x7C, 0x05, 0xFC, 0xE3,
	0x51, 0x7A, 0xF2, 0xCC, 0x54, 0x16, 0x2C, 0xA4,
	0xCE, 0x5F, 0x59, 0x12, 0x77, 0xEB, 0xD9, 0x23,
	0xE3, 0x86, 0xFB, 0xD7, 0x48, 0x76, 0x9D, 0xE3,
	0x89, 0x87, 0x39, 0xFA, 0x7B, 0x21, 0x0B, 0x76,
	0xB2, 0xED, 0x1C, 0x27, 0x4B, 0xD5, 0x27, 0x05,
	0x8C, 0x7D, 0x58, 0x6C, 0xCA, 0xA5, 0x54, 0x9A,
	0x0F, 0xCB, 0xE9, 0x88, 0x31, 0xAD, 0x49, 0xEE,
	0x38, 0xFB, 0xC9, 0xFB, 0xB4, 0x7A, 0x00, 0x58,
	0x20, 0x32, 0xD3, 0x53, 0x5A, 0xDD, 0x74, 0x95,
	0x60, 0x59, 0x09, 0xAE, 0x7E, 0xEC, 0x74, 0xA3,
	0xB7, 0x1C, 0x6D, 0xF2, 0xAE, 0x79, 0xA4, 0x7C
};

static const uint8_t ciphertext512_aes256cbc_digest_enc[] = {
	0xF3, 0xDD, 0xF0, 0x0B, 0xFF, 0xA2, 0x6A, 0x04,
	0xBE, 0xDA, 0x52, 0xA6, 0xFE, 0x6B, 0xA6, 0xA7,
	0x48, 0x1D, 0x7D, 0x98, 0x65, 0xDB, 0xEF, 0x06,
	0x26, 0xB5, 0x8E, 0xEB, 0x05, 0x0E, 0x77, 0x98,
	0x17, 0x8E, 0xD0, 0xD4, 0x7B, 0x92, 0x8F, 0x5C,
	0xD0, 0x74, 0x5C, 0xA8, 0x4B, 0x54, 0xB6, 0x2F,
	0x83, 0x72, 0x2C, 0xFF, 0x72, 0xE9, 0xE4, 0x15,
	0x4C, 0x32, 0xAF, 0xC8, 0xC9, 0x89, 0x3C, 0x6E,
	0x31, 0xD5, 0xC0, 0x16, 0xC0, 0x31, 0x7D, 0x11,
	0xAB, 0xCB, 0xDE, 0xD2, 0xD6, 0xAA, 0x76, 0x5E,
	0xBA, 0xF6, 0xE2, 0x92, 0xCB, 0x86, 0x07, 0xFA,
	0xD4, 0x9E, 0x83, 0xED, 0xFD, 0xB8, 0x70, 0x54,
	0x6B, 0xBE, 0xEC, 0x72, 0xDD, 0x28, 0x5E, 0x95,
	0x78, 0xA5, 0x28, 0x43, 0x3D, 0x6D, 0xB1, 0xD9,
	0x69, 0x1F, 0xC9, 0x66, 0x0E, 0x32, 0x44, 0x08,
	0xD2, 0xAE, 0x2C, 0x43, 0xF2, 0xD0, 0x7D, 0x26,
	0x70, 0xE5, 0xA1, 0xCA, 0x37, 0xE9, 0x7D, 0xC7,
	0xA3, 0xFA, 0x81, 0x91, 0x64, 0xAA, 0x64, 0x91,
	0x9A, 0x95, 0x2D, 0xC9, 0xF9, 0xCE, 0xFE, 0x9F,
	0xC4, 0xD8, 0x81, 0xBE, 0x57, 0x84, 0xC5, 0x02,
	0xDB, 0x30, 0xC1, 0xD9, 0x0E, 0xA0, 0xA6, 0x00,
	0xD6, 0xF3, 0x52, 0x7E, 0x0D, 0x23, 0x6B, 0x2B,
	0x34, 0x99, 0x1F, 0x70, 0x27, 0x6D, 0x58, 0x84,
	0x93, 0x77, 0xB8, 0x3E, 0xF1, 0x71, 0x58, 0x42,
	0x8B, 0x2B, 0xC8, 0x6D, 0x05, 0x84, 0xFF, 0x4E,
	0x85, 0xEF, 0x4A, 0x9D, 0x91, 0x6A, 0xD5, 0xE1,
	0xAF, 0x01, 0xEB, 0x83, 0x8F, 0x23, 0x7C, 0x7F,
	0x12, 0x91, 0x05, 0xF0, 0x4E, 0xD9, 0x17, 0x62,
	0x75, 0xBB, 0xAC, 0x97, 0xEE, 0x3B, 0x4E, 0xC7,
	0xE5, 0x92, 0xF8, 0x9D, 0x4C, 0xF9, 0xEE, 0x55,
	0x18, 0xBB, 0xCC, 0xB4, 0xF2, 0x59, 0xB9, 0xFC,
	0x7A, 0x0F, 0x98, 0xD4, 0x8B, 0xFE, 0xF7, 0x83,
	0x46, 0xE2, 0x83, 0x33, 0x3E, 0x95, 0x8D, 0x17,
	0x1E, 0x85, 0xF8, 0x8C, 0x51, 0xB0, 0x6C, 0xB5,
	0x5E, 0x95, 0xBA, 0x4B, 0x69, 0x1B, 0x48, 0x69,
	0x0B, 0x8F, 0xA5, 0x18, 0x13, 0xB9, 0x77, 0xD1,
	0x80, 0x32, 0x32, 0x6D, 0x53, 0xA1, 0x95, 0x40,
	0x96, 0x8A, 0xCC, 0xA3, 0x69, 0xF8, 0x9F, 0xB5,
	0x8E, 0xD2, 0x68, 0x07, 0x4F, 0xA7, 0xEC, 0xF8,
	0x20, 0x21, 0x58, 0xF8, 0xD8, 0x9E, 0x5F, 0x40,
	0xBA, 0xB9, 0x76, 0x57, 0x3B, 0x17, 0xAD, 0xEE,
	0xCB, 0xDF, 0x07, 0xC1, 0xDF, 0x66, 0xA8, 0x0D,
	0xC2, 0xCE, 0x8F, 0x79, 0xC3, 0x32, 0xE0, 0x8C,
	0xFE, 0x5A, 0xF3, 0x55, 0x27, 0x73, 0x6F, 0xA1,
	0x54, 0xC6, 0xFC, 0x28, 0x9D, 0xBE, 0x97, 0xB9,
	0x54, 0x97, 0x72, 0x3A, 0x61, 0xAF, 0x6F, 0xDE,
	0xF8, 0x0E, 0xBB, 0x6B, 0x96, 0x84, 0xDD, 0x9B,
	0x62, 0xBA, 0x47, 0xB5, 0xC9, 0x3B, 0x4E, 0x8C,
	0x78, 0x2A, 0xCC, 0x0A, 0x69, 0x54, 0x25, 0x5E,
	0x8B, 0xAC, 0x56, 0xD9, 0xFE, 0x48, 0xBA, 0xCE,
	0xA9, 0xCE, 0xA6, 0x1D, 0xBF, 0x3E, 0x3C, 0x66,
	0x40, 0x71, 0x79, 0xAD, 0x5B, 0x26, 0xAD, 0xBE,
	0x58, 0x13, 0x64, 0x60, 0x7C, 0x05, 0xFC, 0xE3,
	0x51, 0x7A, 0xF2, 0xCC, 0x54, 0x16, 0x2C, 0xA4,
	0xCE, 0x5F, 0x59, 0x12, 0x77, 0xEB, 0xD9, 0x23,
	0xE3, 0x86, 0xFB, 0xD7, 0x48, 0x76, 0x9D, 0xE3,
	0x89, 0x87, 0x39, 0xFA, 0x7B, 0x21, 0x0B, 0x76,
	0xB2, 0xED, 0x1C, 0x27, 0x4B, 0xD5, 0x27, 0x05,
	0x8C, 0x7D, 0x58, 0x6C, 0xCA, 0xA5, 0x54, 0x9A,
	0x0F, 0xCB, 0xE9, 0x88, 0x31, 0xAD, 0x49, 0xEE,
	0x38, 0xFB, 0xC9, 0xFB, 0xB4, 0x7A, 0x00, 0x58,
	0x20, 0x32, 0xD3, 0x53, 0x5A, 0xDD, 0x74, 0x95,
	0x60, 0x59, 0x09, 0xAE, 0x7E, 0xEC, 0x74, 0xA3,
	0xB7, 0x1C, 0x6D, 0xF2, 0xAE, 0x79, 0xA4, 0x7C,
	0x50, 0xB1, 0x8D, 0x56, 0x22, 0x75, 0xD6, 0x8F,
	0x93, 0xB5, 0xED, 0xE8, 0x3D, 0x20, 0x4F, 0x9F,
	0x80, 0x2F, 0x56, 0x95, 0xA0, 0x80, 0xC6, 0xC2,
	0xA8, 0x27, 0xFB, 0x48, 0x96, 0x58, 0xA1, 0x93,
};

/** AES-256-CBC test vector */
static const struct blockcipher_test_data aes_test_data_11 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CBC,
	.cipher_key = {
		.data = {
			0xE4, 0x23, 0x33, 0x8A, 0x35, 0x64, 0x61, 0xE2,
			0x49, 0x03, 0xDD, 0xC6, 0xB8, 0xCA, 0x55, 0x7A,
			0xD4, 0xC3, 0xA3, 0xAA, 0x33, 0x62, 0x61, 0xE0,
			0x37, 0x07, 0xB8, 0x23, 0xA2, 0xA3, 0xB5, 0x8D
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
			0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_common,
		.len = 512
	},
	.ciphertext = {
		.data = ciphertext512_aes256cbc,
		.len = 512
	}
};

/** AES-256-CBC SHA1 test vector (Digest encrypted mode) */
static const struct blockcipher_test_data aes_test_data_11_digest_enc = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CBC,
	.cipher_key = {
		.data = {
			0xE4, 0x23, 0x33, 0x8A, 0x35, 0x64, 0x61, 0xE2,
			0x49, 0x03, 0xDD, 0xC6, 0xB8, 0xCA, 0x55, 0x7A,
			0xD4, 0xC3, 0xA3, 0xAA, 0x33, 0x62, 0x61, 0xE0,
			0x37, 0x07, 0xB8, 0x23, 0xA2, 0xA3, 0xB5, 0x8D
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
			0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_common_digest_enc,
		.len = 512
	},
	.ciphertext = {
		.data = ciphertext512_aes256cbc_digest_enc,
		.len = 512
	},
	.auth_algo = RTE_CRYPTO_AUTH_SHA1_HMAC,
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD
		},
		.len = 20
	},
	.digest = {
		.data = {
			0x9A, 0x4F, 0x88, 0x1B, 0xB6, 0x8F, 0xD8, 0x60,
			0x42, 0x1A, 0x7D, 0x3D, 0xF5, 0x82, 0x80, 0xF1,
			0x18, 0x8C, 0x1D, 0x32
		},
		.len = 20,
	}
};

/** AES-128-CBC SHA256 HMAC test vector (160 bytes) */
static const struct blockcipher_test_data aes_test_data_12 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CBC,
	.cipher_key = {
		.data = {
			0xE4, 0x23, 0x33, 0x8A, 0x35, 0x64, 0x61, 0xE2,
			0x49, 0x03, 0xDD, 0xC6, 0xB8, 0xCA, 0x55, 0x7A
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
			0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_common,
		.len = 160
	},
	.ciphertext = {
		.data = ciphertext512_aes128cbc,
		.len = 160
	},
	.auth_algo = RTE_CRYPTO_AUTH_SHA256_HMAC,
	.auth_key = {
		.data = {
			0x42, 0x1A, 0x7D, 0x3D, 0xF5, 0x82, 0x80, 0xF1,
			0xF1, 0x35, 0x5C, 0x3B, 0xDD, 0x9A, 0x65, 0xBA,
			0x58, 0x34, 0x85, 0x61, 0x1C, 0x42, 0x10, 0x76,
			0x9A, 0x4F, 0x88, 0x1B, 0xB6, 0x8F, 0xD8, 0x60
		},
		.len = 32
	},
	.digest = {
		.data = {
			0x92, 0xEC, 0x65, 0x9A, 0x52, 0xCC, 0x50, 0xA5,
			0xEE, 0x0E, 0xDF, 0x1E, 0xA4, 0xC9, 0xC1, 0x04,
			0xD5, 0xDC, 0x78, 0x90, 0xF4, 0xE3, 0x35, 0x62,
			0xAD, 0x95, 0x45, 0x28, 0x5C, 0xF8, 0x8C, 0x0B
		},
		.len = 32,
		.truncated_len = 16
	}
};

/** AES-128-CBC SHA1 HMAC test vector (160 bytes) */
static const struct blockcipher_test_data aes_test_data_13 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CBC,
	.cipher_key = {
		.data = {
			0xE4, 0x23, 0x33, 0x8A, 0x35, 0x64, 0x61, 0xE2,
			0x49, 0x03, 0xDD, 0xC6, 0xB8, 0xCA, 0x55, 0x7A
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
			0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_common,
		.len = 160
	},
	.ciphertext = {
		.data = ciphertext512_aes128cbc,
		.len = 160
	},
	.auth_algo = RTE_CRYPTO_AUTH_SHA1_HMAC,
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD
		},
		.len = 20
	},
	.digest = {
		.data = {
			0x4F, 0x16, 0xEA, 0xF7, 0x4A, 0x88, 0xD3, 0xE0,
			0x0E, 0x12, 0x8B, 0xE7, 0x05, 0xD0, 0x86, 0x48,
			0x22, 0x43, 0x30, 0xA7
		},
		.len = 20,
		.truncated_len = 12
	}
};

/** AES-128-CBC SHA1 test vector (Digest encrypted mode) */
static const struct blockcipher_test_data aes_test_data_13_digest_enc = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CBC,
	.cipher_key = {
		.data = {
			0xE4, 0x23, 0x33, 0x8A, 0x35, 0x64, 0x61, 0xE2,
			0x49, 0x03, 0xDD, 0xC6, 0xB8, 0xCA, 0x55, 0x7A
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
			0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_common_short_digest_enc,
		.len = 50
	},
	.ciphertext = {
		.data = ciphertext512_aes128cbc_short_digest_enc,
		.len = 50
	},
	.auth_algo = RTE_CRYPTO_AUTH_SHA1_HMAC,
	.auth_key = {
		.data = {
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA,
			0xAA, 0xAA, 0xAA, 0xAA
		},
		.len = 20
	},
	.digest = {
		.data = {
			0x12, 0x5D, 0x73, 0x42, 0xB9, 0xAC, 0x11, 0xCD,
			0x91, 0xA3, 0x9A, 0xF4, 0x8A, 0xA1, 0x7B, 0x4F,
			0x63, 0xF1, 0x75, 0xD3
		},
		.len = 20,
	}
};

/** XTS-AES-128 test vector (2 keys * 128 bit) */
static const uint8_t plaintext_aes128xts_16bytes[] = {
	0xEB, 0xAB, 0xCE, 0x95, 0xB1, 0x4D, 0x3C, 0x8D,
	0x6F, 0xB3, 0x50, 0x39, 0x07, 0x90, 0x31, 0x1C
};
static const uint8_t ciphertext_aes128xts_16bytes[] = {
	0x77, 0x8A, 0xE8, 0xB4, 0x3C, 0xB9, 0x8D, 0x5A,
	0x82, 0x50, 0x81, 0xD5, 0xBE, 0x47, 0x1C, 0x63
};
static const struct
blockcipher_test_data aes_test_data_xts_key_32_pt_16 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_XTS,
	.cipher_key = {
		.data = {
			0xA1, 0xB9, 0x0C, 0xBA, 0x3F, 0x06, 0xAC, 0x35,
			0x3B, 0x2C, 0x34, 0x38, 0x76, 0x08, 0x17, 0x62,
			0x09, 0x09, 0x23, 0x02, 0x6E, 0x91, 0x77, 0x18,
			0x15, 0xF2, 0x9D, 0xAB, 0x01, 0x93, 0x2F, 0x2F
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x4F, 0xAE, 0xF7, 0x11, 0x7C, 0xDA, 0x59, 0xC6,
			0x6E, 0x4B, 0x92, 0x01, 0x3E, 0x76, 0x8A, 0xD5
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes128xts_16bytes,
		.len = 16
	},
	.ciphertext = {
		.data = ciphertext_aes128xts_16bytes,
		.len = 16
	}
};
static const uint8_t plaintext_aes128xts_32bytes[] = {
	0xB0, 0x4D, 0x84, 0xDA, 0x85, 0x6B, 0x9A, 0x59,
	0xCE, 0x2D, 0x62, 0x67, 0x46, 0xF6, 0x89, 0xA8,
	0x05, 0x1D, 0xAC, 0xD6, 0xBC, 0xE3, 0xB9, 0x90,
	0xAA, 0x90, 0x1E, 0x40, 0x30, 0x64, 0x88, 0x79
};
static const uint8_t ciphertext_aes128xts_32bytes[] = {
	0xF9, 0x41, 0x03, 0x9E, 0xBA, 0xB8, 0xCA, 0xC3,
	0x9D, 0x59, 0x24, 0x7C, 0xBB, 0xCB, 0x4D, 0x81,
	0x6C, 0x72, 0x6D, 0xAE, 0xD1, 0x15, 0x77, 0x69,
	0x2C, 0x55, 0xE4, 0xAC, 0x6D, 0x3E, 0x68, 0x20
};
static const struct
blockcipher_test_data aes_test_data_xts_key_32_pt_32 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_XTS,
	.cipher_key = {
		.data = {
			0x78, 0x3A, 0x83, 0xEC, 0x52, 0xA2, 0x74, 0x05,
			0xDF, 0xF9, 0xDE, 0x4C, 0x57, 0xF9, 0xC9, 0x79,
			0xB3, 0x60, 0xB6, 0xA5, 0xDF, 0x88, 0xD6, 0x7E,
			0xC1, 0xA0, 0x52, 0xE6, 0xF5, 0x82, 0xA7, 0x17
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x88, 0x6E, 0x97, 0x5B, 0x29, 0xBD, 0xF6, 0xF0,
			0xC0, 0x1B, 0xB4, 0x7F, 0x61, 0xF6, 0xF0, 0xF5
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes128xts_32bytes,
		.len = 32
	},
	.ciphertext = {
		.data = ciphertext_aes128xts_32bytes,
		.len = 32
	}
};

/** XTS-AES-256 test vector (2 keys * 256 bit) */
/* Encryption */
static const uint8_t plaintext_aes256xts_32bytes[] = {
	0x2E, 0xED, 0xEA, 0x52, 0xCD, 0x82, 0x15, 0xE1,
	0xAC, 0xC6, 0x47, 0xE8, 0x10, 0xBB, 0xC3, 0x64,
	0x2E, 0x87, 0x28, 0x7F, 0x8D, 0x2E, 0x57, 0xE3,
	0x6C, 0x0A, 0x24, 0xFB, 0xC1, 0x2A, 0x20, 0x2E
};
static const uint8_t ciphertext_aes256xts_32bytes[] = {
	0xCB, 0xAA, 0xD0, 0xE2, 0xF6, 0xCE, 0xA3, 0xF5,
	0x0B, 0x37, 0xF9, 0x34, 0xD4, 0x6A, 0x9B, 0x13,
	0x0B, 0x9D, 0x54, 0xF0, 0x7E, 0x34, 0xF3, 0x6A,
	0xF7, 0x93, 0xE8, 0x6F, 0x73, 0xC6, 0xD7, 0xDB
};
static const struct
blockcipher_test_data aes_test_data_xts_key_64_pt_32 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_XTS,
	.cipher_key = {
		.data = {
			0x1E, 0xA6, 0x61, 0xC5, 0x8D, 0x94, 0x3A, 0x0E,
			0x48, 0x01, 0xE4, 0x2F, 0x4B, 0x09, 0x47, 0x14,
			0x9E, 0x7F, 0x9F, 0x8E, 0x3E, 0x68, 0xD0, 0xC7,
			0x50, 0x52, 0x10, 0xBD, 0x31, 0x1A, 0x0E, 0x7C,
			0xD6, 0xE1, 0x3F, 0xFD, 0xF2, 0x41, 0x8D, 0x8D,
			0x19, 0x11, 0xC0, 0x04, 0xCD, 0xA5, 0x8D, 0xA3,
			0xD6, 0x19, 0xB7, 0xE2, 0xB9, 0x14, 0x1E, 0x58,
			0x31, 0x8E, 0xEA, 0x39, 0x2C, 0xF4, 0x1B, 0x08
		},
		.len = 64
	},
	.iv = {
		.data = {
			0xAD, 0xF8, 0xD9, 0x26, 0x27, 0x46, 0x4A, 0xD2,
			0xF0, 0x42, 0x8E, 0x84, 0xA9, 0xF8, 0x75, 0x64
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes256xts_32bytes,
		.len = 32
	},
	.ciphertext = {
		.data = ciphertext_aes256xts_32bytes,
		.len = 32
	}
};
static const uint8_t plaintext_aes256xts_48bytes[] = {
	0xBB, 0xB2, 0x34, 0xDB, 0x01, 0xBE, 0x79, 0xD3,
	0xDC, 0x7D, 0xCF, 0x49, 0xBC, 0x53, 0xB8, 0xEF,
	0xC6, 0x2F, 0xE7, 0x17, 0x94, 0x39, 0x06, 0x1D,
	0x73, 0xA6, 0xB2, 0x0E, 0xDB, 0x9B, 0x7D, 0xA0,
	0x45, 0x0B, 0x19, 0xF0, 0x20, 0x82, 0x02, 0x09,
	0xAF, 0xE0, 0x81, 0x12, 0xAE, 0x4A, 0xFD, 0xD3
};
static const uint8_t ciphertext_aes256xts_48bytes[] = {
	0xCB, 0xF6, 0x88, 0x48, 0xC4, 0x20, 0x49, 0xEF,
	0xB1, 0x46, 0xE2, 0xD1, 0xE4, 0x11, 0x5F, 0x62,
	0xE4, 0xFA, 0xA4, 0xEF, 0xFF, 0x19, 0x8F, 0x1A,
	0x0A, 0xA0, 0xC9, 0x8B, 0xA0, 0x2C, 0xC4, 0x4D,
	0xA3, 0x76, 0xD7, 0x62, 0xD6, 0x15, 0x1F, 0x23,
	0x45, 0x87, 0xBF, 0x12, 0x8B, 0x6A, 0x7F, 0xFA
};
static const struct
blockcipher_test_data aes_test_data_xts_key_64_pt_48 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_XTS,
	.cipher_key = {
		.data = {
			0x7F, 0xB0, 0x92, 0x2F, 0xCE, 0x09, 0xED, 0xDD,
			0x36, 0x65, 0xA1, 0x1F, 0x52, 0x35, 0xD5, 0x1E,
			0xF7, 0x72, 0x06, 0xA7, 0xDE, 0x45, 0x47, 0x75,
			0xB6, 0x9D, 0xCC, 0x54, 0x59, 0xAC, 0xDB, 0x24,
			0xCC, 0xF0, 0x5C, 0x41, 0x5A, 0xF5, 0xAB, 0x8A,
			0x06, 0x62, 0x3D, 0x19, 0x8D, 0x5B, 0x91, 0x85,
			0x95, 0xA9, 0xDC, 0xAA, 0xF5, 0x39, 0x2E, 0xE7,
			0x17, 0xC1, 0x04, 0x7F, 0x2F, 0x08, 0xF6, 0x2B
		},
		.len = 64
	},
	.iv = {
		.data = {
			0x8E, 0xA3, 0x63, 0x8B, 0x9D, 0x40, 0x62, 0xF1,
			0x69, 0x19, 0x6F, 0xF4, 0x55, 0x5A, 0xD0, 0xAF
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes256xts_48bytes,
		.len = 48
	},
	.ciphertext = {
		.data = ciphertext_aes256xts_48bytes,
		.len = 48
	}
};

static const uint8_t plaintext_aes256xts_512bytes[] = {
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79
};
static const uint8_t ciphertext_aes256xts_512bytes[] = {
	0xD3, 0x94, 0x3E, 0xC9, 0xD3, 0x43, 0x13, 0xD3,
	0x07, 0x7E, 0x51, 0x11, 0x97, 0xA5, 0xB1, 0xB2,
	0xB0, 0x55, 0xF8, 0xD6, 0xA2, 0x10, 0x78, 0x76,
	0x37, 0x95, 0x4F, 0x25, 0x99, 0xCC, 0x12, 0xD6,
	0xBB, 0x2A, 0x24, 0xF4, 0x2D, 0x82, 0xFF, 0x56,
	0xEC, 0x55, 0x3B, 0xDD, 0xF9, 0xE1, 0xC8, 0x48,
	0x68, 0x16, 0xD2, 0x2B, 0x95, 0x7C, 0x1F, 0xCB,
	0x32, 0xD6, 0x8F, 0x9A, 0x2E, 0xF7, 0x9B, 0xBE,
	0x72, 0xFC, 0x55, 0x33, 0x27, 0x21, 0x2F, 0x69,
	0xCA, 0x76, 0xA5, 0x9B, 0x21, 0x2E, 0x40, 0x57,
	0x2C, 0x1C, 0x98, 0x41, 0x9A, 0x5E, 0x55, 0x38,
	0xDE, 0xC2, 0x09, 0x57, 0x32, 0xA3, 0x34, 0x40,
	0xC1, 0x8D, 0xCF, 0x70, 0x15, 0xF2, 0x6F, 0x4F,
	0x6A, 0x04, 0xA6, 0x6D, 0xFF, 0x53, 0x25, 0x6A,
	0x0E, 0xD0, 0x87, 0x63, 0xA1, 0x6C, 0xB1, 0x99,
	0x4A, 0x42, 0xF5, 0xF6, 0xEA, 0xA4, 0xEB, 0x6D,
	0x70, 0x9B, 0x0F, 0x85, 0xE2, 0x43, 0x6C, 0x27,
	0x54, 0x57, 0x52, 0x1C, 0xCF, 0x72, 0x11, 0x83,
	0xC3, 0xF5, 0xC6, 0xB6, 0x07, 0xEC, 0x1A, 0xF5,
	0xAC, 0xA1, 0xF2, 0x3A, 0x01, 0x53, 0x0C, 0xA4,
	0x40, 0x19, 0xAF, 0x7B, 0x2D, 0xE7, 0x56, 0x8B,
	0x71, 0x5F, 0x8D, 0x96, 0xF1, 0x5D, 0x33, 0xAC,
	0xE7, 0xAD, 0x96, 0xDB, 0xBB, 0xF7, 0xF5, 0x3F,
	0x0E, 0x02, 0x2E, 0x80, 0xE9, 0xA2, 0x30, 0xD2,
	0x82, 0x65, 0xEC, 0x1A, 0xB2, 0xE8, 0x98, 0x23,
	0x42, 0x79, 0x43, 0x85, 0x3B, 0xF9, 0xFB, 0xBB,
	0xC5, 0x29, 0xAD, 0x95, 0xAE, 0x22, 0x5B, 0x26,
	0xDF, 0x76, 0x65, 0x37, 0x17, 0xBC, 0x58, 0xBB,
	0x1B, 0x0A, 0x71, 0xDC, 0x68, 0xFF, 0x90, 0x36,
	0x37, 0xB7, 0x49, 0x5E, 0x49, 0x4E, 0xE4, 0x1E,
	0x24, 0x39, 0x48, 0xC7, 0x68, 0x19, 0xED, 0x94,
	0xB5, 0xF6, 0x85, 0xFA, 0xE6, 0xB8, 0x2B, 0x9E,
	0x07, 0x9E, 0xFB, 0x1D, 0x61, 0x06, 0x47, 0x2A,
	0x3E, 0x1E, 0xD8, 0x52, 0xFB, 0xB7, 0xE3, 0xB4,
	0x0D, 0xA8, 0x15, 0x1E, 0x98, 0x02, 0xBD, 0x89,
	0x89, 0xE3, 0x38, 0x2C, 0xAB, 0x50, 0x25, 0x30,
	0xB4, 0x5E, 0xA5, 0xCD, 0xA8, 0x9B, 0xA4, 0x2A,
	0xED, 0x19, 0x3B, 0xC3, 0x05, 0x07, 0x57, 0xB5,
	0x52, 0x11, 0x74, 0x95, 0x51, 0x5A, 0xD8, 0xED,
	0xDF, 0x37, 0x91, 0x83, 0x27, 0xD5, 0x08, 0x82,
	0xB3, 0x42, 0x08, 0xC7, 0x81, 0x35, 0x5F, 0x58,
	0x28, 0x69, 0x0D, 0x97, 0x1D, 0x28, 0xE6, 0xB0,
	0x58, 0x93, 0xCE, 0x2A, 0xB4, 0x7D, 0x4B, 0x83,
	0x20, 0x1E, 0x08, 0xEF, 0x68, 0x51, 0xEB, 0xB4,
	0xFA, 0x78, 0xB5, 0xE5, 0x2D, 0x93, 0x07, 0x99,
	0xBB, 0xDD, 0x9A, 0x4E, 0xD6, 0xE7, 0x31, 0x9F,
	0x4D, 0xB4, 0x05, 0x45, 0x89, 0x59, 0x42, 0xFF,
	0x0C, 0xD4, 0xC2, 0xF3, 0xA0, 0xF7, 0xD3, 0xCE,
	0x26, 0x2F, 0x9B, 0xCB, 0x98, 0x24, 0xDE, 0x50,
	0xF6, 0x81, 0x24, 0xA3, 0xB4, 0x99, 0x94, 0xDB,
	0xF5, 0xF4, 0x74, 0xAC, 0xED, 0xF1, 0xD6, 0x2F,
	0x89, 0x48, 0x41, 0x24, 0xBB, 0xA7, 0x64, 0x48,
	0xAE, 0x36, 0xE3, 0xF3, 0xA8, 0x37, 0x4F, 0xE6,
	0x24, 0x92, 0xC4, 0x27, 0x86, 0x68, 0xC4, 0xA2,
	0x2F, 0xB2, 0xA8, 0xFF, 0x27, 0x5F, 0x59, 0xB9,
	0x86, 0x89, 0x5A, 0x09, 0xB8, 0xDA, 0xD6, 0xBA,
	0x26, 0x86, 0x51, 0xC2, 0xDF, 0xCF, 0x07, 0x49,
	0x1C, 0xB7, 0x1A, 0x80, 0xD4, 0x97, 0x13, 0xAA,
	0xE8, 0x38, 0xA7, 0x2C, 0xB7, 0xE2, 0xE0, 0x33,
	0xF9, 0xC3, 0x21, 0xF6, 0x2A, 0xAA, 0x1A, 0xF7,
	0x39, 0x1F, 0x2F, 0x7E, 0x38, 0xC8, 0x82, 0x3F,
	0xCC, 0xD8, 0xE2, 0xD9, 0x02, 0xE6, 0x8E, 0x31,
	0xA7, 0x37, 0xE8, 0x59, 0x3F, 0xE1, 0x60, 0x1C,
	0x74, 0x52, 0xE4, 0x49, 0xF7, 0x21, 0x60, 0x11
};
static const struct
blockcipher_test_data aes_test_data_xts_wrapped_key_48_pt_512_du_512 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_XTS,
	.cipher_key = {
		.data = {
			0x94, 0xD8, 0xAE, 0xD7, 0x74, 0xBF, 0x51, 0xF1,
			0x2D, 0x19, 0x0B, 0x9A, 0xB3, 0xDC, 0x1E, 0xA9,
			0x5C, 0xC5, 0x47, 0xC1, 0xBB, 0x94, 0xDB, 0xFA,
			0x2B, 0x1E, 0x54, 0xA5, 0x76, 0xAD, 0x07, 0x4B,
			0x46, 0xE0, 0xAA, 0xF9, 0xF7, 0x5C, 0x64, 0x2D,
			0x8F, 0x04, 0x17, 0x72, 0xE5, 0x8D, 0xFE, 0x9C
		},
		.len = 48
	},
	.iv = {
		.data = {
			0x4F, 0xAE, 0xF7, 0x11, 0x7C, 0xDA, 0x59, 0xC6,
			0x6E, 0x4B, 0x92, 0x01, 0x3E, 0x76, 0x8A, 0xD5
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes256xts_512bytes,
		.len = 512
	},
	.ciphertext = {
		.data = ciphertext_aes256xts_512bytes,
		.len = 512
	},
	.xts_dataunit_len = 512,
	.wrapped_key = true
};
static const uint8_t plaintext_aes256xts_4096bytes[] = {
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79,
	0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79
};
static const uint8_t ciphertext_aes256xts_4096bytes[] = {
	0xD3, 0x94, 0x3E, 0xC9, 0xD3, 0x43, 0x13, 0xD3,
	0x07, 0x7E, 0x51, 0x11, 0x97, 0xA5, 0xB1, 0xB2,
	0xB0, 0x55, 0xF8, 0xD6, 0xA2, 0x10, 0x78, 0x76,
	0x37, 0x95, 0x4F, 0x25, 0x99, 0xCC, 0x12, 0xD6,
	0xBB, 0x2A, 0x24, 0xF4, 0x2D, 0x82, 0xFF, 0x56,
	0xEC, 0x55, 0x3B, 0xDD, 0xF9, 0xE1, 0xC8, 0x48,
	0x68, 0x16, 0xD2, 0x2B, 0x95, 0x7C, 0x1F, 0xCB,
	0x32, 0xD6, 0x8F, 0x9A, 0x2E, 0xF7, 0x9B, 0xBE,
	0x72, 0xFC, 0x55, 0x33, 0x27, 0x21, 0x2F, 0x69,
	0xCA, 0x76, 0xA5, 0x9B, 0x21, 0x2E, 0x40, 0x57,
	0x2C, 0x1C, 0x98, 0x41, 0x9A, 0x5E, 0x55, 0x38,
	0xDE, 0xC2, 0x09, 0x57, 0x32, 0xA3, 0x34, 0x40,
	0xC1, 0x8D, 0xCF, 0x70, 0x15, 0xF2, 0x6F, 0x4F,
	0x6A, 0x04, 0xA6, 0x6D, 0xFF, 0x53, 0x25, 0x6A,
	0x0E, 0xD0, 0x87, 0x63, 0xA1, 0x6C, 0xB1, 0x99,
	0x4A, 0x42, 0xF5, 0xF6, 0xEA, 0xA4, 0xEB, 0x6D,
	0x70, 0x9B, 0x0F, 0x85, 0xE2, 0x43, 0x6C, 0x27,
	0x54, 0x57, 0x52, 0x1C, 0xCF, 0x72, 0x11, 0x83,
	0xC3, 0xF5, 0xC6, 0xB6, 0x07, 0xEC, 0x1A, 0xF5,
	0xAC, 0xA1, 0xF2, 0x3A, 0x01, 0x53, 0x0C, 0xA4,
	0x40, 0x19, 0xAF, 0x7B, 0x2D, 0xE7, 0x56, 0x8B,
	0x71, 0x5F, 0x8D, 0x96, 0xF1, 0x5D, 0x33, 0xAC,
	0xE7, 0xAD, 0x96, 0xDB, 0xBB, 0xF7, 0xF5, 0x3F,
	0x0E, 0x02, 0x2E, 0x80, 0xE9, 0xA2, 0x30, 0xD2,
	0x82, 0x65, 0xEC, 0x1A, 0xB2, 0xE8, 0x98, 0x23,
	0x42, 0x79, 0x43, 0x85, 0x3B, 0xF9, 0xFB, 0xBB,
	0xC5, 0x29, 0xAD, 0x95, 0xAE, 0x22, 0x5B, 0x26,
	0xDF, 0x76, 0x65, 0x37, 0x17, 0xBC, 0x58, 0xBB,
	0x1B, 0x0A, 0x71, 0xDC, 0x68, 0xFF, 0x90, 0x36,
	0x37, 0xB7, 0x49, 0x5E, 0x49, 0x4E, 0xE4, 0x1E,
	0x24, 0x39, 0x48, 0xC7, 0x68, 0x19, 0xED, 0x94,
	0xB5, 0xF6, 0x85, 0xFA, 0xE6, 0xB8, 0x2B, 0x9E,
	0x07, 0x9E, 0xFB, 0x1D, 0x61, 0x06, 0x47, 0x2A,
	0x3E, 0x1E, 0xD8, 0x52, 0xFB, 0xB7, 0xE3, 0xB4,
	0x0D, 0xA8, 0x15, 0x1E, 0x98, 0x02, 0xBD, 0x89,
	0x89, 0xE3, 0x38, 0x2C, 0xAB, 0x50, 0x25, 0x30,
	0xB4, 0x5E, 0xA5, 0xCD, 0xA8, 0x9B, 0xA4, 0x2A,
	0xED, 0x19, 0x3B, 0xC3, 0x05, 0x07, 0x57, 0xB5,
	0x52, 0x11, 0x74, 0x95, 0x51, 0x5A, 0xD8, 0xED,
	0xDF, 0x37, 0x91, 0x83, 0x27, 0xD5, 0x08, 0x82,
	0xB3, 0x42, 0x08, 0xC7, 0x81, 0x35, 0x5F, 0x58,
	0x28, 0x69, 0x0D, 0x97, 0x1D, 0x28, 0xE6, 0xB0,
	0x58, 0x93, 0xCE, 0x2A, 0xB4, 0x7D, 0x4B, 0x83,
	0x20, 0x1E, 0x08, 0xEF, 0x68, 0x51, 0xEB, 0xB4,
	0xFA, 0x78, 0xB5, 0xE5, 0x2D, 0x93, 0x07, 0x99,
	0xBB, 0xDD, 0x9A, 0x4E, 0xD6, 0xE7, 0x31, 0x9F,
	0x4D, 0xB4, 0x05, 0x45, 0x89, 0x59, 0x42, 0xFF,
	0x0C, 0xD4, 0xC2, 0xF3, 0xA0, 0xF7, 0xD3, 0xCE,
	0x26, 0x2F, 0x9B, 0xCB, 0x98, 0x24, 0xDE, 0x50,
	0xF6, 0x81, 0x24, 0xA3, 0xB4, 0x99, 0x94, 0xDB,
	0xF5, 0xF4, 0x74, 0xAC, 0xED, 0xF1, 0xD6, 0x2F,
	0x89, 0x48, 0x41, 0x24, 0xBB, 0xA7, 0x64, 0x48,
	0xAE, 0x36, 0xE3, 0xF3, 0xA8, 0x37, 0x4F, 0xE6,
	0x24, 0x92, 0xC4, 0x27, 0x86, 0x68, 0xC4, 0xA2,
	0x2F, 0xB2, 0xA8, 0xFF, 0x27, 0x5F, 0x59, 0xB9,
	0x86, 0x89, 0x5A, 0x09, 0xB8, 0xDA, 0xD6, 0xBA,
	0x26, 0x86, 0x51, 0xC2, 0xDF, 0xCF, 0x07, 0x49,
	0x1C, 0xB7, 0x1A, 0x80, 0xD4, 0x97, 0x13, 0xAA,
	0xE8, 0x38, 0xA7, 0x2C, 0xB7, 0xE2, 0xE0, 0x33,
	0xF9, 0xC3, 0x21, 0xF6, 0x2A, 0xAA, 0x1A, 0xF7,
	0x39, 0x1F, 0x2F, 0x7E, 0x38, 0xC8, 0x82, 0x3F,
	0xCC, 0xD8, 0xE2, 0xD9, 0x02, 0xE6, 0x8E, 0x31,
	0xA7, 0x37, 0xE8, 0x59, 0x3F, 0xE1, 0x60, 0x1C,
	0x74, 0x52, 0xE4, 0x49, 0xF7, 0x21, 0x60, 0x11,
	0x8F, 0xD0, 0xA1, 0x4B, 0xE3, 0xF1, 0x18, 0x3D,
	0x90, 0x89, 0x54, 0x27, 0xA0, 0xF9, 0x32, 0x09,
	0x3D, 0x9D, 0x9A, 0x09, 0x53, 0xC6, 0x7E, 0x95,
	0x85, 0x53, 0x98, 0x4C, 0x23, 0xEA, 0x54, 0xBD,
	0x6F, 0x50, 0xBC, 0x4C, 0xCF, 0x37, 0xC5, 0x7B,
	0x4B, 0xCE, 0x84, 0xAF, 0xE2, 0xE2, 0x55, 0x49,
	0xBC, 0xBF, 0x92, 0xCA, 0x1E, 0x5E, 0x10, 0xDF,
	0x60, 0x87, 0x09, 0xA1, 0x4C, 0x1D, 0x7E, 0x1E,
	0x59, 0xE9, 0xCF, 0xDA, 0x45, 0x3F, 0xE2, 0x0F,
	0x53, 0x8D, 0x8B, 0x79, 0xBD, 0xD8, 0xB0, 0xE3,
	0x5B, 0x7C, 0x55, 0x4A, 0x84, 0xF0, 0x1E, 0xF9,
	0xE8, 0xF3, 0x09, 0x4D, 0x0B, 0xD7, 0x77, 0xCC,
	0x3F, 0x70, 0x22, 0x7D, 0x17, 0x27, 0x48, 0x57,
	0xE2, 0x36, 0xA0, 0x84, 0x3B, 0xDE, 0x05, 0x34,
	0xEF, 0x55, 0x12, 0xF4, 0x9A, 0x99, 0x0D, 0x28,
	0x86, 0x28, 0x99, 0x6B, 0x22, 0xEE, 0x63, 0xF0,
	0x68, 0x9C, 0xE1, 0x70, 0xF6, 0x26, 0xD8, 0x3B,
	0xF9, 0x57, 0x18, 0x3D, 0xAD, 0x66, 0xF0, 0xCF,
	0x7B, 0x0C, 0x28, 0x4D, 0xB8, 0xEB, 0x7B, 0x04,
	0x1E, 0x7D, 0x40, 0x5F, 0x5A, 0x1E, 0x7E, 0x08,
	0x7F, 0x4C, 0x1E, 0x18, 0xE5, 0x3E, 0x6C, 0x90,
	0x3C, 0x89, 0x13, 0x2A, 0xC4, 0x2A, 0x94, 0xB5,
	0x3E, 0x18, 0x1C, 0x4C, 0xBA, 0xEA, 0x86, 0xD2,
	0x05, 0xA9, 0x59, 0x9C, 0x80, 0xC2, 0x45, 0xAD,
	0x30, 0x99, 0x18, 0x6A, 0x2F, 0x73, 0x8C, 0xF0,
	0xFE, 0xA4, 0xBD, 0x44, 0x3E, 0xEB, 0x98, 0x75,
	0x48, 0x08, 0x57, 0x45, 0xD8, 0x41, 0xDE, 0x61,
	0x6D, 0x06, 0x93, 0xC4, 0x99, 0x1B, 0x23, 0xB5,
	0x12, 0x22, 0x5C, 0xC7, 0x9E, 0x18, 0xEA, 0x64,
	0xDB, 0xCE, 0x1A, 0xAC, 0x5D, 0x9B, 0x80, 0xE2,
	0xBF, 0x3E, 0xC2, 0xA4, 0x78, 0x4F, 0xF1, 0xE3,
	0x7D, 0x2A, 0x20, 0x94, 0x13, 0xCD, 0xF3, 0x1C,
	0x33, 0x9C, 0xC1, 0x59, 0x85, 0x52, 0xCB, 0xDB,
	0x03, 0xDF, 0x11, 0xE6, 0xAB, 0x95, 0x82, 0x65,
	0x7A, 0x88, 0x73, 0xEE, 0xBA, 0x21, 0x1C, 0x2F,
	0xCD, 0xD7, 0xC6, 0xE5, 0x13, 0xDE, 0x7A, 0x9E,
	0xEE, 0x83, 0x8D, 0xC6, 0x47, 0x63, 0xE0, 0xC7,
	0xC4, 0xBE, 0x19, 0x25, 0xEE, 0xCC, 0x0A, 0x13,
	0x18, 0x9D, 0x34, 0x5B, 0x55, 0x6C, 0xC1, 0x6E,
	0xBF, 0x5A, 0xC5, 0x61, 0x75, 0x77, 0x49, 0x8C,
	0x67, 0x61, 0xE8, 0x72, 0x87, 0xE8, 0xCA, 0xBE,
	0x6E, 0xC3, 0xD7, 0x81, 0x8C, 0x78, 0x79, 0xC8,
	0x72, 0xDA, 0x1A, 0x40, 0x7D, 0x60, 0xE2, 0x5A,
	0x47, 0x38, 0xA1, 0x21, 0x22, 0x6B, 0x54, 0x74,
	0xDD, 0xF3, 0xBC, 0x96, 0x28, 0x7E, 0xC2, 0x8B,
	0x13, 0xEE, 0x53, 0xBC, 0x34, 0x67, 0x07, 0x87,
	0xD5, 0x6B, 0x93, 0x22, 0x21, 0xB9, 0xED, 0x17,
	0xE4, 0xA1, 0x96, 0xB2, 0xC6, 0xFF, 0x79, 0xA0,
	0xA7, 0xF9, 0xDD, 0x92, 0x78, 0xF7, 0xE3, 0x16,
	0x79, 0xEF, 0xEF, 0x31, 0x4D, 0x1D, 0x75, 0xF9,
	0xCF, 0x5A, 0x1B, 0x68, 0x16, 0x7F, 0xAF, 0x5F,
	0x30, 0xB7, 0xEF, 0xF8, 0x94, 0x63, 0x73, 0x3D,
	0xB3, 0x63, 0xE4, 0xE6, 0xD8, 0xAD, 0xF4, 0x80,
	0x5E, 0x82, 0xA3, 0xFB, 0x3D, 0x0F, 0xCF, 0x59,
	0xB8, 0x76, 0xAF, 0x27, 0x83, 0xE3, 0x2D, 0x6F,
	0xE0, 0xF3, 0x11, 0xD5, 0xAE, 0x82, 0x14, 0x1D,
	0x78, 0x95, 0xBF, 0x31, 0x22, 0x1B, 0x80, 0x12,
	0x02, 0xD7, 0x4D, 0x1B, 0x92, 0xE3, 0x15, 0xBA,
	0x67, 0xD6, 0x8F, 0xD4, 0xDA, 0xBF, 0xD4, 0x62,
	0xAD, 0x76, 0xDA, 0x04, 0xA2, 0xEA, 0x98, 0xD3,
	0xC3, 0x6E, 0x5F, 0x26, 0x3C, 0x5E, 0xD9, 0xEA,
	0x09, 0xF0, 0x02, 0xFC, 0xD2, 0x11, 0xF8, 0xA8,
	0x7E, 0xFF, 0x06, 0x28, 0x5B, 0xE5, 0x6F, 0x9A,
	0x00, 0xE7, 0x7F, 0xB9, 0xFB, 0x59, 0xBB, 0xDD,
	0x85, 0xF3, 0x40, 0xCE, 0xA3, 0x5E, 0x2E, 0x2E,
	0x34, 0x91, 0x58, 0x41, 0x00, 0xB5, 0xE1, 0x88,
	0x24, 0x51, 0xC7, 0xB6, 0xF3, 0x21, 0x52, 0x6E,
	0xE7, 0xFC, 0x42, 0xE7, 0x9C, 0xCC, 0x1E, 0x51,
	0x45, 0x39, 0xBE, 0x09, 0xFE, 0x1A, 0xC4, 0xF0,
	0x79, 0xF4, 0x05, 0xC7, 0xA7, 0xF8, 0x0F, 0xB6,
	0x5A, 0x7B, 0xD7, 0xE1, 0x6F, 0xF0, 0x9D, 0x67,
	0xA3, 0xE3, 0x3E, 0x2E, 0xB9, 0x8C, 0x83, 0x9E,
	0xFD, 0x2E, 0xA2, 0x92, 0x99, 0x3C, 0xC0, 0x99,
	0x01, 0xAB, 0x0D, 0xFA, 0x55, 0x96, 0x04, 0x60,
	0x1A, 0xAD, 0x4C, 0xBB, 0x3D, 0xBB, 0x7D, 0x8B,
	0x9F, 0x28, 0x85, 0x7D, 0xB9, 0xE4, 0x05, 0x79,
	0x7B, 0x63, 0xDD, 0x7F, 0x4D, 0xE7, 0x50, 0xD9,
	0x41, 0xFF, 0x53, 0xB1, 0xCE, 0x42, 0x7B, 0xD6,
	0x05, 0x1B, 0x4E, 0xAF, 0xC4, 0x8C, 0x17, 0xC8,
	0x52, 0xBD, 0x03, 0x3B, 0x92, 0x57, 0x4E, 0xA8,
	0x15, 0xC3, 0x26, 0x1C, 0x55, 0xC1, 0xFF, 0xAE,
	0xA9, 0x26, 0x2D, 0xA7, 0x8E, 0x3A, 0x7F, 0xA3,
	0x48, 0xA5, 0xBC, 0x14, 0x84, 0xF2, 0x90, 0xCE,
	0x35, 0x0F, 0x64, 0x6B, 0xD8, 0x1C, 0x12, 0xFE,
	0x5A, 0x4F, 0x0E, 0xCE, 0x81, 0x4E, 0x79, 0x6B,
	0xCF, 0x56, 0xA7, 0xDB, 0x24, 0xBC, 0xB0, 0x84,
	0x4C, 0xB0, 0xDA, 0xBE, 0xE6, 0x8F, 0xD7, 0x8E,
	0x0E, 0xA0, 0xD3, 0x55, 0xC2, 0x4A, 0x34, 0x1C,
	0xF9, 0xC7, 0x3D, 0x29, 0x70, 0x8B, 0xF0, 0x99,
	0x61, 0xF5, 0x11, 0xFB, 0x82, 0xE2, 0x67, 0x35,
	0x60, 0x78, 0x47, 0x81, 0x2A, 0x74, 0x5E, 0x4D,
	0x48, 0xD3, 0x7C, 0x32, 0xCA, 0x1B, 0xD2, 0xA2,
	0x5C, 0x3A, 0x2F, 0xCE, 0xB4, 0x6C, 0x3A, 0x6A,
	0x8F, 0x67, 0x46, 0x12, 0xE7, 0xAE, 0x6A, 0x3B,
	0x99, 0x04, 0x5E, 0x96, 0xD0, 0xB9, 0x84, 0xF6,
	0xA7, 0x64, 0x11, 0xE8, 0x0C, 0x51, 0xFD, 0x3F,
	0x18, 0xFA, 0xE8, 0x52, 0xD9, 0x4B, 0x99, 0x7A,
	0x25, 0x2B, 0x1B, 0x21, 0xAD, 0x8C, 0xFE, 0x0D,
	0x34, 0x51, 0x91, 0x75, 0x55, 0x6F, 0xEB, 0x9F,
	0x42, 0xDC, 0x73, 0x7D, 0x31, 0x0A, 0x74, 0x13,
	0x80, 0xB8, 0xC3, 0xED, 0x73, 0x9D, 0x79, 0x42,
	0xC0, 0x33, 0xAB, 0xC1, 0xCB, 0xB9, 0xD0, 0xBE,
	0xA0, 0x78, 0xB8, 0x3B, 0xEB, 0x3D, 0x1A, 0x3F,
	0xFB, 0x9B, 0xAA, 0x8F, 0x89, 0xF9, 0xD1, 0x22,
	0x82, 0xE6, 0x66, 0xEE, 0x2A, 0xFD, 0x9F, 0xF8,
	0x92, 0x7E, 0x10, 0xF5, 0xD5, 0x23, 0x0B, 0xB1,
	0xD6, 0xF1, 0x7A, 0x3D, 0x73, 0xE9, 0xCE, 0x7F,
	0xE6, 0x0B, 0x17, 0xBC, 0x23, 0xAE, 0x72, 0xB6,
	0xFA, 0x19, 0x46, 0xBB, 0xFE, 0xA4, 0xC1, 0x64,
	0xA8, 0x5E, 0xE8, 0xBB, 0x63, 0x58, 0x19, 0x50,
	0xAA, 0x36, 0xC2, 0x4B, 0x38, 0x24, 0xD1, 0x2E,
	0xAE, 0xAD, 0x6E, 0x34, 0x64, 0xA8, 0xC8, 0xF2,
	0x4E, 0x74, 0x5C, 0x98, 0xE8, 0xDF, 0x99, 0x8C,
	0x41, 0x79, 0x60, 0x2D, 0xD5, 0xF4, 0xE3, 0xE9,
	0x1D, 0xF6, 0x5A, 0xA9, 0x69, 0x8E, 0xA1, 0x4F,
	0xD3, 0x1B, 0x09, 0xA8, 0x7A, 0xD1, 0xE1, 0xCF,
	0xAC, 0xBA, 0xD0, 0xD1, 0x34, 0x34, 0x8F, 0xC1,
	0xD1, 0xA8, 0xAF, 0x6E, 0x92, 0xE0, 0xB0, 0xF6,
	0xF9, 0x08, 0xA0, 0xCB, 0x58, 0x34, 0xF6, 0x68,
	0xA2, 0xBF, 0x05, 0x39, 0x63, 0xBA, 0x4F, 0xEF,
	0xE3, 0x95, 0x69, 0xD5, 0x89, 0x7C, 0x64, 0x07,
	0x13, 0x42, 0x14, 0xF1, 0xA6, 0x9B, 0x87, 0xE5,
	0xF4, 0x49, 0xAE, 0x67, 0x65, 0xCC, 0xF2, 0x26,
	0xF8, 0x31, 0xBD, 0x33, 0x6A, 0x87, 0x77, 0x4E,
	0xB1, 0xEE, 0xA4, 0xA2, 0xC8, 0xA0, 0x4A, 0xC1,
	0xDF, 0x55, 0xE0, 0xDE, 0x53, 0x15, 0x3B, 0xEC,
	0x55, 0x32, 0xCA, 0x06, 0xE4, 0x78, 0x59, 0x63,
	0x10, 0x68, 0xA9, 0x46, 0x1B, 0xEF, 0x73, 0x6D,
	0x1A, 0x02, 0x64, 0x12, 0x76, 0x9B, 0xDB, 0x7C,
	0x03, 0x35, 0x19, 0xE1, 0x58, 0x7A, 0x87, 0x0C,
	0x76, 0xDC, 0xFC, 0xC0, 0x28, 0xE4, 0xA2, 0x07,
	0x9C, 0x28, 0x05, 0x21, 0x13, 0x58, 0xEF, 0x05,
	0xBB, 0xAB, 0x94, 0xA2, 0x93, 0xBC, 0x31, 0x61,
	0x26, 0x39, 0x38, 0x0C, 0xC4, 0x67, 0xDA, 0xA5,
	0xE4, 0x1E, 0x1B, 0xB6, 0xE5, 0x73, 0xD6, 0x6C,
	0xEE, 0xBC, 0x9D, 0xB9, 0xE7, 0xD9, 0x45, 0x2F,
	0xF2, 0xB6, 0x92, 0x54, 0x41, 0x05, 0xB7, 0xB7,
	0xFC, 0x37, 0x63, 0x6A, 0xB4, 0xBE, 0xB8, 0x3E,
	0xD8, 0x53, 0x3B, 0xF8, 0x7D, 0x9A, 0x05, 0xDF,
	0x20, 0x02, 0x27, 0x64, 0x38, 0xFA, 0x7D, 0xAF,
	0x7F, 0xFA, 0xD1, 0xB7, 0x32, 0xC5, 0x74, 0x3E,
	0x04, 0xA2, 0x67, 0x79, 0x02, 0x2E, 0x6F, 0xA1,
	0x27, 0x87, 0x07, 0xB5, 0x9F, 0x0A, 0x7D, 0x5E,
	0x14, 0xA0, 0x31, 0x46, 0x3F, 0xA9, 0xDE, 0x98,
	0xB9, 0x89, 0xA0, 0x4A, 0x7A, 0xBD, 0x15, 0xAE,
	0x2D, 0x0B, 0x38, 0x9A, 0xD8, 0x0E, 0xD2, 0xBA,
	0x6D, 0xA1, 0x04, 0x1E, 0x4E, 0x39, 0x87, 0x4B,
	0xC8, 0x3C, 0x74, 0x35, 0x4D, 0xC8, 0x1B, 0x42,
	0x06, 0x5B, 0x73, 0xB7, 0x33, 0x86, 0x4A, 0x10,
	0x2A, 0x10, 0x16, 0x28, 0x6F, 0x2A, 0xE3, 0x86,
	0xDE, 0xA3, 0x44, 0x23, 0xE2, 0x90, 0xC4, 0x20,
	0x90, 0xE0, 0xB8, 0xE6, 0xA7, 0xB6, 0xD6, 0x92,
	0xF4, 0xF8, 0x8A, 0xBC, 0xAC, 0x31, 0x47, 0x8F,
	0xAA, 0xE0, 0xD9, 0xF7, 0xE3, 0xCB, 0x11, 0xA4,
	0x6B, 0x05, 0xB3, 0xB8, 0x72, 0x69, 0xE6, 0xDD,
	0x75, 0x0F, 0x20, 0x1D, 0x3F, 0xC6, 0x96, 0xA0,
	0x18, 0xB6, 0x24, 0xA1, 0xA6, 0xFD, 0x0C, 0x80,
	0x1E, 0xD2, 0x28, 0xA2, 0x1A, 0x27, 0xF4, 0x23,
	0x59, 0x1A, 0xCC, 0x0F, 0xD4, 0x99, 0xD0, 0xB4,
	0x1E, 0x91, 0xC7, 0xD8, 0x8F, 0x8C, 0x5B, 0xEB,
	0xB5, 0x9F, 0xFF, 0x4F, 0xD0, 0xD5, 0xB7, 0x60,
	0xCC, 0x0A, 0x10, 0x38, 0xBF, 0xA8, 0x2E, 0xCC,
	0xEB, 0x26, 0xB0, 0x78, 0xB3, 0xE0, 0x40, 0xAF,
	0xCD, 0x12, 0xC5, 0x3A, 0x24, 0xD8, 0xEE, 0x3A,
	0x64, 0x83, 0x2E, 0xD9, 0x25, 0x21, 0x66, 0xA5,
	0x28, 0xD1, 0xE1, 0x84, 0x25, 0x1B, 0x20, 0xB8,
	0xF5, 0x76, 0xB6, 0x3E, 0x4B, 0xC6, 0xEC, 0xC1,
	0xC7, 0xAC, 0xC4, 0xAD, 0xCE, 0xF0, 0xB4, 0x0F,
	0x35, 0x1E, 0xCE, 0x4E, 0xE3, 0x57, 0x30, 0xFC,
	0xF4, 0x9B, 0x86, 0xB0, 0xDD, 0x3F, 0x2F, 0xB6,
	0x10, 0x20, 0xE4, 0x24, 0x17, 0x1C, 0x24, 0xC6,
	0x89, 0xE4, 0x14, 0xAD, 0x2E, 0x41, 0x08, 0x33,
	0x88, 0xB1, 0x6F, 0x11, 0x85, 0xAF, 0x58, 0x17,
	0xE3, 0x91, 0xB4, 0x72, 0xA2, 0x7F, 0xA3, 0x98,
	0xAF, 0xB7, 0x6B, 0x58, 0x76, 0xA3, 0x11, 0x1C,
	0x8A, 0x1A, 0xE6, 0x58, 0x54, 0xB0, 0xB9, 0x6E,
	0x46, 0xCB, 0x16, 0xC0, 0x63, 0x0C, 0xEE, 0xA2,
	0xAE, 0xF6, 0x71, 0xEF, 0xD1, 0xB9, 0x3D, 0xB7,
	0x76, 0xCE, 0x5B, 0x84, 0x66, 0x7C, 0x7D, 0xF1,
	0x96, 0x60, 0x34, 0xF6, 0xD1, 0x64, 0x27, 0xD9,
	0xF3, 0x78, 0x8B, 0xF4, 0xC3, 0x1D, 0x37, 0xC0,
	0xF4, 0x4A, 0xD0, 0xA5, 0x9A, 0xEB, 0xDD, 0x79,
	0x54, 0x5D, 0xEB, 0x04, 0xC1, 0xA4, 0xBC, 0xED,
	0xE3, 0x74, 0xC3, 0xB9, 0x9A, 0x6A, 0xAA, 0x06,
	0xD1, 0xF0, 0x0F, 0xC5, 0xEF, 0x7E, 0x0B, 0xC8,
	0xF4, 0x94, 0x4E, 0x69, 0x0E, 0x36, 0x00, 0x13,
	0x45, 0xCE, 0x68, 0x13, 0xFE, 0x7F, 0x29, 0xA2,
	0x1D, 0x79, 0xDF, 0xF2, 0x27, 0xFB, 0xAE, 0x52,
	0x05, 0x78, 0xD7, 0xB9, 0xF7, 0x38, 0x68, 0xD5,
	0xBA, 0xD7, 0xCF, 0x09, 0xC6, 0xD2, 0x5B, 0xC6,
	0x98, 0xE4, 0xEC, 0xD5, 0xE9, 0xC2, 0xA5, 0x1A,
	0x52, 0xC8, 0xA7, 0xBA, 0x3D, 0x74, 0x75, 0x00,
	0xAA, 0xDD, 0x6A, 0x3F, 0xB6, 0x2F, 0x08, 0xB7,
	0x1C, 0x6B, 0x52, 0x0C, 0xC9, 0xE4, 0x4D, 0xF4,
	0xC5, 0x26, 0x1F, 0x35, 0x41, 0x25, 0x68, 0x17,
	0xA8, 0x81, 0x75, 0xF4, 0x66, 0x41, 0xB5, 0xE4,
	0x1D, 0x92, 0xEE, 0xDA, 0x0F, 0x56, 0x76, 0xC6,
	0xAA, 0x0F, 0xA8, 0x63, 0x8D, 0xF0, 0x69, 0x63,
	0x93, 0x45, 0xBC, 0x76, 0x40, 0xBE, 0xA9, 0x96,
	0x36, 0xAF, 0x2F, 0x6B, 0x3E, 0xAB, 0xF3, 0xC0,
	0xD7, 0xD5, 0xB1, 0x23, 0x23, 0xA2, 0xA0, 0xC4,
	0xC5, 0x70, 0xEF, 0x66, 0x79, 0x15, 0xF0, 0xD0,
	0x40, 0x0A, 0x33, 0x0C, 0xF3, 0x32, 0x6D, 0x8D,
	0xB4, 0x44, 0x46, 0x78, 0x3F, 0x8D, 0x75, 0x40,
	0xA5, 0x60, 0xBC, 0x9B, 0x76, 0xDF, 0x25, 0xF4,
	0xE9, 0xED, 0xAC, 0x74, 0x2F, 0x9A, 0x00, 0xC4,
	0x2B, 0x52, 0x26, 0x79, 0x09, 0x19, 0x57, 0x89,
	0x60, 0x14, 0xBE, 0x65, 0xBD, 0x7B, 0x4D, 0x7D,
	0x9B, 0x8B, 0x9E, 0x72, 0x6C, 0x0C, 0x57, 0xC7,
	0x00, 0x08, 0x38, 0x7C, 0x37, 0x45, 0x9D, 0x55,
	0xA2, 0x62, 0x5E, 0x34, 0x19, 0x99, 0x31, 0x16,
	0xF1, 0x14, 0x44, 0x2D, 0xE3, 0x7E, 0x22, 0xE1,
	0xA2, 0xB8, 0x9A, 0x9F, 0xE0, 0x37, 0x29, 0xBB,
	0xCD, 0x46, 0xEE, 0x0A, 0x62, 0x2B, 0x98, 0x34,
	0xBA, 0x9E, 0x54, 0x1B, 0xB1, 0x5C, 0x4F, 0xE9,
	0xAA, 0xE4, 0x95, 0x8C, 0xA4, 0xEF, 0xC2, 0xB1,
	0x7F, 0xF9, 0x80, 0xDA, 0x55, 0x95, 0x92, 0xC0,
	0x86, 0xF4, 0x2D, 0x99, 0x3E, 0x17, 0xDC, 0x55,
	0xA6, 0x33, 0x85, 0x90, 0x31, 0xC8, 0xFF, 0x58,
	0x83, 0xC5, 0xBA, 0x60, 0x20, 0x5F, 0x87, 0x29,
	0x20, 0x5A, 0x7D, 0x44, 0x2B, 0xA0, 0xE2, 0x99,
	0xC8, 0x70, 0xBE, 0x89, 0xC5, 0xBC, 0xF6, 0x0D,
	0x04, 0xC0, 0x96, 0xD1, 0x5C, 0xD1, 0x90, 0x43,
	0xD3, 0x7B, 0x73, 0x52, 0x30, 0xB6, 0xA9, 0x7C,
	0x0A, 0xA3, 0x24, 0x0E, 0x80, 0xFE, 0xBE, 0x31,
	0xFD, 0xB5, 0x96, 0x04, 0x2B, 0xCF, 0x0B, 0x28,
	0x1F, 0x7A, 0xCF, 0xC4, 0x82, 0x78, 0x52, 0x30,
	0xB1, 0x34, 0x12, 0x50, 0x03, 0x09, 0x1C, 0x8B,
	0x80, 0x60, 0xE3, 0xA1, 0xE5, 0x61, 0xF7, 0xD7,
	0xB6, 0x74, 0xBE, 0xD6, 0x58, 0x03, 0xD3, 0xE5,
	0xF7, 0xAC, 0x07, 0x60, 0xB7, 0x8A, 0xEC, 0xFA,
	0xC6, 0x0F, 0xF0, 0x20, 0x04, 0x6B, 0x8F, 0x61,
	0x09, 0x92, 0x03, 0xFB, 0x85, 0x99, 0x94, 0x9D,
	0x2E, 0x6A, 0xC2, 0x9F, 0x20, 0x46, 0x2A, 0x96,
	0xED, 0x42, 0x7D, 0x64, 0xA9, 0xE4, 0x1B, 0xDE,
	0x11, 0x20, 0x12, 0x93, 0xE6, 0x2B, 0xE5, 0x93,
	0x48, 0x37, 0x8C, 0x5A, 0x54, 0x0D, 0xEB, 0xF0,
	0x9F, 0x9D, 0xE4, 0xA5, 0xC4, 0x93, 0x6F, 0x6A,
	0xE3, 0x99, 0x69, 0xD9, 0xFE, 0x0C, 0x4E, 0xEC,
	0x8B, 0x30, 0x1F, 0x7A, 0xB8, 0xC8, 0x5B, 0x61,
	0x8E, 0xC2, 0x10, 0x90, 0x57, 0xB4, 0x72, 0x58,
	0x7F, 0x41, 0x29, 0x7E, 0xF9, 0xBE, 0x40, 0xC3,
	0x6F, 0xA9, 0xE3, 0x00, 0xE9, 0xC8, 0xFD, 0x4B,
	0xFD, 0x3F, 0xE3, 0x3F, 0x25, 0x22, 0xFD, 0xB7,
	0x2D, 0x57, 0xEF, 0x91, 0x08, 0xF0, 0x20, 0x56,
	0x30, 0xFA, 0x83, 0x69, 0xFD, 0x56, 0x5A, 0x9B,
	0xCE, 0xF8, 0x28, 0x02, 0xB4, 0x91, 0x35, 0x75,
	0x9E, 0x63, 0x99, 0x48, 0xCF, 0x35, 0xF5, 0x58,
	0x0C, 0x48, 0x8F, 0x0A, 0x2D, 0x9A, 0xE6, 0x40,
	0xF6, 0x21, 0xB5, 0x69, 0xC1, 0x09, 0x31, 0x00,
	0xA3, 0xC1, 0x4C, 0x99, 0x70, 0x4F, 0x5A, 0x63,
	0x17, 0x90, 0xB8, 0xF8, 0x3A, 0x0E, 0xFD, 0x67,
	0xEA, 0x0E, 0xBA, 0x7B, 0x1E, 0xEF, 0x37, 0x84,
	0xD5, 0x51, 0x37, 0x01, 0xD6, 0x93, 0x15, 0xDF,
	0x56, 0x89, 0x0E, 0x54, 0xF5, 0x1D, 0xF7, 0xE5,
	0xB7, 0xC1, 0xF1, 0xC2, 0xD9, 0x14, 0x6F, 0x40,
	0x55, 0x67, 0x50, 0x7C, 0x58, 0x35, 0x8B, 0x39,
	0xCB, 0xB5, 0x87, 0xF5, 0x55, 0x5E, 0x26, 0x8C,
	0x5B, 0x73, 0x0E, 0xBB, 0x25, 0x51, 0x0E, 0xAD,
	0x57, 0x72, 0x7B, 0x68, 0x83, 0x11, 0x1E, 0x3A,
	0x3D, 0xA4, 0x7C, 0x18, 0xB7, 0x70, 0x18, 0xBC,
	0x72, 0x03, 0x4A, 0xA1, 0xD2, 0xF9, 0xA9, 0x8A,
	0x25, 0x45, 0x19, 0xEE, 0x93, 0x06, 0xB5, 0x09,
	0x71, 0xC9, 0x2D, 0xFD, 0x2B, 0xF3, 0xC7, 0x64,
	0x5F, 0xCE, 0x71, 0x1D, 0x81, 0x96, 0x67, 0xBF,
	0x01, 0x39, 0x8C, 0xE7, 0xA2, 0xD0, 0x98, 0x57,
	0x5A, 0xFD, 0x21, 0xC7, 0x46, 0xAA, 0xB4, 0xE4,
	0x0E, 0xBE, 0xC6, 0x68, 0x3E, 0x38, 0xF5, 0xA2,
	0xED, 0x73, 0xCC, 0x53, 0x7E, 0x7E, 0x03, 0x32,
	0xDC, 0xB6, 0xC1, 0x03, 0x9E, 0xB3, 0x2A, 0xAD,
	0xC0, 0xC3, 0x6E, 0x47, 0xFB, 0x1E, 0xB7, 0x0D,
	0x86, 0x95, 0x09, 0xA6, 0x9D, 0x6F, 0x92, 0xFC,
	0xFF, 0x2C, 0x7D, 0x09, 0x16, 0x68, 0x50, 0x3E,
	0x4F, 0x23, 0x4C, 0x93, 0x95, 0x2A, 0xE1, 0x9B,
	0x16, 0xF0, 0x0F, 0xFF, 0x79, 0xA8, 0x06, 0xF9,
	0x70, 0x61, 0x72, 0x2C, 0xE8, 0x91, 0x01, 0x6D,
	0x45, 0xE5, 0x82, 0x5D, 0x26, 0x21, 0xAD, 0x3D,
	0x77, 0x73, 0x23, 0x04, 0x84, 0x27, 0xA3, 0x5D,
	0x6D, 0xA8, 0x99, 0xC1, 0xCE, 0x4F, 0xA9, 0xF7,
	0xAB, 0x5C, 0xDE, 0x01, 0xE6, 0x1E, 0xEF, 0xE6,
	0xFD, 0xE0, 0x68, 0x85, 0x3E, 0xEE, 0xBF, 0xF1,
	0x0D, 0x79, 0xF4, 0xA2, 0xB4, 0x14, 0xBC, 0x0C,
	0x49, 0x77, 0x03, 0x71, 0x08, 0x3E, 0x40, 0xA6,
	0xD7, 0x03, 0xFA, 0xE2, 0xFB, 0xC7, 0x59, 0x30,
	0x6E, 0x07, 0x06, 0x1C, 0x7C, 0x47, 0xE5, 0x4C,
	0x57, 0x0A, 0x91, 0x4A, 0x43, 0xE4, 0x8A, 0xCD,
	0x6E, 0x92, 0x01, 0xE2, 0x52, 0xC1, 0x92, 0x34,
	0x8E, 0x64, 0x0F, 0x39, 0x63, 0x53, 0xAB, 0xE5,
	0x44, 0xD5, 0xAA, 0xAA, 0xF6, 0x03, 0x89, 0xB9,
	0xDD, 0xB2, 0x2D, 0x56, 0x1A, 0xE0, 0x72, 0x5A,
	0x52, 0x19, 0x46, 0xEA, 0xB3, 0xCE, 0xB3, 0x59,
	0x46, 0x7A, 0xA7, 0x48, 0x37, 0x0C, 0x09, 0xBA,
	0x92, 0x70, 0x17, 0x7F, 0xF5, 0xD3, 0x60, 0x44,
	0xC4, 0xC6, 0xC6, 0x7D, 0xD2, 0x21, 0xAC, 0x3F,
	0x62, 0x6C, 0xE9, 0xBA, 0x4C, 0xF3, 0x82, 0x7E,
	0x6D, 0x3A, 0x92, 0xDC, 0x94, 0xE4, 0x5F, 0xA6,
	0x8B, 0x66, 0xA0, 0xDD, 0xE2, 0x97, 0x83, 0xED,
	0xF5, 0x9D, 0xDF, 0x74, 0x77, 0x23, 0x7D, 0xDA,
	0xC4, 0xFB, 0x92, 0x1A, 0xD9, 0x37, 0x36, 0xD2,
	0x88, 0xC9, 0xEA, 0x0F, 0x98, 0xBD, 0xC5, 0xF8,
	0xAA, 0x19, 0x75, 0x12, 0x6A, 0x41, 0xB5, 0xB3,
	0xB5, 0xA4, 0x96, 0xDC, 0x2B, 0x49, 0x86, 0x66,
	0x35, 0xD8, 0x4A, 0x62, 0xB4, 0xCB, 0x1E, 0x27,
	0xC1, 0xAD, 0x34, 0x0E, 0x26, 0x16, 0xF2, 0xC2,
	0x22, 0x52, 0x84, 0xD8, 0xD1, 0x32, 0xB8, 0x9C,
	0xFE, 0x64, 0x42, 0x9F, 0xE4, 0x69, 0xF0, 0xAE,
	0x3B, 0xD9, 0x2C, 0xA0, 0x14, 0xEB, 0x69, 0x74,
	0x7C, 0xE2, 0xA6, 0x60, 0xE1, 0x52, 0x1C, 0xCC,
	0xBF, 0xE6, 0xA1, 0x83, 0x20, 0x5D, 0x9E, 0xA3,
	0xFB, 0x84, 0x8B, 0x33, 0xE6, 0xC9, 0x32, 0x83,
	0xC0, 0x3F, 0x98, 0x1D, 0x6E, 0xC0, 0x50, 0x71,
	0x29, 0x60, 0x5F, 0x36, 0xB4, 0x68, 0x1D, 0xB9,
	0x76, 0x73, 0xC3, 0x80, 0xC5, 0xBC, 0x59, 0x7B,
	0x59, 0xB4, 0xE0, 0x6A, 0x80, 0xCD, 0x4D, 0x8C,
	0x9E, 0xE0, 0x0B, 0x45, 0x7D, 0x54, 0xD4, 0xC4,
	0x97, 0x6C, 0x54, 0xEF, 0x14, 0x64, 0xBD, 0x3B,
	0xD7, 0xEE, 0xF4, 0xD1, 0x41, 0x76, 0x3A, 0x24,
	0x7A, 0xC2, 0xCA, 0x68, 0x28, 0x53, 0x46, 0xF7,
	0x1B, 0xDA, 0x4B, 0x7A, 0x56, 0x75, 0x86, 0xFB,
	0x31, 0x2C, 0x27, 0xF9, 0x4D, 0x35, 0xA4, 0x82,
	0xE7, 0x2F, 0x41, 0xB4, 0xCA, 0xCE, 0x75, 0x94,
	0x08, 0x54, 0xE2, 0x9E, 0x99, 0xC9, 0x85, 0xDE,
	0x6F, 0x80, 0x95, 0x59, 0x3E, 0x54, 0x9F, 0x31,
	0xF8, 0xDE, 0xD0, 0xD7, 0xA6, 0xD4, 0xD3, 0xBB,
	0xD9, 0xC7, 0x55, 0xDD, 0xAE, 0xAD, 0x9E, 0x57,
	0x4A, 0x33, 0x5D, 0x7A, 0xA6, 0xA3, 0xCA, 0xF9,
	0x4C, 0x5B, 0x51, 0xCC, 0x22, 0xBB, 0x76, 0x44,
	0x17, 0xDE, 0x22, 0xA1, 0xDF, 0x80, 0x13, 0x7D,
	0xE5, 0x34, 0x7E, 0x75, 0x73, 0x10, 0x40, 0xFB,
	0x9A, 0x21, 0xCD, 0xD3, 0xD3, 0x84, 0xB6, 0x0C,
	0x31, 0x1E, 0xB5, 0x42, 0xF4, 0x34, 0x11, 0x7F,
	0x4A, 0x23, 0xA8, 0xA5, 0x8F, 0x20, 0xCD, 0xA9,
	0xF2, 0xE4, 0xEE, 0xFA, 0x57, 0xD1, 0x22, 0x1C,
	0xA5, 0xDC, 0x0B, 0x25, 0xFE, 0xC2, 0xA7, 0x7E,
	0x09, 0x2E, 0xDA, 0x40, 0x9F, 0x6C, 0xC8, 0x71,
	0x58, 0x91, 0x04, 0x25, 0xD0, 0x06, 0xEA, 0x1B,
	0xCD, 0x9D, 0x50, 0xD8, 0x40, 0x24, 0xAC, 0xC3,
	0xB4, 0x07, 0x6E, 0x76, 0xF4, 0x4C, 0xD8, 0x80,
	0xD0, 0x20, 0xF5, 0x15, 0x6A, 0x0A, 0x12, 0xF8,
	0x6B, 0x67, 0x77, 0x34, 0xAE, 0x60, 0x68, 0x13,
	0x5B, 0x8E, 0xFF, 0x5E, 0x7A, 0x77, 0x67, 0x0D,
	0xE6, 0x96, 0x43, 0x9F, 0x8F, 0x77, 0x5F, 0x97,
	0x23, 0x91, 0x33, 0x72, 0xC7, 0x8A, 0xC7, 0x80,
	0xCF, 0xE7, 0x71, 0x06, 0x25, 0xB7, 0x4B, 0x89,
	0x6A, 0x46, 0x67, 0x19, 0x49, 0x44, 0x03, 0x52,
	0x32, 0xB1, 0x8F, 0xE7, 0x9E, 0xDA, 0x03, 0x41,
	0xA3, 0xAC, 0xE5, 0xF3, 0x96, 0xE6, 0xAC, 0xFF,
	0xEC, 0x35, 0x4D, 0x83, 0xA9, 0xCE, 0x7C, 0x52,
	0xF2, 0x36, 0x97, 0xF0, 0x28, 0x36, 0x54, 0x59,
	0x96, 0xEA, 0xEE, 0xB2, 0xC1, 0xAB, 0xA4, 0x96,
	0x62, 0xD4, 0x3C, 0xF0, 0x1F, 0x2D, 0x65, 0x0E,
	0x46, 0x7E, 0x12, 0x31, 0x8F, 0xA7, 0x8D, 0x7A,
	0x4A, 0x41, 0x15, 0x57, 0x90, 0xF6, 0xF1, 0xE8,
	0xE8, 0xE3, 0x57, 0x7B, 0x55, 0x85, 0x95, 0x97,
	0xB3, 0x29, 0x3D, 0x02, 0x73, 0x1E, 0x87, 0x1F,
	0x01, 0x89, 0x06, 0x88, 0x9E, 0x8A, 0x2E, 0xE0,
	0x99, 0xFC, 0xF0, 0x48, 0x60, 0x37, 0x65, 0x25,
	0xDB, 0x89, 0xC9, 0x7A, 0x51, 0x7E, 0x35, 0x92,
	0x00, 0xC9, 0x61, 0x3F, 0x88, 0xE3, 0x20, 0x01,
	0x46, 0x5A, 0x2C, 0x37, 0x02, 0xC9, 0x15, 0x0F,
	0xB2, 0xEB, 0xC3, 0x55, 0x18, 0xF0, 0x15, 0x1A,
	0x08, 0x8E, 0xB8, 0x9D, 0x18, 0xE4, 0x9F, 0x34,
	0x10, 0x67, 0x68, 0x57, 0x60, 0x61, 0xEC, 0xD5,
	0xD9, 0xA8, 0x3A, 0xAB, 0xD6, 0xD2, 0x7A, 0x47,
	0x3D, 0xA4, 0x08, 0x7C, 0x3E, 0x4D, 0x76, 0x08,
	0x19, 0x22, 0xB2, 0x89, 0x57, 0x84, 0xC2, 0x98,
	0x72, 0xB8, 0x8B, 0xE0, 0x85, 0xA1, 0x3A, 0xC2,
	0xA0, 0x06, 0x43, 0x03, 0xCF, 0x4F, 0x27, 0x80,
	0x48, 0x9A, 0xBC, 0xB3, 0x3C, 0xC4, 0x5E, 0xAC,
	0x8B, 0x85, 0x6F, 0x21, 0xD6, 0xFE, 0x12, 0x90,
	0x53, 0x2F, 0x65, 0x32, 0x8D, 0x03, 0xFE, 0xFE,
	0x61, 0x04, 0x47, 0x24, 0x6A, 0xB5, 0x01, 0x98,
	0xB9, 0x27, 0x10, 0xE1, 0x32, 0x3D, 0x2A, 0xA0,
	0xC5, 0x70, 0xDE, 0x1E, 0x10, 0xD7, 0x01, 0x50,
	0x4F, 0x87, 0xCA, 0xD9, 0xBF, 0x12, 0xEA, 0x38,
	0x4C, 0x43, 0xD5, 0x5A, 0xEF, 0x3E, 0x21, 0x8E,
	0x59, 0x77, 0x23, 0xED, 0x51, 0x09, 0x99, 0x73,
	0xD2, 0x56, 0x04, 0xDD, 0x8F, 0x5F, 0xDF, 0x79,
	0xFF, 0x16, 0x8C, 0xB0, 0xBA, 0x8A, 0x1A, 0x56,
	0xAF, 0xCA, 0x19, 0xF2, 0x64, 0x1A, 0xF5, 0x1E,
	0xA7, 0xA7, 0x84, 0x3D, 0xAD, 0xC1, 0x0E, 0x22,
	0xA1, 0x45, 0xFC, 0xB4, 0x13, 0x91, 0x34, 0xB7,
	0x48, 0xEF, 0x9E, 0xD9, 0x0B, 0xE3, 0x82, 0x75,
	0x80, 0xC5, 0xD9, 0xA0, 0x77, 0xA3, 0xF9, 0xCC,
	0x67, 0xDD, 0xCB, 0x28, 0xC6, 0xE8, 0x2C, 0xB8,
	0xAC, 0x63, 0xBD, 0x3B, 0x28, 0x4A, 0xE9, 0x2D,
	0x29, 0x84, 0xD7, 0x4F, 0x61, 0x06, 0xE3, 0x37,
	0xC1, 0x58, 0x20, 0x5D, 0x0A, 0xE7, 0x45, 0x29,
	0x7D, 0xED, 0x0F, 0xCE, 0x00, 0x95, 0x2A, 0x62,
	0x38, 0xA8, 0x1A, 0x3A, 0x96, 0x0E, 0x56, 0xD9,
	0x18, 0xC2, 0x25, 0xA5, 0xAA, 0x27, 0x0A, 0x6E,
	0xDD, 0x1C, 0x35, 0x6C, 0xC1, 0x26, 0x90, 0xF6,
	0x43, 0x1B, 0x34, 0xDA, 0xE1, 0x5D, 0x09, 0x7C,
	0xBE, 0x0F, 0x40, 0xD3, 0x24, 0x82, 0x0B, 0xFF,
	0xE6, 0xB7, 0x10, 0xD6, 0x36, 0xD0, 0xE6, 0xC0,
	0xBE, 0x65, 0x4C, 0x83, 0xF1, 0xDA, 0xDE, 0xCE,
	0xE8, 0x5A, 0x80, 0x88, 0xFE, 0x9B, 0x79, 0x54,
	0xA3, 0xA4, 0x5A, 0x76, 0xD0, 0xE2, 0xCE, 0x92,
	0x53, 0x7D, 0x9C, 0xDA, 0xA1, 0xED, 0x9F, 0x56,
	0x05, 0x0A, 0xA4, 0x81, 0xC7, 0x82, 0x5B, 0xB8,
	0xC7, 0xA8, 0x95, 0x21, 0x99, 0x0B, 0x0F, 0xD2,
	0x66, 0x68, 0xC3, 0x07, 0x53, 0x93, 0x8C, 0x68,
	0x5A, 0xF5, 0x6F, 0x5E, 0x07, 0x68, 0x70, 0xF2,
	0x6A, 0x78, 0xA8, 0xDB, 0x24, 0x6F, 0xD9, 0x74,
	0x38, 0x29, 0xBF, 0x50, 0xCC, 0xC8, 0x34, 0x82,
	0x69, 0x1A, 0xF4, 0x1A, 0x95, 0xE1, 0x31, 0x39,
	0x00, 0xBE, 0xF6, 0x25, 0x19, 0x5A, 0xF6, 0xA7,
	0xB7, 0xC7, 0xAC, 0xFA, 0x52, 0x6D, 0xC7, 0xEA,
	0xFF, 0xC4, 0xB0, 0x84, 0x41, 0x2D, 0x6B, 0x22,
	0x57, 0x1F, 0x8A, 0x97, 0x71, 0xEF, 0x54, 0xA7,
	0xA9, 0xB5, 0xA0, 0x7A, 0xFF, 0x52, 0xBC, 0x78,
	0x56, 0x8C, 0x9C, 0x2A, 0xFB, 0x31, 0xA6, 0xC8,
	0x87, 0xC8, 0x82, 0x72, 0x00, 0x68, 0x27, 0xDE,
	0x54, 0x67, 0x4C, 0x36, 0x4E, 0xE2, 0x77, 0x7F,
	0xAD, 0x15, 0x1B, 0xC9, 0x07, 0xC4, 0x84, 0x50,
	0x84, 0x45, 0xB5, 0x1D, 0xCE, 0x3F, 0x7C, 0xDF,
	0x73, 0x88, 0x3E, 0x6D, 0xAC, 0x18, 0x67, 0x2C,
	0x1D, 0x31, 0xD5, 0x41, 0x6E, 0xFC, 0x3D, 0xFE,
	0x5C, 0x6D, 0x3B, 0xCB, 0x67, 0x05, 0x44, 0x8B,
	0x02, 0xB7, 0xF5, 0x05, 0xFD, 0x1D, 0x7E, 0x13,
	0x90, 0xCE, 0xED, 0xD2, 0xAB, 0x08, 0xFF, 0xC3,
	0x91, 0x2C, 0x79, 0x06, 0xDB, 0x54, 0xAB, 0xFF,
	0xF6, 0x9D, 0xBB, 0xDC, 0x3C, 0xCD, 0x03, 0xE7,
	0xD8, 0x1A, 0x4E, 0x7F, 0xCB, 0x1B, 0xA3, 0xCA,
	0xDC, 0x14, 0xC5, 0xFE, 0x46, 0x38, 0x07, 0x82,
	0x5E, 0x9A, 0x77, 0x9C, 0xB4, 0x44, 0x26, 0xBA,
	0xC2, 0x27, 0xD0, 0xF4, 0x75, 0x67, 0x4A, 0x15,
	0x2A, 0x55, 0x2A, 0x61, 0x87, 0x55, 0xA0, 0xFB,
	0xE0, 0x93, 0xA5, 0xD7, 0xDF, 0x0D, 0x97, 0xD3,
	0x93, 0x39, 0x0B, 0x5A, 0xC6, 0x86, 0x17, 0x7D,
	0x6A, 0xA4, 0x07, 0x9C, 0xB9, 0x0F, 0x42, 0x15,
	0x46, 0x71, 0x45, 0x70, 0x75, 0x4B, 0xD5, 0x80,
	0x64, 0x62, 0x92, 0x50, 0xBC, 0x10, 0xA1, 0x68,
	0x60, 0x5A, 0x6F, 0x31, 0x26, 0xCA, 0xB1, 0x48,
	0xFB, 0xC6, 0xF4, 0x3A, 0xCA, 0x52, 0x20, 0x2F,
	0x7D, 0xEC, 0x19, 0xF8, 0xAA, 0x27, 0xC2, 0x23,
	0xD0, 0x8F, 0x60, 0xFC, 0x7F, 0xA0, 0xCB, 0xDC,
	0xA2, 0xC6, 0xC4, 0xC5, 0x13, 0x00, 0xF3, 0x43,
	0x48, 0x6D, 0xFD, 0x7D, 0xB9, 0xA8, 0x14, 0xB0,
	0x0C, 0x72, 0x82, 0x2F, 0x99, 0x64, 0x41, 0x2B,
	0xB3, 0x34, 0x73, 0x73, 0xF4, 0x26, 0x1D, 0x06,
	0xDF, 0x6E, 0xF4, 0x20, 0x1E, 0x31, 0xE3, 0x8A,
	0x01, 0x6C, 0xDB, 0x3C, 0xE3, 0xC6, 0xC4, 0xC5,
	0xB8, 0x20, 0x51, 0xF1, 0xD6, 0xB0, 0x30, 0xB7,
	0x2D, 0xDA, 0x95, 0x01, 0x0D, 0xED, 0xEE, 0x6F,
	0x69, 0xFD, 0xCF, 0x9D, 0xDD, 0x05, 0xD6, 0xC0,
	0xFE, 0x11, 0x67, 0xAF, 0x53, 0x94, 0x60, 0xFC,
	0x56, 0xBA, 0x0C, 0x5F, 0xA7, 0x7E, 0xDA, 0x65
};
static const struct
blockcipher_test_data aes_test_data_xts_wrapped_key_48_pt_4096_du_4096 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_XTS,
	.cipher_key = {
		.data = {
			0x94, 0xD8, 0xAE, 0xD7, 0x74, 0xBF, 0x51, 0xF1,
			0x2D, 0x19, 0x0B, 0x9A, 0xB3, 0xDC, 0x1E, 0xA9,
			0x5C, 0xC5, 0x47, 0xC1, 0xBB, 0x94, 0xDB, 0xFA,
			0x2B, 0x1E, 0x54, 0xA5, 0x76, 0xAD, 0x07, 0x4B,
			0x46, 0xE0, 0xAA, 0xF9, 0xF7, 0x5C, 0x64, 0x2D,
			0x8F, 0x04, 0x17, 0x72, 0xE5, 0x8D, 0xFE, 0x9C
		},
		.len = 48
	},
	.iv = {
		.data = {
			0x4F, 0xAE, 0xF7, 0x11, 0x7C, 0xDA, 0x59, 0xC6,
			0x6E, 0x4B, 0x92, 0x01, 0x3E, 0x76, 0x8A, 0xD5
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes256xts_4096bytes,
		.len = 4096
	},
	.ciphertext = {
		.data = ciphertext_aes256xts_4096bytes,
		.len = 4096
	},
	.xts_dataunit_len = 4096,
	.wrapped_key = true
};

static const struct
blockcipher_test_data aes_test_data_xts_wrapped_key_48_pt_4096_du_0 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_XTS,
	.cipher_key = {
		.data = {
			0x94, 0xD8, 0xAE, 0xD7, 0x74, 0xBF, 0x51, 0xF1,
			0x2D, 0x19, 0x0B, 0x9A, 0xB3, 0xDC, 0x1E, 0xA9,
			0x5C, 0xC5, 0x47, 0xC1, 0xBB, 0x94, 0xDB, 0xFA,
			0x2B, 0x1E, 0x54, 0xA5, 0x76, 0xAD, 0x07, 0x4B,
			0x46, 0xE0, 0xAA, 0xF9, 0xF7, 0x5C, 0x64, 0x2D,
			0x8F, 0x04, 0x17, 0x72, 0xE5, 0x8D, 0xFE, 0x9C
		},
		.len = 48
	},
	.iv = {
		.data = {
			0x4F, 0xAE, 0xF7, 0x11, 0x7C, 0xDA, 0x59, 0xC6,
			0x6E, 0x4B, 0x92, 0x01, 0x3E, 0x76, 0x8A, 0xD5
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes256xts_4096bytes,
		.len = 4096
	},
	.ciphertext = {
		.data = ciphertext_aes256xts_4096bytes,
		.len = 4096
	},
	.xts_dataunit_len = 0,
	.wrapped_key = true
};

static const struct
blockcipher_test_data aes_test_data_xts_wrapped_key_48_pt_512_du_0 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_XTS,
	.cipher_key = {
		.data = {
			0x94, 0xD8, 0xAE, 0xD7, 0x74, 0xBF, 0x51, 0xF1,
			0x2D, 0x19, 0x0B, 0x9A, 0xB3, 0xDC, 0x1E, 0xA9,
			0x5C, 0xC5, 0x47, 0xC1, 0xBB, 0x94, 0xDB, 0xFA,
			0x2B, 0x1E, 0x54, 0xA5, 0x76, 0xAD, 0x07, 0x4B,
			0x46, 0xE0, 0xAA, 0xF9, 0xF7, 0x5C, 0x64, 0x2D,
			0x8F, 0x04, 0x17, 0x72, 0xE5, 0x8D, 0xFE, 0x9C
		},
		.len = 48
	},
	.iv = {
		.data = {
			0x4F, 0xAE, 0xF7, 0x11, 0x7C, 0xDA, 0x59, 0xC6,
			0x6E, 0x4B, 0x92, 0x01, 0x3E, 0x76, 0x8A, 0xD5
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes256xts_512bytes,
		.len = 512
	},
	.ciphertext = {
		.data = ciphertext_aes256xts_512bytes,
		.len = 512
	},
	.xts_dataunit_len = 0,
	.wrapped_key = true
};


/* AES-DOCSIS-BPI test vectors */

/* Multiple of AES block size */
static const struct blockcipher_test_data aes_test_data_docsis_1 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_DOCSISBPI,
	.cipher_key = {
		.data = {
			0xE4, 0x23, 0x33, 0x8A, 0x35, 0x64, 0x61, 0xE2,
			0x49, 0x03, 0xDD, 0xC6, 0xB8, 0xCA, 0x55, 0x7A
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
			0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_common,
		.len = 512
	},
	.ciphertext = {
		.data = ciphertext512_aes128cbc,
		.len = 512
	}
};

/* Less than AES block size */
static const struct blockcipher_test_data aes_test_data_docsis_2 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_DOCSISBPI,
	.cipher_key = {
		.data = {
			0xE6, 0x60, 0x0F, 0xD8, 0x85, 0x2E, 0xF5, 0xAB,
			0xE6, 0x60, 0x0F, 0xD8, 0x85, 0x2E, 0xF5, 0xAB
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x81, 0x0E, 0x52, 0x8E, 0x1C, 0x5F, 0xDA, 0x1A,
			0x81, 0x0E, 0x52, 0x8E, 0x1C, 0x5F, 0xDA, 0x1A
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_docsis_bpi_cfb,
		.len = 7
	},
	.ciphertext = {
		.data = ciphertext_aes_docsis_bpi_cfb,
		.len = 7
	}
};

/* Not multiple of AES block size */
static const struct blockcipher_test_data aes_test_data_docsis_3 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_DOCSISBPI,
	.cipher_key = {
		.data = {
			0xE6, 0x60, 0x0F, 0xD8, 0x85, 0x2E, 0xF5, 0xAB,
			0xE6, 0x60, 0x0F, 0xD8, 0x85, 0x2E, 0xF5, 0xAB
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x81, 0x0E, 0x52, 0x8E, 0x1C, 0x5F, 0xDA, 0x1A,
			0x81, 0x0E, 0x52, 0x8E, 0x1C, 0x5F, 0xDA, 0x1A
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_docsis_bpi_cbc_cfb,
		.len = 19
	},
	.ciphertext = {
		.data = ciphertext_aes_docsis_bpi_cbc_cfb,
		.len = 19
	}
};

/* Multiple of AES 256 block size */
static const struct blockcipher_test_data aes_test_data_docsis_4 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_DOCSISBPI,
	.cipher_key = {
		.data = {
			0xE4, 0x23, 0x33, 0x8A, 0x35, 0x64, 0x61, 0xE2,
			0x49, 0x03, 0xDD, 0xC6, 0xB8, 0xCA, 0x55, 0x7A,
			0xD4, 0xC3, 0xA3, 0xAA, 0x33, 0x62, 0x61, 0xE0,
			0x37, 0x07, 0xB8, 0x23, 0xA2, 0xA3, 0xB5, 0x8D
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
			0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_common,
		.len = 512
	},
	.ciphertext = {
		.data = ciphertext512_aes256cbc,
		.len = 512
	}
};

/* Less than AES 256 block size */
static const struct blockcipher_test_data aes_test_data_docsis_5 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_DOCSISBPI,
	.cipher_key = {
		.data = {
			0xE6, 0x60, 0x0F, 0xD8, 0x85, 0x2E, 0xF5, 0xAB,
			0xE6, 0x60, 0x0F, 0xD8, 0x85, 0x2E, 0xF5, 0xAB,
			0xE6, 0x60, 0x0F, 0xD8, 0x85, 0x2E, 0xF5, 0xAB,
			0xE6, 0x60, 0x0F, 0xD8, 0x85, 0x2E, 0xF5, 0xAB
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x81, 0x0E, 0x52, 0x8E, 0x1C, 0x5F, 0xDA, 0x1A,
			0x81, 0x0E, 0x52, 0x8E, 0x1C, 0x5F, 0xDA, 0x1A
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_docsis_bpi_cfb,
		.len = 7
	},
	.ciphertext = {
		.data = ciphertext_aes256_docsis_bpi_cfb,
		.len = 7
	}
};

/* Not multiple of AES 256 block size */
static const struct blockcipher_test_data aes_test_data_docsis_6 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_DOCSISBPI,
	.cipher_key = {
		.data = {
			0xE6, 0x60, 0x0F, 0xD8, 0x85, 0x2E, 0xF5, 0xAB,
			0xE6, 0x60, 0x0F, 0xD8, 0x85, 0x2E, 0xF5, 0xAB,
			0xE6, 0x60, 0x0F, 0xD8, 0x85, 0x2E, 0xF5, 0xAB,
			0xE6, 0x60, 0x0F, 0xD8, 0x85, 0x2E, 0xF5, 0xAB
		},
		.len = 32
	},
	.iv = {
		.data = {
			0x81, 0x0E, 0x52, 0x8E, 0x1C, 0x5F, 0xDA, 0x1A,
			0x81, 0x0E, 0x52, 0x8E, 0x1C, 0x5F, 0xDA, 0x1A
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_docsis_bpi_cbc_cfb,
		.len = 19
	},
	.ciphertext = {
		.data = ciphertext_aes256_docsis_bpi_cbc_cfb,
		.len = 19
	}
};

static const uint8_t
cipher_aescbc_offset_16[] = {
	0x57, 0x68, 0x61, 0x74, 0x20, 0x61, 0x20, 0x6C,
	0x6F, 0x75, 0x73, 0x79, 0x20, 0x65, 0x61, 0x72,
	0x68, 0x2A, 0x6A, 0x82, 0xE0, 0x73, 0xC7, 0x51,
	0x81, 0xF4, 0x47, 0x27, 0x1A, 0xEF, 0x76, 0x15,
	0x1C, 0xE1, 0x38, 0x5F, 0xE1, 0x81, 0x77, 0xC7,
	0x8B, 0xF0, 0x69, 0xC3, 0x3C, 0x45, 0x1C, 0x0A,
	0xA3, 0x93, 0xBF, 0x60, 0x57, 0x88, 0xD2, 0xFF,
	0xE1, 0x8F, 0xC0, 0x64, 0x2C, 0x42, 0xC5, 0x22,
	0xE3, 0x5F, 0x71, 0x1F, 0xF7, 0x62, 0xA2, 0x7E,
	0x0D, 0x42, 0xD9, 0xE7, 0xF3, 0x10, 0xB0, 0xEE,
};

/** AES-128-CBC SHA1 OOP test vector for swapping src/dst */
static const struct blockcipher_test_data aes_test_data_14 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_AES_CBC,
	.cipher_key = {
		.data = {
			0xE4, 0x23, 0x33, 0x8A, 0x35, 0x64, 0x61, 0xE2,
			0x49, 0x03, 0xDD, 0xC6, 0xB8, 0xCA, 0x55, 0x7A
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
			0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_aes_common,
		.len = 80
	},
	.cipher_offset = 16,
	.auth_offset = 0,
	.ciphertext = {
		.data = cipher_aescbc_offset_16,
		.len = 80
	},
	.auth_algo = RTE_CRYPTO_AUTH_SHA1_HMAC,
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD
		},
		.len = 20
	},
	.digest = {
		.data = {
			0xCC, 0x15, 0x83, 0xF7, 0x23, 0x87, 0x96, 0xA7,
			0x29, 0x34, 0x32, 0xE4, 0x4C, 0x06, 0xE8, 0xEB,
			0x70, 0x72, 0x4B, 0xAD
		},
		.len = 20,
		.truncated_len = 12
	}
};

static const struct blockcipher_test_case aes_chain_test_cases[] = {
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Decryption Digest "
			"Verify OOP Offset",
		.test_data = &aes_test_data_14,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
		{
		.test_descr = "AES-128-CTR HMAC-SHA1 Encryption Digest",
		.test_data = &aes_test_data_1,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
	},
	{
		.test_descr = "AES-128-CTR HMAC-SHA1 Decryption Digest "
			"Verify",
		.test_data = &aes_test_data_1,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
	},
	{
		.test_descr = "AES-192-CTR XCBC Encryption Digest",
		.test_data = &aes_test_data_2,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
	},
	{
		.test_descr = "AES-192-CTR XCBC Decryption Digest Verify",
		.test_data = &aes_test_data_2,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
	},
	{
		.test_descr = "AES-192-CTR XCBC Decryption Digest Verify "
				"Scatter Gather (Inplace)",
		.test_data = &aes_test_data_2,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG,
		.sgl_flag = RTE_CRYPTODEV_FF_IN_PLACE_SGL,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-192-CTR XCBC Decryption Digest Verify "
				"Scatter Gather OOP (SGL in SGL out)",
		.test_data = &aes_test_data_2,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_SGL_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-192-CTR XCBC Decryption Digest Verify "
				"Scatter Gather OOP (LB in SGL out)",
		.test_data = &aes_test_data_2,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_LB_IN_SGL_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-192-CTR XCBC Decryption Digest Verify "
				"Scatter Gather OOP (SGL in LB out)",
		.test_data = &aes_test_data_2,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_LB_OUT,
		.sgl_segs = 3
	},

	{
		.test_descr = "AES-256-CTR HMAC-SHA1 Encryption Digest",
		.test_data = &aes_test_data_3,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
	},
	{
		.test_descr = "AES-256-CTR HMAC-SHA1 Decryption Digest "
			"Verify",
		.test_data = &aes_test_data_3,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Encryption Digest",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Encryption Digest "
			"(short buffers)",
		.test_data = &aes_test_data_13,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Encryption Digest "
				"Scatter Gather (Inplace)",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG,
		.sgl_flag = RTE_CRYPTODEV_FF_IN_PLACE_SGL,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Encryption Digest "
				"Scatter Gather OOP (SGL in SGL out)",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_SGL_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Encryption Digest "
				"Scatter Gather OOP 16 segs (SGL in SGL out)",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_SGL_OUT,
		.sgl_segs = 16
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Encryption Digest "
				"Scatter Gather OOP (LB in SGL out)",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_LB_IN_SGL_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Encryption Digest "
				"Scatter Gather OOP (SGL in LB out)",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_LB_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Decryption Digest "
			"Verify",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Decryption Digest "
			"Verify Scatter Gather (Inplace)",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG,
		.sgl_flag = RTE_CRYPTODEV_FF_IN_PLACE_SGL,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Decryption Digest "
			"Verify Scatter Gather OOP (SGL in SGL out)",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_SGL_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Decryption Digest "
			"Verify Scatter Gather OOP 16 segs (SGL in SGL out)",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_SGL_OUT,
		.sgl_segs = 16
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Decryption Digest "
			"Verify Scatter Gather OOP (LB in SGL out)",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_LB_IN_SGL_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Decryption Digest "
			"Verify Scatter Gather OOP (SGL in LB out)",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_LB_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Decryption Digest "
			"Verify (short buffers)",
		.test_data = &aes_test_data_13,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA256 Encryption Digest",
		.test_data = &aes_test_data_5,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA256 Encryption Digest "
			"(short buffers)",
		.test_data = &aes_test_data_12,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA256 Decryption Digest "
			"Verify",
		.test_data = &aes_test_data_5,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA256 Decryption Digest "
			"Verify (short buffers)",
		.test_data = &aes_test_data_12,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA512 Encryption Digest",
		.test_data = &aes_test_data_6,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA512 Encryption Digest "
			"Sessionless",
		.test_data = &aes_test_data_6,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SESSIONLESS,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA512 Encryption Digest "
				"Scatter Gather Sessionless (Inplace)",
		.test_data = &aes_test_data_6,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SESSIONLESS |
			BLOCKCIPHER_TEST_FEATURE_SG,
		.sgl_flag = RTE_CRYPTODEV_FF_IN_PLACE_SGL,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA512 Encryption Digest "
				"Scatter Gather Sessionless OOP (SGL in SGL out)",
		.test_data = &aes_test_data_6,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SESSIONLESS |
			BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_SGL_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA512 Encryption Digest "
				"Scatter Gather Sessionless OOP (LB in SGL out)",
		.test_data = &aes_test_data_6,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SESSIONLESS |
			BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_LB_IN_SGL_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA512 Encryption Digest "
				"Scatter Gather Sessionless OOP (SGL in LB out)",
		.test_data = &aes_test_data_6,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SESSIONLESS |
			BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_LB_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA512 Decryption Digest "
			"Verify",
		.test_data = &aes_test_data_6,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA512 Decryption Digest "
			"Verify Scatter Gather (Inplace)",
		.test_data = &aes_test_data_6,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG,
		.sgl_flag = RTE_CRYPTODEV_FF_IN_PLACE_SGL,
		.sgl_segs = 2
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA512 Decryption Digest "
			"Verify Scatter Gather OOP (SGL in SGL out)",
		.test_data = &aes_test_data_6,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_SGL_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA512 Decryption Digest "
			"Verify Scatter Gather OOP (LB in SGL out)",
		.test_data = &aes_test_data_6,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_LB_IN_SGL_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA512 Decryption Digest "
			"Verify Scatter Gather OOP (SGL in LB out)",
		.test_data = &aes_test_data_6,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_LB_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC XCBC Encryption Digest",
		.test_data = &aes_test_data_7,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
	},
	{
		.test_descr = "AES-128-CBC XCBC Decryption Digest Verify",
		.test_data = &aes_test_data_7,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Encryption Digest "
			"OOP",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Encryption Digest "
			"OOP Offset",
		.test_data = &aes_test_data_14,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Decryption Digest "
			"Verify OOP",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA224 Encryption Digest",
		.test_data = &aes_test_data_8,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA224 Decryption Digest "
			"Verify",
		.test_data = &aes_test_data_8,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA384 Encryption Digest",
		.test_data = &aes_test_data_9,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA384 Decryption Digest "
			"Verify",
		.test_data = &aes_test_data_9,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Encryption Digest "
				"Sessionless",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SESSIONLESS,
	},
	{
		.test_descr =
				"AES-128-CBC HMAC-SHA1 Decryption Digest "
				"Verify Sessionless",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SESSIONLESS,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Encryption Digest"
			"(Encrypted Digest mode)",
		.test_data = &aes_test_data_4_digest_enc,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN_ENC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_DIGEST_ENCRYPTED,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Encryption Digest "
			"Scatter Gather (Encrypted Digest mode)",
		.test_data = &aes_test_data_4_digest_enc,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN_ENC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
				BLOCKCIPHER_TEST_FEATURE_DIGEST_ENCRYPTED,
		.sgl_flag = RTE_CRYPTODEV_FF_IN_PLACE_SGL,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Encryption Digest "
			"(short buffers) (Encrypted Digest mode)",
		.test_data = &aes_test_data_13_digest_enc,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN_ENC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_DIGEST_ENCRYPTED,
	},
	{
		.test_descr = "AES-192-CBC HMAC-SHA1 Encryption Digest "
			"Sessionless (Encrypted Digest mode)",
		.test_data = &aes_test_data_10_digest_enc,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN_ENC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SESSIONLESS |
				BLOCKCIPHER_TEST_FEATURE_DIGEST_ENCRYPTED,
	},
	{
		.test_descr = "AES-256-CBC HMAC-SHA1 Encryption Digest "
			"Scatter Gather Sessionless (Encrypted Digest mode)",
		.test_data = &aes_test_data_11_digest_enc,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN_ENC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_SESSIONLESS |
			BLOCKCIPHER_TEST_FEATURE_DIGEST_ENCRYPTED,
		.sgl_flag = RTE_CRYPTODEV_FF_IN_PLACE_SGL,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Decryption Digest "
			"Verify (Encrypted Digest mode)",
		.test_data = &aes_test_data_4_digest_enc,
		.op_mask = BLOCKCIPHER_TEST_OP_DEC_AUTH_VERIFY,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_DIGEST_ENCRYPTED,
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Decryption Digest "
			"Verify Scatter Gather (Encrypted Digest mode)",
		.test_data = &aes_test_data_4_digest_enc,
		.op_mask = BLOCKCIPHER_TEST_OP_DEC_AUTH_VERIFY,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
				BLOCKCIPHER_TEST_FEATURE_DIGEST_ENCRYPTED,
		.sgl_flag = RTE_CRYPTODEV_FF_IN_PLACE_SGL,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Decryption Digest "
			"Verify (short buffers) (Encrypted Digest mode)",
		.test_data = &aes_test_data_13_digest_enc,
		.op_mask = BLOCKCIPHER_TEST_OP_DEC_AUTH_VERIFY,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_DIGEST_ENCRYPTED,
	},
	{
		.test_descr = "AES-256-CBC HMAC-SHA1 Decryption Digest "
			"Verify Sessionless (Encrypted Digest mode)",
		.test_data = &aes_test_data_11_digest_enc,
		.op_mask = BLOCKCIPHER_TEST_OP_DEC_AUTH_VERIFY,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SESSIONLESS |
				BLOCKCIPHER_TEST_FEATURE_DIGEST_ENCRYPTED,
	},
	{
		.test_descr = "AES-192-CBC HMAC-SHA1 Decryption Digest "
			"Verify Scatter Gather Sessionless (Encrypted Digest mode)",
		.test_data = &aes_test_data_10_digest_enc,
		.op_mask = BLOCKCIPHER_TEST_OP_DEC_AUTH_VERIFY,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SESSIONLESS |
				BLOCKCIPHER_TEST_FEATURE_SG |
				BLOCKCIPHER_TEST_FEATURE_DIGEST_ENCRYPTED,
		.sgl_flag = RTE_CRYPTODEV_FF_IN_PLACE_SGL,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC HMAC-SHA1 Decryption Digest "
			"Verify Sessionless (Encrypted Digest mode)",
		.test_data = &aes_test_data_4_digest_enc,
		.op_mask = BLOCKCIPHER_TEST_OP_DEC_AUTH_VERIFY,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SESSIONLESS |
				BLOCKCIPHER_TEST_FEATURE_DIGEST_ENCRYPTED,
	},
	{
		.test_descr = "NULL-CIPHER-NULL-AUTH encrypt & gen x8byte",
		.test_data = &null_test_data_chain_x8_multiple,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
	},
	{
		.test_descr = "NULL-AUTH-NULL-CIPHER verify & decrypt x8byte",
		.test_data = &null_test_data_chain_x8_multiple,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
	},
	{
		.test_descr = "NULL-CIPHER-NULL-AUTH encrypt & gen x8byte - OOP",
		.test_data = &null_test_data_chain_x8_multiple,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "NULL-AUTH-NULL-CIPHER verify & decrypt x8byte - OOP",
		.test_data = &null_test_data_chain_x8_multiple,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "NULL-CIPHER-NULL-AUTH encrypt & gen x4byte",
		.test_data = &null_test_data_chain_x4_multiple,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
	},
	{
		.test_descr = "NULL-AUTH-NULL-CIPHER verify & decrypt x4byte",
		.test_data = &null_test_data_chain_x4_multiple,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
	},
	{
		.test_descr = "NULL-CIPHER-NULL-AUTH encrypt & gen x4byte - OOP",
		.test_data = &null_test_data_chain_x4_multiple,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "NULL-AUTH-NULL-CIPHER verify & decrypt x4byte - OOP",
		.test_data = &null_test_data_chain_x4_multiple,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "NULL-CIPHER-NULL-AUTH encrypt & gen x1byte",
		.test_data = &null_test_data_chain_x1_multiple,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
	},
	{
		.test_descr = "NULL-AUTH-NULL-CIPHER verify & decrypt x1byte",
		.test_data = &null_test_data_chain_x1_multiple,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
	},
	{
		.test_descr = "NULL-CIPHER-NULL-AUTH encrypt & gen x1byte - OOP",
		.test_data = &null_test_data_chain_x1_multiple,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "NULL-AUTH-NULL-CIPHER verify & decrypt x1byte - OOP",
		.test_data = &null_test_data_chain_x1_multiple,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
};

static const struct blockcipher_test_case aes_cipheronly_test_cases[] = {
	{
		.test_descr = "AES-128-CBC Encryption",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-128-CBC Encryption Scatter gather (Inplace)",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG,
		.sgl_flag = RTE_CRYPTODEV_FF_IN_PLACE_SGL,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC Encryption Scatter gather OOP (SGL in SGL out)",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_SGL_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC Encryption Scatter gather OOP (LB in SGL out)",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_LB_IN_SGL_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC Encryption Scatter gather OOP (SGL in LB out)",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_LB_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-128-CBC Decryption",
		.test_data = &aes_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-192-CBC Encryption",
		.test_data = &aes_test_data_10,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-192-CBC Encryption Scatter gather (Inplace)",
		.test_data = &aes_test_data_10,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG,
		.sgl_flag = RTE_CRYPTODEV_FF_IN_PLACE_SGL,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-192-CBC Encryption Scatter gather OOP (SGL in SGL out)",
		.test_data = &aes_test_data_10,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_SGL_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-192-CBC Encryption Scatter gather OOP (LB in SGL out)",
		.test_data = &aes_test_data_10,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_LB_IN_SGL_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-192-CBC Encryption Scatter gather OOP (SGL in LB out)",
		.test_data = &aes_test_data_10,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_LB_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-192-CBC Decryption",
		.test_data = &aes_test_data_10,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-192-CBC Decryption Scatter Gather (Inplace)",
		.test_data = &aes_test_data_10,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG,
		.sgl_flag = RTE_CRYPTODEV_FF_IN_PLACE_SGL,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-192-CBC Decryption Scatter Gather OOP (SGL in SGL out)",
		.test_data = &aes_test_data_10,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_SGL_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-192-CBC Decryption Scatter Gather OOP (LB in SGL out)",
		.test_data = &aes_test_data_10,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_LB_IN_SGL_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-192-CBC Decryption Scatter Gather OOP (SGL in LB out)",
		.test_data = &aes_test_data_10,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_LB_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-256-CBC Encryption",
		.test_data = &aes_test_data_11,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-256-CBC Decryption",
		.test_data = &aes_test_data_11,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-256-CBC OOP Encryption",
		.test_data = &aes_test_data_11,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "AES-256-CBC OOP Decryption",
		.test_data = &aes_test_data_11,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "AES-128-CTR Encryption",
		.test_data = &aes_test_data_1,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-128-CTR Decryption",
		.test_data = &aes_test_data_1,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-192-CTR Encryption",
		.test_data = &aes_test_data_2,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-192-CTR Decryption",
		.test_data = &aes_test_data_2,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-256-CTR Encryption",
		.test_data = &aes_test_data_3,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-256-CTR Decryption",
		.test_data = &aes_test_data_3,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-128-CTR Encryption (12-byte IV)",
		.test_data = &aes_test_data_1_IV_12_bytes,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-192-CTR Encryption (12-byte IV)",
		.test_data = &aes_test_data_2_IV_12_bytes,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-256-CTR Encryption (12-byte IV)",
		.test_data = &aes_test_data_3_IV_12_bytes,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-128-XTS Encryption (16-byte plaintext)",
		.test_data = &aes_test_data_xts_key_32_pt_16,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-128-XTS Decryption (16-byte plaintext)",
		.test_data = &aes_test_data_xts_key_32_pt_16,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-128-XTS Encryption (32-byte plaintext)",
		.test_data = &aes_test_data_xts_key_32_pt_32,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-128-XTS Decryption (32-byte plaintext)",
		.test_data = &aes_test_data_xts_key_32_pt_32,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-256-XTS Encryption (32-byte plaintext)",
		.test_data = &aes_test_data_xts_key_64_pt_32,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-256-XTS Decryption (32-byte plaintext)",
		.test_data = &aes_test_data_xts_key_64_pt_32,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-256-XTS Encryption (48-byte plaintext)",
		.test_data = &aes_test_data_xts_key_64_pt_48,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-256-XTS Decryption (48-byte plaintext)",
		.test_data = &aes_test_data_xts_key_64_pt_48,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-256-XTS Encryption (512-byte plaintext"
			      " Dataunit 512)",
		.test_data = &aes_test_data_xts_wrapped_key_48_pt_512_du_512,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-256-XTS Decryption (512-byte plaintext"
			      " Dataunit 512)",
		.test_data = &aes_test_data_xts_wrapped_key_48_pt_512_du_512,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-256-XTS Encryption (512-byte plaintext"
			      " Dataunit 0)",
		.test_data = &aes_test_data_xts_wrapped_key_48_pt_512_du_0,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-256-XTS Decryption (512-byte plaintext"
			      " Dataunit 0)",
		.test_data = &aes_test_data_xts_wrapped_key_48_pt_512_du_0,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-256-XTS Encryption (4096-byte plaintext"
			      " Dataunit 4096)",
		.test_data = &aes_test_data_xts_wrapped_key_48_pt_4096_du_4096,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-256-XTS Decryption (4096-byte plaintext"
			      " Dataunit 4096)",
		.test_data = &aes_test_data_xts_wrapped_key_48_pt_4096_du_4096,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-256-XTS Encryption (4096-byte plaintext"
			      " Dataunit 0)",
		.test_data = &aes_test_data_xts_wrapped_key_48_pt_4096_du_0,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-256-XTS Decryption (4096-byte plaintext"
			      " Dataunit 0)",
		.test_data = &aes_test_data_xts_wrapped_key_48_pt_4096_du_0,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-256-XTS Encryption (512-byte plaintext"
			      " Dataunit 512) Scatter gather OOP",
		.test_data = &aes_test_data_xts_wrapped_key_48_pt_512_du_512,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP |
				BLOCKCIPHER_TEST_FEATURE_SG,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_LB_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-256-XTS Decryption (512-byte plaintext"
			      " Dataunit 512) Scatter gather OOP",
		.test_data = &aes_test_data_xts_wrapped_key_48_pt_512_du_512,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP |
				BLOCKCIPHER_TEST_FEATURE_SG,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_LB_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-256-XTS Encryption (512-byte plaintext"
			      " Dataunit 0) Scatter gather OOP",
		.test_data = &aes_test_data_xts_wrapped_key_48_pt_512_du_0,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP |
				BLOCKCIPHER_TEST_FEATURE_SG,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_LB_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-256-XTS Decryption (512-byte plaintext"
			      " Dataunit 0) Scatter gather OOP",
		.test_data = &aes_test_data_xts_wrapped_key_48_pt_512_du_0,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP |
				BLOCKCIPHER_TEST_FEATURE_SG,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_LB_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-256-XTS Encryption (4096-byte plaintext"
			      " Dataunit 4096) Scatter gather OOP",
		.test_data = &aes_test_data_xts_wrapped_key_48_pt_4096_du_4096,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP |
				BLOCKCIPHER_TEST_FEATURE_SG,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_LB_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-256-XTS Decryption (4096-byte plaintext"
			      " Dataunit 4096) Scatter gather OOP",
		.test_data = &aes_test_data_xts_wrapped_key_48_pt_4096_du_4096,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP |
				BLOCKCIPHER_TEST_FEATURE_SG,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_LB_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-256-XTS Encryption (4096-byte plaintext"
			      " Dataunit 0) Scatter gather OOP",
		.test_data = &aes_test_data_xts_wrapped_key_48_pt_4096_du_0,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP |
				BLOCKCIPHER_TEST_FEATURE_SG,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_LB_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "AES-256-XTS Decryption (4096-byte plaintext"
			      " Dataunit 0) Scatter gather OOP",
		.test_data = &aes_test_data_xts_wrapped_key_48_pt_4096_du_0,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP |
				BLOCKCIPHER_TEST_FEATURE_SG,
		.sgl_flag = RTE_CRYPTODEV_FF_OOP_SGL_IN_LB_OUT,
		.sgl_segs = 3
	},
	{
		.test_descr = "cipher-only - NULL algo - x8 - encryption",
		.test_data = &null_test_data_chain_x8_multiple,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "cipher-only - NULL algo - x8 - decryption",
		.test_data = &null_test_data_chain_x8_multiple,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "cipher-only - NULL algo - x4 - encryption",
		.test_data = &null_test_data_chain_x4_multiple,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "cipher-only - NULL algo - x4 - decryption",
		.test_data = &null_test_data_chain_x4_multiple,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "cipher-only - NULL algo - x4 - encryption - OOP",
		.test_data = &null_test_data_chain_x4_multiple,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "cipher-only - NULL algo - x4 - decryption - OOP",
		.test_data = &null_test_data_chain_x4_multiple,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
		},
	{
		.test_descr = "cipher-only - NULL algo - x1 - encryption",
		.test_data = &null_test_data_chain_x1_multiple,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "cipher-only - NULL algo - x1 - decryption",
		.test_data = &null_test_data_chain_x1_multiple,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-128-ECB Encryption",
		.test_data = &aes_test_data_15,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-128-ECB Decryption",
		.test_data = &aes_test_data_15,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-192-ECB Encryption",
		.test_data = &aes_test_data_16,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-192-ECB Decryption",
		.test_data = &aes_test_data_16,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-256-ECB Encryption",
		.test_data = &aes_test_data_17,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-256-ECB Decryption",
		.test_data = &aes_test_data_17,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-128-ECB Encryption (128-byte plaintext)",
		.test_data = &aes_test_data_18,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-128-ECB Decryption (128-byte plaintext)",
		.test_data = &aes_test_data_18,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-192-ECB Encryption (192-byte plaintext)",
		.test_data = &aes_test_data_19,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-192-ECB Decryption (192-byte plaintext)",
		.test_data = &aes_test_data_19,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-256-ECB Encryption (256-byte plaintext)",
		.test_data = &aes_test_data_20,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-256-ECB Decryption (256-byte plaintext)",
		.test_data = &aes_test_data_20,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-128-ECB Encryption (1008-byte plaintext)",
		.test_data = &aes_test_data_21,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-128-ECB Decryption (1008-byte plaintext)",
		.test_data = &aes_test_data_21,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
};

static const struct blockcipher_test_case aes_docsis_test_cases[] = {

	{
		.test_descr = "AES-DOCSIS-BPI Full Block Encryption",
		.test_data = &aes_test_data_docsis_1,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-DOCSIS-BPI Runt Block Encryption",
		.test_data = &aes_test_data_docsis_2,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-DOCSIS-BPI Uneven Encryption",
		.test_data = &aes_test_data_docsis_3,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-DOCSIS-BPI Full Block Decryption",
		.test_data = &aes_test_data_docsis_1,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-DOCSIS-BPI Runt Block Decryption",
		.test_data = &aes_test_data_docsis_2,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-DOCSIS-BPI Uneven Decryption",
		.test_data = &aes_test_data_docsis_3,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-DOCSIS-BPI OOP Full Block Encryption",
		.test_data = &aes_test_data_docsis_1,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "AES-DOCSIS-BPI OOP Runt Block Encryption",
		.test_data = &aes_test_data_docsis_2,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},

	{
		.test_descr = "AES-DOCSIS-BPI OOP Uneven Block Encryption",
		.test_data = &aes_test_data_docsis_3,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "AES-DOCSIS-BPI OOP Full Block Decryption",
		.test_data = &aes_test_data_docsis_1,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "AES-DOCSIS-BPI OOP Runt Block Decryption",
		.test_data = &aes_test_data_docsis_2,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "AES-DOCSIS-BPI OOP Uneven Block Decryption",
		.test_data = &aes_test_data_docsis_3,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	/* AES 256 */
	{
		.test_descr = "AES-256-DOCSIS-BPI Full Block Encryption",
		.test_data = &aes_test_data_docsis_4,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-256-DOCSIS-BPI Runt Block Encryption",
		.test_data = &aes_test_data_docsis_5,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-256-DOCSIS-BPI Uneven Encryption",
		.test_data = &aes_test_data_docsis_6,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "AES-256-DOCSIS-BPI Full Block Decryption",
		.test_data = &aes_test_data_docsis_4,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-256-DOCSIS-BPI Runt Block Decryption",
		.test_data = &aes_test_data_docsis_5,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-256-DOCSIS-BPI Uneven Decryption",
		.test_data = &aes_test_data_docsis_6,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "AES-256-DOCSIS-BPI OOP Full Block Encryption",
		.test_data = &aes_test_data_docsis_4,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "AES-256-DOCSIS-BPI OOP Runt Block Encryption",
		.test_data = &aes_test_data_docsis_5,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "AES-256-DOCSIS-BPI OOP Uneven Block Encryption",
		.test_data = &aes_test_data_docsis_6,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "AES-256-DOCSIS-BPI OOP Full Block Decryption",
		.test_data = &aes_test_data_docsis_4,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "AES-256-DOCSIS-BPI OOP Runt Block Decryption",
		.test_data = &aes_test_data_docsis_5,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "AES-256-DOCSIS-BPI OOP Uneven Block Decryption",
		.test_data = &aes_test_data_docsis_6,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
};
#endif /* TEST_CRYPTODEV_AES_TEST_VECTORS_H_ */
