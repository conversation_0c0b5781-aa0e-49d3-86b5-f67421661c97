/* SPDX-License-Identifier: BSD-3-Clause
 * Copyright(c) 2018 Cavium Networks
 * Copyright (c) 2019 Intel Corporation
 */

#ifndef TEST_CRYPTODEV_MOD_TEST_VECTORS_H_
#define TEST_CRYPTODEV_MOD_TEST_VECTORS_H_

#define DATA_SIZE 1024

struct modex_test_data {
	enum rte_crypto_asym_xform_type xform_type;
	const char *description;
	struct {
		uint8_t data[DATA_SIZE];
		uint16_t len;
	} base;
	struct {
		uint8_t data[DATA_SIZE];
		uint16_t len;
	} exponent;
	struct {
		uint8_t data[DATA_SIZE];
		uint16_t len;
	} modulus;
	struct {
		uint8_t data[DATA_SIZE];
		uint16_t len;
	} reminder;
};
struct modinv_test_data {
	enum rte_crypto_asym_xform_type xform_type;
	const char *description;
	struct {
		uint8_t data[DATA_SIZE];
		uint16_t len;
	} base;
	struct {
		uint8_t data[DATA_SIZE];
		uint16_t len;
	} modulus;
	struct {
		uint8_t data[DATA_SIZE];
		uint16_t len;
	} inverse;
};

/* ModExp #1 */
static const struct
modex_test_data modex_test_case_m128_b20_e3 = {
	.description =
		"Modular Exponentiation (mod=128, base=20, exp=3, res=128)",
	.xform_type = RTE_CRYPTO_ASYM_XFORM_MODEX,
	.base = {
		.data = {
			0xF8, 0xBA, 0x1A, 0x55, 0xD0, 0x2F, 0x85,
			0xAE, 0x96, 0x7B, 0xB6, 0x2F, 0xB6, 0xCD,
			0xA8, 0xEB, 0x7E, 0x78, 0xA0, 0x50
		},
		.len = 20
	},
	.exponent = {
		.data = {
			0x01, 0x00, 0x01
		},
		.len = 3
	},
	.reminder = {
		.data = {
			0x2C, 0x60, 0x75, 0x45, 0x98, 0x9D, 0xE0, 0x72,
			0xA0, 0x9D, 0x3A, 0x9E, 0x03, 0x38, 0x73, 0x3C,
			0x31, 0x83, 0x04, 0xFE, 0x75, 0x43, 0xE6, 0x17,
			0x5C, 0x01, 0x29, 0x51, 0x69, 0x33, 0x62, 0x2D,
			0x78, 0xBE, 0xAE, 0xC4, 0xBC, 0xDE, 0x7E, 0x2C,
			0x77, 0x84, 0xF2, 0xC5, 0x14, 0xB5, 0x2F, 0xF7,
			0xC5, 0x94, 0xEF, 0x86, 0x75, 0x75, 0xB5, 0x11,
			0xE5, 0x0E, 0x0A, 0x29, 0x76, 0xE2, 0xEA, 0x32,
			0x0E, 0x43, 0x77, 0x7E, 0x2C, 0x27, 0xAC, 0x3B,
			0x86, 0xA5, 0xDB, 0xC9, 0x48, 0x40, 0xE8, 0x99,
			0x9A, 0x0A, 0x3D, 0xD6, 0x74, 0xFA, 0x2E, 0x2E,
			0x5B, 0xAF, 0x8C, 0x99, 0x44, 0x2A, 0x67, 0x38,
			0x27, 0x41, 0x59, 0x9D, 0xB8, 0x51, 0xC9, 0xF7,
			0x43, 0x61, 0x31, 0x6E, 0xF1, 0x25, 0x38, 0x7F,
			0xAE, 0xC6, 0xD0, 0xBB, 0x29, 0x76, 0x3F, 0x46,
			0x2E, 0x1B, 0xE4, 0x67, 0x71, 0xE3, 0x87, 0x5A
		},
		.len = 128
	},
	.modulus = {
		.data = {
			0xb3, 0xa1, 0xaf, 0xb7, 0x13, 0x08, 0x00, 0x0a,
			0x35, 0xdc, 0x2b, 0x20, 0x8d, 0xa1, 0xb5, 0xce,
			0x47, 0x8a, 0xc3, 0x80, 0xf4, 0x7d, 0x4a, 0xa2,
			0x62, 0xfd, 0x61, 0x7f, 0xb5, 0xa8, 0xde, 0x0a,
			0x17, 0x97, 0xa0, 0xbf, 0xdf, 0x56, 0x5a, 0x3d,
			0x51, 0x56, 0x4f, 0x70, 0x70, 0x3f, 0x63, 0x6a,
			0x44, 0x5b, 0xad, 0x84, 0x0d, 0x3f, 0x27, 0x6e,
			0x3b, 0x34, 0x91, 0x60, 0x14, 0xb9, 0xaa, 0x72,
			0xfd, 0xa3, 0x64, 0xd2, 0x03, 0xa7, 0x53, 0x87,
			0x9e, 0x88, 0x0b, 0xc1, 0x14, 0x93, 0x1a, 0x62,
			0xff, 0xb1, 0x5d, 0x74, 0xcd, 0x59, 0x63, 0x18,
			0x11, 0x3d, 0x4f, 0xba, 0x75, 0xd4, 0x33, 0x4e,
			0x23, 0x6b, 0x7b, 0x57, 0x44, 0xe1, 0xd3, 0x03,
			0x13, 0xa6, 0xf0, 0x8b, 0x60, 0xb0, 0x9e, 0xee,
			0x75, 0x08, 0x9d, 0x71, 0x63, 0x13, 0xcb, 0xa6,
			0x81, 0x92, 0x14, 0x03, 0x22, 0x2d, 0xde, 0x55
		},
		.len = 128
	},
};

/* ModInv #1 */
static const struct
modinv_test_data modinv_test_case = {
	.description = "Modular Inverse (mod=128, base=20, exp=3, inv=128)",
	.xform_type = RTE_CRYPTO_ASYM_XFORM_MODINV,
	.base = {
		.data = {
			0xF8, 0xBA, 0x1A, 0x55, 0xD0, 0x2F, 0x85,
			0xAE, 0x96, 0x7B, 0xB6, 0x2F, 0xB6, 0xCD,
			0xA8, 0xEB, 0x7E, 0x78, 0xA0, 0x50
		},
		.len = 20
	},
	.inverse = {
		.data = {
			0x52, 0xb1, 0xa3, 0x8c, 0xc5, 0x8a, 0xb9, 0x1f,
			0xb6, 0x82, 0xf5, 0x6a, 0x9a, 0xde, 0x8d, 0x2e,
			0x62, 0x4b, 0xac, 0x49, 0x21, 0x1d, 0x30, 0x4d,
			0x32, 0xac, 0x1f, 0x40, 0x6d, 0x52, 0xc7, 0x9b,
			0x6c, 0x0a, 0x82, 0x3a, 0x2c, 0xaf, 0x6b, 0x6d,
			0x17, 0xbe, 0x43, 0xed, 0x97, 0x78, 0xeb, 0x4c,
			0x92, 0x6f, 0xcf, 0xed, 0xb1, 0x09, 0xcb, 0x27,
			0xc2, 0xde, 0x62, 0xfd, 0x21, 0xe6, 0xbd, 0x4f,
			0xfe, 0x7a, 0x1b, 0x50, 0xfe, 0x10, 0x4a, 0xb0,
			0xb7, 0xcf, 0xdb, 0x7d, 0xca, 0xc2, 0xf0, 0x1c,
			0x39, 0x48, 0x6a, 0xb5, 0x4d, 0x8c, 0xfe, 0x63,
			0x91, 0x9c, 0x21, 0xc3, 0x0e, 0x76, 0xad, 0x44,
			0x8d, 0x54, 0x33, 0x99, 0xe1, 0x80, 0x19, 0xba,
			0xb5, 0xac, 0x7d, 0x9c, 0xce, 0x91, 0x2a, 0xd9,
			0x2c, 0xe1, 0x16, 0xd6, 0xd7, 0xcf, 0x9d, 0x05,
			0x9a, 0x66, 0x9a, 0x3a, 0xc1, 0xb8, 0x4b, 0xc3
		},
		.len = 128
	},
	.modulus = {
		.data = {
			0xb3, 0xa1, 0xaf, 0xb7, 0x13, 0x08, 0x00, 0x0a,
			0x35, 0xdc, 0x2b, 0x20, 0x8d, 0xa1, 0xb5, 0xce,
			0x47, 0x8a, 0xc3, 0x80, 0xf4, 0x7d, 0x4a, 0xa2,
			0x62, 0xfd, 0x61, 0x7f, 0xb5, 0xa8, 0xde, 0x0a,
			0x17, 0x97, 0xa0, 0xbf, 0xdf, 0x56, 0x5a, 0x3d,
			0x51, 0x56, 0x4f, 0x70, 0x70, 0x3f, 0x63, 0x6a,
			0x44, 0x5b, 0xad, 0x84, 0x0d, 0x3f, 0x27, 0x6e,
			0x3b, 0x34, 0x91, 0x60, 0x14, 0xb9, 0xaa, 0x72,
			0xfd, 0xa3, 0x64, 0xd2, 0x03, 0xa7, 0x53, 0x87,
			0x9e, 0x88, 0x0b, 0xc1, 0x14, 0x93, 0x1a, 0x62,
			0xff, 0xb1, 0x5d, 0x74, 0xcd, 0x59, 0x63, 0x18,
			0x11, 0x3d, 0x4f, 0xba, 0x75, 0xd4, 0x33, 0x4e,
			0x23, 0x6b, 0x7b, 0x57, 0x44, 0xe1, 0xd3, 0x03,
			0x13, 0xa6, 0xf0, 0x8b, 0x60, 0xb0, 0x9e, 0xee,
			0x75, 0x08, 0x9d, 0x71, 0x63, 0x13, 0xcb, 0xa6,
			0x81, 0x92, 0x14, 0x03, 0x22, 0x2d, 0xde, 0x55
		},
		.len = 128
	},
};

/* modular operation test data */
uint8_t base[] = {
	0xF8, 0xBA, 0x1A, 0x55, 0xD0, 0x2F, 0x85,
	0xAE, 0x96, 0x7B, 0xB6, 0x2F, 0xB6, 0xCD,
	0xA8, 0xEB, 0x7E, 0x78, 0xA0, 0x50
};

/* MODEX data. 8< */
uint8_t mod_p[] = {
	0x00, 0xb3, 0xa1, 0xaf, 0xb7, 0x13, 0x08, 0x00,
	0x0a, 0x35, 0xdc, 0x2b, 0x20, 0x8d, 0xa1, 0xb5,
	0xce, 0x47, 0x8a, 0xc3, 0x80, 0xf4, 0x7d, 0x4a,
	0xa2, 0x62, 0xfd, 0x61, 0x7f, 0xb5, 0xa8, 0xde,
	0x0a, 0x17, 0x97, 0xa0, 0xbf, 0xdf, 0x56, 0x5a,
	0x3d, 0x51, 0x56, 0x4f, 0x70, 0x70, 0x3f, 0x63,
	0x6a, 0x44, 0x5b, 0xad, 0x84, 0x0d, 0x3f, 0x27,
	0x6e, 0x3b, 0x34, 0x91, 0x60, 0x14, 0xb9, 0xaa,
	0x72, 0xfd, 0xa3, 0x64, 0xd2, 0x03, 0xa7, 0x53,
	0x87, 0x9e, 0x88, 0x0b, 0xc1, 0x14, 0x93, 0x1a,
	0x62, 0xff, 0xb1, 0x5d, 0x74, 0xcd, 0x59, 0x63,
	0x18, 0x11, 0x3d, 0x4f, 0xba, 0x75, 0xd4, 0x33,
	0x4e, 0x23, 0x6b, 0x7b, 0x57, 0x44, 0xe1, 0xd3,
	0x03, 0x13, 0xa6, 0xf0, 0x8b, 0x60, 0xb0, 0x9e,
	0xee, 0x75, 0x08, 0x9d, 0x71, 0x63, 0x13, 0xcb,
	0xa6, 0x81, 0x92, 0x14, 0x03, 0x22, 0x2d, 0xde,
	0x55
};

uint8_t mod_e[] = {0x01, 0x00, 0x01};
/* >8 End of MODEX data. */

/* Precomputed modular exponentiation for verification */
uint8_t mod_exp[] = {
	0x2C, 0x60, 0x75, 0x45, 0x98, 0x9D, 0xE0, 0x72,
	0xA0, 0x9D, 0x3A, 0x9E, 0x03, 0x38, 0x73, 0x3C,
	0x31, 0x83, 0x04, 0xFE, 0x75, 0x43, 0xE6, 0x17,
	0x5C, 0x01, 0x29, 0x51, 0x69, 0x33, 0x62, 0x2D,
	0x78, 0xBE, 0xAE, 0xC4, 0xBC, 0xDE, 0x7E, 0x2C,
	0x77, 0x84, 0xF2, 0xC5, 0x14, 0xB5, 0x2F, 0xF7,
	0xC5, 0x94, 0xEF, 0x86, 0x75, 0x75, 0xB5, 0x11,
	0xE5, 0x0E, 0x0A, 0x29, 0x76, 0xE2, 0xEA, 0x32,
	0x0E, 0x43, 0x77, 0x7E, 0x2C, 0x27, 0xAC, 0x3B,
	0x86, 0xA5, 0xDB, 0xC9, 0x48, 0x40, 0xE8, 0x99,
	0x9A, 0x0A, 0x3D, 0xD6, 0x74, 0xFA, 0x2E, 0x2E,
	0x5B, 0xAF, 0x8C, 0x99, 0x44, 0x2A, 0x67, 0x38,
	0x27, 0x41, 0x59, 0x9D, 0xB8, 0x51, 0xC9, 0xF7,
	0x43, 0x61, 0x31, 0x6E, 0xF1, 0x25, 0x38, 0x7F,
	0xAE, 0xC6, 0xD0, 0xBB, 0x29, 0x76, 0x3F, 0x46,
	0x2E, 0x1B, 0xE4, 0x67, 0x71, 0xE3, 0x87, 0x5A
};

/* Precomputed modular inverse for verification */
uint8_t mod_inv[] = {
	0x52, 0xb1, 0xa3, 0x8c, 0xc5, 0x8a, 0xb9, 0x1f,
	0xb6, 0x82, 0xf5, 0x6a, 0x9a, 0xde, 0x8d, 0x2e,
	0x62, 0x4b, 0xac, 0x49, 0x21, 0x1d, 0x30, 0x4d,
	0x32, 0xac, 0x1f, 0x40, 0x6d, 0x52, 0xc7, 0x9b,
	0x6c, 0x0a, 0x82, 0x3a, 0x2c, 0xaf, 0x6b, 0x6d,
	0x17, 0xbe, 0x43, 0xed, 0x97, 0x78, 0xeb, 0x4c,
	0x92, 0x6f, 0xcf, 0xed, 0xb1, 0x09, 0xcb, 0x27,
	0xc2, 0xde, 0x62, 0xfd, 0x21, 0xe6, 0xbd, 0x4f,
	0xfe, 0x7a, 0x1b, 0x50, 0xfe, 0x10, 0x4a, 0xb0,
	0xb7, 0xcf, 0xdb, 0x7d, 0xca, 0xc2, 0xf0, 0x1c,
	0x39, 0x48, 0x6a, 0xb5, 0x4d, 0x8c, 0xfe, 0x63,
	0x91, 0x9c, 0x21, 0xc3, 0x0e, 0x76, 0xad, 0x44,
	0x8d, 0x54, 0x33, 0x99, 0xe1, 0x80, 0x19, 0xba,
	0xb5, 0xac, 0x7d, 0x9c, 0xce, 0x91, 0x2a, 0xd9,
	0x2c, 0xe1, 0x16, 0xd6, 0xd7, 0xcf, 0x9d, 0x05,
	0x9a, 0x66, 0x9a, 0x3a, 0xc1, 0xb8, 0x4b, 0xc3
};

/* MODEX vector. 8< */
struct rte_crypto_asym_xform modex_xform = {
	.next = NULL,
	.xform_type = RTE_CRYPTO_ASYM_XFORM_MODEX,
	.modex = {
		.modulus = {
			.data = mod_p,
			.length = sizeof(mod_p)
		},
		.exponent = {
			.data = mod_e,
			.length = sizeof(mod_e)
		}
	}
};
/* >8 End of MODEX vector. */

struct rte_crypto_asym_xform modinv_xform = {
	.next = NULL,
	.xform_type = RTE_CRYPTO_ASYM_XFORM_MODINV,
	.modinv = {
		.modulus = {
			.data = mod_p,
			.length = sizeof(mod_p)
		}
	}
};

static const struct
modex_test_data modex_test_cases[] = {
{
	.description = "Modular Exponentiation (mod=20, base=20, exp=12, res=18)",
	.xform_type = RTE_CRYPTO_ASYM_XFORM_MODEX,
	.base = {
		.data = {
			0x00, 0x00, 0x45, 0xCA, 0x2C, 0x5C, 0x3A, 0x90,
			0x00, 0xC4, 0xD7, 0x47, 0xA8, 0x2B, 0x12, 0x07,
			0xBD, 0x1F, 0xD7, 0x81
		},
		.len = 20
	},
	.exponent = {
		.data = {
			0x00, 0x00, 0x00, 0x75, 0x74, 0x19, 0x19, 0x69,
			0xBF, 0x15, 0x2A, 0xAC
		},
		.len = 12
	},
	.reminder = {
		.data = {
			0x5c, 0x94, 0x8f, 0x00, 0x79, 0xe3, 0xe1, 0x0b,
			0x3f, 0x3e, 0x36, 0x75, 0xed, 0x1d, 0x84, 0xc6,
			0x36, 0x9e
		},
		.len = 18
	},
	.modulus = {
		.data = {
			0x00, 0x00, 0x99, 0x28, 0x09, 0x8A, 0xE9, 0x89,
			0xBB, 0x81, 0x3B, 0x07, 0x0E, 0x31, 0x00, 0x7F,
			0x79, 0x97, 0xED, 0x35
		},
		.len = 20
	}
},
{
	.description = "Modular Exponentiation (mod=32, base=20, exp=12, res=17)",
	.xform_type = RTE_CRYPTO_ASYM_XFORM_MODEX,
	.base = {
		.data = {
			0x01, 0x31, 0x72, 0xFB, 0x81, 0x9D, 0x81, 0x7A,
			0x91, 0xDC, 0xE6, 0x6C, 0x2D, 0x55, 0xD9, 0x25,
			0x7A, 0xB2, 0xFF, 0xFF
		},
		.len = 20
	},
	.exponent = {
		.data = {
			0x00, 0x00, 0x00, 0x02, 0x36, 0x38, 0x31, 0x47,
			0x3C, 0x07, 0x36, 0x21
		},
		.len = 12
	},
	.reminder = {
		.data = {
			0x02, 0x99, 0x2F, 0xE3, 0x00, 0x9F, 0xF0, 0x9E,
			0x65, 0x3C, 0x0B, 0x4A, 0xD3, 0x1B, 0x7C, 0x7F,
			0x1C
		},
		.len = 17
	},
	.modulus = {
		.data = {
			0x00, 0x00, 0x00, 0x00,	0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02,
			0xCE, 0xF0, 0x7C, 0x13, 0x26, 0x90, 0xAF, 0x49,
			0x06, 0x4D, 0xA4, 0x5C, 0xB2, 0x43, 0x13, 0x25,
		},
		.len = 32
	}
}
};

static const struct
modex_test_data modex_group_test_cases[] = {
{
	.description = "Modular Exponentiation tests for Group 5",
	.xform_type = RTE_CRYPTO_ASYM_XFORM_MODEX,
	.base = {
		.data = {
			0x2,
		},
		.len = 1
	},
	.exponent = {
		.data = {
			0x6C, 0x80, 0xFF, 0x29, 0xF9, 0x27, 0x2E, 0x6D,
			0xE1, 0xB7, 0x3F, 0x13, 0x77, 0xD2, 0x3E, 0x49,
			0xCE, 0xAE, 0xBD, 0x73, 0x7A, 0x0F, 0xE7, 0xA4,
			0x20, 0x49, 0x72, 0x87, 0x4E, 0x1B
		},
		.len = 30
	},
	.reminder = {
		.data = {
			0xE8, 0x06, 0x12, 0x55, 0x7D, 0xFE, 0xB8, 0xEC,
			0x44, 0x8E, 0xB2, 0xC9, 0x82, 0xE9, 0x92, 0x65,
			0x45, 0x58, 0x8D, 0xD7, 0x03, 0x6D, 0x55, 0x8B,
			0x31, 0x77, 0x9B, 0xE9, 0x49, 0x1A, 0x7F, 0x13,
			0xA4, 0xF9, 0x03, 0xC4, 0x8D, 0x15, 0xD2, 0xE7,
			0x90, 0x38, 0x88, 0xE6, 0x1C, 0x97, 0x4D, 0xFB,
			0x93, 0x9E, 0x36, 0x22, 0xC8, 0x00, 0xD0, 0xF5,
			0xA0, 0x45, 0xA1, 0x5E, 0xFE, 0x00, 0x47, 0x25,
			0x39, 0x49, 0x40, 0xAE, 0x30, 0xCE, 0xF2, 0xB4,
			0x22, 0x54, 0x2C, 0xA8, 0xC0, 0x3C, 0xB8, 0xCF,
			0x17, 0xFC, 0x20, 0x4F, 0x34, 0x00, 0xB2, 0xFA,
			0xCC, 0xB7, 0xED, 0xD5, 0xBF, 0x86, 0xA1, 0xFC,
			0x58, 0x74, 0xD7, 0x35, 0xC9, 0xBD, 0xAB, 0xE8,
			0xCD, 0xBA, 0xD5, 0xB2, 0xCF, 0xC9, 0x76, 0xD1,
			0xB3, 0x06, 0x6E, 0xAD, 0x82, 0xDE, 0xEF, 0xA0,
			0x26, 0xB0, 0xBA, 0x07, 0x84, 0x25, 0x8D, 0x52,
			0xBF, 0xDC, 0x66, 0xA8, 0x2A, 0xDF, 0x0D, 0xB1,
			0x91, 0x0B, 0xBF, 0x6C, 0x6E, 0xB4, 0x3D, 0x58,
			0x65, 0x3C, 0xE1, 0xAE, 0x1D, 0xCC, 0x3C, 0x72,
			0x3F, 0x49, 0x4E, 0xC5, 0x58, 0x9D, 0xF4, 0xBB,
			0x4F, 0x2E, 0x30, 0x96, 0x4C, 0x31, 0x4C, 0xE9,
			0x0A, 0xFA, 0x88, 0x94, 0x56, 0x15, 0x48, 0xB6,
			0x75, 0x8E, 0xDC, 0x8E, 0xB4, 0x87, 0x88, 0x60,
			0x82, 0xCA, 0x2C, 0xD9, 0x67, 0xB3, 0xDF, 0x86
		},
		.len = 192
	},
	.modulus = {
		.data = {
			0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
			0xC9, 0x0F, 0xDA, 0xA2, 0x21, 0x68, 0xC2, 0x34,
			0xC4, 0xC6, 0x62, 0x8B, 0x80, 0xDC, 0x1C, 0xD1,
			0x29, 0x02, 0x4E, 0x08, 0x8A, 0x67, 0xCC, 0x74,
			0x02, 0x0B, 0xBE, 0xA6, 0x3B, 0x13, 0x9B, 0x22,
			0x51, 0x4A, 0x08, 0x79, 0x8E, 0x34, 0x04, 0xDD,
			0xEF, 0x95, 0x19, 0xB3, 0xCD, 0x3A, 0x43, 0x1B,
			0x30, 0x2B, 0x0A, 0x6D, 0xF2, 0x5F, 0x14, 0x37,
			0x4F, 0xE1, 0x35, 0x6D, 0x6D, 0x51, 0xC2, 0x45,
			0xE4, 0x85, 0xB5, 0x76, 0x62, 0x5E, 0x7E, 0xC6,
			0xF4, 0x4C, 0x42, 0xE9, 0xA6, 0x37, 0xED, 0x6B,
			0x0B, 0xFF, 0x5C, 0xB6, 0xF4, 0x06, 0xB7, 0xED,
			0xEE, 0x38, 0x6B, 0xFB, 0x5A, 0x89, 0x9F, 0xA5,
			0xAE, 0x9F, 0x24, 0x11, 0x7C, 0x4B, 0x1F, 0xE6,
			0x49, 0x28, 0x66, 0x51, 0xEC, 0xE4, 0x5B, 0x3D,
			0xC2, 0x00, 0x7C, 0xB8, 0xA1, 0x63, 0xBF, 0x05,
			0x98, 0xDA, 0x48, 0x36, 0x1C, 0x55, 0xD3, 0x9A,
			0x69, 0x16, 0x3F, 0xA8, 0xFD, 0x24, 0xCF, 0x5F,
			0x83, 0x65, 0x5D, 0x23, 0xDC, 0xA3, 0xAD, 0x96,
			0x1C, 0x62, 0xF3, 0x56, 0x20, 0x85, 0x52, 0xBB,
			0x9E, 0xD5, 0x29, 0x07, 0x70, 0x96, 0x96, 0x6D,
			0x67, 0x0C, 0x35, 0x4E, 0x4A, 0xBC, 0x98, 0x04,
			0xF1, 0x74, 0x6C, 0x08, 0xCA, 0x23, 0x73, 0x27,
			0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 192
	},
},
{
	.description = "Modular Exponentiation tests for Group 14",
	.xform_type = RTE_CRYPTO_ASYM_XFORM_MODEX,
	.base = {
		.data = {
			0x2,
		},
		.len = 1
	},
	.exponent = {
		.data = {
			0x8E, 0x4E, 0x41, 0xA2, 0xE0, 0x59, 0xA8, 0x29,
			0x71, 0xF6, 0x21, 0xC9, 0xD5, 0x0E, 0x36, 0x0F,
			0x59, 0xD6, 0x74, 0x4C, 0x3A, 0xC7, 0x13, 0x5E,
			0x7D, 0x2D, 0x43, 0x63, 0x5A, 0x3D, 0xCA, 0x5F,
			0xF7, 0xB2, 0x3D, 0x9C, 0x3F, 0xA1, 0x5D, 0x71
		},
		.len = 40
	},
	.reminder = {
		.data = {
			0xA2, 0x89, 0x92, 0x04, 0x6D, 0xB8, 0x89, 0xA1,
			0x5B, 0x5F, 0x68, 0x34, 0x7E, 0xFE, 0xA0, 0x7F,
			0xCC, 0x8B, 0x11, 0xE2, 0x0F, 0xFE, 0x44, 0x15,
			0x10, 0xF3, 0x29, 0x9D, 0xB7, 0x3E, 0xD0, 0x83,
			0xF2, 0x35, 0x88, 0x9F, 0x72, 0x59, 0xB6, 0xCD,
			0x5A, 0xC1, 0x45, 0x49, 0x7B, 0xEB, 0xAC, 0x10,
			0x66, 0xF6, 0xBF, 0xC5, 0x8D, 0x0E, 0x31, 0x22,
			0x73, 0xF7, 0xF2, 0x76, 0xAF, 0xE0, 0x39, 0x79,
			0x28, 0xE5, 0x13, 0x91, 0x32, 0x38, 0x7B, 0x17,
			0xDE, 0xA8, 0x2D, 0xF6, 0x76, 0xDF, 0x1E, 0xE5,
			0xC4, 0x8D, 0x35, 0x4E, 0x16, 0xC4, 0x54, 0x16,
			0x3D, 0x61, 0xB1, 0x20, 0xD9, 0xEE, 0xA1, 0x39,
			0x70, 0x9F, 0xEA, 0xD7, 0x38, 0xB0, 0xF3, 0x25,
			0x24, 0xAC, 0xC7, 0x47, 0xA3, 0xE6, 0x7B, 0xB1,
			0x4A, 0x43, 0x40, 0xF5, 0xBA, 0x87, 0xE0, 0x98,
			0x93, 0x4E, 0x18, 0x04, 0xDB, 0x32, 0xC9, 0x73,
			0xF5, 0x9C, 0x98, 0x39, 0x60, 0x5F, 0x32, 0xAC,
			0x4B, 0x96, 0x18, 0x4A, 0xB7, 0x82, 0xE3, 0xA9,
			0x7A, 0xA4, 0x86, 0x0A, 0x6A, 0x64, 0xBF, 0xF5,
			0x1E, 0xD9, 0x51, 0x8C, 0x82, 0xB5, 0xEA, 0xBF,
			0xAE, 0x27, 0x0E, 0xB8, 0x24, 0x86, 0x21, 0xB2,
			0x5D, 0x3C, 0xEA, 0xCF, 0xD3, 0x1B, 0x52, 0x06,
			0x84, 0x8A, 0x92, 0xBD, 0x18, 0xDA, 0x4A, 0x9D,
			0x06, 0x3F, 0x5F, 0xE6, 0xE5, 0x44, 0x01, 0x66,
			0x83, 0xC8, 0x50, 0x59, 0x61, 0x4F, 0xF8, 0xFC,
			0x4E, 0xA0, 0xD1, 0xC0, 0x3F, 0x43, 0x34, 0x1F,
			0xE9, 0x8F, 0xFE, 0x06, 0x84, 0x10, 0xC7, 0x21,
			0x2F, 0x57, 0x2D, 0xC3, 0x09, 0x0F, 0x54, 0x0C,
			0xE1, 0x75, 0xEF, 0xF9, 0xBE, 0x03, 0xC9, 0x78,
			0x8B, 0x79, 0xAB, 0x66, 0x80, 0x77, 0x10, 0x9E,
			0xF2, 0xAE, 0xC4, 0x31, 0x0D, 0x96, 0xD8, 0x52,
			0xC4, 0xC3, 0x8E, 0x8A, 0xAE, 0xEC, 0xBD, 0x88
		},
		.len = 256
	},
	.modulus = {
		.data = {
			0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
			0xC9, 0x0F, 0xDA, 0xA2, 0x21, 0x68, 0xC2, 0x34,
			0xC4, 0xC6, 0x62, 0x8B, 0x80, 0xDC, 0x1C, 0xD1,
			0x29, 0x02, 0x4E, 0x08, 0x8A, 0x67, 0xCC, 0x74,
			0x02, 0x0B, 0xBE, 0xA6, 0x3B, 0x13, 0x9B, 0x22,
			0x51, 0x4A, 0x08, 0x79, 0x8E, 0x34, 0x04, 0xDD,
			0xEF, 0x95, 0x19, 0xB3, 0xCD, 0x3A, 0x43, 0x1B,
			0x30, 0x2B, 0x0A, 0x6D, 0xF2, 0x5F, 0x14, 0x37,
			0x4F, 0xE1, 0x35, 0x6D, 0x6D, 0x51, 0xC2, 0x45,
			0xE4, 0x85, 0xB5, 0x76, 0x62, 0x5E, 0x7E, 0xC6,
			0xF4, 0x4C, 0x42, 0xE9, 0xA6, 0x37, 0xED, 0x6B,
			0x0B, 0xFF, 0x5C, 0xB6, 0xF4, 0x06, 0xB7, 0xED,
			0xEE, 0x38, 0x6B, 0xFB, 0x5A, 0x89, 0x9F, 0xA5,
			0xAE, 0x9F, 0x24, 0x11, 0x7C, 0x4B, 0x1F, 0xE6,
			0x49, 0x28, 0x66, 0x51, 0xEC, 0xE4, 0x5B, 0x3D,
			0xC2, 0x00, 0x7C, 0xB8, 0xA1, 0x63, 0xBF, 0x05,
			0x98, 0xDA, 0x48, 0x36, 0x1C, 0x55, 0xD3, 0x9A,
			0x69, 0x16, 0x3F, 0xA8, 0xFD, 0x24, 0xCF, 0x5F,
			0x83, 0x65, 0x5D, 0x23, 0xDC, 0xA3, 0xAD, 0x96,
			0x1C, 0x62, 0xF3, 0x56, 0x20, 0x85, 0x52, 0xBB,
			0x9E, 0xD5, 0x29, 0x07, 0x70, 0x96, 0x96, 0x6D,
			0x67, 0x0C, 0x35, 0x4E, 0x4A, 0xBC, 0x98, 0x04,
			0xF1, 0x74, 0x6C, 0x08, 0xCA, 0x18, 0x21, 0x7C,
			0x32, 0x90, 0x5E, 0x46, 0x2E, 0x36, 0xCE, 0x3B,
			0xE3, 0x9E, 0x77, 0x2C, 0x18, 0x0E, 0x86, 0x03,
			0x9B, 0x27, 0x83, 0xA2, 0xEC, 0x07, 0xA2, 0x8F,
			0xB5, 0xC5, 0x5D, 0xF0, 0x6F, 0x4C, 0x52, 0xC9,
			0xDE, 0x2B, 0xCB, 0xF6, 0x95, 0x58, 0x17, 0x18,
			0x39, 0x95, 0x49, 0x7C, 0xEA, 0x95, 0x6A, 0xE5,
			0x15, 0xD2, 0x26, 0x18, 0x98, 0xFA, 0x05, 0x10,
			0x15, 0x72, 0x8E, 0x5A, 0x8A, 0xAC, 0xAA, 0x68,
			0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 256
	},
},
{
	.description = "Modular Exponentiation tests for Group 15",
	.xform_type = RTE_CRYPTO_ASYM_XFORM_MODEX,
	.base = {
		.data = {
			0x2,
		},
		.len = 1
	},
	.exponent = {
		.data = {
			0x63, 0x4D, 0x67, 0x4E, 0x4A, 0x16, 0x0F, 0xEB,
			0x76, 0xC8, 0xAB, 0x3B, 0x4A, 0x74, 0x03, 0x02,
			0x2F, 0xAC, 0x34, 0x23, 0xC8, 0x2E, 0x1E, 0x60,
			0x63, 0x1E, 0x7D, 0x3F, 0x22, 0xB9, 0xBF, 0x2D,
			0x4F, 0xB3, 0x72, 0xAC, 0x1E, 0x62, 0xA7, 0x47,
			0x7A, 0xF3, 0x45, 0xAB, 0x5B, 0x67, 0x12, 0x80,
			0x77, 0xDA, 0xF8, 0xF4
		},
		.len = 52
	},
	.reminder = {
		.data = {
			0x9D, 0xB2, 0xDB, 0xE6, 0x8A, 0x58, 0xF2, 0x34,
			0xE3, 0x1B, 0x9B, 0x29, 0x0F, 0x6A, 0xCD, 0xE9,
			0xA5, 0xAB, 0x80, 0x0E, 0x23, 0x76, 0xC4, 0xB1,
			0xA0, 0x22, 0x27, 0x61, 0x18, 0xE7, 0x07, 0x03,
			0x51, 0x43, 0xD2, 0x4E, 0xBF, 0x87, 0xF2, 0x1D,
			0xA6, 0xC6, 0xDB, 0x12, 0x5F, 0x2F, 0x64, 0x2D,
			0xC5, 0x59, 0x9C, 0xE3, 0x64, 0x0B, 0xF7, 0x14,
			0xA1, 0x7A, 0x32, 0x3C, 0xC1, 0x48, 0xA5, 0x20,
			0xE6, 0x93, 0x46, 0x3D, 0x5C, 0xF2, 0x7A, 0x42,
			0x94, 0x53, 0x88, 0xB6, 0x82, 0x7C, 0xBF, 0xA5,
			0x66, 0xD7, 0xE4, 0x7A, 0xD7, 0x14, 0xF4, 0xBD,
			0xED, 0xFB, 0xEC, 0x6B, 0xF8, 0x44, 0x70, 0x82,
			0x4C, 0x15, 0x5E, 0x4B, 0x4F, 0xC5, 0x4C, 0x9A,
			0x95, 0xCE, 0xA8, 0x51, 0x1B, 0x1E, 0x19, 0xAC,
			0x22, 0xCC, 0x46, 0x61, 0xF5, 0xF9, 0x94, 0x2B,
			0x15, 0x87, 0x9B, 0x93, 0x76, 0xD6, 0xA3, 0x5A,
			0x77, 0xFD, 0xEC, 0x77, 0xB9, 0xE3, 0x62, 0x2B,
			0x66, 0x31, 0x4C, 0x43, 0x55, 0x23, 0x05, 0x9A,
			0xBF, 0x57, 0xBF, 0x58, 0xC6, 0x56, 0x1E, 0x2B,
			0xDC, 0xFE, 0xC7, 0x74, 0xA0, 0x48, 0xFF, 0x8B,
			0x7E, 0xDC, 0x89, 0x5C, 0xEE, 0x7E, 0xD6, 0x78,
			0xA6, 0x7A, 0xA2, 0xB7, 0x84, 0x56, 0x50, 0x17,
			0xFC, 0x42, 0xE0, 0x63, 0xF2, 0xD5, 0x21, 0x21,
			0xA9, 0x47, 0x0F, 0x88, 0x2F, 0x25, 0x9B, 0x53,
			0xD6, 0x77, 0x0A, 0x5C, 0x30, 0xFE, 0xC7, 0x49,
			0xCD, 0xE8, 0xA7, 0xCA, 0xD9, 0x95, 0xF0, 0x2B,
			0x9F, 0x5B, 0xEB, 0xA7, 0xD2, 0x43, 0xCE, 0x90,
			0xCE, 0x84, 0x49, 0x46, 0x53, 0x7C, 0x27, 0x46,
			0x82, 0xA0, 0xC1, 0xE6, 0x8C, 0x21, 0xAC, 0x93,
			0x5E, 0xD0, 0x70, 0xB6, 0x20, 0x92, 0xEB, 0x1C,
			0xB3, 0x0A, 0xBF, 0x73, 0x8C, 0x76, 0x88, 0xDC,
			0x68, 0xEE, 0x96, 0xB0, 0x88, 0xB5, 0x84, 0xE0,
			0xC4, 0x61, 0x20, 0x84, 0x65, 0x40, 0x13, 0xC2,
			0x16, 0x85, 0x0C, 0xA7, 0xC9, 0x83, 0x73, 0x1D,
			0x33, 0x18, 0xA5, 0x53, 0x0E, 0xB0, 0xB6, 0xB5,
			0xF8, 0x88, 0xD2, 0x7C, 0x70, 0xFD, 0xEA, 0x45,
			0xE8, 0xD6, 0x35, 0xA7, 0xB2, 0x54, 0x93, 0x9C,
			0x41, 0x73, 0x9F, 0x74, 0x6F, 0xB8, 0x55, 0xF3,
			0x3F, 0xC9, 0xD2, 0xE6, 0xD3, 0xEC, 0x6E, 0xD5,
			0x2A, 0xEA, 0x00, 0xAC, 0xC4, 0xCA, 0xE4, 0x4E,
			0x29, 0x02, 0x43, 0x85, 0x2D, 0xC9, 0xEA, 0xE0,
			0x5F, 0x14, 0xF6, 0x16, 0xEE, 0x10, 0x42, 0xBD,
			0xE2, 0x8F, 0x0E, 0x10, 0x3C, 0xFA, 0xF8, 0x6B,
			0xE3, 0x58, 0x19, 0xB1, 0xEE, 0xDF, 0x7E, 0x0D,
			0x1C, 0x55, 0x7F, 0x93, 0x99, 0x72, 0xD2, 0xE8,
			0x9A, 0x8E, 0xB2, 0x69, 0xD7, 0x2B, 0xFA, 0xEA,
			0xD4, 0x5F, 0xC4, 0xA1, 0x73, 0x80, 0x83, 0xE1,
			0x2B, 0x4B, 0xC4, 0xCB, 0x5F, 0xE6, 0x2C, 0x29
		},
		.len = 384
	},
	.modulus = {
		.data = {
			0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
			0xC9, 0x0F, 0xDA, 0xA2, 0x21, 0x68, 0xC2, 0x34,
			0xC4, 0xC6, 0x62, 0x8B, 0x80, 0xDC, 0x1C, 0xD1,
			0x29, 0x02, 0x4E, 0x08, 0x8A, 0x67, 0xCC, 0x74,
			0x02, 0x0B, 0xBE, 0xA6, 0x3B, 0x13, 0x9B, 0x22,
			0x51, 0x4A, 0x08, 0x79, 0x8E, 0x34, 0x04, 0xDD,
			0xEF, 0x95, 0x19, 0xB3, 0xCD, 0x3A, 0x43, 0x1B,
			0x30, 0x2B, 0x0A, 0x6D, 0xF2, 0x5F, 0x14, 0x37,
			0x4F, 0xE1, 0x35, 0x6D, 0x6D, 0x51, 0xC2, 0x45,
			0xE4, 0x85, 0xB5, 0x76, 0x62, 0x5E, 0x7E, 0xC6,
			0xF4, 0x4C, 0x42, 0xE9, 0xA6, 0x37, 0xED, 0x6B,
			0x0B, 0xFF, 0x5C, 0xB6, 0xF4, 0x06, 0xB7, 0xED,
			0xEE, 0x38, 0x6B, 0xFB, 0x5A, 0x89, 0x9F, 0xA5,
			0xAE, 0x9F, 0x24, 0x11, 0x7C, 0x4B, 0x1F, 0xE6,
			0x49, 0x28, 0x66, 0x51, 0xEC, 0xE4, 0x5B, 0x3D,
			0xC2, 0x00, 0x7C, 0xB8, 0xA1, 0x63, 0xBF, 0x05,
			0x98, 0xDA, 0x48, 0x36, 0x1C, 0x55, 0xD3, 0x9A,
			0x69, 0x16, 0x3F, 0xA8, 0xFD, 0x24, 0xCF, 0x5F,
			0x83, 0x65, 0x5D, 0x23, 0xDC, 0xA3, 0xAD, 0x96,
			0x1C, 0x62, 0xF3, 0x56, 0x20, 0x85, 0x52, 0xBB,
			0x9E, 0xD5, 0x29, 0x07, 0x70, 0x96, 0x96, 0x6D,
			0x67, 0x0C, 0x35, 0x4E, 0x4A, 0xBC, 0x98, 0x04,
			0xF1, 0x74, 0x6C, 0x08, 0xCA, 0x18, 0x21, 0x7C,
			0x32, 0x90, 0x5E, 0x46, 0x2E, 0x36, 0xCE, 0x3B,
			0xE3, 0x9E, 0x77, 0x2C, 0x18, 0x0E, 0x86, 0x03,
			0x9B, 0x27, 0x83, 0xA2, 0xEC, 0x07, 0xA2, 0x8F,
			0xB5, 0xC5, 0x5D, 0xF0, 0x6F, 0x4C, 0x52, 0xC9,
			0xDE, 0x2B, 0xCB, 0xF6, 0x95, 0x58, 0x17, 0x18,
			0x39, 0x95, 0x49, 0x7C, 0xEA, 0x95, 0x6A, 0xE5,
			0x15, 0xD2, 0x26, 0x18, 0x98, 0xFA, 0x05, 0x10,
			0x15, 0x72, 0x8E, 0x5A, 0x8A, 0xAA, 0xC4, 0x2D,
			0xAD, 0x33, 0x17, 0x0D, 0x04, 0x50, 0x7A, 0x33,
			0xA8, 0x55, 0x21, 0xAB, 0xDF, 0x1C, 0xBA, 0x64,
			0xEC, 0xFB, 0x85, 0x04, 0x58, 0xDB, 0xEF, 0x0A,
			0x8A, 0xEA, 0x71, 0x57, 0x5D, 0x06, 0x0C, 0x7D,
			0xB3, 0x97, 0x0F, 0x85, 0xA6, 0xE1, 0xE4, 0xC7,
			0xAB, 0xF5, 0xAE, 0x8C, 0xDB, 0x09, 0x33, 0xD7,
			0x1E, 0x8C, 0x94, 0xE0, 0x4A, 0x25, 0x61, 0x9D,
			0xCE, 0xE3, 0xD2, 0x26, 0x1A, 0xD2, 0xEE, 0x6B,
			0xF1, 0x2F, 0xFA, 0x06, 0xD9, 0x8A, 0x08, 0x64,
			0xD8, 0x76, 0x02, 0x73, 0x3E, 0xC8, 0x6A, 0x64,
			0x52, 0x1F, 0x2B, 0x18, 0x17, 0x7B, 0x20, 0x0C,
			0xBB, 0xE1, 0x17, 0x57, 0x7A, 0x61, 0x5D, 0x6C,
			0x77, 0x09, 0x88, 0xC0, 0xBA, 0xD9, 0x46, 0xE2,
			0x08, 0xE2, 0x4F, 0xA0, 0x74, 0xE5, 0xAB, 0x31,
			0x43, 0xDB, 0x5B, 0xFC, 0xE0, 0xFD, 0x10, 0x8E,
			0x4B, 0x82, 0xD1, 0x20, 0xA9, 0x3A, 0xD2, 0xCA,
			0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 384
	},
},
{
	.description = "Modular Exponentiation tests for Group 16",
	.xform_type = RTE_CRYPTO_ASYM_XFORM_MODEX,
	.base = {
		.data = {
			0x2,
		},
		.len = 1
	},
	.exponent = {
		.data = {
			0xF2, 0x77, 0xFF, 0x91, 0x08, 0xF6, 0x16, 0x8E,
			0xEE, 0x8C, 0xCC, 0x62, 0x07, 0xA4, 0xE3, 0x0F,
			0xB8, 0xE8, 0xFD, 0x77, 0xEA, 0x06, 0x1D, 0x9F,
			0x2A, 0x96, 0xE8, 0x0D, 0x66, 0xA4, 0x97, 0x7E,
			0xDA, 0xDB, 0xC0, 0xC0, 0x2F, 0x72, 0xCD, 0xFC,
			0xBE, 0xC3, 0xAA, 0x46, 0x31, 0x7C, 0x4B, 0x4D,
			0x0B, 0x14, 0x02, 0x5C, 0x7F, 0x29, 0xC1, 0xDE,
			0xC5, 0x06, 0x70, 0x0B
		},
		.len = 60
	},
	.reminder = {
		.data = {
			0x84, 0xCF, 0x17, 0x2E, 0xDF, 0x12, 0xD4, 0x4E,
			0x5F, 0x61, 0x14, 0x7F, 0x5B, 0xBF, 0x54, 0x5D,
			0x81, 0xE9, 0xF0, 0xA2, 0xE1, 0xB1, 0xC4, 0x37,
			0x5C, 0xE4, 0xA6, 0x1F, 0xB3, 0x3D, 0x58, 0xE6,
			0x6B, 0xCD, 0x83, 0x94, 0x20, 0x46, 0xC3, 0x1E,
			0xBE, 0x20, 0x44, 0x79, 0xF6, 0xC2, 0xD9, 0x57,
			0x99, 0x97, 0x4E, 0x62, 0xF8, 0xC8, 0x30, 0x85,
			0xA9, 0x06, 0xD5, 0xA8, 0x3A, 0x38, 0x02, 0x79,
			0xF1, 0xB8, 0x9B, 0x09, 0xF5, 0xFE, 0xC4, 0xC7,
			0x22, 0xD5, 0xC7, 0x41, 0x91, 0x0B, 0xF0, 0xC5,
			0xB4, 0x3D, 0xF3, 0x5C, 0xAE, 0xDD, 0xA1, 0x35,
			0xD2, 0x05, 0xDF, 0x90, 0x52, 0xB6, 0x79, 0xB1,
			0x58, 0x02, 0x73, 0x49, 0x7B, 0x8E, 0x72, 0x19,
			0x4D, 0xC2, 0x6E, 0xFE, 0x87, 0xF3, 0x0A, 0x59,
			0x83, 0x3D, 0x16, 0x71, 0xB4, 0xDE, 0x19, 0x24,
			0x0F, 0x33, 0xE2, 0x24, 0x0E, 0xEA, 0x40, 0x39,
			0xBE, 0x3D, 0x53, 0x94, 0x32, 0x70, 0xD4, 0x16,
			0xC7, 0xF6, 0x74, 0x03, 0x26, 0x33, 0x12, 0x5E,
			0x38, 0x6E, 0xEB, 0x02, 0x94, 0x6B, 0x2D, 0xAB,
			0x5F, 0xE3, 0x08, 0xD9, 0xA2, 0x7C, 0xA2, 0x49,
			0x52, 0x5A, 0x14, 0x6C, 0xEF, 0x3D, 0xDF, 0x1E,
			0x5F, 0x60, 0x52, 0x63, 0x83, 0xB1, 0x3D, 0xAC,
			0x4F, 0x30, 0x7C, 0xE1, 0x9E, 0x9E, 0x00, 0x40,
			0x76, 0x78, 0x45, 0xF9, 0xCF, 0x64, 0xD2, 0xA3,
			0x23, 0xB9, 0x50, 0xF6, 0x62, 0xBA, 0x8A, 0xE8,
			0x5E, 0xD5, 0x44, 0x0E, 0xAA, 0x90, 0xAD, 0x74,
			0xEC, 0x2F, 0xB0, 0xA9, 0xC8, 0xA7, 0x4E, 0x50,
			0x65, 0xC3, 0xE5, 0x9A, 0xB7, 0x4C, 0x7F, 0x0E,
			0xE4, 0x55, 0x18, 0x81, 0x42, 0x1D, 0x3A, 0xED,
			0x96, 0x7B, 0x54, 0x26, 0x73, 0x06, 0xB3, 0xE5,
			0xE0, 0x38, 0x52, 0x80, 0xE2, 0x00, 0xDF, 0x9A,
			0xFB, 0xFB, 0x72, 0x67, 0x62, 0xC6, 0x22, 0x4E,
			0xD4, 0x3C, 0xD1, 0x45, 0xF9, 0xD4, 0x47, 0x84,
			0x0E, 0x03, 0xE1, 0x14, 0xD4, 0xAC, 0xC9, 0x5D,
			0x70, 0x9F, 0x75, 0x63, 0xDC, 0x96, 0x60, 0x60,
			0xC1, 0x78, 0x85, 0x55, 0xA0, 0xC9, 0xC3, 0xE9,
			0xC8, 0xF2, 0x48, 0x26, 0x51, 0x0F, 0x19, 0x4C,
			0xE7, 0x57, 0x78, 0x00, 0x27, 0x3A, 0x11, 0x3B,
			0x49, 0x7E, 0x7C, 0x57, 0x6A, 0xA3, 0xCD, 0x3C,
			0xF7, 0x6E, 0x26, 0xBC, 0x8A, 0x7D, 0xD5, 0x59,
			0x9F, 0x33, 0x7E, 0xB6, 0x93, 0xB1, 0x49, 0xBA,
			0xFC, 0xBC, 0xD3, 0x67, 0x68, 0x3C, 0xCA, 0xD2,
			0x03, 0x30, 0xA5, 0x45, 0x61, 0x6C, 0xB5, 0xC1,
			0xA3, 0x80, 0x1E, 0xD6, 0x92, 0x96, 0x2D, 0x26,
			0x6E, 0x3F, 0x81, 0x2D, 0x20, 0x93, 0x6A, 0x6A,
			0xE6, 0x39, 0x52, 0x35, 0x14, 0x07, 0x0D, 0x3D,
			0xA7, 0xA9, 0x65, 0x58, 0x28, 0x01, 0x08, 0x28,
			0x7A, 0xE0, 0x35, 0xAE, 0x42, 0x47, 0x37, 0x9F,
			0x60, 0x8D, 0xD9, 0xE3, 0xDF, 0x2B, 0x27, 0xB5,
			0x7D, 0x69, 0x0D, 0xF3, 0xAD, 0x50, 0x4A, 0x5B,
			0xA6, 0xD6, 0x86, 0x99, 0xD9, 0x60, 0x65, 0xC5,
			0x85, 0xB9, 0x7A, 0x48, 0xAD, 0x99, 0xDA, 0x55,
			0x6A, 0x42, 0x88, 0x25, 0x70, 0x34, 0xBC, 0x03,
			0xBC, 0x9D, 0xDA, 0x86, 0x6C, 0x56, 0x82, 0x50,
			0xF9, 0x77, 0xC7, 0xE5, 0x32, 0x67, 0xD3, 0x59,
			0xBD, 0x5C, 0xE8, 0x58, 0x07, 0x43, 0x99, 0x21,
			0x7E, 0xE2, 0x0D, 0x2E, 0x58, 0x72, 0x5C, 0x20,
			0xEA, 0xBF, 0x06, 0x34, 0xF6, 0x33, 0x22, 0x37,
			0x8B, 0xA6, 0x62, 0xE7, 0x93, 0x78, 0x5B, 0x5B,
			0xEE, 0xAA, 0xD5, 0x49, 0xCB, 0x93, 0xEF, 0x6B,
			0x0A, 0xBD, 0xC3, 0x5C, 0x3E, 0xA8, 0x81, 0xD2,
			0xBA, 0x8D, 0x7B, 0x29, 0x0E, 0xFB, 0xD8, 0x73,
			0x2D, 0xE9, 0x79, 0x6D, 0xEF, 0xB7, 0x3A, 0x67,
			0x7C, 0x30, 0xFE, 0xC0, 0x66, 0x10, 0x14, 0x2B
		},
		.len = 512
	},
	.modulus = {
		.data = {
			0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
			0xC9, 0x0F, 0xDA, 0xA2, 0x21, 0x68, 0xC2, 0x34,
			0xC4, 0xC6, 0x62, 0x8B, 0x80, 0xDC, 0x1C, 0xD1,
			0x29, 0x02, 0x4E, 0x08, 0x8A, 0x67, 0xCC, 0x74,
			0x02, 0x0B, 0xBE, 0xA6, 0x3B, 0x13, 0x9B, 0x22,
			0x51, 0x4A, 0x08, 0x79, 0x8E, 0x34, 0x04, 0xDD,
			0xEF, 0x95, 0x19, 0xB3, 0xCD, 0x3A, 0x43, 0x1B,
			0x30, 0x2B, 0x0A, 0x6D, 0xF2, 0x5F, 0x14, 0x37,
			0x4F, 0xE1, 0x35, 0x6D, 0x6D, 0x51, 0xC2, 0x45,
			0xE4, 0x85, 0xB5, 0x76, 0x62, 0x5E, 0x7E, 0xC6,
			0xF4, 0x4C, 0x42, 0xE9, 0xA6, 0x37, 0xED, 0x6B,
			0x0B, 0xFF, 0x5C, 0xB6, 0xF4, 0x06, 0xB7, 0xED,
			0xEE, 0x38, 0x6B, 0xFB, 0x5A, 0x89, 0x9F, 0xA5,
			0xAE, 0x9F, 0x24, 0x11, 0x7C, 0x4B, 0x1F, 0xE6,
			0x49, 0x28, 0x66, 0x51, 0xEC, 0xE4, 0x5B, 0x3D,
			0xC2, 0x00, 0x7C, 0xB8, 0xA1, 0x63, 0xBF, 0x05,
			0x98, 0xDA, 0x48, 0x36, 0x1C, 0x55, 0xD3, 0x9A,
			0x69, 0x16, 0x3F, 0xA8, 0xFD, 0x24, 0xCF, 0x5F,
			0x83, 0x65, 0x5D, 0x23, 0xDC, 0xA3, 0xAD, 0x96,
			0x1C, 0x62, 0xF3, 0x56, 0x20, 0x85, 0x52, 0xBB,
			0x9E, 0xD5, 0x29, 0x07, 0x70, 0x96, 0x96, 0x6D,
			0x67, 0x0C, 0x35, 0x4E, 0x4A, 0xBC, 0x98, 0x04,
			0xF1, 0x74, 0x6C, 0x08, 0xCA, 0x18, 0x21, 0x7C,
			0x32, 0x90, 0x5E, 0x46, 0x2E, 0x36, 0xCE, 0x3B,
			0xE3, 0x9E, 0x77, 0x2C, 0x18, 0x0E, 0x86, 0x03,
			0x9B, 0x27, 0x83, 0xA2, 0xEC, 0x07, 0xA2, 0x8F,
			0xB5, 0xC5, 0x5D, 0xF0, 0x6F, 0x4C, 0x52, 0xC9,
			0xDE, 0x2B, 0xCB, 0xF6, 0x95, 0x58, 0x17, 0x18,
			0x39, 0x95, 0x49, 0x7C, 0xEA, 0x95, 0x6A, 0xE5,
			0x15, 0xD2, 0x26, 0x18, 0x98, 0xFA, 0x05, 0x10,
			0x15, 0x72, 0x8E, 0x5A, 0x8A, 0xAA, 0xC4, 0x2D,
			0xAD, 0x33, 0x17, 0x0D, 0x04, 0x50, 0x7A, 0x33,
			0xA8, 0x55, 0x21, 0xAB, 0xDF, 0x1C, 0xBA, 0x64,
			0xEC, 0xFB, 0x85, 0x04, 0x58, 0xDB, 0xEF, 0x0A,
			0x8A, 0xEA, 0x71, 0x57, 0x5D, 0x06, 0x0C, 0x7D,
			0xB3, 0x97, 0x0F, 0x85, 0xA6, 0xE1, 0xE4, 0xC7,
			0xAB, 0xF5, 0xAE, 0x8C, 0xDB, 0x09, 0x33, 0xD7,
			0x1E, 0x8C, 0x94, 0xE0, 0x4A, 0x25, 0x61, 0x9D,
			0xCE, 0xE3, 0xD2, 0x26, 0x1A, 0xD2, 0xEE, 0x6B,
			0xF1, 0x2F, 0xFA, 0x06, 0xD9, 0x8A, 0x08, 0x64,
			0xD8, 0x76, 0x02, 0x73, 0x3E, 0xC8, 0x6A, 0x64,
			0x52, 0x1F, 0x2B, 0x18, 0x17, 0x7B, 0x20, 0x0C,
			0xBB, 0xE1, 0x17, 0x57, 0x7A, 0x61, 0x5D, 0x6C,
			0x77, 0x09, 0x88, 0xC0, 0xBA, 0xD9, 0x46, 0xE2,
			0x08, 0xE2, 0x4F, 0xA0, 0x74, 0xE5, 0xAB, 0x31,
			0x43, 0xDB, 0x5B, 0xFC, 0xE0, 0xFD, 0x10, 0x8E,
			0x4B, 0x82, 0xD1, 0x20, 0xA9, 0x21, 0x08, 0x01,
			0x1A, 0x72, 0x3C, 0x12, 0xA7, 0x87, 0xE6, 0xD7,
			0x88, 0x71, 0x9A, 0x10, 0xBD, 0xBA, 0x5B, 0x26,
			0x99, 0xC3, 0x27, 0x18, 0x6A, 0xF4, 0xE2, 0x3C,
			0x1A, 0x94, 0x68, 0x34, 0xB6, 0x15, 0x0B, 0xDA,
			0x25, 0x83, 0xE9, 0xCA, 0x2A, 0xD4, 0x4C, 0xE8,
			0xDB, 0xBB, 0xC2, 0xDB, 0x04, 0xDE, 0x8E, 0xF9,
			0x2E, 0x8E, 0xFC, 0x14, 0x1F, 0xBE, 0xCA, 0xA6,
			0x28, 0x7C, 0x59, 0x47, 0x4E, 0x6B, 0xC0, 0x5D,
			0x99, 0xB2, 0x96, 0x4F, 0xA0, 0x90, 0xC3, 0xA2,
			0x23, 0x3B, 0xA1, 0x86, 0x51, 0x5B, 0xE7, 0xED,
			0x1F, 0x61, 0x29, 0x70, 0xCE, 0xE2, 0xD7, 0xAF,
			0xB8, 0x1B, 0xDD, 0x76, 0x21, 0x70, 0x48, 0x1C,
			0xD0, 0x06, 0x91, 0x27, 0xD5, 0xB0, 0x5A, 0xA9,
			0x93, 0xB4, 0xEA, 0x98, 0x8D, 0x8F, 0xDD, 0xC1,
			0x86, 0xFF, 0xB7, 0xDC, 0x90, 0xA6, 0xC0, 0x8F,
			0x4D, 0xF4, 0x35, 0xC9, 0x34, 0x06, 0x31, 0x99,
			0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 512
	},
},
{
	.description = "Modular Exponentiation tests for Group 17",
	.xform_type = RTE_CRYPTO_ASYM_XFORM_MODEX,
	.base = {
		.data = {
			0x2,
		},
		.len = 1
	},
	.exponent = {
		.data = {
			0x06, 0x8B, 0x74, 0x99, 0x02, 0xCE, 0x50, 0x2C,
			0xED, 0x29, 0x2F, 0xFB, 0x14, 0x74, 0x11, 0x7A,
			0x7C, 0x1D, 0xBF, 0xF8, 0xC3, 0x2D, 0xFD, 0x45,
			0x56, 0xCF, 0xCD, 0x92, 0x12, 0xF2, 0xC1, 0x96,
			0x73, 0x11, 0x4C, 0xAC, 0xFA, 0x0C, 0x4B, 0x2B,
			0xFA, 0xED, 0xA5, 0x5A, 0xDD, 0xF7, 0x5F, 0x75,
			0xB0, 0x18, 0x69, 0x63, 0xB0, 0x8E, 0x04, 0xA2,
			0x0D, 0x1F, 0x68, 0xA9, 0x1A, 0x75, 0x8A, 0x29,
			0xD4, 0xC1, 0x31, 0xAC
		},
		.len = 68
	},
	.reminder = {
		.data = {
			0xD7, 0x9A, 0xC8, 0x8C, 0x83, 0xA1, 0xF4, 0x30,
			0x75, 0x97, 0x39, 0x70, 0xC0, 0xE7, 0x07, 0x90,
			0xC7, 0x30, 0xCA, 0x16, 0x24, 0x1F, 0x5A, 0x81,
			0xE2, 0xE8, 0x7F, 0x63, 0x43, 0x25, 0x08, 0x59,
			0x70, 0xE0, 0x8E, 0x5C, 0xBA, 0xE7, 0xF5, 0x9B,
			0x0D, 0x89, 0x21, 0x2A, 0x0A, 0x4A, 0xA8, 0x65,
			0x9C, 0x8C, 0x52, 0x64, 0x7B, 0xB5, 0xCA, 0x38,
			0x47, 0x9B, 0xEE, 0x4D, 0x1E, 0x55, 0x61, 0xF6,
			0xBF, 0x4D, 0x4F, 0x94, 0x22, 0x93, 0xC8, 0x3F,
			0x60, 0x54, 0x37, 0x2E, 0xB3, 0x2D, 0x7A, 0x20,
			0x84, 0xD1, 0x58, 0x61, 0x30, 0x04, 0x0B, 0xFE,
			0x3F, 0xD4, 0xBF, 0x4D, 0xFA, 0x7A, 0x00, 0x55,
			0xCC, 0x81, 0x1C, 0x3F, 0xE2, 0xB5, 0xA5, 0xBC,
			0x42, 0x1E, 0x91, 0x67, 0xF9, 0x15, 0xD4, 0x1B,
			0xDF, 0x5E, 0xE6, 0x00, 0xC6, 0xD1, 0xBD, 0x57,
			0xB6, 0x29, 0x90, 0x3E, 0xA6, 0x17, 0x0F, 0xC7,
			0x7C, 0x5F, 0x6B, 0xED, 0x80, 0x0F, 0xD8, 0xA8,
			0x86, 0x5F, 0x5B, 0xCF, 0x91, 0x50, 0x59, 0x70,
			0x04, 0x0E, 0xBC, 0x93, 0xFB, 0xFE, 0xFE, 0x28,
			0x5B, 0x53, 0x89, 0x69, 0xD8, 0xFA, 0x55, 0x15,
			0x70, 0xDF, 0x7F, 0x12, 0x5A, 0x27, 0x3E, 0xB5,
			0x0A, 0xB2, 0xE1, 0xC4, 0x35, 0xE6, 0xED, 0x49,
			0xF5, 0x89, 0xCC, 0x10, 0xA1, 0xD3, 0x2E, 0x1B,
			0x41, 0xD6, 0x83, 0xB9, 0x6F, 0xE1, 0xAB, 0x09,
			0x47, 0x76, 0xF0, 0x0F, 0xF4, 0xC6, 0xB9, 0xFC,
			0x76, 0xA5, 0xB8, 0xC0, 0x58, 0x70, 0x1F, 0xC3,
			0xE8, 0x71, 0x00, 0x95, 0x8D, 0x57, 0x0A, 0xA3,
			0xAC, 0x07, 0x59, 0x25, 0xA1, 0xC7, 0x1F, 0x21,
			0x79, 0x20, 0x1C, 0xD6, 0xD0, 0x2C, 0x83, 0x97,
			0x71, 0xC8, 0x2A, 0xA1, 0x00, 0x4D, 0x43, 0x4D,
			0x77, 0x0F, 0x33, 0x1F, 0x8B, 0x9D, 0xDF, 0xAE,
			0x24, 0xF5, 0xF3, 0x30, 0x2C, 0x7E, 0x01, 0x5A,
			0xCA, 0x94, 0xFF, 0x44, 0xF8, 0xBE, 0x37, 0xD5,
			0x56, 0xCE, 0xC9, 0x28, 0xE9, 0xA6, 0xDE, 0x0E,
			0xB8, 0x50, 0xF2, 0x42, 0x10, 0x75, 0x6C, 0x31,
			0x98, 0xC6, 0xD1, 0x61, 0x2B, 0xC1, 0x25, 0x15,
			0x50, 0x1C, 0x19, 0x17, 0x9B, 0xAB, 0x39, 0xA2,
			0x8B, 0xC1, 0xF0, 0x42, 0x8B, 0xC5, 0xAB, 0x78,
			0x21, 0x4E, 0x09, 0xB4, 0x15, 0x0C, 0xEE, 0x1E,
			0xD4, 0xFE, 0x53, 0x78, 0x1D, 0x39, 0x35, 0x0F,
			0xC7, 0x1C, 0xEF, 0xB7, 0xF4, 0x51, 0x70, 0x99,
			0x1E, 0xEF, 0x37, 0x5D, 0xBD, 0x88, 0x3A, 0xC2,
			0x96, 0x83, 0x92, 0x93, 0x1B, 0xB7, 0x3E, 0x13,
			0xFD, 0xA2, 0x81, 0x40, 0xDA, 0x24, 0x9A, 0xD3,
			0x81, 0x1C, 0x50, 0xCF, 0xB7, 0x1D, 0x5A, 0x70,
			0x68, 0xED, 0xD5, 0xDE, 0x88, 0x67, 0xA4, 0xAC,
			0x77, 0x42, 0xBC, 0x19, 0x4D, 0x3C, 0x54, 0x35,
			0xD2, 0x97, 0xAB, 0x0C, 0xC2, 0x94, 0x31, 0xFF,
			0x37, 0xC2, 0xA5, 0xEA, 0x14, 0x11, 0x39, 0x6C,
			0xDB, 0xEA, 0xE2, 0x9B, 0xE7, 0x11, 0x05, 0x4F,
			0xDB, 0x73, 0x4A, 0x79, 0x60, 0x8F, 0x47, 0x56,
			0xB2, 0x35, 0x92, 0xC9, 0x04, 0xCD, 0x4C, 0x76,
			0x20, 0x61, 0x4A, 0xB4, 0x2E, 0xDD, 0xD1, 0xF0,
			0x7F, 0xB2, 0xEC, 0x40, 0x20, 0x77, 0xE8, 0xFF,
			0x1C, 0x05, 0xB9, 0x3D, 0x08, 0x3B, 0x40, 0x46,
			0xC0, 0x71, 0xE2, 0xF8, 0x51, 0x72, 0x22, 0xD5,
			0x39, 0x3F, 0x6C, 0xB0, 0xFE, 0xF7, 0xFF, 0xFA,
			0x98, 0xBC, 0x62, 0x8C, 0x1E, 0x0C, 0x9D, 0xAC,
			0x16, 0xF6, 0xF4, 0x08, 0x43, 0x5E, 0x9C, 0xD4,
			0xC6, 0xFA, 0xC3, 0x44, 0x0D, 0xDD, 0x62, 0x13,
			0xF5, 0x2C, 0x1B, 0x9A, 0xD3, 0x55, 0x9C, 0x56,
			0xBF, 0x2D, 0x5B, 0x11, 0x46, 0xD7, 0xCC, 0x1F,
			0x31, 0x3E, 0xE4, 0xC1, 0x31, 0x20, 0x44, 0x38,
			0x6F, 0x2F, 0x97, 0xF1, 0x10, 0xF6, 0x40, 0x66,
			0xF5, 0xC6, 0x6A, 0x91, 0x2F, 0x01, 0x5A, 0x0D,
			0x41, 0x89, 0x16, 0xC9, 0xC5, 0x66, 0xCF, 0x65,
			0xDE, 0x68, 0x7E, 0x41, 0x3A, 0x2C, 0x84, 0x4F,
			0x44, 0x87, 0x78, 0x77, 0x7F, 0x7D, 0x80, 0x64,
			0xE3, 0x2A, 0xAD, 0x89, 0x8C, 0xBC, 0xD5, 0x78,
			0xE6, 0xCF, 0xF9, 0xF2, 0x98, 0x24, 0x8C, 0xC3,
			0x1F, 0x0D, 0xDA, 0xAA, 0xFC, 0xE0, 0xE0, 0xFD,
			0x3B, 0xB4, 0x2E, 0x32, 0x4E, 0x28, 0xF2, 0xC6,
			0x1B, 0xEA, 0x9E, 0x22, 0x58, 0x68, 0x8C, 0xD0,
			0x0B, 0x42, 0x98, 0x92, 0x66, 0xB5, 0xA0, 0xAA,
			0x67, 0xFF, 0x91, 0xEC, 0xD3, 0x14, 0x80, 0xE2,
			0x49, 0x01, 0x86, 0xE9, 0x91, 0xC3, 0x6E, 0xF8,
			0xC8, 0x8C, 0xC0, 0x2C, 0x19, 0x3B, 0xBF, 0x2F,
			0xC3, 0xF0, 0x8F, 0x72, 0x07, 0x6D, 0x95, 0x57,
			0x63, 0x6F, 0x18, 0xB2, 0x7E, 0x59, 0xC8, 0x22,
			0x91, 0xC8, 0xF7, 0x09, 0xF2, 0x81, 0x4B, 0x09,
			0xAA, 0x94, 0x20, 0xDF, 0xFA, 0x83, 0xEB, 0x8B,
			0x93, 0xF6, 0x10, 0xC8, 0x4B, 0x0E, 0xAE, 0xD8,
			0x98, 0x1A, 0x6B, 0xEA, 0x5C, 0x12, 0x07, 0x13,
			0x6C, 0x32, 0x6D, 0x54, 0x30, 0x2F, 0x90, 0x16,
			0xD7, 0xB6, 0xA4, 0x96, 0x3F, 0x90, 0x0C, 0x46,
			0x4E, 0x0E, 0x5A, 0x3F, 0x96, 0x47, 0x7B, 0x34,
			0x2C, 0xE4, 0xE0, 0x2A, 0x90, 0x31, 0x26, 0xB5,
			0x69, 0xE6, 0x60, 0x53, 0x28, 0x91, 0xED, 0x6C,
			0x4F, 0x9E, 0x33, 0xD9, 0x58, 0x98, 0x28, 0x01,
			0x7D, 0x68, 0xDE, 0xFB, 0x2B, 0xC4, 0x2C, 0x76,
			0xCD, 0xEF, 0x4B, 0x62, 0x3C, 0x43, 0xF6, 0xE5,
			0x49, 0xAF, 0xEE, 0x7C, 0x1F, 0xCF, 0x03, 0x76,
			0x3D, 0x92, 0x41, 0xB3, 0x74, 0x8F, 0x0A, 0x5A,
			0x7F, 0xA4, 0x25, 0xD5, 0xF9, 0xFF, 0xCD, 0x96,
			0x6F, 0x1A, 0xA4, 0x84, 0x7D, 0x25, 0x66, 0xDA,
			0x7B, 0xF1, 0x5C, 0xC3, 0x60, 0x2D, 0xE8, 0x80
		},
		.len = 768
	},
	.modulus = {
		.data = {
			0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
			0xC9, 0x0F, 0xDA, 0xA2, 0x21, 0x68, 0xC2, 0x34,
			0xC4, 0xC6, 0x62, 0x8B, 0x80, 0xDC, 0x1C, 0xD1,
			0x29, 0x02, 0x4E, 0x08, 0x8A, 0x67, 0xCC, 0x74,
			0x02, 0x0B, 0xBE, 0xA6, 0x3B, 0x13, 0x9B, 0x22,
			0x51, 0x4A, 0x08, 0x79, 0x8E, 0x34, 0x04, 0xDD,
			0xEF, 0x95, 0x19, 0xB3, 0xCD, 0x3A, 0x43, 0x1B,
			0x30, 0x2B, 0x0A, 0x6D, 0xF2, 0x5F, 0x14, 0x37,
			0x4F, 0xE1, 0x35, 0x6D, 0x6D, 0x51, 0xC2, 0x45,
			0xE4, 0x85, 0xB5, 0x76, 0x62, 0x5E, 0x7E, 0xC6,
			0xF4, 0x4C, 0x42, 0xE9, 0xA6, 0x37, 0xED, 0x6B,
			0x0B, 0xFF, 0x5C, 0xB6, 0xF4, 0x06, 0xB7, 0xED,
			0xEE, 0x38, 0x6B, 0xFB, 0x5A, 0x89, 0x9F, 0xA5,
			0xAE, 0x9F, 0x24, 0x11, 0x7C, 0x4B, 0x1F, 0xE6,
			0x49, 0x28, 0x66, 0x51, 0xEC, 0xE4, 0x5B, 0x3D,
			0xC2, 0x00, 0x7C, 0xB8, 0xA1, 0x63, 0xBF, 0x05,
			0x98, 0xDA, 0x48, 0x36, 0x1C, 0x55, 0xD3, 0x9A,
			0x69, 0x16, 0x3F, 0xA8, 0xFD, 0x24, 0xCF, 0x5F,
			0x83, 0x65, 0x5D, 0x23, 0xDC, 0xA3, 0xAD, 0x96,
			0x1C, 0x62, 0xF3, 0x56, 0x20, 0x85, 0x52, 0xBB,
			0x9E, 0xD5, 0x29, 0x07, 0x70, 0x96, 0x96, 0x6D,
			0x67, 0x0C, 0x35, 0x4E, 0x4A, 0xBC, 0x98, 0x04,
			0xF1, 0x74, 0x6C, 0x08, 0xCA, 0x18, 0x21, 0x7C,
			0x32, 0x90, 0x5E, 0x46, 0x2E, 0x36, 0xCE, 0x3B,
			0xE3, 0x9E, 0x77, 0x2C, 0x18, 0x0E, 0x86, 0x03,
			0x9B, 0x27, 0x83, 0xA2, 0xEC, 0x07, 0xA2, 0x8F,
			0xB5, 0xC5, 0x5D, 0xF0, 0x6F, 0x4C, 0x52, 0xC9,
			0xDE, 0x2B, 0xCB, 0xF6, 0x95, 0x58, 0x17, 0x18,
			0x39, 0x95, 0x49, 0x7C, 0xEA, 0x95, 0x6A, 0xE5,
			0x15, 0xD2, 0x26, 0x18, 0x98, 0xFA, 0x05, 0x10,
			0x15, 0x72, 0x8E, 0x5A, 0x8A, 0xAA, 0xC4, 0x2D,
			0xAD, 0x33, 0x17, 0x0D, 0x04, 0x50, 0x7A, 0x33,
			0xA8, 0x55, 0x21, 0xAB, 0xDF, 0x1C, 0xBA, 0x64,
			0xEC, 0xFB, 0x85, 0x04, 0x58, 0xDB, 0xEF, 0x0A,
			0x8A, 0xEA, 0x71, 0x57, 0x5D, 0x06, 0x0C, 0x7D,
			0xB3, 0x97, 0x0F, 0x85, 0xA6, 0xE1, 0xE4, 0xC7,
			0xAB, 0xF5, 0xAE, 0x8C, 0xDB, 0x09, 0x33, 0xD7,
			0x1E, 0x8C, 0x94, 0xE0, 0x4A, 0x25, 0x61, 0x9D,
			0xCE, 0xE3, 0xD2, 0x26, 0x1A, 0xD2, 0xEE, 0x6B,
			0xF1, 0x2F, 0xFA, 0x06, 0xD9, 0x8A, 0x08, 0x64,
			0xD8, 0x76, 0x02, 0x73, 0x3E, 0xC8, 0x6A, 0x64,
			0x52, 0x1F, 0x2B, 0x18, 0x17, 0x7B, 0x20, 0x0C,
			0xBB, 0xE1, 0x17, 0x57, 0x7A, 0x61, 0x5D, 0x6C,
			0x77, 0x09, 0x88, 0xC0, 0xBA, 0xD9, 0x46, 0xE2,
			0x08, 0xE2, 0x4F, 0xA0, 0x74, 0xE5, 0xAB, 0x31,
			0x43, 0xDB, 0x5B, 0xFC, 0xE0, 0xFD, 0x10, 0x8E,
			0x4B, 0x82, 0xD1, 0x20, 0xA9, 0x21, 0x08, 0x01,
			0x1A, 0x72, 0x3C, 0x12, 0xA7, 0x87, 0xE6, 0xD7,
			0x88, 0x71, 0x9A, 0x10, 0xBD, 0xBA, 0x5B, 0x26,
			0x99, 0xC3, 0x27, 0x18, 0x6A, 0xF4, 0xE2, 0x3C,
			0x1A, 0x94, 0x68, 0x34, 0xB6, 0x15, 0x0B, 0xDA,
			0x25, 0x83, 0xE9, 0xCA, 0x2A, 0xD4, 0x4C, 0xE8,
			0xDB, 0xBB, 0xC2, 0xDB, 0x04, 0xDE, 0x8E, 0xF9,
			0x2E, 0x8E, 0xFC, 0x14, 0x1F, 0xBE, 0xCA, 0xA6,
			0x28, 0x7C, 0x59, 0x47, 0x4E, 0x6B, 0xC0, 0x5D,
			0x99, 0xB2, 0x96, 0x4F, 0xA0, 0x90, 0xC3, 0xA2,
			0x23, 0x3B, 0xA1, 0x86, 0x51, 0x5B, 0xE7, 0xED,
			0x1F, 0x61, 0x29, 0x70, 0xCE, 0xE2, 0xD7, 0xAF,
			0xB8, 0x1B, 0xDD, 0x76, 0x21, 0x70, 0x48, 0x1C,
			0xD0, 0x06, 0x91, 0x27, 0xD5, 0xB0, 0x5A, 0xA9,
			0x93, 0xB4, 0xEA, 0x98, 0x8D, 0x8F, 0xDD, 0xC1,
			0x86, 0xFF, 0xB7, 0xDC, 0x90, 0xA6, 0xC0, 0x8F,
			0x4D, 0xF4, 0x35, 0xC9, 0x34, 0x02, 0x84, 0x92,
			0x36, 0xC3, 0xFA, 0xB4, 0xD2, 0x7C, 0x70, 0x26,
			0xC1, 0xD4, 0xDC, 0xB2, 0x60, 0x26, 0x46, 0xDE,
			0xC9, 0x75, 0x1E, 0x76, 0x3D, 0xBA, 0x37, 0xBD,
			0xF8, 0xFF, 0x94, 0x06, 0xAD, 0x9E, 0x53, 0x0E,
			0xE5, 0xDB, 0x38, 0x2F, 0x41, 0x30, 0x01, 0xAE,
			0xB0, 0x6A, 0x53, 0xED, 0x90, 0x27, 0xD8, 0x31,
			0x17, 0x97, 0x27, 0xB0, 0x86, 0x5A, 0x89, 0x18,
			0xDA, 0x3E, 0xDB, 0xEB, 0xCF, 0x9B, 0x14, 0xED,
			0x44, 0xCE, 0x6C, 0xBA, 0xCE, 0xD4, 0xBB, 0x1B,
			0xDB, 0x7F, 0x14, 0x47, 0xE6, 0xCC, 0x25, 0x4B,
			0x33, 0x20, 0x51, 0x51, 0x2B, 0xD7, 0xAF, 0x42,
			0x6F, 0xB8, 0xF4, 0x01, 0x37, 0x8C, 0xD2, 0xBF,
			0x59, 0x83, 0xCA, 0x01, 0xC6, 0x4B, 0x92, 0xEC,
			0xF0, 0x32, 0xEA, 0x15, 0xD1, 0x72, 0x1D, 0x03,
			0xF4, 0x82, 0xD7, 0xCE, 0x6E, 0x74, 0xFE, 0xF6,
			0xD5, 0x5E, 0x70, 0x2F, 0x46, 0x98, 0x0C, 0x82,
			0xB5, 0xA8, 0x40, 0x31, 0x90, 0x0B, 0x1C, 0x9E,
			0x59, 0xE7, 0xC9, 0x7F, 0xBE, 0xC7, 0xE8, 0xF3,
			0x23, 0xA9, 0x7A, 0x7E, 0x36, 0xCC, 0x88, 0xBE,
			0x0F, 0x1D, 0x45, 0xB7, 0xFF, 0x58, 0x5A, 0xC5,
			0x4B, 0xD4, 0x07, 0xB2, 0x2B, 0x41, 0x54, 0xAA,
			0xCC, 0x8F, 0x6D, 0x7E, 0xBF, 0x48, 0xE1, 0xD8,
			0x14, 0xCC, 0x5E, 0xD2, 0x0F, 0x80, 0x37, 0xE0,
			0xA7, 0x97, 0x15, 0xEE, 0xF2, 0x9B, 0xE3, 0x28,
			0x06, 0xA1, 0xD5, 0x8B, 0xB7, 0xC5, 0xDA, 0x76,
			0xF5, 0x50, 0xAA, 0x3D, 0x8A, 0x1F, 0xBF, 0xF0,
			0xEB, 0x19, 0xCC, 0xB1, 0xA3, 0x13, 0xD5, 0x5C,
			0xDA, 0x56, 0xC9, 0xEC, 0x2E, 0xF2, 0x96, 0x32,
			0x38, 0x7F, 0xE8, 0xD7, 0x6E, 0x3C, 0x04, 0x68,
			0x04, 0x3E, 0x8F, 0x66, 0x3F, 0x48, 0x60, 0xEE,
			0x12, 0xBF, 0x2D, 0x5B, 0x0B, 0x74, 0x74, 0xD6,
			0xE6, 0x94, 0xF9, 0x1E, 0x6D, 0xCC, 0x40, 0x24,
			0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 768
	},
},
{
	.description = "Modular Exponentiation tests for Group 18",
	.xform_type = RTE_CRYPTO_ASYM_XFORM_MODEX,
	.base = {
		.data = {
			0x2,
		},
		.len = 1
	},
	.exponent = {
		.data = {
			0x01, 0xA6, 0x8A, 0x0A, 0xDA, 0xA6, 0x14, 0x43,
			0x84, 0xD6, 0xEB, 0x11, 0x67, 0xA6, 0xD2, 0xAC,
			0x11, 0x5D, 0x15, 0x99, 0x31, 0x99, 0xAE, 0x08,
			0x3D, 0xEC, 0x19, 0x57, 0x3D, 0xDF, 0x96, 0x7C,
			0x9A, 0x1A, 0x72, 0x80, 0x1F, 0xF3, 0x50, 0x91,
			0xD0, 0x70, 0x11, 0x37, 0xA8, 0xFE, 0xE4, 0x37,
			0x3B, 0x36, 0x62, 0x89, 0xCF, 0x31, 0x1D, 0x76,
			0x28, 0xBE, 0x5F, 0x25, 0x12, 0x6E, 0x72, 0x83,
			0x8A, 0x1A, 0xC2, 0xFA, 0xD6, 0x49, 0x2C, 0x4F,
			0x2D, 0xF1, 0x77, 0x67, 0x49, 0xDA
		},
		.len = 78
	},
	.reminder = {
		.data = {
			0x2D, 0x06, 0x07, 0x48, 0x35, 0xEF, 0xAB, 0x87,
			0x00, 0x1B, 0xCC, 0x5C, 0x67, 0x6B, 0xE5, 0x75,
			0x42, 0x64, 0x8F, 0x4E, 0x42, 0x94, 0x05, 0x42,
			0xB7, 0x1C, 0x06, 0x21, 0x6B, 0x06, 0x51, 0x28,
			0xEA, 0x6B, 0x28, 0x3A, 0x2A, 0x3D, 0x92, 0xE0,
			0x1B, 0x67, 0x2A, 0xD8, 0x5E, 0x5D, 0xFF, 0x0A,
			0x84, 0xE5, 0xA2, 0xDE, 0xBC, 0xB3, 0xAC, 0x02,
			0x01, 0xB6, 0x97, 0xFA, 0x3C, 0x03, 0xC4, 0xF4,
			0x50, 0x5A, 0x89, 0x19, 0x9A, 0x92, 0xD6, 0x4D,
			0x0B, 0x59, 0xCB, 0xB2, 0x3F, 0x2D, 0x0A, 0x83,
			0x93, 0x7F, 0xB3, 0x51, 0xAC, 0xBD, 0x8D, 0x63,
			0x5B, 0xDA, 0x04, 0x1B, 0x82, 0x57, 0xA9, 0xA5,
			0x7E, 0x5B, 0xDD, 0xBB, 0xF5, 0x9A, 0x0E, 0x8F,
			0x19, 0x37, 0xE3, 0x7C, 0xB9, 0x4D, 0x6F, 0x98,
			0xB0, 0x64, 0xF4, 0xB8, 0x88, 0xCA, 0x1E, 0xFF,
			0xD5, 0x90, 0xB6, 0xAA, 0x02, 0x9C, 0x3B, 0x4C,
			0x1B, 0xC8, 0xCA, 0xB0, 0x1C, 0x77, 0xBB, 0x76,
			0x52, 0x24, 0x1E, 0xC8, 0x65, 0x33, 0xD2, 0x9D,
			0x62, 0xB4, 0xE9, 0x14, 0x14, 0xA3, 0x6E, 0xE4,
			0xA8, 0x40, 0xEF, 0x37, 0x3F, 0x61, 0x3D, 0xEC,
			0xD2, 0x14, 0xEE, 0x21, 0x4E, 0xAA, 0x0A, 0x7F,
			0x7E, 0x00, 0x55, 0xF8, 0xD8, 0xA0, 0x0E, 0x3B,
			0xAD, 0xA0, 0xDC, 0x6B, 0x73, 0xD6, 0x7C, 0xA3,
			0x02, 0x71, 0x2E, 0x2C, 0xC7, 0xC3, 0x70, 0xD7,
			0xBE, 0x82, 0xB6, 0xF1, 0xBA, 0x95, 0x79, 0x57,
			0x05, 0x8E, 0x5D, 0x12, 0xD5, 0xB2, 0x2F, 0x53,
			0xF0, 0x55, 0x9A, 0x2B, 0xEF, 0x2A, 0x87, 0x7B,
			0x60, 0xC4, 0xAD, 0xDC, 0xA7, 0x61, 0x06, 0xD9,
			0xD6, 0x66, 0x91, 0x6F, 0x9E, 0x6D, 0xA7, 0x10,
			0x25, 0x16, 0x01, 0x92, 0x11, 0x94, 0xCA, 0x42,
			0x8C, 0x69, 0xFF, 0x29, 0x65, 0xA0, 0x56, 0x9B,
			0x7B, 0xC6, 0xF7, 0xB4, 0xE0, 0xC7, 0xE7, 0x97,
			0xD8, 0x38, 0xEC, 0x24, 0x06, 0x1F, 0xDF, 0x2F,
			0x6B, 0x3D, 0xEC, 0x93, 0xAF, 0xBB, 0x29, 0xAF,
			0xC8, 0x5B, 0x38, 0xD4, 0xD0, 0x3A, 0x2A, 0xCA,
			0xB8, 0x16, 0xD6, 0x8A, 0xCE, 0xCF, 0x86, 0xAD,
			0x72, 0xB6, 0x9A, 0x43, 0x8A, 0xB7, 0x8C, 0x1C,
			0x71, 0xA0, 0xF9, 0x86, 0x66, 0x9A, 0xA3, 0x2D,
			0x3B, 0xD7, 0x89, 0xBC, 0xCE, 0xF6, 0xFA, 0x7A,
			0xDD, 0x96, 0x6F, 0x64, 0xFE, 0x19, 0x33, 0xCB,
			0xCC, 0x98, 0x75, 0xA3, 0x73, 0x3C, 0x00, 0x24,
			0x15, 0x63, 0xBE, 0xC3, 0x0E, 0xD4, 0x9A, 0xDF,
			0x35, 0x2A, 0xCA, 0x48, 0xDE, 0x5C, 0xD0, 0x41,
			0xF8, 0xF8, 0xBE, 0x35, 0xC5, 0xC4, 0x95, 0x5A,
			0x63, 0x92, 0x20, 0xEA, 0x40, 0xAD, 0x8F, 0x31,
			0xD9, 0x85, 0xF2, 0x81, 0xB2, 0x43, 0x9A, 0x33,
			0x08, 0x30, 0x68, 0xB6, 0xF2, 0x56, 0x8B, 0xBA,
			0x1E, 0x97, 0x94, 0x37, 0xEF, 0xC4, 0x27, 0xDD,
			0xAE, 0x02, 0x22, 0xE1, 0x04, 0xDB, 0xF8, 0xA7,
			0xAE, 0x75, 0x98, 0x46, 0xED, 0x45, 0xAA, 0x06,
			0xC4, 0xAB, 0xFC, 0xFF, 0xB8, 0x84, 0x90, 0x15,
			0x4A, 0xDC, 0x66, 0x09, 0x59, 0x92, 0x48, 0x06,
			0xC1, 0x8C, 0xD3, 0xB2, 0x4F, 0xF9, 0x47, 0x0A,
			0xCC, 0xA7, 0x0D, 0x66, 0x39, 0x2C, 0x7D, 0x2D,
			0xD7, 0x9E, 0x3B, 0xA6, 0xC6, 0x8E, 0x11, 0x04,
			0x4A, 0x5F, 0x6F, 0x59, 0x4A, 0x5D, 0x97, 0xF7,
			0x7A, 0xCD, 0xF8, 0x6A, 0x3D, 0xA1, 0xAD, 0x0A,
			0x6B, 0x0C, 0x94, 0x30, 0x5C, 0xFE, 0x58, 0x73,
			0x31, 0x61, 0x02, 0x84, 0xE0, 0x8A, 0xFE, 0xD5,
			0xB4, 0x3B, 0xA0, 0xCE, 0x0A, 0x36, 0x06, 0x6B,
			0x20, 0xF6, 0x80, 0x51, 0x17, 0xF4, 0x5E, 0xFD,
			0x83, 0x9D, 0xCD, 0x7F, 0x1F, 0xB3, 0xC7, 0x5C,
			0x29, 0x34, 0x43, 0x13, 0x31, 0xBE, 0x74, 0x12,
			0xAB, 0x18, 0x7D, 0x2B, 0xB1, 0xAC, 0x2A, 0x22,
			0x65, 0xBB, 0x87, 0x91, 0xF5, 0x5A, 0xB4, 0x0B,
			0x57, 0x85, 0xF1, 0xB4, 0xC0, 0xB9, 0x9B, 0x78,
			0x48, 0xA0, 0x02, 0x10, 0xCE, 0x01, 0x92, 0x24,
			0x88, 0xCE, 0xC7, 0xDC, 0xF9, 0xA7, 0x4B, 0x9A,
			0x09, 0xEB, 0xFC, 0x8A, 0xCE, 0xA6, 0xBB, 0xAC,
			0x14, 0x91, 0xBC, 0x7B, 0x49, 0x80, 0xD2, 0x3C,
			0x07, 0xB7, 0xD6, 0x41, 0xAB, 0x77, 0x98, 0x0C,
			0xAF, 0x19, 0xCD, 0x16, 0xD6, 0xB9, 0x79, 0xDB,
			0xD0, 0xBA, 0x72, 0x7A, 0x5E, 0xD3, 0xE4, 0x72,
			0xA7, 0x4C, 0x56, 0x9E, 0x2C, 0x78, 0x31, 0xC7,
			0x8D, 0x7D, 0x9C, 0x9C, 0xEE, 0x6F, 0x5E, 0x36,
			0x4A, 0xF6, 0x5C, 0x4E, 0x69, 0x42, 0x17, 0x86,
			0x85, 0x3C, 0x41, 0x22, 0x86, 0x23, 0x8A, 0x78,
			0x4C, 0x08, 0x0B, 0x27, 0x3B, 0x16, 0xCD, 0xE8,
			0x0B, 0x8A, 0xCA, 0xAB, 0x5F, 0x1A, 0x6F, 0x7C,
			0xE6, 0xAC, 0xFF, 0x22, 0xF3, 0xF1, 0xF8, 0x10,
			0x18, 0x68, 0x81, 0x4C, 0x41, 0x2E, 0xEC, 0x2B,
			0xE3, 0xDA, 0xD8, 0x41, 0xD1, 0x66, 0x3F, 0xD1,
			0x6A, 0xF7, 0x9D, 0xD4, 0xDB, 0xE5, 0x04, 0xC0,
			0xB8, 0xA2, 0xDD, 0x43, 0x13, 0x00, 0x8C, 0x07,
			0xD2, 0x25, 0x14, 0x8B, 0x81, 0x25, 0x22, 0xF1,
			0xD4, 0xEB, 0xD2, 0xAD, 0xC8, 0x6C, 0xCA, 0x8D,
			0x08, 0x8B, 0x3B, 0x2C, 0xF0, 0x05, 0xD0, 0x10,
			0xC8, 0x5C, 0xB8, 0xA0, 0xC7, 0xD5, 0x33, 0xAB,
			0x5F, 0x79, 0x7C, 0x0C, 0x13, 0x32, 0xDF, 0x02,
			0xDC, 0x1A, 0x62, 0x07, 0xA0, 0x76, 0x53, 0xE3,
			0xF7, 0x66, 0xA3, 0x85, 0x91, 0x85, 0x8F, 0x18,
			0x48, 0xCA, 0x49, 0x60, 0xED, 0xB9, 0x51, 0xB7,
			0x77, 0xDB, 0x66, 0x25, 0xFC, 0x43, 0xD8, 0xEC,
			0xBB, 0xC0, 0x02, 0x1F, 0x92, 0x42, 0xA7, 0x47,
			0x73, 0x56, 0xE1, 0xAB, 0x88, 0x21, 0x36, 0x84,
			0x99, 0xC9, 0x36, 0x0F, 0x90, 0xDA, 0x9A, 0x04,
			0x88, 0x88, 0x92, 0x57, 0xA3, 0x93, 0x35, 0x80,
			0xF8, 0x44, 0xAE, 0xA8, 0xD3, 0x4B, 0x7F, 0xAA,
			0x8B, 0x90, 0x4F, 0x3C, 0xBA, 0x53, 0x55, 0x29,
			0x9C, 0x60, 0x21, 0x75, 0xE3, 0x0F, 0x87, 0xA7,
			0xA3, 0x83, 0x5C, 0x9B, 0xB6, 0x68, 0x18, 0x42,
			0xB1, 0xDE, 0x82, 0x0A, 0xD3, 0xCB, 0xE1, 0xAB,
			0x46, 0x9E, 0xC3, 0xA5, 0xBF, 0xA1, 0xCE, 0x04,
			0x4B, 0x63, 0x76, 0x30, 0x9B, 0x7C, 0x5E, 0xE7,
			0x8A, 0xEC, 0x8F, 0x77, 0xBF, 0x02, 0xCC, 0xB2,
			0xCE, 0xFE, 0x9F, 0xFE, 0x2F, 0x48, 0xE0, 0x16,
			0x3B, 0xFC, 0x2B, 0x48, 0xE0, 0x4C, 0xF7, 0xB9,
			0x0B, 0x03, 0xEE, 0xF2, 0x91, 0x0F, 0xF5, 0xC5,
			0xA5, 0x17, 0x21, 0x41, 0xF1, 0x4F, 0x47, 0xCB,
			0x28, 0x94, 0xF3, 0xD2, 0xFA, 0x0E, 0x03, 0x53,
			0x8F, 0xC9, 0x3D, 0x42, 0x3D, 0xD2, 0x4B, 0x29,
			0x5F, 0x3D, 0x4A, 0x9D, 0x0C, 0x19, 0x32, 0x34,
			0xA1, 0x51, 0x4A, 0x30, 0xA9, 0xEA, 0x22, 0x93,
			0xCF, 0x7E, 0xDB, 0xBE, 0x70, 0x67, 0xD5, 0x1E,
			0x7B, 0xDE, 0x06, 0x55, 0xC2, 0xAF, 0x70, 0x58,
			0x10, 0xA5, 0x13, 0x3A, 0x7A, 0xBE, 0x7B, 0x35,
			0x24, 0x53, 0x13, 0xB9, 0x04, 0x11, 0x3D, 0x00,
			0xA3, 0xA2, 0x52, 0x88, 0x1A, 0x99, 0x06, 0x29,
			0xA3, 0x27, 0xA6, 0xF0, 0xDF, 0xCD, 0x01, 0x3D,
			0x86, 0xE0, 0x12, 0x60, 0x3C, 0x28, 0xE1, 0xF1,
			0x9B, 0x25, 0xDF, 0xC3, 0xAF, 0x9B, 0x30, 0x8B,
			0xFF, 0x58, 0x5C, 0x2F, 0xEF, 0x7E, 0xB7, 0x62,
			0xFE, 0x19, 0x7A, 0xC8, 0x4F, 0x1F, 0x61, 0xCE,
			0x29, 0x21, 0x82, 0x78, 0x48, 0x45, 0x0F, 0xAF,
			0xFB, 0x9D, 0x31, 0xED, 0x75, 0x78, 0x47, 0xD8,
			0x17, 0xAA, 0x37, 0xA8, 0x11, 0x51, 0xE5, 0xF5,
			0x23, 0x04, 0x46, 0xA6, 0x8D, 0xFD, 0xFA, 0xC8,
			0xBB, 0x58, 0x05, 0x45, 0xF0, 0x8D, 0x94, 0x7E
		},
		.len = 1024
	},
	.modulus = {
		.data = {
			0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
			0xC9, 0x0F, 0xDA, 0xA2, 0x21, 0x68, 0xC2, 0x34,
			0xC4, 0xC6, 0x62, 0x8B, 0x80, 0xDC, 0x1C, 0xD1,
			0x29, 0x02, 0x4E, 0x08, 0x8A, 0x67, 0xCC, 0x74,
			0x02, 0x0B, 0xBE, 0xA6, 0x3B, 0x13, 0x9B, 0x22,
			0x51, 0x4A, 0x08, 0x79, 0x8E, 0x34, 0x04, 0xDD,
			0xEF, 0x95, 0x19, 0xB3, 0xCD, 0x3A, 0x43, 0x1B,
			0x30, 0x2B, 0x0A, 0x6D, 0xF2, 0x5F, 0x14, 0x37,
			0x4F, 0xE1, 0x35, 0x6D, 0x6D, 0x51, 0xC2, 0x45,
			0xE4, 0x85, 0xB5, 0x76, 0x62, 0x5E, 0x7E, 0xC6,
			0xF4, 0x4C, 0x42, 0xE9, 0xA6, 0x37, 0xED, 0x6B,
			0x0B, 0xFF, 0x5C, 0xB6, 0xF4, 0x06, 0xB7, 0xED,
			0xEE, 0x38, 0x6B, 0xFB, 0x5A, 0x89, 0x9F, 0xA5,
			0xAE, 0x9F, 0x24, 0x11, 0x7C, 0x4B, 0x1F, 0xE6,
			0x49, 0x28, 0x66, 0x51, 0xEC, 0xE4, 0x5B, 0x3D,
			0xC2, 0x00, 0x7C, 0xB8, 0xA1, 0x63, 0xBF, 0x05,
			0x98, 0xDA, 0x48, 0x36, 0x1C, 0x55, 0xD3, 0x9A,
			0x69, 0x16, 0x3F, 0xA8, 0xFD, 0x24, 0xCF, 0x5F,
			0x83, 0x65, 0x5D, 0x23, 0xDC, 0xA3, 0xAD, 0x96,
			0x1C, 0x62, 0xF3, 0x56, 0x20, 0x85, 0x52, 0xBB,
			0x9E, 0xD5, 0x29, 0x07, 0x70, 0x96, 0x96, 0x6D,
			0x67, 0x0C, 0x35, 0x4E, 0x4A, 0xBC, 0x98, 0x04,
			0xF1, 0x74, 0x6C, 0x08, 0xCA, 0x18, 0x21, 0x7C,
			0x32, 0x90, 0x5E, 0x46, 0x2E, 0x36, 0xCE, 0x3B,
			0xE3, 0x9E, 0x77, 0x2C, 0x18, 0x0E, 0x86, 0x03,
			0x9B, 0x27, 0x83, 0xA2, 0xEC, 0x07, 0xA2, 0x8F,
			0xB5, 0xC5, 0x5D, 0xF0, 0x6F, 0x4C, 0x52, 0xC9,
			0xDE, 0x2B, 0xCB, 0xF6, 0x95, 0x58, 0x17, 0x18,
			0x39, 0x95, 0x49, 0x7C, 0xEA, 0x95, 0x6A, 0xE5,
			0x15, 0xD2, 0x26, 0x18, 0x98, 0xFA, 0x05, 0x10,
			0x15, 0x72, 0x8E, 0x5A, 0x8A, 0xAA, 0xC4, 0x2D,
			0xAD, 0x33, 0x17, 0x0D, 0x04, 0x50, 0x7A, 0x33,
			0xA8, 0x55, 0x21, 0xAB, 0xDF, 0x1C, 0xBA, 0x64,
			0xEC, 0xFB, 0x85, 0x04, 0x58, 0xDB, 0xEF, 0x0A,
			0x8A, 0xEA, 0x71, 0x57, 0x5D, 0x06, 0x0C, 0x7D,
			0xB3, 0x97, 0x0F, 0x85, 0xA6, 0xE1, 0xE4, 0xC7,
			0xAB, 0xF5, 0xAE, 0x8C, 0xDB, 0x09, 0x33, 0xD7,
			0x1E, 0x8C, 0x94, 0xE0, 0x4A, 0x25, 0x61, 0x9D,
			0xCE, 0xE3, 0xD2, 0x26, 0x1A, 0xD2, 0xEE, 0x6B,
			0xF1, 0x2F, 0xFA, 0x06, 0xD9, 0x8A, 0x08, 0x64,
			0xD8, 0x76, 0x02, 0x73, 0x3E, 0xC8, 0x6A, 0x64,
			0x52, 0x1F, 0x2B, 0x18, 0x17, 0x7B, 0x20, 0x0C,
			0xBB, 0xE1, 0x17, 0x57, 0x7A, 0x61, 0x5D, 0x6C,
			0x77, 0x09, 0x88, 0xC0, 0xBA, 0xD9, 0x46, 0xE2,
			0x08, 0xE2, 0x4F, 0xA0, 0x74, 0xE5, 0xAB, 0x31,
			0x43, 0xDB, 0x5B, 0xFC, 0xE0, 0xFD, 0x10, 0x8E,
			0x4B, 0x82, 0xD1, 0x20, 0xA9, 0x21, 0x08, 0x01,
			0x1A, 0x72, 0x3C, 0x12, 0xA7, 0x87, 0xE6, 0xD7,
			0x88, 0x71, 0x9A, 0x10, 0xBD, 0xBA, 0x5B, 0x26,
			0x99, 0xC3, 0x27, 0x18, 0x6A, 0xF4, 0xE2, 0x3C,
			0x1A, 0x94, 0x68, 0x34, 0xB6, 0x15, 0x0B, 0xDA,
			0x25, 0x83, 0xE9, 0xCA, 0x2A, 0xD4, 0x4C, 0xE8,
			0xDB, 0xBB, 0xC2, 0xDB, 0x04, 0xDE, 0x8E, 0xF9,
			0x2E, 0x8E, 0xFC, 0x14, 0x1F, 0xBE, 0xCA, 0xA6,
			0x28, 0x7C, 0x59, 0x47, 0x4E, 0x6B, 0xC0, 0x5D,
			0x99, 0xB2, 0x96, 0x4F, 0xA0, 0x90, 0xC3, 0xA2,
			0x23, 0x3B, 0xA1, 0x86, 0x51, 0x5B, 0xE7, 0xED,
			0x1F, 0x61, 0x29, 0x70, 0xCE, 0xE2, 0xD7, 0xAF,
			0xB8, 0x1B, 0xDD, 0x76, 0x21, 0x70, 0x48, 0x1C,
			0xD0, 0x06, 0x91, 0x27, 0xD5, 0xB0, 0x5A, 0xA9,
			0x93, 0xB4, 0xEA, 0x98, 0x8D, 0x8F, 0xDD, 0xC1,
			0x86, 0xFF, 0xB7, 0xDC, 0x90, 0xA6, 0xC0, 0x8F,
			0x4D, 0xF4, 0x35, 0xC9, 0x34, 0x02, 0x84, 0x92,
			0x36, 0xC3, 0xFA, 0xB4, 0xD2, 0x7C, 0x70, 0x26,
			0xC1, 0xD4, 0xDC, 0xB2, 0x60, 0x26, 0x46, 0xDE,
			0xC9, 0x75, 0x1E, 0x76, 0x3D, 0xBA, 0x37, 0xBD,
			0xF8, 0xFF, 0x94, 0x06, 0xAD, 0x9E, 0x53, 0x0E,
			0xE5, 0xDB, 0x38, 0x2F, 0x41, 0x30, 0x01, 0xAE,
			0xB0, 0x6A, 0x53, 0xED, 0x90, 0x27, 0xD8, 0x31,
			0x17, 0x97, 0x27, 0xB0, 0x86, 0x5A, 0x89, 0x18,
			0xDA, 0x3E, 0xDB, 0xEB, 0xCF, 0x9B, 0x14, 0xED,
			0x44, 0xCE, 0x6C, 0xBA, 0xCE, 0xD4, 0xBB, 0x1B,
			0xDB, 0x7F, 0x14, 0x47, 0xE6, 0xCC, 0x25, 0x4B,
			0x33, 0x20, 0x51, 0x51, 0x2B, 0xD7, 0xAF, 0x42,
			0x6F, 0xB8, 0xF4, 0x01, 0x37, 0x8C, 0xD2, 0xBF,
			0x59, 0x83, 0xCA, 0x01, 0xC6, 0x4B, 0x92, 0xEC,
			0xF0, 0x32, 0xEA, 0x15, 0xD1, 0x72, 0x1D, 0x03,
			0xF4, 0x82, 0xD7, 0xCE, 0x6E, 0x74, 0xFE, 0xF6,
			0xD5, 0x5E, 0x70, 0x2F, 0x46, 0x98, 0x0C, 0x82,
			0xB5, 0xA8, 0x40, 0x31, 0x90, 0x0B, 0x1C, 0x9E,
			0x59, 0xE7, 0xC9, 0x7F, 0xBE, 0xC7, 0xE8, 0xF3,
			0x23, 0xA9, 0x7A, 0x7E, 0x36, 0xCC, 0x88, 0xBE,
			0x0F, 0x1D, 0x45, 0xB7, 0xFF, 0x58, 0x5A, 0xC5,
			0x4B, 0xD4, 0x07, 0xB2, 0x2B, 0x41, 0x54, 0xAA,
			0xCC, 0x8F, 0x6D, 0x7E, 0xBF, 0x48, 0xE1, 0xD8,
			0x14, 0xCC, 0x5E, 0xD2, 0x0F, 0x80, 0x37, 0xE0,
			0xA7, 0x97, 0x15, 0xEE, 0xF2, 0x9B, 0xE3, 0x28,
			0x06, 0xA1, 0xD5, 0x8B, 0xB7, 0xC5, 0xDA, 0x76,
			0xF5, 0x50, 0xAA, 0x3D, 0x8A, 0x1F, 0xBF, 0xF0,
			0xEB, 0x19, 0xCC, 0xB1, 0xA3, 0x13, 0xD5, 0x5C,
			0xDA, 0x56, 0xC9, 0xEC, 0x2E, 0xF2, 0x96, 0x32,
			0x38, 0x7F, 0xE8, 0xD7, 0x6E, 0x3C, 0x04, 0x68,
			0x04, 0x3E, 0x8F, 0x66, 0x3F, 0x48, 0x60, 0xEE,
			0x12, 0xBF, 0x2D, 0x5B, 0x0B, 0x74, 0x74, 0xD6,
			0xE6, 0x94, 0xF9, 0x1E, 0x6D, 0xBE, 0x11, 0x59,
			0x74, 0xA3, 0x92, 0x6F, 0x12, 0xFE, 0xE5, 0xE4,
			0x38, 0x77, 0x7C, 0xB6, 0xA9, 0x32, 0xDF, 0x8C,
			0xD8, 0xBE, 0xC4, 0xD0, 0x73, 0xB9, 0x31, 0xBA,
			0x3B, 0xC8, 0x32, 0xB6, 0x8D, 0x9D, 0xD3, 0x00,
			0x74, 0x1F, 0xA7, 0xBF, 0x8A, 0xFC, 0x47, 0xED,
			0x25, 0x76, 0xF6, 0x93, 0x6B, 0xA4, 0x24, 0x66,
			0x3A, 0xAB, 0x63, 0x9C, 0x5A, 0xE4, 0xF5, 0x68,
			0x34, 0x23, 0xB4, 0x74, 0x2B, 0xF1, 0xC9, 0x78,
			0x23, 0x8F, 0x16, 0xCB, 0xE3, 0x9D, 0x65, 0x2D,
			0xE3, 0xFD, 0xB8, 0xBE, 0xFC, 0x84, 0x8A, 0xD9,
			0x22, 0x22, 0x2E, 0x04, 0xA4, 0x03, 0x7C, 0x07,
			0x13, 0xEB, 0x57, 0xA8, 0x1A, 0x23, 0xF0, 0xC7,
			0x34, 0x73, 0xFC, 0x64, 0x6C, 0xEA, 0x30, 0x6B,
			0x4B, 0xCB, 0xC8, 0x86, 0x2F, 0x83, 0x85, 0xDD,
			0xFA, 0x9D, 0x4B, 0x7F, 0xA2, 0xC0, 0x87, 0xE8,
			0x79, 0x68, 0x33, 0x03, 0xED, 0x5B, 0xDD, 0x3A,
			0x06, 0x2B, 0x3C, 0xF5, 0xB3, 0xA2, 0x78, 0xA6,
			0x6D, 0x2A, 0x13, 0xF8, 0x3F, 0x44, 0xF8, 0x2D,
			0xDF, 0x31, 0x0E, 0xE0, 0x74, 0xAB, 0x6A, 0x36,
			0x45, 0x97, 0xE8, 0x99, 0xA0, 0x25, 0x5D, 0xC1,
			0x64, 0xF3, 0x1C, 0xC5, 0x08, 0x46, 0x85, 0x1D,
			0xF9, 0xAB, 0x48, 0x19, 0x5D, 0xED, 0x7E, 0xA1,
			0xB1, 0xD5, 0x10, 0xBD, 0x7E, 0xE7, 0x4D, 0x73,
			0xFA, 0xF3, 0x6B, 0xC3, 0x1E, 0xCF, 0xA2, 0x68,
			0x35, 0x90, 0x46, 0xF4, 0xEB, 0x87, 0x9F, 0x92,
			0x40, 0x09, 0x43, 0x8B, 0x48, 0x1C, 0x6C, 0xD7,
			0x88, 0x9A, 0x00, 0x2E, 0xD5, 0xEE, 0x38, 0x2B,
			0xC9, 0x19, 0x0D, 0xA6, 0xFC, 0x02, 0x6E, 0x47,
			0x95, 0x58, 0xE4, 0x47, 0x56, 0x77, 0xE9, 0xAA,
			0x9E, 0x30, 0x50, 0xE2, 0x76, 0x56, 0x94, 0xDF,
			0xC8, 0x1F, 0x56, 0xE8, 0x80, 0xB9, 0x6E, 0x71,
			0x60, 0xC9, 0x80, 0xDD, 0x98, 0xED, 0xD3, 0xDF,
			0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
		},
		.len = 1024
	},
},
{
	/*
	 * Referenced from RFC 5114 for checking g^a mod p = A.
	 * base and modulus data are from Section 2.3.
	 * exponent and reminder data are from Appendix A.3.
	 */
	.description = "Modular Exponentiation tests for Group 24",
	.xform_type = RTE_CRYPTO_ASYM_XFORM_MODEX,
	.base = {
		.data = {
			0x3F, 0xB3, 0x2C, 0x9B, 0x73, 0x13, 0x4D, 0x0B,
			0x2E, 0x77, 0x50, 0x66, 0x60, 0xED, 0xBD, 0x48,
			0x4C, 0xA7, 0xB1, 0x8F, 0x21, 0xEF, 0x20, 0x54,
			0x07, 0xF4, 0x79, 0x3A, 0x1A, 0x0B, 0xA1, 0x25,
			0x10, 0xDB, 0xC1, 0x50, 0x77, 0xBE, 0x46, 0x3F,
			0xFF, 0x4F, 0xED, 0x4A, 0xAC, 0x0B, 0xB5, 0x55,
			0xBE, 0x3A, 0x6C, 0x1B, 0x0C, 0x6B, 0x47, 0xB1,
			0xBC, 0x37, 0x73, 0xBF, 0x7E, 0x8C, 0x6F, 0x62,
			0x90, 0x12, 0x28, 0xF8, 0xC2, 0x8C, 0xBB, 0x18,
			0xA5, 0x5A, 0xE3, 0x13, 0x41, 0x00, 0x0A, 0x65,
			0x01, 0x96, 0xF9, 0x31, 0xC7, 0x7A, 0x57, 0xF2,
			0xDD, 0xF4, 0x63, 0xE5, 0xE9, 0xEC, 0x14, 0x4B,
			0x77, 0x7D, 0xE6, 0x2A, 0xAA, 0xB8, 0xA8, 0x62,
			0x8A, 0xC3, 0x76, 0xD2, 0x82, 0xD6, 0xED, 0x38,
			0x64, 0xE6, 0x79, 0x82, 0x42, 0x8E, 0xBC, 0x83,
			0x1D, 0x14, 0x34, 0x8F, 0x6F, 0x2F, 0x91, 0x93,
			0xB5, 0x04, 0x5A, 0xF2, 0x76, 0x71, 0x64, 0xE1,
			0xDF, 0xC9, 0x67, 0xC1, 0xFB, 0x3F, 0x2E, 0x55,
			0xA4, 0xBD, 0x1B, 0xFF, 0xE8, 0x3B, 0x9C, 0x80,
			0xD0, 0x52, 0xB9, 0x85, 0xD1, 0x82, 0xEA, 0x0A,
			0xDB, 0x2A, 0x3B, 0x73, 0x13, 0xD3, 0xFE, 0x14,
			0xC8, 0x48, 0x4B, 0x1E, 0x05, 0x25, 0x88, 0xB9,
			0xB7, 0xD2, 0xBB, 0xD2, 0xDF, 0x01, 0x61, 0x99,
			0xEC, 0xD0, 0x6E, 0x15, 0x57, 0xCD, 0x09, 0x15,
			0xB3, 0x35, 0x3B, 0xBB, 0x64, 0xE0, 0xEC, 0x37,
			0x7F, 0xD0, 0x28, 0x37, 0x0D, 0xF9, 0x2B, 0x52,
			0xC7, 0x89, 0x14, 0x28, 0xCD, 0xC6, 0x7E, 0xB6,
			0x18, 0x4B, 0x52, 0x3D, 0x1D, 0xB2, 0x46, 0xC3,
			0x2F, 0x63, 0x07, 0x84, 0x90, 0xF0, 0x0E, 0xF8,
			0xD6, 0x47, 0xD1, 0x48, 0xD4, 0x79, 0x54, 0x51,
			0x5E, 0x23, 0x27, 0xCF, 0xEF, 0x98, 0xC5, 0x82,
			0x66, 0x4B, 0x4C, 0x0F, 0x6C, 0xC4, 0x16, 0x59,
		},
		.len = 256
	},
	.exponent = {
		.data = {
			0x08, 0x81, 0x38, 0x2C, 0xDB, 0x87, 0x66, 0x0C,
			0x6D, 0xC1, 0x3E, 0x61, 0x49, 0x38, 0xD5, 0xB9,
			0xC8, 0xB2, 0xF2, 0x48, 0x58, 0x1C, 0xC5, 0xE3,
			0x1B, 0x35, 0x45, 0x43, 0x97, 0xFC, 0xE5, 0x0E,
		},
		.len = 32
	},
	.reminder = {
		.data = {
			0x2E, 0x93, 0x80, 0xC8, 0x32, 0x3A, 0xF9, 0x75,
			0x45, 0xBC, 0x49, 0x41, 0xDE, 0xB0, 0xEC, 0x37,
			0x42, 0xC6, 0x2F, 0xE0, 0xEC, 0xE8, 0x24, 0xA6,
			0xAB, 0xDB, 0xE6, 0x6C, 0x59, 0xBE, 0xE0, 0x24,
			0x29, 0x11, 0xBF, 0xB9, 0x67, 0x23, 0x5C, 0xEB,
			0xA3, 0x5A, 0xE1, 0x3E, 0x4E, 0xC7, 0x52, 0xBE,
			0x63, 0x0B, 0x92, 0xDC, 0x4B, 0xDE, 0x28, 0x47,
			0xA9, 0xC6, 0x2C, 0xB8, 0x15, 0x27, 0x45, 0x42,
			0x1F, 0xB7, 0xEB, 0x60, 0xA6, 0x3C, 0x0F, 0xE9,
			0x15, 0x9F, 0xCC, 0xE7, 0x26, 0xCE, 0x7C, 0xD8,
			0x52, 0x3D, 0x74, 0x50, 0x66, 0x7E, 0xF8, 0x40,
			0xE4, 0x91, 0x91, 0x21, 0xEB, 0x5F, 0x01, 0xC8,
			0xC9, 0xB0, 0xD3, 0xD6, 0x48, 0xA9, 0x3B, 0xFB,
			0x75, 0x68, 0x9E, 0x82, 0x44, 0xAC, 0x13, 0x4A,
			0xF5, 0x44, 0x71, 0x1C, 0xE7, 0x9A, 0x02, 0xDC,
			0xC3, 0x42, 0x26, 0x68, 0x47, 0x80, 0xDD, 0xDC,
			0xB4, 0x98, 0x59, 0x41, 0x06, 0xC3, 0x7F, 0x5B,
			0xC7, 0x98, 0x56, 0x48, 0x7A, 0xF5, 0xAB, 0x02,
			0x2A, 0x2E, 0x5E, 0x42, 0xF0, 0x98, 0x97, 0xC1,
			0xA8, 0x5A, 0x11, 0xEA, 0x02, 0x12, 0xAF, 0x04,
			0xD9, 0xB4, 0xCE, 0xBC, 0x93, 0x7C, 0x3C, 0x1A,
			0x3E, 0x15, 0xA8, 0xA0, 0x34, 0x2E, 0x33, 0x76,
			0x15, 0xC8, 0x4E, 0x7F, 0xE3, 0xB8, 0xB9, 0xB8,
			0x7F, 0xB1, 0xE7, 0x3A, 0x15, 0xAF, 0x12, 0xA3,
			0x0D, 0x74, 0x6E, 0x06, 0xDF, 0xC3, 0x4F, 0x29,
			0x0D, 0x79, 0x7C, 0xE5, 0x1A, 0xA1, 0x3A, 0xA7,
			0x85, 0xBF, 0x66, 0x58, 0xAF, 0xF5, 0xE4, 0xB0,
			0x93, 0x00, 0x3C, 0xBE, 0xAF, 0x66, 0x5B, 0x3C,
			0x2E, 0x11, 0x3A, 0x3A, 0x4E, 0x90, 0x52, 0x69,
			0x34, 0x1D, 0xC0, 0x71, 0x14, 0x26, 0x68, 0x5F,
			0x4E, 0xF3, 0x7E, 0x86, 0x8A, 0x81, 0x26, 0xFF,
			0x3F, 0x22, 0x79, 0xB5, 0x7C, 0xA6, 0x7E, 0x29,
		},
		.len = 256
	},
	.modulus = {
		.data = {
			0x87, 0xA8, 0xE6, 0x1D, 0xB4, 0xB6, 0x66, 0x3C,
			0xFF, 0xBB, 0xD1, 0x9C, 0x65, 0x19, 0x59, 0x99,
			0x8C, 0xEE, 0xF6, 0x08, 0x66, 0x0D, 0xD0, 0xF2,
			0x5D, 0x2C, 0xEE, 0xD4, 0x43, 0x5E, 0x3B, 0x00,
			0xE0, 0x0D, 0xF8, 0xF1, 0xD6, 0x19, 0x57, 0xD4,
			0xFA, 0xF7, 0xDF, 0x45, 0x61, 0xB2, 0xAA, 0x30,
			0x16, 0xC3, 0xD9, 0x11, 0x34, 0x09, 0x6F, 0xAA,
			0x3B, 0xF4, 0x29, 0x6D, 0x83, 0x0E, 0x9A, 0x7C,
			0x20, 0x9E, 0x0C, 0x64, 0x97, 0x51, 0x7A, 0xBD,
			0x5A, 0x8A, 0x9D, 0x30, 0x6B, 0xCF, 0x67, 0xED,
			0x91, 0xF9, 0xE6, 0x72, 0x5B, 0x47, 0x58, 0xC0,
			0x22, 0xE0, 0xB1, 0xEF, 0x42, 0x75, 0xBF, 0x7B,
			0x6C, 0x5B, 0xFC, 0x11, 0xD4, 0x5F, 0x90, 0x88,
			0xB9, 0x41, 0xF5, 0x4E, 0xB1, 0xE5, 0x9B, 0xB8,
			0xBC, 0x39, 0xA0, 0xBF, 0x12, 0x30, 0x7F, 0x5C,
			0x4F, 0xDB, 0x70, 0xC5, 0x81, 0xB2, 0x3F, 0x76,
			0xB6, 0x3A, 0xCA, 0xE1, 0xCA, 0xA6, 0xB7, 0x90,
			0x2D, 0x52, 0x52, 0x67, 0x35, 0x48, 0x8A, 0x0E,
			0xF1, 0x3C, 0x6D, 0x9A, 0x51, 0xBF, 0xA4, 0xAB,
			0x3A, 0xD8, 0x34, 0x77, 0x96, 0x52, 0x4D, 0x8E,
			0xF6, 0xA1, 0x67, 0xB5, 0xA4, 0x18, 0x25, 0xD9,
			0x67, 0xE1, 0x44, 0xE5, 0x14, 0x05, 0x64, 0x25,
			0x1C, 0xCA, 0xCB, 0x83, 0xE6, 0xB4, 0x86, 0xF6,
			0xB3, 0xCA, 0x3F, 0x79, 0x71, 0x50, 0x60, 0x26,
			0xC0, 0xB8, 0x57, 0xF6, 0x89, 0x96, 0x28, 0x56,
			0xDE, 0xD4, 0x01, 0x0A, 0xBD, 0x0B, 0xE6, 0x21,
			0xC3, 0xA3, 0x96, 0x0A, 0x54, 0xE7, 0x10, 0xC3,
			0x75, 0xF2, 0x63, 0x75, 0xD7, 0x01, 0x41, 0x03,
			0xA4, 0xB5, 0x43, 0x30, 0xC1, 0x98, 0xAF, 0x12,
			0x61, 0x16, 0xD2, 0x27, 0x6E, 0x11, 0x71, 0x5F,
			0x69, 0x38, 0x77, 0xFA, 0xD7, 0xEF, 0x09, 0xCA,
			0xDB, 0x09, 0x4A, 0xE9, 0x1E, 0x1A, 0x15, 0x97,
		},
		.len = 256
	},
},
{
	/*
	 * Referenced from RFC 5114 for checking A^q mod p = 1.
	 * base is yA from Appendix A.3.
	 * exponent and modulus data are from Section 2.3.
	 */
	.description = "Modular Exponentiation tests for Group 24 subgroup",
	.xform_type = RTE_CRYPTO_ASYM_XFORM_MODEX,
	.base = {
		.data = {
			0x2E, 0x93, 0x80, 0xC8, 0x32, 0x3A, 0xF9, 0x75,
			0x45, 0xBC, 0x49, 0x41, 0xDE, 0xB0, 0xEC, 0x37,
			0x42, 0xC6, 0x2F, 0xE0, 0xEC, 0xE8, 0x24, 0xA6,
			0xAB, 0xDB, 0xE6, 0x6C, 0x59, 0xBE, 0xE0, 0x24,
			0x29, 0x11, 0xBF, 0xB9, 0x67, 0x23, 0x5C, 0xEB,
			0xA3, 0x5A, 0xE1, 0x3E, 0x4E, 0xC7, 0x52, 0xBE,
			0x63, 0x0B, 0x92, 0xDC, 0x4B, 0xDE, 0x28, 0x47,
			0xA9, 0xC6, 0x2C, 0xB8, 0x15, 0x27, 0x45, 0x42,
			0x1F, 0xB7, 0xEB, 0x60, 0xA6, 0x3C, 0x0F, 0xE9,
			0x15, 0x9F, 0xCC, 0xE7, 0x26, 0xCE, 0x7C, 0xD8,
			0x52, 0x3D, 0x74, 0x50, 0x66, 0x7E, 0xF8, 0x40,
			0xE4, 0x91, 0x91, 0x21, 0xEB, 0x5F, 0x01, 0xC8,
			0xC9, 0xB0, 0xD3, 0xD6, 0x48, 0xA9, 0x3B, 0xFB,
			0x75, 0x68, 0x9E, 0x82, 0x44, 0xAC, 0x13, 0x4A,
			0xF5, 0x44, 0x71, 0x1C, 0xE7, 0x9A, 0x02, 0xDC,
			0xC3, 0x42, 0x26, 0x68, 0x47, 0x80, 0xDD, 0xDC,
			0xB4, 0x98, 0x59, 0x41, 0x06, 0xC3, 0x7F, 0x5B,
			0xC7, 0x98, 0x56, 0x48, 0x7A, 0xF5, 0xAB, 0x02,
			0x2A, 0x2E, 0x5E, 0x42, 0xF0, 0x98, 0x97, 0xC1,
			0xA8, 0x5A, 0x11, 0xEA, 0x02, 0x12, 0xAF, 0x04,
			0xD9, 0xB4, 0xCE, 0xBC, 0x93, 0x7C, 0x3C, 0x1A,
			0x3E, 0x15, 0xA8, 0xA0, 0x34, 0x2E, 0x33, 0x76,
			0x15, 0xC8, 0x4E, 0x7F, 0xE3, 0xB8, 0xB9, 0xB8,
			0x7F, 0xB1, 0xE7, 0x3A, 0x15, 0xAF, 0x12, 0xA3,
			0x0D, 0x74, 0x6E, 0x06, 0xDF, 0xC3, 0x4F, 0x29,
			0x0D, 0x79, 0x7C, 0xE5, 0x1A, 0xA1, 0x3A, 0xA7,
			0x85, 0xBF, 0x66, 0x58, 0xAF, 0xF5, 0xE4, 0xB0,
			0x93, 0x00, 0x3C, 0xBE, 0xAF, 0x66, 0x5B, 0x3C,
			0x2E, 0x11, 0x3A, 0x3A, 0x4E, 0x90, 0x52, 0x69,
			0x34, 0x1D, 0xC0, 0x71, 0x14, 0x26, 0x68, 0x5F,
			0x4E, 0xF3, 0x7E, 0x86, 0x8A, 0x81, 0x26, 0xFF,
			0x3F, 0x22, 0x79, 0xB5, 0x7C, 0xA6, 0x7E, 0x29,
		},
		.len = 256
	},
	.exponent = {
		.data = {
			0x8C, 0xF8, 0x36, 0x42, 0xA7, 0x09, 0xA0, 0x97,
			0xB4, 0x47, 0x99, 0x76, 0x40, 0x12, 0x9D, 0xA2,
			0x99, 0xB1, 0xA4, 0x7D, 0x1E, 0xB3, 0x75, 0x0B,
			0xA3, 0x08, 0xB0, 0xFE, 0x64, 0xF5, 0xFB, 0xD3,
		},
		.len = 32
	},
	.reminder = {
		.data = {
			0x1
		},
		.len = 1
	},
	.modulus = {
		.data = {
			0x87, 0xA8, 0xE6, 0x1D, 0xB4, 0xB6, 0x66, 0x3C,
			0xFF, 0xBB, 0xD1, 0x9C, 0x65, 0x19, 0x59, 0x99,
			0x8C, 0xEE, 0xF6, 0x08, 0x66, 0x0D, 0xD0, 0xF2,
			0x5D, 0x2C, 0xEE, 0xD4, 0x43, 0x5E, 0x3B, 0x00,
			0xE0, 0x0D, 0xF8, 0xF1, 0xD6, 0x19, 0x57, 0xD4,
			0xFA, 0xF7, 0xDF, 0x45, 0x61, 0xB2, 0xAA, 0x30,
			0x16, 0xC3, 0xD9, 0x11, 0x34, 0x09, 0x6F, 0xAA,
			0x3B, 0xF4, 0x29, 0x6D, 0x83, 0x0E, 0x9A, 0x7C,
			0x20, 0x9E, 0x0C, 0x64, 0x97, 0x51, 0x7A, 0xBD,
			0x5A, 0x8A, 0x9D, 0x30, 0x6B, 0xCF, 0x67, 0xED,
			0x91, 0xF9, 0xE6, 0x72, 0x5B, 0x47, 0x58, 0xC0,
			0x22, 0xE0, 0xB1, 0xEF, 0x42, 0x75, 0xBF, 0x7B,
			0x6C, 0x5B, 0xFC, 0x11, 0xD4, 0x5F, 0x90, 0x88,
			0xB9, 0x41, 0xF5, 0x4E, 0xB1, 0xE5, 0x9B, 0xB8,
			0xBC, 0x39, 0xA0, 0xBF, 0x12, 0x30, 0x7F, 0x5C,
			0x4F, 0xDB, 0x70, 0xC5, 0x81, 0xB2, 0x3F, 0x76,
			0xB6, 0x3A, 0xCA, 0xE1, 0xCA, 0xA6, 0xB7, 0x90,
			0x2D, 0x52, 0x52, 0x67, 0x35, 0x48, 0x8A, 0x0E,
			0xF1, 0x3C, 0x6D, 0x9A, 0x51, 0xBF, 0xA4, 0xAB,
			0x3A, 0xD8, 0x34, 0x77, 0x96, 0x52, 0x4D, 0x8E,
			0xF6, 0xA1, 0x67, 0xB5, 0xA4, 0x18, 0x25, 0xD9,
			0x67, 0xE1, 0x44, 0xE5, 0x14, 0x05, 0x64, 0x25,
			0x1C, 0xCA, 0xCB, 0x83, 0xE6, 0xB4, 0x86, 0xF6,
			0xB3, 0xCA, 0x3F, 0x79, 0x71, 0x50, 0x60, 0x26,
			0xC0, 0xB8, 0x57, 0xF6, 0x89, 0x96, 0x28, 0x56,
			0xDE, 0xD4, 0x01, 0x0A, 0xBD, 0x0B, 0xE6, 0x21,
			0xC3, 0xA3, 0x96, 0x0A, 0x54, 0xE7, 0x10, 0xC3,
			0x75, 0xF2, 0x63, 0x75, 0xD7, 0x01, 0x41, 0x03,
			0xA4, 0xB5, 0x43, 0x30, 0xC1, 0x98, 0xAF, 0x12,
			0x61, 0x16, 0xD2, 0x27, 0x6E, 0x11, 0x71, 0x5F,
			0x69, 0x38, 0x77, 0xFA, 0xD7, 0xEF, 0x09, 0xCA,
			0xDB, 0x09, 0x4A, 0xE9, 0x1E, 0x1A, 0x15, 0x97,
		},
		.len = 256
	},
},
};

#endif /* TEST_CRYPTODEV_MOD_TEST_VECTORS_H__ */
