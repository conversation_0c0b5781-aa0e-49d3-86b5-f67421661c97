/* SPDX-License-Identifier: BSD-3-Clause
 *
 * Copyright (C) 2015-2016 Freescale Semiconductor,Inc.
 * Copyright 2018-2022 NXP
 */

#ifndef SECURITY_PDCP_TEST_VECTOR_H_
#define SECURITY_PDCP_TEST_VECTOR_H_

#include <rte_security.h>

/*
 * PDCP test vectors and related structures.
 */
enum pdcp_dir {
	PDCP_DIR_UPLINK = 0,
	PDCP_DIR_DOWNLINK = 1,
	PDCP_DIR_INVALID
};

struct pdcp_test_param {
	uint8_t type;
	enum rte_security_pdcp_domain domain;
	enum rte_crypto_cipher_algorithm cipher_alg;
	uint8_t cipher_key_len;
	enum rte_crypto_auth_algorithm auth_alg;
	uint8_t auth_key_len;
	const char *name;
};

struct pdcp_short_mac_test {
	uint32_t test_idx;
	struct pdcp_short_mac_test_param {
		enum rte_security_pdcp_domain domain;
		enum rte_crypto_auth_algorithm auth_alg;
		uint8_t auth_key_len;
		const char *name;
	} param;
	const uint8_t *auth_key;
	const uint8_t *data_in;
	uint32_t in_len;
	const uint8_t *data_out;
};

static const struct pdcp_short_mac_test list_pdcp_smac_tests[] = {
	{
		.test_idx = 1,
		.param = {.name = "PDCP-SMAC SNOW3G UIA2",
			.auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
			.domain = RTE_SECURITY_PDCP_MODE_SHORT_MAC,
			.auth_key_len = 16,
		},
		.auth_key = (uint8_t[]){ 0x2b, 0xd6, 0x45, 0x9f, 0x82, 0xc5,
					 0xb3, 0x00, 0x95, 0x2c, 0x49, 0x10,
					 0x48, 0x81, 0xff, 0x48 },
		.data_in = (uint8_t[]){ 0x33, 0x32, 0x34, 0x62, 0x63, 0x39,
					0x38 },
		.in_len = 7,
		.data_out = (uint8_t[]){ 0x33, 0x32, 0x34, 0x62, 0x63, 0x39,
					 0x38, 0x56, 0xd2, 0x09, 0xae },
	},

	{
		.test_idx = 2,
		.param = {.name = "PDCP-SMAC AES CMAC 1",
			.auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
			.domain = RTE_SECURITY_PDCP_MODE_SHORT_MAC,
			.auth_key_len = 16,
		},
		.auth_key = (uint8_t[]){ 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
					 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
					 0x00, 0x00, 0x00, 0x00 },
		.data_in = (uint8_t[]){ 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
					0x00 },
		.in_len = 7,
		.data_out = (uint8_t[]){ 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
					 0x00, 0x13, 0xf8, 0x4b, 0xea },
	},

	{
		.test_idx = 3,
		.param = {.name = "PDCP-SMAC AES CMAC 2",
			.auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
			.domain = RTE_SECURITY_PDCP_MODE_SHORT_MAC,
			.auth_key_len = 16,
		},
		.auth_key = (uint8_t[]){ 0x16, 0xc1, 0x98, 0x14,  0x9a, 0x2c,
					 0xf4, 0x12, 0x4f, 0xd4, 0x14, 0xec,
					 0x72, 0x43, 0x29, 0x04 },
		.data_in = (uint8_t[]){ 0x00, 0xc0, 0x00, 0x00, 0x00, 0x05,
					0x09, 0xe4 },
		.in_len = 8,
		.data_out = (uint8_t[]){ 0x00, 0xc0, 0x00, 0x00, 0x00, 0x05,
					 0x09, 0xe4, 0xdd, 0xff, 0xde, 0xa9 },
	},

	{
		.test_idx = 4,
		.param = {.name = "PDCP-SMAC AES CMAC 3",
			.auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
			.domain = RTE_SECURITY_PDCP_MODE_SHORT_MAC,
			.auth_key_len = 16,
		},
		.auth_key = (uint8_t[]){ 0xD3, 0xC5, 0xD5, 0x92, 0x32, 0x7F,
					 0xB1, 0x1C, 0x40, 0x35, 0xC6, 0x68,
					 0x0A, 0xF8, 0xC6, 0xD3 },
		.data_in = (uint8_t[]){ 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
					0x00 },
		.in_len = 7,
		.data_out = (uint8_t[]){ 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
					 0x00, 0x23, 0xea, 0x95, 0xb0 },
	},

	{
		.test_idx = 5,
		.param = {.name = "PDCP-SMAC NULL",
			.auth_alg = RTE_CRYPTO_AUTH_NULL,
			.domain = RTE_SECURITY_PDCP_MODE_SHORT_MAC,
			.auth_key_len = 16,
		},
		.auth_key = (uint8_t[]){ 0x2B, 0xD6, 0x45, 0x9F, 0x82, 0xC5,
					 0xB3, 0x00, 0x95, 0x2C, 0x49, 0x10,
					 0x48, 0x81, 0xFF, 0x48
		},
		.data_in = (uint8_t[]){ 0x33, 0x32, 0x34, 0x62, 0x63, 0x39,
					0x38 },
		.in_len = 7,
		.data_out = (uint8_t[]){ 0x33, 0x32, 0x34, 0x62, 0x63, 0x39,
					 0x38, 0x00, 0x00, 0x00, 0x00 },
	},
	{
		.test_idx = 6,
		.param = {.name = "PDCP-SMAC ZUC",
			.auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
			.domain = RTE_SECURITY_PDCP_MODE_SHORT_MAC,
			.auth_key_len = 16,
		},
		.auth_key = (uint8_t[]){ 0xB2, 0xA4, 0x73, 0xB6, 0x78, 0x5C,
					0x51, 0x8E, 0x9C, 0x1E, 0x9B, 0xC6,
					0x66, 0xE4, 0x84, 0x24
		},
		.data_in = (uint8_t[]){ 0x00, 0x40, 0x00, 0x00, 0x00, 0x05,
					0x09, 0xe4 },
		.in_len = 8,
		.data_out = (uint8_t[]){ 0x00, 0x40, 0x00, 0x00, 0x00, 0x05,
					0x09, 0xe4, 0xCC, 0x7D, 0xD0, 0xE4 },
	},
};

static struct pdcp_test_param pdcp_test_params[] = {
	{
	 .name =
	 "PDCP Control Plane with NULL encryption and NULL integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 0,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP Control Plane with NULL encryption and NULL integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 0,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP Control Plane with NULL encryption and SNOW f9 integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with NULL encryption and SNOW f9 integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with NULL encryption and AES CMAC integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with NULL encryption and AES CMAC integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with NULL encryption and ZUC integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with NULL encryption and ZUC integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with SNOW f8 encryption and NULL integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP Control Plane with SNOW f8 encryption and NULL integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP Control Plane with SNOW f8 encryption and SNOW f9 integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with SNOW f8 encryption and SNOW f9 integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with SNOW f8 encryption and AES CMAC integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with SNOW f8 encryption and AES CMAC integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with SNOW f8 encryption and ZUC integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with SNOW f8 encryption and ZUC integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with AES CTR encryption and NULL integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP Control Plane with AES CTR encryption and NULL integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP Control Plane with AES CTR encryption and SNOW f9 integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with AES CTR encryption and SNOW f9 integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with AES CTR encryption and AES CMAC integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with AES CTR encryption and AES CMAC integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with AES CTR encryption and ZUC integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with AES CTR encryption and ZUC integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with ZUC encryption and NULL integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP Control Plane with ZUC encryption and NULL integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP Control Plane with ZUC encryption and SNOW f9 integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with ZUC encryption and SNOW f9 integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with ZUC encryption and AES CMAC integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with ZUC encryption and AES CMAC integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with ZUC encryption and ZUC integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with ZUC encryption and ZUC integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },

	 /* For 12-bit C-plane */
	{
	 .name =
	 "PDCP Control Plane with NULL encryption and NULL integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 0,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP Control Plane with NULL encryption and NULL integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 0,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP Control Plane with NULL encryption and SNOW f9 integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with NULL encryption and SNOW f9 integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with NULL encryption and AES CMAC integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with NULL encryption and AES CMAC integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with NULL encryption and ZUC integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with NULL encryption and ZUC integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with SNOW f8 encryption and NULL integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP Control Plane with SNOW f8 encryption and NULL integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP Control Plane with SNOW f8 encryption and SNOW f9 integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with SNOW f8 encryption and SNOW f9 integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with SNOW f8 encryption and AES CMAC integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with SNOW f8 encryption and AES CMAC integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with SNOW f8 encryption and ZUC integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with SNOW f8 encryption and ZUC integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with AES CTR encryption and NULL integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP Control Plane with AES CTR encryption and NULL integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP Control Plane with AES CTR encryption and SNOW f9 integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with AES CTR encryption and SNOW f9 integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with AES CTR encryption and AES CMAC integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with AES CTR encryption and AES CMAC integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with AES CTR encryption and ZUC integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with AES CTR encryption and ZUC integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with ZUC encryption and NULL integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP Control Plane with ZUC encryption and NULL integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP Control Plane with ZUC encryption and SNOW f9 integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with ZUC encryption and SNOW f9 integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with ZUC encryption and AES CMAC integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with ZUC encryption and AES CMAC integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with ZUC encryption and ZUC integrity Uplink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_CONTROL,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP Control Plane with ZUC encryption and ZUC integrity Downlink",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption Uplink with long sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption Downlink with long sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption Uplink with short sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption Downlink with short sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption Uplink with 15 bit sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption Downlink with 15 bit sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption Uplink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption Downlink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption Uplink with long sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption Downlink with long sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption Uplink with short sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption Downlink with short sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption Uplink with 15 bit sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption Downlink with 15 bit sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption Uplink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption Downlink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption Uplink with long sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption Downlink with long sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption Uplink with short sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption Downlink with short sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption Uplink with 15 bit sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption Downlink with 15 bit sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption Uplink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption Downlink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption Uplink with long sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption Downlink with long sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption Uplink with short sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption Downlink with short sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption Uplink with 15 bit sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption Downlink with 15 bit sequence number",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption Uplink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption Downlink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = 0,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },

	 /********* 12-bit uplane with integrity **************************/
	{
	 .name =
	 "PDCP User Plane with NULL encryption and NULL integrity Uplink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption and NULL integrity Downlink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption and SNOW f9 integrity Uplink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption and SNOW f9 integrity Downlink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption and AES CMAC integrity Uplink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption and AES CMAC integrity Downlink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption and ZUC integrity Uplink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption and ZUC integrity Downlink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption and NULL integrity Uplink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption and NULL integrity Downlink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption and SNOW f9 integrity Uplink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption and SNOW f9 integrity Downlink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption and AES CMAC integrity Uplink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption and AES CMAC integrity Downlink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption and ZUC integrity Uplink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption and ZUC integrity Downlink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption and NULL integrity Uplink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption and NULL integrity Downlink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption and SNOW f9 integrity Uplink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption and SNOW f9 integrity Downlink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption and AES CMAC integrity Uplink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption and AES CMAC integrity Downlink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption and ZUC integrity Uplink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption and ZUC integrity Downlink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption and NULL integrity Uplink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption and NULL integrity Downlink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption and SNOW f9 integrity Uplink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption and SNOW f9 integrity Downlink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption and AES CMAC integrity Uplink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption and AES CMAC integrity Downlink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption and ZUC integrity Uplink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption and ZUC integrity Downlink with 12 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	 /********* 18-bit uplane with integrity **************************/
	{
	 .name =
	 "PDCP User Plane with NULL encryption and NULL integrity Uplink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption and NULL integrity Downlink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption and SNOW f9 integrity Uplink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption and SNOW f9 integrity Downlink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption and AES CMAC integrity Uplink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption and AES CMAC integrity Downlink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption and ZUC integrity Uplink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with NULL encryption and ZUC integrity Downlink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_NULL,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 0,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption and NULL integrity Uplink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption and NULL integrity Downlink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption and SNOW f9 integrity Uplink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption and SNOW f9 integrity Downlink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption and AES CMAC integrity Uplink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption and AES CMAC integrity Downlink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption and ZUC integrity Uplink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with SNOW f8 encryption and ZUC integrity Downlink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption and NULL integrity Uplink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption and NULL integrity Downlink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption and SNOW f9 integrity Uplink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption and SNOW f9 integrity Downlink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption and AES CMAC integrity Uplink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption and AES CMAC integrity Downlink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption and ZUC integrity Uplink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with AES CTR encryption and ZUC integrity Downlink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption and NULL integrity Uplink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption and NULL integrity Downlink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_NULL,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 0,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption and SNOW f9 integrity Uplink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption and SNOW f9 integrity Downlink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption and AES CMAC integrity Uplink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption and AES CMAC integrity Downlink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption and ZUC integrity Uplink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
	{
	 .name =
	 "PDCP User Plane with ZUC encryption and ZUC integrity Downlink with 18 bit SN",
	 .cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
	 .auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
	 .domain = RTE_SECURITY_PDCP_MODE_DATA,
	 .cipher_key_len = 16,
	 .auth_key_len = 16,
	 },
};

static uint32_t pdcp_test_hfn[] = {
	/* Control Plane w/NULL enc. + NULL int. UL */
	0x000fa557,
	/* Control Plane w/NULL enc. + NULL int. DL */
	0x000fa557,
	/* Control Plane w/NULL enc. + SNOW f9 int. UL */
	0x000fa557,
	/* Control Plane w/NULL enc. + SNOW f9 int. DL */
	0x000fa557,
	/* Control Plane w/NULL enc. + AES CMAC int. UL */
	0x000fa557,
	/* Control Plane w/NULL enc. + AES CMAC int. DL */
	0x000fa557,
	/* Control Plane w/NULL enc. + ZUC int. UL */
	0x000fa557,
	/* Control Plane w/NULL enc. + ZUC int. DL */
	0x000fa557,
	/* Control Plane w/SNOW f8 enc. + NULL int. UL */
	0x000fa557,
	/* Control Plane w/SNOW f8 enc. + NULL int. DL */
	0x000fa557,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. UL */
	0x000fa557,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. DL */
	0x000fa557,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. UL */
	0x000fa557,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. DL */
	0x000fa557,
	/* Control Plane w/SNOW f8 enc. + ZUC int. UL */
	0x000fa557,
	/* Control Plane w/SNOW f8 enc. + ZUC int. DL */
	0x000fa557,
	/* Control Plane w/AES CTR enc. + NULL int. UL */
	0x000fa557,
	/* Control Plane w/AES CTR enc. + NULL int. DL */
	0x000fa557,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. UL */
	0x000fa557,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. DL */
	0x000fa557,
	/* Control Plane w/AES CTR enc. + AES CMAC int. UL */
	0x000fa557,
	/* Control Plane w/AES CTR enc. + AES CMAC int. DL */
	0x000fa557,
	/* Control Plane w/AES CTR enc. + ZUC int. UL */
	0x000fa557,
	/* Control Plane w/AES CTR enc. + ZUC int. DL */
	0x000fa557,
	/* Control Plane w/ZUC enc. + NULL int. UL */
	0x000fa557,
	/* Control Plane w/ZUC enc. + NULL int. DL */
	0x000fa557,
	/* Control Plane w/ZUC enc. + SNOW f9 int. UL */
	0x000fa557,
	/* Control Plane w/ZUC enc. + SNOW f9 int. DL */
	0x000fa557,
	/* Control Plane w/ZUC enc. + AES CMAC int. UL */
	0x000fa557,
	/* Control Plane w/ZUC enc. + AES CMAC int. DL */
	0x000fa557,
	/* Control Plane w/ZUC enc. + ZUC int. UL */
	0x000fa557,
	/* Control Plane w/ZUC enc. + ZUC int. DL */
	0x000fa557,

	/* For 12-bit C-plane ***********************/
	/* Control Plane w/NULL enc. + NULL int. UL */
	0x01,
	/* Control Plane w/NULL enc. + NULL int. DL */
	0x01,
	/* Control Plane w/NULL enc. + SNOW f9 int. UL */
	0x01,
	/* Control Plane w/NULL enc. + SNOW f9 int. DL */
	0x01,
	/* Control Plane w/NULL enc. + AES CMAC int. UL */
	0x01,
	/* Control Plane w/NULL enc. + AES CMAC int. DL */
	0x01,
	/* Control Plane w/NULL enc. + ZUC int. UL */
	0x01,
	/* Control Plane w/NULL enc. + ZUC int. DL */
	0x01,
	/* Control Plane w/SNOW f8 enc. + NULL int. UL */
	0x01,
	/* Control Plane w/SNOW f8 enc. + NULL int. DL */
	0x01,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. UL */
	0x01,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. DL */
	0x01,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. UL */
	0x01,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. DL */
	0x01,
	/* Control Plane w/SNOW f8 enc. + ZUC int. UL */
	0x01,
	/* Control Plane w/SNOW f8 enc. + ZUC int. DL */
	0x01,
	/* Control Plane w/AES CTR enc. + NULL int. UL */
	0x01,
	/* Control Plane w/AES CTR enc. + NULL int. DL */
	0x01,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. UL */
	0x01,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. DL */
	0x01,
	/* Control Plane w/AES CTR enc. + AES CMAC int. UL */
	0x01,
	/* Control Plane w/AES CTR enc. + AES CMAC int. DL */
	0x01,
	/* Control Plane w/AES CTR enc. + ZUC int. UL */
	0x01,
	/* Control Plane w/AES CTR enc. + ZUC int. DL */
	0x01,
	/* Control Plane w/ZUC enc. + NULL int. UL */
	0x01,
	/* Control Plane w/ZUC enc. + NULL int. DL */
	0x01,
	/* Control Plane w/ZUC enc. + SNOW f9 int. UL */
	0x01,
	/* Control Plane w/ZUC enc. + SNOW f9 int. DL */
	0x01,
	/* Control Plane w/ZUC enc. + AES CMAC int. UL */
	0x01,
	/* Control Plane w/ZUC enc. + AES CMAC int. DL */
	0x01,
	/* Control Plane w/ZUC enc. + ZUC int. UL */
	0x01,
	/* Control Plane w/ZUC enc. + ZUC int. DL */
	0x01,

	/* 12-bit C-plane ends ***********************/

	/* User Plane w/NULL enc. UL LONG SN */
	0x000fa557,
	/* User Plane w/NULL enc. DL LONG SN */
	0x000fa557,
	/* User Plane w/NULL enc. UL SHORT SN */
	0x000fa557,
	/* User Plane w/NULL enc. DL SHORT SN */
	0x000fa557,
	/* User Plane w/NULL enc. UL 15 BIT SN */
	0x000fa557,
	/* User Plane w/NULL enc. DL 15 BIT SN */
	0x000fa557,
	/* User Plane w/NULL enc. UL 18-bit SN */
	0x01,
	/* User Plane w/NULL enc. DL 18-bit SN */
	0x01,
	/* User Plane w/SNOW f8 enc. UL LONG SN */
	0x000fa557,
	/* User Plane w/SNOW f8 enc. DL LONG SN */
	0x000fa557,
	/* User Plane w/SNOW f8 enc. UL SHORT SN */
	0x000fa557,
	/* User Plane w/SNOW f8 enc. DL SHORT SN */
	0x000fa557,
	/* User Plane w/SNOW f8 enc. UL 15 BIT SN */
	0x000fa557,
	/* User Plane w/SNOW f8 enc. DL 15 BIT SN */
	0x000fa557,
	/* User Plane w/SNOW f8 enc. UL 18-bit SN */
	0x01,
	/* User Plane w/SNOW f8 enc. DL 18-bit SN */
	0x01,
	/* User Plane w/AES CTR enc. UL LONG SN */
	0x000fa557,
	/* User Plane w/AES CTR enc. DL LONG SN */
	0x000fa557,
	/* User Plane w/AES CTR enc. UL SHORT SN */
	0x000fa557,
	/* User Plane w/AES CTR enc. DL SHORT SN */
	0x000fa557,
	/* User Plane w/AES CTR enc. UL 15 BIT SN */
	0x000fa557,
	/* User Plane w/AES CTR enc. DL 15 BIT SN */
	0x000fa557,
	/* User Plane w/AES CTR enc. UL 18-bit SN */
	0x01,
	/* User Plane w/AES CTR enc. DL 18-bit SN */
	0x01,
	/* User Plane w/ZUC enc. UL LONG SN */
	0x000fa557,
	/* User Plane w/ZUC enc. DL LONG SN */
	0x000fa557,
	/* User Plane w/ZUC enc. UL SHORT SN */
	0x000fa557,
	/* User Plane w/ZUC enc. DL SHORT SN */
	0x000fa557,
	/* User Plane w/ZUC enc. UL 15 BIT SN */
	0x000fa557,
	/* User Plane w/ZUC enc. DL 15 BIT SN */
	0x000fa557,
	/* User Plane w/ZUC enc. UL 18-bit SN */
	0x01,
	/* User Plane w/ZUC enc. DL 18-bit SN */
	0x01,

	 /********* 12-bit uplane with integrity **************************/
	/* User Plane w/NULL enc. + NULL int. UL for 12-bit SN*/
	0x01,
	/* User Plane w/NULL enc. + NULL int. DL for 12-bit SN*/
	0x01,
	/* User Plane w/NULL enc. + SNOW f9 int. UL for 12-bit SN*/
	0x01,
	/* User Plane w/NULL enc. + SNOW f9 int. DL for 12-bit SN*/
	0x01,
	/* User Plane w/NULL enc. + AES CMAC int. UL for 12-bit SN*/
	0x01,
	/* User Plane w/NULL enc. + AES CMAC int. DL for 12-bit SN*/
	0x01,
	/* User Plane w/NULL enc. + ZUC int. UL for 12-bit SN*/
	0x01,
	/* User Plane w/NULL enc. + ZUC int. DL for 12-bit SN*/
	0x01,
	/* User Plane w/SNOW f8 enc. + NULL int. UL for 12-bit SN*/
	0x01,
	/* User Plane w/SNOW f8 enc. + NULL int. DL for 12-bit SN*/
	0x01,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. UL for 12-bit SN*/
	0x01,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. DL for 12-bit SN*/
	0x01,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. UL for 12-bit SN*/
	0x01,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. DL for 12-bit SN*/
	0x01,
	/* User Plane w/SNOW f8 enc. + ZUC int. UL for 12-bit SN*/
	0x01,
	/* User Plane w/SNOW f8 enc. + ZUC int. DL for 12-bit SN*/
	0x01,
	/* User Plane w/AES CTR enc. + NULL int. UL for 12-bit SN*/
	0x01,
	/* User Plane w/AES CTR enc. + NULL int. DL for 12-bit SN*/
	0x01,
	/* User Plane w/AES CTR enc. + SNOW f9 int. UL for 12-bit SN*/
	0x01,
	/* User Plane w/AES CTR enc. + SNOW f9 int. DL for 12-bit SN*/
	0x01,
	/* User Plane w/AES CTR enc. + AES CMAC int. UL for 12-bit SN*/
	0x01,
	/* User Plane w/AES CTR enc. + AES CMAC int. DL for 12-bit SN*/
	0x01,
	/* User Plane w/AES CTR enc. + ZUC int. UL for 12-bit SN*/
	0x01,
	/* User Plane w/AES CTR enc. + ZUC int. DL for 12-bit SN*/
	0x01,
	/* User Plane w/ZUC enc. + NULL int. UL for 12-bit SN*/
	0x01,
	/* User Plane w/ZUC enc. + NULL int. DL for 12-bit SN*/
	0x01,
	/* User Plane w/ZUC enc. + SNOW f9 int. UL for 12-bit SN*/
	0x01,
	/* User Plane w/ZUC enc. + SNOW f9 int. DL for 12-bit SN*/
	0x01,
	/* User Plane w/ZUC enc. + AES CMAC int. UL for 12-bit SN*/
	0x01,
	/* User Plane w/ZUC enc. + AES CMAC int. DL for 12-bit SN*/
	0x01,
	/* User Plane w/ZUC enc. + ZUC int. UL for 12-bit SN*/
	0x01,
	/* User Plane w/ZUC enc. + ZUC int. DL for 12-bit SN*/
	0x01,

	 /********* 18-bit uplane with integrity **************************/
	/* User Plane w/NULL enc. + NULL int. UL for 18-bit SN*/
	0x01,
	/* User Plane w/NULL enc. + NULL int. DL for 18-bit SN*/
	0x01,
	/* User Plane w/NULL enc. + SNOW f9 int. UL for 18-bit SN*/
	0x01,
	/* User Plane w/NULL enc. + SNOW f9 int. DL for 18-bit SN*/
	0x01,
	/* User Plane w/NULL enc. + AES CMAC int. UL for 18-bit SN*/
	0x01,
	/* User Plane w/NULL enc. + AES CMAC int. DL for 18-bit SN*/
	0x01,
	/* User Plane w/NULL enc. + ZUC int. UL for 18-bit SN*/
	0x01,
	/* User Plane w/NULL enc. + ZUC int. DL for 18-bit SN*/
	0x01,
	/* User Plane w/SNOW f8 enc. + NULL int. UL for 18-bit SN*/
	0x01,
	/* User Plane w/SNOW f8 enc. + NULL int. DL for 18-bit SN*/
	0x01,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. UL for 18-bit SN*/
	0x01,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. DL for 18-bit SN*/
	0x01,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. UL for 18-bit SN*/
	0x01,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. DL for 18-bit SN*/
	0x01,
	/* User Plane w/SNOW f8 enc. + ZUC int. UL for 18-bit SN*/
	0x01,
	/* User Plane w/SNOW f8 enc. + ZUC int. DL for 18-bit SN*/
	0x01,
	/* User Plane w/AES CTR enc. + NULL int. UL for 18-bit SN*/
	0x01,
	/* User Plane w/AES CTR enc. + NULL int. DL for 18-bit SN*/
	0x01,
	/* User Plane w/AES CTR enc. + SNOW f9 int. UL for 18-bit SN*/
	0x01,
	/* User Plane w/AES CTR enc. + SNOW f9 int. DL for 18-bit SN*/
	0x01,
	/* User Plane w/AES CTR enc. + AES CMAC int. UL for 18-bit SN*/
	0x01,
	/* User Plane w/AES CTR enc. + AES CMAC int. DL for 18-bit SN*/
	0x01,
	/* User Plane w/AES CTR enc. + ZUC int. UL for 18-bit SN*/
	0x01,
	/* User Plane w/AES CTR enc. + ZUC int. DL for 18-bit SN*/
	0x01,
	/* User Plane w/ZUC enc. + NULL int. UL for 18-bit SN*/
	0x01,
	/* User Plane w/ZUC enc. + NULL int. DL for 18-bit SN*/
	0x01,
	/* User Plane w/ZUC enc. + SNOW f9 int. UL for 18-bit SN*/
	0x01,
	/* User Plane w/ZUC enc. + SNOW f9 int. DL for 18-bit SN*/
	0x01,
	/* User Plane w/ZUC enc. + AES CMAC int. UL for 18-bit SN*/
	0x01,
	/* User Plane w/ZUC enc. + AES CMAC int. DL for 18-bit SN*/
	0x01,
	/* User Plane w/ZUC enc. + ZUC int. UL for 18-bit SN*/
	0x01,
	/* User Plane w/ZUC enc. + ZUC int. DL for 18-bit SN*/
	0x01,
};

static uint32_t pdcp_test_hfn_threshold[] = {
	/* Control Plane w/NULL enc. + NULL int. UL */
	0x000fa558,
	/* Control Plane w/NULL enc. + NULL int. DL */
	0x000fa558,
	/* Control Plane w/NULL enc. + SNOW f9 int. UL */
	0x000fa558,
	/* Control Plane w/NULL enc. + SNOW f9 int. DL */
	0x000fa558,
	/* Control Plane w/NULL enc. + AES CMAC int. UL */
	0x000fa558,
	/* Control Plane w/NULL enc. + AES CMAC int. DL */
	0x000fa558,
	/* Control Plane w/NULL enc. + ZUC int. UL */
	0x000fa558,
	/* Control Plane w/NULL enc. + ZUC int. DL */
	0x000fa558,
	/* Control Plane w/SNOW f8 enc. + NULL int. UL */
	0x000fa558,
	/* Control Plane w/SNOW f8 enc. + NULL int. DL */
	0x000fa558,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. UL */
	0x000fa558,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. DL */
	0x000fa558,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. UL */
	0x000fa558,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. DL */
	0x000fa558,
	/* Control Plane w/SNOW f8 enc. + ZUC int. UL */
	0x000fa558,
	/* Control Plane w/SNOW f8 enc. + ZUC int. DL */
	0x000fa558,
	/* Control Plane w/AES CTR enc. + NULL int. UL */
	0x000fa558,
	/* Control Plane w/AES CTR enc. + NULL int. DL */
	0x000fa558,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. UL */
	0x000fa558,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. DL */
	0x000fa558,
	/* Control Plane w/AES CTR enc. + AES CMAC int. UL */
	0x000fa558,
	/* Control Plane w/AES CTR enc. + AES CMAC int. DL */
	0x000fa558,
	/* Control Plane w/AES CTR enc. + ZUC int. UL */
	0x000fa558,
	/* Control Plane w/AES CTR enc. + ZUC int. DL */
	0x000fa558,
	/* Control Plane w/ZUC enc. + NULL int. UL */
	0x000fa558,
	/* Control Plane w/ZUC enc. + NULL int. DL */
	0x000fa558,
	/* Control Plane w/ZUC enc. + SNOW f9 int. UL */
	0x000fa558,
	/* Control Plane w/ZUC enc. + SNOW f9 int. DL */
	0x000fa558,
	/* Control Plane w/ZUC enc. + AES CMAC int. UL */
	0x000fa558,
	/* Control Plane w/ZUC enc. + AES CMAC int. DL */
	0x000fa558,
	/* Control Plane w/ZUC enc. + ZUC int. UL */
	0x000fa558,
	/* Control Plane w/ZUC enc. + ZUC int. DL */
	0x000fa558,

	/*********** For 12-bit C-plane ***********************/
	/* Control Plane w/NULL enc. + NULL int. UL */
	0x70C0A,
	/* Control Plane w/NULL enc. + NULL int. DL */
	0x70C0A,
	/* Control Plane w/NULL enc. + SNOW f9 int. UL */
	0x70C0A,
	/* Control Plane w/NULL enc. + SNOW f9 int. DL */
	0x70C0A,
	/* Control Plane w/NULL enc. + AES CMAC int. UL */
	0x70C0A,
	/* Control Plane w/NULL enc. + AES CMAC int. DL */
	0x70C0A,
	/* Control Plane w/NULL enc. + ZUC int. UL */
	0x70C0A,
	/* Control Plane w/NULL enc. + ZUC int. DL */
	0x70C0A,
	/* Control Plane w/SNOW f8 enc. + NULL int. UL */
	0x70C0A,
	/* Control Plane w/SNOW f8 enc. + NULL int. DL */
	0x70C0A,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. UL */
	0x70C0A,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. DL */
	0x70C0A,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. UL */
	0x70C0A,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. DL */
	0x70C0A,
	/* Control Plane w/SNOW f8 enc. + ZUC int. UL */
	0x70C0A,
	/* Control Plane w/SNOW f8 enc. + ZUC int. DL */
	0x70C0A,
	/* Control Plane w/AES CTR enc. + NULL int. UL */
	0x70C0A,
	/* Control Plane w/AES CTR enc. + NULL int. DL */
	0x70C0A,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. UL */
	0x70C0A,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. DL */
	0x70C0A,
	/* Control Plane w/AES CTR enc. + AES CMAC int. UL */
	0x70C0A,
	/* Control Plane w/AES CTR enc. + AES CMAC int. DL */
	0x70C0A,
	/* Control Plane w/AES CTR enc. + ZUC int. UL */
	0x70C0A,
	/* Control Plane w/AES CTR enc. + ZUC int. DL */
	0x70C0A,
	/* Control Plane w/ZUC enc. + NULL int. UL */
	0x70C0A,
	/* Control Plane w/ZUC enc. + NULL int. DL */
	0x70C0A,
	/* Control Plane w/ZUC enc. + SNOW f9 int. UL */
	0x70C0A,
	/* Control Plane w/ZUC enc. + SNOW f9 int. DL */
	0x70C0A,
	/* Control Plane w/ZUC enc. + AES CMAC int. UL */
	0x70C0A,
	/* Control Plane w/ZUC enc. + AES CMAC int. DL */
	0x70C0A,
	/* Control Plane w/ZUC enc. + ZUC int. UL */
	0x70C0A,
	/* Control Plane w/ZUC enc. + ZUC int. DL */
	0x70C0A,

	/* User Plane w/NULL enc. UL LONG SN */
	0x000fa558,
	/* User Plane w/NULL enc. DL LONG SN */
	0x000fa558,
	/* User Plane w/NULL enc. UL SHORT SN */
	0x000fa558,
	/* User Plane w/NULL enc. DL SHORT SN */
	0x000fa558,
	/* User Plane w/NULL enc. UL 15 BIT SN */
	0x000fa558,
	/* User Plane w/NULL enc. DL 15 BIT SN */
	0x000fa558,
	/* User Plane w/NULL enc. UL 18-bit SN */
	0x00002195,
	/* User Plane w/NULL enc. DL 18-bit SN */
	0x00002195,
	/* User Plane w/SNOW f8 enc. UL LONG SN */
	0x000fa558,
	/* User Plane w/SNOW f8 enc. DL LONG SN */
	0x000fa558,
	/* User Plane w/SNOW f8 enc. UL SHORT SN */
	0x000fa558,
	/* User Plane w/SNOW f8 enc. DL SHORT SN */
	0x000fa558,
	/* User Plane w/SNOW f8 enc. UL 15 BIT SN */
	0x000fa558,
	/* User Plane w/SNOW f8 enc. DL 15 BIT SN */
	0x000fa558,
	/* User Plane w/SNOW f8 enc. UL 18-bit SN */
	0x00000791,
	/* User Plane w/SNOW f8 enc. DL 18-bit SN */
	0x00002195,
	/* User Plane w/AES CTR enc. UL LONG SN */
	0x000fa558,
	/* User Plane w/AES CTR enc. DL LONG SN */
	0x000fa558,
	/* User Plane w/AES CTR enc. UL SHORT SN */
	0x000fa558,
	/* User Plane w/AES CTR enc. DL SHORT SN */
	0x000fa558,
	/* User Plane w/AES CTR enc. UL 15 BIT SN */
	0x000fa558,
	/* User Plane w/AES CTR enc. DL 15 BIT SN */
	0x000fa558,
	/* User Plane w/AES CTR enc. UL 18-bit SN */
	0x00000791,
	/* User Plane w/AES CTR enc. DL 18-bit SN */
	0x00002195,
	/* User Plane w/ZUC enc. UL LONG SN */
	0x000fa558,
	/* User Plane w/ZUC enc. DL LONG SN */
	0x000fa558,
	/* User Plane w/ZUC enc. UL SHORT SN */
	0x000fa558,
	/* User Plane w/ZUC enc. DL SHORT SN */
	0x000fa558,
	/* User Plane w/ZUC enc. UL 15 BIT SN */
	0x000fa558,
	/* User Plane w/ZUC enc. DL 15 BIT SN */
	0x000fa558,
	/* User Plane w/ZUC enc. UL for 18-bit SN*/
	0x00000791,
	/* User Plane w/ZUC enc. DL for 18-bit SN*/
	0x00002195,

	/*** u-plane with integrity for 12-bit SN ******/

	/* User Plane w/NULL enc. + NULL int. UL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/NULL enc. + NULL int. DL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/NULL enc. + SNOW f9 int. UL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/NULL enc. + SNOW f9 int. DL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/NULL enc. + AES CMAC int. UL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/NULL enc. + AES CMAC int. DL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/NULL enc. + ZUC int. UL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/NULL enc. + ZUC int. DL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/SNOW f8 enc. + NULL int. UL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/SNOW f8 enc. + NULL int. DL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. UL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. DL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. UL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. DL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/SNOW f8 enc. + ZUC int. UL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/SNOW f8 enc. + ZUC int. DL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/AES CTR enc. + NULL int. UL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/AES CTR enc. + NULL int. DL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/AES CTR enc. + SNOW f9 int. UL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/AES CTR enc. + SNOW f9 int. DL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/AES CTR enc. + AES CMAC int. UL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/AES CTR enc. + AES CMAC int. DL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/AES CTR enc. + ZUC int. UL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/AES CTR enc. + ZUC int. DL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/ZUC enc. + NULL int. UL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/ZUC enc. + NULL int. DL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/ZUC enc. + SNOW f9 int. UL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/ZUC enc. + SNOW f9 int. DL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/ZUC enc. + AES CMAC int. UL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/ZUC enc. + AES CMAC int. DL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/ZUC enc. + ZUC int. UL for 12-bit SN*/
	0x70C0A,
	/* User Plane w/ZUC enc. + ZUC int. DL for 12-bit SN*/
	0x70C0A,

	/*** u-plane with integrity for 18-bit SN ******/
	/* User Plane w/NULL enc. + NULL int. UL for 18-bit SN*/
	0x00002195,
	/* User Plane w/NULL enc. + NULL int. DL for 18-bit SN*/
	0x00002195,
	/* User Plane w/NULL enc. + SNOW f9 int. UL for 18-bit SN*/
	0x00000791,
	/* User Plane w/NULL enc. + SNOW f9 int. DL for 18-bit SN*/
	0x00002195,
	/* User Plane w/NULL enc. + AES CMAC int. UL for 18-bit SN*/
	0x00000791,
	/* User Plane w/NULL enc. + AES CMAC int. DL for 18-bit SN*/
	0x00002195,
	/* User Plane w/NULL enc. + ZUC int. UL for 18-bit SN*/
	0x00000791,
	/* User Plane w/NULL enc. + ZUC int. DL for 18-bit SN*/
	0x00002195,
	/* User Plane w/SNOW f8 enc. + NULL int. UL for 18-bit SN*/
	0x00000791,
	/* User Plane w/SNOW f8 enc. + NULL int. DL for 18-bit SN*/
	0x00002195,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. UL for 18-bit SN*/
	0x00000791,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. DL for 18-bit SN*/
	0x00002195,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. UL for 18-bit SN*/
	0x00000791,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. DL for 18-bit SN*/
	0x00002195,
	/* User Plane w/SNOW f8 enc. + ZUC int. UL for 18-bit SN*/
	0x00000791,
	/* User Plane w/SNOW f8 enc. + ZUC int. DL for 18-bit SN*/
	0x00002195,
	/* User Plane w/AES CTR enc. + NULL int. UL for 18-bit SN*/
	0x00000791,
	/* User Plane w/AES CTR enc. + NULL int. DL for 18-bit SN*/
	0x00002195,
	/* User Plane w/AES CTR enc. + SNOW f9 int. UL for 18-bit SN*/
	0x00000791,
	/* User Plane w/AES CTR enc. + SNOW f9 int. DL for 18-bit SN*/
	0x00002195,
	/* User Plane w/AES CTR enc. + AES CMAC int. UL for 18-bit SN*/
	0x00000791,
	/* User Plane w/AES CTR enc. + AES CMAC int. DL for 18-bit SN*/
	0x00002195,
	/* User Plane w/AES CTR enc. + ZUC int. UL for 18-bit SN*/
	0x00000791,
	/* User Plane w/AES CTR enc. + ZUC int. DL for 18-bit SN*/
	0x00002195,
	/* User Plane w/ZUC enc. + NULL int. UL for 18-bit SN*/
	0x00000791,
	/* User Plane w/ZUC enc. + NULL int. DL for 18-bit SN*/
	0x00002195,
	/* User Plane w/ZUC enc. + SNOW f9 int. UL for 18-bit SN*/
	0x00000791,
	/* User Plane w/ZUC enc. + SNOW f9 int. DL for 18-bit SN*/
	0x00002195,
	/* User Plane w/ZUC enc. + AES CMAC int. UL for 18-bit SN*/
	0x00000791,
	/* User Plane w/ZUC enc. + AES CMAC int. DL for 18-bit SN*/
	0x00002195,
	/* User Plane w/ZUC enc. + ZUC int. UL for 18-bit SN*/
	0x00000791,
	/* User Plane w/ZUC enc. + ZUC int. DL for 18-bit SN*/
	0x00002195,
};

static uint8_t pdcp_test_bearer[] = {
	/* Control Plane w/NULL enc. + NULL int. UL */
	0x03,
	/* Control Plane w/NULL enc. + NULL int. DL */
	0x03,
	/* Control Plane w/NULL enc. + SNOW f9 int. UL */
	0x03,
	/* Control Plane w/NULL enc. + SNOW f9 int. DL */
	0x03,
	/* Control Plane w/NULL enc. + AES CMAC int. UL */
	0x03,
	/* Control Plane w/NULL enc. + AES CMAC int. DL */
	0x03,
	/* Control Plane w/NULL enc. + ZUC int. UL */
	0x03,
	/* Control Plane w/NULL enc. + ZUC int. DL */
	0x03,
	/* Control Plane w/SNOW f8 enc. + NULL int. UL */
	0x03,
	/* Control Plane w/SNOW f8 enc. + NULL int. DL */
	0x03,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. UL */
	0x03,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. DL */
	0x03,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. UL */
	0x03,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. DL */
	0x03,
	/* Control Plane w/SNOW f8 enc. + ZUC int. UL */
	0x03,
	/* Control Plane w/SNOW f8 enc. + ZUC int. DL */
	0x03,
	/* Control Plane w/AES CTR enc. + NULL int. UL */
	0x03,
	/* Control Plane w/AES CTR enc. + NULL int. DL */
	0x03,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. UL */
	0x03,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. DL */
	0x03,
	/* Control Plane w/AES CTR enc. + AES CMAC int. UL */
	0x03,
	/* Control Plane w/AES CTR enc. + AES CMAC int. DL */
	0x03,
	/* Control Plane w/AES CTR enc. + ZUC int. UL */
	0x03,
	/* Control Plane w/AES CTR enc. + ZUC int. DL */
	0x03,
	/* Control Plane w/ZUC enc. + NULL int. UL */
	0x03,
	/* Control Plane w/ZUC enc. + NULL int. DL */
	0x03,
	/* Control Plane w/ZUC enc. + SNOW f9 int. UL */
	0x03,
	/* Control Plane w/ZUC enc. + SNOW f9 int. DL */
	0x03,
	/* Control Plane w/ZUC enc. + AES CMAC int. UL */
	0x03,
	/* Control Plane w/ZUC enc. + AES CMAC int. DL */
	0x03,
	/* Control Plane w/ZUC enc. + ZUC int. UL */
	0x03,
	/* Control Plane w/ZUC enc. + ZUC int. DL */
	0x03,

	/************** For 12-bit C-plane ********************/
	/* Control Plane w/NULL enc. + NULL int. UL */
	0x16,
	/* Control Plane w/NULL enc. + NULL int. DL */
	0x16,
	/* Control Plane w/NULL enc. + SNOW f9 int. UL */
	0x16,
	/* Control Plane w/NULL enc. + SNOW f9 int. DL */
	0x16,
	/* Control Plane w/NULL enc. + AES CMAC int. UL */
	0x16,
	/* Control Plane w/NULL enc. + AES CMAC int. DL */
	0x16,
	/* Control Plane w/NULL enc. + ZUC int. UL */
	0x16,
	/* Control Plane w/NULL enc. + ZUC int. DL */
	0x16,
	/* Control Plane w/SNOW f8 enc. + NULL int. UL */
	0x16,
	/* Control Plane w/SNOW f8 enc. + NULL int. DL */
	0x16,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. UL */
	0x16,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. DL */
	0x16,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. UL */
	0x16,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. DL */
	0x16,
	/* Control Plane w/SNOW f8 enc. + ZUC int. UL */
	0x16,
	/* Control Plane w/SNOW f8 enc. + ZUC int. DL */
	0x16,
	/* Control Plane w/AES CTR enc. + NULL int. UL */
	0x16,
	/* Control Plane w/AES CTR enc. + NULL int. DL */
	0x16,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. UL */
	0x16,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. DL */
	0x16,
	/* Control Plane w/AES CTR enc. + AES CMAC int. UL */
	0x16,
	/* Control Plane w/AES CTR enc. + AES CMAC int. DL */
	0x16,
	/* Control Plane w/AES CTR enc. + ZUC int. UL */
	0x16,
	/* Control Plane w/AES CTR enc. + ZUC int. DL */
	0x16,
	/* Control Plane w/ZUC enc. + NULL int. UL */
	0x16,
	/* Control Plane w/ZUC enc. + NULL int. DL */
	0x16,
	/* Control Plane w/ZUC enc. + SNOW f9 int. UL */
	0x16,
	/* Control Plane w/ZUC enc. + SNOW f9 int. DL */
	0x16,
	/* Control Plane w/ZUC enc. + AES CMAC int. UL */
	0x16,
	/* Control Plane w/ZUC enc. + AES CMAC int. DL */
	0x16,
	/* Control Plane w/ZUC enc. + ZUC int. UL */
	0x16,
	/* Control Plane w/ZUC enc. + ZUC int. DL */
	0x16,

	/* User Plane w/NULL enc. UL LONG SN */
	0x03,
	/* User Plane w/NULL enc. DL LONG SN */
	0x03,
	/* User Plane w/NULL enc. UL SHORT SN */
	0x03,
	/* User Plane w/NULL enc. DL SHORT SN */
	0x03,
	/* User Plane w/NULL enc. UL 15 BIT SN */
	0x03,
	/* User Plane w/NULL enc. DL 15 BIT SN */
	0x03,
	/* User Plane w/NULL enc. UL 18-bit SN */
	0x16,
	/* User Plane w/NULL enc. DL 18-bit SN */
	0x16,
	/* User Plane w/SNOW f8 enc. UL LONG SN */
	0x03,
	/* User Plane w/SNOW f8 enc. DL LONG SN */
	0x03,
	/* User Plane w/SNOW f8 enc. UL SHORT SN */
	0x03,
	/* User Plane w/SNOW f8 enc. DL SHORT SN */
	0x03,
	/* User Plane w/SNOW f8 enc. UL 15 BIT SN */
	0x03,
	/* User Plane w/SNOW f8 enc. DL 15 BIT SN */
	0x03,
	/* User Plane w/SNOW f8 enc. UL 18-bit SN */
	0x0B,
	/* User Plane w/SNOW f8 enc. DL 18-bit SN */
	0x16,
	/* User Plane w/AES CTR enc. UL LONG SN */
	0x03,
	/* User Plane w/AES CTR enc. DL LONG SN */
	0x03,
	/* User Plane w/AES CTR enc. UL SHORT SN */
	0x03,
	/* User Plane w/AES CTR enc. DL SHORT SN */
	0x03,
	/* User Plane w/AES CTR enc. UL 15 BIT SN */
	0x03,
	/* User Plane w/AES CTR enc. DL 15 BIT SN */
	0x03,
	/* User Plane w/AES CTR enc. UL 18-bit SN */
	0x0B,
	/* User Plane w/AES CTR enc. DL 18-bit SN */
	0x16,
	/* User Plane w/ZUC enc. UL LONG SN */
	0x03,
	/* User Plane w/ZUC enc. DL LONG SN */
	0x03,
	/* User Plane w/ZUC enc. UL SHORT SN */
	0x03,
	/* User Plane w/ZUC enc. DL SHORT SN */
	0x03,
	/* User Plane w/ZUC enc. UL 15 BIT SN */
	0x03,
	/* User Plane w/ZUC enc. DL 15 BIT SN */
	0x03,
	/* User Plane w/ZUC enc. UL 18-bit SN */
	0x0B,
	/* User Plane w/ZUC enc. DL 18-bit SN */
	0x16,

	/*** u-plane with integrity for 12-bit SN ******/

	/* User Plane w/NULL enc. + NULL int. UL for 12-bit SN*/
	0x16,
	/* User Plane w/NULL enc. + NULL int. DL for 12-bit SN*/
	0x16,
	/* User Plane w/NULL enc. + SNOW f9 int. UL for 12-bit SN*/
	0x16,
	/* User Plane w/NULL enc. + SNOW f9 int. DL for 12-bit SN*/
	0x16,
	/* User Plane w/NULL enc. + AES CMAC int. UL for 12-bit SN*/
	0x16,
	/* User Plane w/NULL enc. + AES CMAC int. DL for 12-bit SN*/
	0x16,
	/* User Plane w/NULL enc. + ZUC int. UL for 12-bit SN*/
	0x16,
	/* User Plane w/NULL enc. + ZUC int. DL for 12-bit SN*/
	0x16,

	/* User Plane w/SNOW f8 enc. + NULL int. UL for 12-bit SN*/
	0x16,
	/* User Plane w/SNOW f8 enc. + NULL int. DL for 12-bit SN*/
	0x16,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. UL for 12-bit SN*/
	0x16,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. DL for 12-bit SN*/
	0x16,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. UL for 12-bit SN*/
	0x16,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. DL for 12-bit SN*/
	0x16,
	/* User Plane w/SNOW f8 enc. + ZUC int. UL for 12-bit SN*/
	0x16,
	/* User Plane w/SNOW f8 enc. + ZUC int. DL for 12-bit SN*/
	0x16,
	/* User Plane w/AES CTR enc. + NULL int. UL for 12-bit SN*/
	0x16,
	/* User Plane w/AES CTR enc. + NULL int. DL for 12-bit SN*/
	0x16,
	/* User Plane w/AES CTR enc. + SNOW f9 int. UL for 12-bit SN*/
	0x16,
	/* User Plane w/AES CTR enc. + SNOW f9 int. DL for 12-bit SN*/
	0x16,
	/* User Plane w/AES CTR enc. + AES CMAC int. UL for 12-bit SN*/
	0x16,
	/* User Plane w/AES CTR enc. + AES CMAC int. DL for 12-bit SN*/
	0x16,
	/* User Plane w/AES CTR enc. + ZUC int. UL for 12-bit SN*/
	0x16,
	/* User Plane w/AES CTR enc. + ZUC int. DL for 12-bit SN*/
	0x16,

	/* User Plane w/ZUC enc. + NULL int. UL for 12-bit SN*/
	0x16,
	/* User Plane w/ZUC enc. + NULL int. DL for 12-bit SN*/
	0x16,
	/* User Plane w/ZUC enc. + SNOW f9 int. UL for 12-bit SN*/
	0x16,
	/* User Plane w/ZUC enc. + SNOW f9 int. DL for 12-bit SN*/
	0x16,
	/* User Plane w/ZUC enc. + AES CMAC int. UL for 12-bit SN*/
	0x16,
	/* User Plane w/ZUC enc. + AES CMAC int. DL for 12-bit SN*/
	0x16,
	/* User Plane w/ZUC enc. + ZUC int. UL for 12-bit SN*/
	0x16,
	/* User Plane w/ZUC enc. + ZUC int. DL for 12-bit SN*/
	0x16,

	/*** u-plane with integrity for 18-bit SN ******/
	/* User Plane w/NULL enc. + NULL int. UL for 18-bit SN*/
	0x16,
	/* User Plane w/NULL enc. + NULL int. DL for 18-bit SN*/
	0x16,
	/* User Plane w/NULL enc. + SNOW f9 int. UL for 18-bit SN*/
	0x0B,
	/* User Plane w/NULL enc. + SNOW f9 int. DL for 18-bit SN*/
	0x16,
	/* User Plane w/NULL enc. + AES CMAC int. UL for 18-bit SN*/
	0x0B,
	/* User Plane w/NULL enc. + AES CMAC int. DL for 18-bit SN*/
	0x16,
	/* User Plane w/NULL enc. + ZUC int. UL for 18-bit SN*/
	0x0B,
	/* User Plane w/NULL enc. + ZUC int. DL for 18-bit SN*/
	0x16,
	/* User Plane w/SNOW f8 enc. + NULL int. UL for 18-bit SN*/
	0x0B,
	/* User Plane w/SNOW f8 enc. + NULL int. DL for 18-bit SN*/
	0x16,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. UL for 18-bit SN*/
	0x0B,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. DL for 18-bit SN*/
	0x16,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. UL for 18-bit SN*/
	0x0B,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. DL for 18-bit SN*/
	0x16,
	/* User Plane w/SNOW f8 enc. + ZUC int. UL for 18-bit SN*/
	0x0B,
	/* User Plane w/SNOW f8 enc. + ZUC int. DL for 18-bit SN*/
	0x16,
	/* User Plane w/AES CTR enc. + NULL int. UL for 18-bit SN*/
	0x0B,
	/* User Plane w/AES CTR enc. + NULL int. DL for 18-bit SN*/
	0x16,
	/* User Plane w/AES CTR enc. + SNOW f9 int. UL for 18-bit SN*/
	0x0B,
	/* User Plane w/AES CTR enc. + SNOW f9 int. DL for 18-bit SN*/
	0x16,
	/* User Plane w/AES CTR enc. + AES CMAC int. UL for 18-bit SN*/
	0x0B,
	/* User Plane w/AES CTR enc. + AES CMAC int. DL for 18-bit SN*/
	0x16,
	/* User Plane w/AES CTR enc. + ZUC int. UL for 18-bit SN*/
	0x0B,
	/* User Plane w/AES CTR enc. + ZUC int. DL for 18-bit SN*/
	0x16,
	/* User Plane w/ZUC enc. + NULL int. UL for 18-bit SN*/
	0x0B,
	/* User Plane w/ZUC enc. + NULL int. DL for 18-bit SN*/
	0x16,
	/* User Plane w/ZUC enc. + SNOW f9 int. UL for 18-bit SN*/
	0x0B,
	/* User Plane w/ZUC enc. + SNOW f9 int. DL for 18-bit SN*/
	0x16,
	/* User Plane w/ZUC enc. + AES CMAC int. UL for 18-bit SN*/
	0x0B,
	/* User Plane w/ZUC enc. + AES CMAC int. DL for 18-bit SN*/
	0x16,
	/* User Plane w/ZUC enc. + ZUC int. UL for 18-bit SN*/
	0x0B,
	/* User Plane w/ZUC enc. + ZUC int. DL for 18-bit SN*/
	0x16,

};

static uint8_t pdcp_test_packet_direction[] = {
	/* Control Plane w/NULL enc. + NULL int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/NULL enc. + NULL int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/NULL enc. + SNOW f9 int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/NULL enc. + SNOW f9 int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/NULL enc. + AES CMAC int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/NULL enc. + AES CMAC int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/NULL enc. + ZUC int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/NULL enc. + ZUC int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/SNOW f8 enc. + NULL int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/SNOW f8 enc. + NULL int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/SNOW f8 enc. + ZUC int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/SNOW f8 enc. + ZUC int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/AES CTR enc. + NULL int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/AES CTR enc. + NULL int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/AES CTR enc. + AES CMAC int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/AES CTR enc. + AES CMAC int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/AES CTR enc. + ZUC int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/AES CTR enc. + ZUC int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/ZUC enc. + NULL int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/ZUC enc. + NULL int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/ZUC enc. + SNOW f9 int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/ZUC enc. + SNOW f9 int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/ZUC enc. + AES CMAC int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/ZUC enc. + AES CMAC int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/ZUC enc. + ZUC int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/ZUC enc. + ZUC int. DL */
	PDCP_DIR_DOWNLINK,

	/***************** For 12-bit C-plane **********************/
	/* Control Plane w/NULL enc. + NULL int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/NULL enc. + NULL int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/NULL enc. + SNOW f9 int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/NULL enc. + SNOW f9 int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/NULL enc. + AES CMAC int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/NULL enc. + AES CMAC int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/NULL enc. + ZUC int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/NULL enc. + ZUC int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/SNOW f8 enc. + NULL int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/SNOW f8 enc. + NULL int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/SNOW f8 enc. + ZUC int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/SNOW f8 enc. + ZUC int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/AES CTR enc. + NULL int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/AES CTR enc. + NULL int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/AES CTR enc. + AES CMAC int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/AES CTR enc. + AES CMAC int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/AES CTR enc. + ZUC int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/AES CTR enc. + ZUC int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/ZUC enc. + NULL int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/ZUC enc. + NULL int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/ZUC enc. + SNOW f9 int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/ZUC enc. + SNOW f9 int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/ZUC enc. + AES CMAC int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/ZUC enc. + AES CMAC int. DL */
	PDCP_DIR_DOWNLINK,
	/* Control Plane w/ZUC enc. + ZUC int. UL */
	PDCP_DIR_UPLINK,
	/* Control Plane w/ZUC enc. + ZUC int. DL */
	PDCP_DIR_DOWNLINK,

	/* User Plane w/NULL enc. UL LONG SN */
	PDCP_DIR_UPLINK,
	/* User Plane w/NULL enc. DL LONG SN */
	PDCP_DIR_DOWNLINK,
	/* User Plane w/NULL enc. UL SHORT SN */
	PDCP_DIR_UPLINK,
	/* User Plane w/NULL enc. DL SHORT SN */
	PDCP_DIR_DOWNLINK,
	/* User Plane w/NULL enc. UL 15 BIT SN */
	PDCP_DIR_UPLINK,
	/* User Plane w/NULL enc. DL 15 BIT SN */
	PDCP_DIR_DOWNLINK,
	/* User Plane w/NULL enc. UL 18-bit SN */
	PDCP_DIR_UPLINK,
	/* User Plane w/NULL enc. DL 18-bit SN */
	PDCP_DIR_DOWNLINK,
	/* User Plane w/SNOW f8 enc. UL LONG SN */
	PDCP_DIR_UPLINK,
	/* User Plane w/SNOW f8 enc. DL LONG SN */
	PDCP_DIR_DOWNLINK,
	/* User Plane w/SNOW f8 enc. UL SHORT SN */
	PDCP_DIR_UPLINK,
	/* User Plane w/SNOW f8 enc. DL SHORT SN */
	PDCP_DIR_DOWNLINK,
	/* User Plane w/SNOW f8 enc. UL 15 BIT SN */
	PDCP_DIR_UPLINK,
	/* User Plane w/SNOW f8 enc. DL 15 BIT SN */
	PDCP_DIR_DOWNLINK,
	/* User Plane w/SNOW f8 enc. UL 18-bit SN */
	PDCP_DIR_UPLINK,
	/* User Plane w/SNOW f8 enc. DL 18-bit SN */
	PDCP_DIR_DOWNLINK,
	/* User Plane w/AES CTR enc. UL LONG SN */
	PDCP_DIR_UPLINK,
	/* User Plane w/AES CTR enc. DL LONG SN */
	PDCP_DIR_DOWNLINK,
	/* User Plane w/AES CTR enc. UL SHORT SN */
	PDCP_DIR_UPLINK,
	/* User Plane w/AES CTR enc. DL SHORT SN */
	PDCP_DIR_DOWNLINK,
	/* User Plane w/AES CTR enc. UL 15 BIT SN */
	PDCP_DIR_UPLINK,
	/* User Plane w/AES CTR enc. DL 15 BIT SN */
	PDCP_DIR_DOWNLINK,
	/* User Plane w/AES CTR enc. UL 18-bit SN */
	PDCP_DIR_UPLINK,
	/* User Plane w/AES CTR enc. DL 18-bit SN */
	PDCP_DIR_DOWNLINK,
	/* User Plane w/ZUC enc. UL LONG SN */
	PDCP_DIR_UPLINK,
	/* User Plane w/ZUC enc. DL LONG SN */
	PDCP_DIR_DOWNLINK,
	/* User Plane w/ZUC enc. UL SHORT SN */
	PDCP_DIR_UPLINK,
	/* User Plane w/ZUC enc. DL SHORT SN */
	PDCP_DIR_DOWNLINK,
	/* User Plane w/ZUC enc. UL 15 BIT SN */
	PDCP_DIR_UPLINK,
	/* User Plane w/ZUC enc. DL 15 BIT SN */
	PDCP_DIR_DOWNLINK,
	/* User Plane w/ZUC enc. UL for 18-bit SN */
	PDCP_DIR_UPLINK,
	/* User Plane w/ZUC enc. DL for 18-bit SN */
	PDCP_DIR_DOWNLINK,

	/*** u-plane with integrity for 12-bit SN ******/

	/* User Plane w/NULL enc. + NULL int. UL for 12-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/NULL enc. + NULL int. DL for 12-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/NULL enc. + SNOW f9 int. UL for 12-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/NULL enc. + SNOW f9 int. DL for 12-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/NULL enc. + AES CMAC int. UL for 12-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/NULL enc. + AES CMAC int. DL for 12-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/NULL enc. + ZUC int. UL for 12-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/NULL enc. + ZUC int. DL for 12-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/SNOW f8 enc. + NULL int. UL for 12-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/SNOW f8 enc. + NULL int. DL for 12-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. UL for 12-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. DL for 12-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. UL for 12-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. DL for 12-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/SNOW f8 enc. + ZUC int. UL for 12-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/SNOW f8 enc. + ZUC int. DL for 12-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/AES CTR enc. + NULL int. UL for 12-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/AES CTR enc. + NULL int. DL for 12-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/AES CTR enc. + SNOW f9 int. UL for 12-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/AES CTR enc. + SNOW f9 int. DL for 12-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/AES CTR enc. + AES CMAC int. UL for 12-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/AES CTR enc. + AES CMAC int. DL for 12-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/AES CTR enc. + ZUC int. UL for 12-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/AES CTR enc. + ZUC int. DL for 12-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/ZUC enc. + NULL int. UL for 12-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/ZUC enc. + NULL int. DL for 12-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/ZUC enc. + SNOW f9 int. UL for 12-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/ZUC enc. + SNOW f9 int. DL for 12-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/ZUC enc. + AES CMAC int. UL for 12-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/ZUC enc. + AES CMAC int. DL for 12-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/ZUC enc. + ZUC int. UL for 12-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/ZUC enc. + ZUC int. DL for 12-bit SN*/
	PDCP_DIR_DOWNLINK,

	/*** u-plane with integrity for 18-bit SN ******/
	/* User Plane w/NULL enc. + NULL int. UL for 18-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/NULL enc. + NULL int. DL for 18-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/NULL enc. + SNOW f9 int. UL for 18-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/NULL enc. + SNOW f9 int. DL for 18-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/NULL enc. + AES CMAC int. UL for 18-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/NULL enc. + AES CMAC int. DL for 18-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/NULL enc. + ZUC int. UL for 18-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/NULL enc. + ZUC int. DL for 18-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/SNOW f8 enc. + NULL int. UL for 18-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/SNOW f8 enc. + NULL int. DL for 18-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. UL for 18-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. DL for 18-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. UL for 18-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. DL for 18-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/SNOW f8 enc. + ZUC int. UL for 18-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/SNOW f8 enc. + ZUC int. DL for 18-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/AES CTR enc. + NULL int. UL for 18-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/AES CTR enc. + NULL int. DL for 18-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/AES CTR enc. + SNOW f9 int. UL for 18-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/AES CTR enc. + SNOW f9 int. DL for 18-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/AES CTR enc. + AES CMAC int. UL for 18-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/AES CTR enc. + AES CMAC int. DL for 18-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/AES CTR enc. + ZUC int. UL for 18-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/AES CTR enc. + ZUC int. DL for 18-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/ZUC enc. + NULL int. UL for 18-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/ZUC enc. + NULL int. DL for 18-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/ZUC enc. + SNOW f9 int. UL for 18-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/ZUC enc. + SNOW f9 int. DL for 18-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/ZUC enc. + AES CMAC int. UL for 18-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/ZUC enc. + AES CMAC int. DL for 18-bit SN*/
	PDCP_DIR_DOWNLINK,
	/* User Plane w/ZUC enc. + ZUC int. UL for 18-bit SN*/
	PDCP_DIR_UPLINK,
	/* User Plane w/ZUC enc. + ZUC int. DL for 18-bit SN*/
	PDCP_DIR_DOWNLINK,

};

static uint8_t pdcp_test_data_sn_size[] = {
	/* Control Plane w/NULL enc. + NULL int. UL */
	5,
	/* Control Plane w/NULL enc. + NULL int. DL */
	5,
	/* Control Plane w/NULL enc. + SNOW f9 int. UL */
	5,
	/* Control Plane w/NULL enc. + SNOW f9 int. DL */
	5,
	/* Control Plane w/NULL enc. + AES CMAC int. UL */
	5,
	/* Control Plane w/NULL enc. + AES CMAC int. DL */
	5,
	/* Control Plane w/NULL enc. + ZUC int. UL */
	5,
	/* Control Plane w/NULL enc. + ZUC int. DL */
	5,
	/* Control Plane w/SNOW f8 enc. + NULL int. UL */
	5,
	/* Control Plane w/SNOW f8 enc. + NULL int. DL */
	5,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. UL */
	5,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. DL */
	5,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. UL */
	5,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. DL */
	5,
	/* Control Plane w/SNOW f8 enc. + ZUC int. UL */
	5,
	/* Control Plane w/SNOW f8 enc. + ZUC int. DL */
	5,
	/* Control Plane w/AES CTR enc. + NULL int. UL */
	5,
	/* Control Plane w/AES CTR enc. + NULL int. DL */
	5,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. UL */
	5,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. DL */
	5,
	/* Control Plane w/AES CTR enc. + AES CMAC int. UL */
	5,
	/* Control Plane w/AES CTR enc. + AES CMAC int. DL */
	5,
	/* Control Plane w/AES CTR enc. + ZUC int. UL */
	5,
	/* Control Plane w/AES CTR enc. + ZUC int. DL */
	5,
	/* Control Plane w/ZUC enc. + NULL int. UL */
	5,
	/* Control Plane w/ZUC enc. + NULL int. DL */
	5,
	/* Control Plane w/ZUC enc. + SNOW f9 int. UL */
	5,
	/* Control Plane w/ZUC enc. + SNOW f9 int. DL */
	5,
	/* Control Plane w/ZUC enc. + AES CMAC int. UL */
	5,
	/* Control Plane w/ZUC enc. + AES CMAC int. DL */
	5,
	/* Control Plane w/ZUC enc. + ZUC int. UL */
	5,
	/* Control Plane w/ZUC enc. + ZUC int. DL */
	5,

	/*************** 12 bit SN for C-plane **************************/
	/* Control Plane w/NULL enc. + NULL int. UL */
	12,
	/* Control Plane w/NULL enc. + NULL int. DL */
	12,
	/* Control Plane w/NULL enc. + SNOW f9 int. UL */
	12,
	/* Control Plane w/NULL enc. + SNOW f9 int. DL */
	12,
	/* Control Plane w/NULL enc. + AES CMAC int. UL */
	12,
	/* Control Plane w/NULL enc. + AES CMAC int. DL */
	12,
	/* Control Plane w/NULL enc. + ZUC int. UL */
	12,
	/* Control Plane w/NULL enc. + ZUC int. DL */
	12,
	/* Control Plane w/SNOW f8 enc. + NULL int. UL */
	12,
	/* Control Plane w/SNOW f8 enc. + NULL int. DL */
	12,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. UL */
	12,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. DL */
	12,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. UL */
	12,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. DL */
	12,
	/* Control Plane w/SNOW f8 enc. + ZUC int. UL */
	12,
	/* Control Plane w/SNOW f8 enc. + ZUC int. DL */
	12,
	/* Control Plane w/AES CTR enc. + NULL int. UL */
	12,
	/* Control Plane w/AES CTR enc. + NULL int. DL */
	12,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. UL */
	12,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. DL */
	12,
	/* Control Plane w/AES CTR enc. + AES CMAC int. UL */
	12,
	/* Control Plane w/AES CTR enc. + AES CMAC int. DL */
	12,
	/* Control Plane w/AES CTR enc. + ZUC int. UL */
	12,
	/* Control Plane w/AES CTR enc. + ZUC int. DL */
	12,
	/* Control Plane w/ZUC enc. + NULL int. UL */
	12,
	/* Control Plane w/ZUC enc. + NULL int. DL */
	12,
	/* Control Plane w/ZUC enc. + SNOW f9 int. UL */
	12,
	/* Control Plane w/ZUC enc. + SNOW f9 int. DL */
	12,
	/* Control Plane w/ZUC enc. + AES CMAC int. UL */
	12,
	/* Control Plane w/ZUC enc. + AES CMAC int. DL */
	12,
	/* Control Plane w/ZUC enc. + ZUC int. UL */
	12,
	/* Control Plane w/ZUC enc. + ZUC int. DL */
	12,
	/*************** 12 bit SN for C-plane end **************************/

	/* User Plane w/NULL enc. UL LONG SN */
	12,
	/* User Plane w/NULL enc. DL LONG SN */
	12,
	/* User Plane w/NULL enc. UL SHORT SN */
	7,
	/* User Plane w/NULL enc. DL SHORT SN */
	7,
	/* User Plane w/NULL enc. UL 15 BIT SN */
	15,
	/* User Plane w/NULL enc. DL 15 BIT SN */
	15,
	/* User Plane w/NULL enc. UL 18 BIT SN */
	18,
	/* User Plane w/NULL enc. DL 18 BIT SN */
	18,
	/* User Plane w/SNOW f8 enc. UL LONG SN */
	12,
	/* User Plane w/SNOW f8 enc. DL LONG SN */
	12,
	/* User Plane w/SNOW f8 enc. UL SHORT SN */
	7,
	/* User Plane w/SNOW f8 enc. DL SHORT SN */
	7,
	/* User Plane w/SNOW f8 enc. UL 15 BIT SN */
	15,
	/* User Plane w/SNOW f8 enc. DL 15 BIT SN */
	15,
	/* User Plane w/SNOW f8 enc. UL 18 BIT SN */
	18,
	/* User Plane w/SNOW f8 enc. DL 18 BIT SN */
	18,
	/* User Plane w/AES CTR enc. UL LONG SN */
	12,
	/* User Plane w/AES CTR enc. DL LONG SN */
	12,
	/* User Plane w/AES CTR enc. UL SHORT SN */
	7,
	/* User Plane w/AES CTR enc. DL SHORT SN */
	7,
	/* User Plane w/AES CTR enc. UL 15 BIT SN */
	15,
	/* User Plane w/AES CTR enc. DL 15 BIT SN */
	15,
	/* User Plane w/AES CTR enc. UL 18 BIT SN */
	18,
	/* User Plane w/AES CTR enc. DL 18 BIT SN */
	18,
	/* User Plane w/ZUC enc. UL LONG SN */
	12,
	/* User Plane w/ZUC enc. DL LONG SN */
	12,
	/* User Plane w/ZUC enc. UL SHORT SN */
	7,
	/* User Plane w/ZUC enc. DL SHORT SN */
	7,
	/* User Plane w/ZUC enc. UL 15 BIT SN */
	15,
	/* User Plane w/ZUC enc. DL 15 BIT SN */
	15,
	/* User Plane w/ZUC enc. UL 18 BIT SN */
	18,
	/* User Plane w/ZUC enc. DL 18 BIT SN */
	18,

	/*** u-plane with integrity for 12-bit SN ******/

	/* User Plane w/NULL enc. + NULL int. UL for 12-bit SN*/
	12,
	/* User Plane w/NULL enc. + NULL int. DL for 12-bit SN*/
	12,
	/* User Plane w/NULL enc. + SNOW f9 int. UL for 12-bit SN*/
	12,
	/* User Plane w/NULL enc. + SNOW f9 int. DL for 12-bit SN*/
	12,
	/* User Plane w/NULL enc. + AES CMAC int. UL for 12-bit SN*/
	12,
	/* User Plane w/NULL enc. + AES CMAC int. DL for 12-bit SN*/
	12,
	/* User Plane w/NULL enc. + ZUC int. UL for 12-bit SN*/
	12,
	/* User Plane w/NULL enc. + ZUC int. DL for 12-bit SN*/
	12,
	/* User Plane w/SNOW f8 enc. + NULL int. UL for 12-bit SN*/
	12,
	/* User Plane w/SNOW f8 enc. + NULL int. DL for 12-bit SN*/
	12,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. UL for 12-bit SN*/
	12,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. DL for 12-bit SN*/
	12,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. UL for 12-bit SN*/
	12,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. DL for 12-bit SN*/
	12,
	/* User Plane w/SNOW f8 enc. + ZUC int. UL for 12-bit SN*/
	12,
	/* User Plane w/SNOW f8 enc. + ZUC int. DL for 12-bit SN*/
	12,
	/* User Plane w/AES CTR enc. + NULL int. UL for 12-bit SN*/
	12,
	/* User Plane w/AES CTR enc. + NULL int. DL for 12-bit SN*/
	12,
	/* User Plane w/AES CTR enc. + SNOW f9 int. UL for 12-bit SN*/
	12,
	/* User Plane w/AES CTR enc. + SNOW f9 int. DL for 12-bit SN*/
	12,
	/* User Plane w/AES CTR enc. + AES CMAC int. UL for 12-bit SN*/
	12,
	/* User Plane w/AES CTR enc. + AES CMAC int. DL for 12-bit SN*/
	12,
	/* User Plane w/AES CTR enc. + ZUC int. UL for 12-bit SN*/
	12,
	/* User Plane w/AES CTR enc. + ZUC int. DL for 12-bit SN*/
	12,
	/* User Plane w/ZUC enc. + NULL int. UL for 12-bit SN*/
	12,
	/* User Plane w/ZUC enc. + NULL int. DL for 12-bit SN*/
	12,
	/* User Plane w/ZUC enc. + SNOW f9 int. UL for 12-bit SN*/
	12,
	/* User Plane w/ZUC enc. + SNOW f9 int. DL for 12-bit SN*/
	12,
	/* User Plane w/ZUC enc. + AES CMAC int. UL for 12-bit SN*/
	12,
	/* User Plane w/ZUC enc. + AES CMAC int. DL for 12-bit SN*/
	12,
	/* User Plane w/ZUC enc. + ZUC int. UL for 12-bit SN*/
	12,
	/* User Plane w/ZUC enc. + ZUC int. DL for 12-bit SN*/
	12,

	/*** u-plane with integrity for 18-bit SN ******/
	/* User Plane w/NULL enc. + NULL int. UL for 18-bit SN*/
	18,
	/* User Plane w/NULL enc. + NULL int. DL for 18-bit SN*/
	18,
	/* User Plane w/NULL enc. + SNOW f9 int. UL for 18-bit SN*/
	18,
	/* User Plane w/NULL enc. + SNOW f9 int. DL for 18-bit SN*/
	18,
	/* User Plane w/NULL enc. + AES CMAC int. UL for 18-bit SN*/
	18,
	/* User Plane w/NULL enc. + AES CMAC int. DL for 18-bit SN*/
	18,
	/* User Plane w/NULL enc. + ZUC int. UL for 18-bit SN*/
	18,
	/* User Plane w/NULL enc. + ZUC int. DL for 18-bit SN*/
	18,
	/* User Plane w/SNOW f8 enc. + NULL int. UL for 18-bit SN*/
	18,
	/* User Plane w/SNOW f8 enc. + NULL int. DL for 18-bit SN*/
	18,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. UL for 18-bit SN*/
	18,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. DL for 18-bit SN*/
	18,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. UL for 18-bit SN*/
	18,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. DL for 18-bit SN*/
	18,
	/* User Plane w/SNOW f8 enc. + ZUC int. UL for 18-bit SN*/
	18,
	/* User Plane w/SNOW f8 enc. + ZUC int. DL for 18-bit SN*/
	18,
	/* User Plane w/AES CTR enc. + NULL int. UL for 18-bit SN*/
	18,
	/* User Plane w/AES CTR enc. + NULL int. DL for 18-bit SN*/
	18,
	/* User Plane w/AES CTR enc. + SNOW f9 int. UL for 18-bit SN*/
	18,
	/* User Plane w/AES CTR enc. + SNOW f9 int. DL for 18-bit SN*/
	18,
	/* User Plane w/AES CTR enc. + AES CMAC int. UL for 18-bit SN*/
	18,
	/* User Plane w/AES CTR enc. + AES CMAC int. DL for 18-bit SN*/
	18,
	/* User Plane w/AES CTR enc. + ZUC int. UL for 18-bit SN*/
	18,
	/* User Plane w/AES CTR enc. + ZUC int. DL for 18-bit SN*/
	18,
	/* User Plane w/ZUC enc. + NULL int. UL for 18-bit SN*/
	18,
	/* User Plane w/ZUC enc. + NULL int. DL for 18-bit SN*/
	18,
	/* User Plane w/ZUC enc. + SNOW f9 int. UL for 18-bit SN*/
	18,
	/* User Plane w/ZUC enc. + SNOW f9 int. DL for 18-bit SN*/
	18,
	/* User Plane w/ZUC enc. + AES CMAC int. UL for 18-bit SN*/
	18,
	/* User Plane w/ZUC enc. + AES CMAC int. DL for 18-bit SN*/
	18,
	/* User Plane w/ZUC enc. + ZUC int. UL for 18-bit SN*/
	18,
	/* User Plane w/ZUC enc. + ZUC int. DL for 18-bit SN*/
	18,
};

static uint8_t *pdcp_test_crypto_key[] = {
	/* Control Plane w/NULL enc. + NULL int. UL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/NULL enc. + NULL int. DL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/NULL enc. + SNOW f9 int. UL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/NULL enc. + SNOW f9 int. DL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/NULL enc. + AES CMAC int. UL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/NULL enc. + AES CMAC int. DL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/NULL enc. + ZUC int. UL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/NULL enc. + ZUC int. DL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/SNOW f8 enc. + NULL int. UL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/SNOW f8 enc. + NULL int. DL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. UL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. DL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. UL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. DL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/SNOW f8 enc. + ZUC int. UL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/SNOW f8 enc. + ZUC int. DL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/AES CTR enc. + NULL int. UL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/AES CTR enc. + NULL int. DL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/AES CTR enc. + SNOW f9 int. UL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/AES CTR enc. + SNOW f9 int. DL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/AES CTR enc. + AES CMAC int. UL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/AES CTR enc. + AES CMAC int. DL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/AES CTR enc. + ZUC int. UL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/AES CTR enc. + ZUC int. DL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/ZUC enc. + NULL int. UL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/ZUC enc. + NULL int. DL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/ZUC enc. + SNOW f9 int. UL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/ZUC enc. + SNOW f9 int. DL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/ZUC enc. + AES CMAC int. UL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/ZUC enc. + AES CMAC int. DL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/ZUC enc. + ZUC int. UL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* Control Plane w/ZUC enc. + ZUC int. DL */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},

	/*********** C-plane 12-bit **************/
	/* Control Plane w/NULL enc. + NULL int. UL */
	NULL,
	/* Control Plane w/NULL enc. + NULL int. DL */
	NULL,
	/* Control Plane w/NULL enc. + SNOW f9 int. UL */
	NULL,
	/* Control Plane w/NULL enc. + SNOW f9 int. DL */
	NULL,
	/* Control Plane w/NULL enc. + AES CMAC int. UL */
	NULL,
	/* Control Plane w/NULL enc. + AES CMAC int. DL */
	NULL,
	/* Control Plane w/NULL enc. + ZUC int. UL */
	NULL,
	/* Control Plane w/NULL enc. + ZUC int. DL */
	NULL,
	/* Control Plane w/SNOW f8 enc. + NULL int. UL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* Control Plane w/SNOW f8 enc. + NULL int. DL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. UL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. DL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},

	/* Control Plane w/SNOW f8 enc. + AES CMAC int. UL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		     0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. DL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		     0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* Control Plane w/SNOW f8 enc. + ZUC int. UL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* Control Plane w/SNOW f8 enc. + ZUC int. DL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},

	/* Control Plane w/AES CTR enc. + NULL int. UL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* Control Plane w/AES CTR enc. + NULL int. DL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* Control Plane w/AES CTR enc. + SNOW f9 int. UL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* Control Plane w/AES CTR enc. + SNOW f9 int. DL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* Control Plane w/AES CTR enc. + AES CMAC int. UL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* Control Plane w/AES CTR enc. + AES CMAC int. DL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* Control Plane w/AES CTR enc. + ZUC int. UL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* Control Plane w/AES CTR enc. + ZUC int. DL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},

	/* Control Plane w/ZUC enc. + NULL int. UL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* Control Plane w/ZUC enc. + NULL int. DL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* Control Plane w/ZUC enc. + SNOW f9 int. UL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* Control Plane w/ZUC enc. + SNOW f9 int. DL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* Control Plane w/ZUC enc. + AES CMAC int. UL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* Control Plane w/ZUC enc. + AES CMAC int. DL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* Control Plane w/ZUC enc. + ZUC int. UL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* Control Plane w/ZUC enc. + ZUC int. DL */
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},

	/* User Plane w/NULL enc. UL LONG SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/NULL enc. DL LONG SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/NULL enc. UL SHORT SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/NULL enc. DL SHORT SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/NULL enc. UL 15 BIT SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/NULL enc. DL 15 BIT SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/NULL enc. UL for 18-bit SN*/
	NULL,
	/* User Plane w/NULL enc. DL for 18-bit SN*/
	NULL,
	/* User Plane w/SNOW f8 enc. UL LONG SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/SNOW f8 enc. DL LONG SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/SNOW f8 enc. UL SHORT SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/SNOW f8 enc. DL SHORT SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/SNOW f8 enc. UL 15 BIT SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/SNOW f8 enc. DL 15 BIT SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/SNOW enc. UL for 18-bit SN*/
	(uint8_t[]){0x60, 0x4C, 0x8A, 0x22, 0x89, 0x56, 0x13, 0x51, 0x7D, 0x61,
		    0xE5, 0xE0, 0x7B, 0x2B, 0xD0, 0x9E},
	/* User Plane w/SNOW enc. DL for 18-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/AES CTR enc. UL LONG SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/AES CTR enc. DL LONG SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/AES CTR enc. UL SHORT SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/AES CTR enc. DL SHORT SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/AES CTR enc. UL 15 BIT SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/AES CTR enc. DL 15 BIT SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/AES enc. UL for 18-bit SN*/
	(uint8_t[]){0x60, 0x4C, 0x8A, 0x22, 0x89, 0x56, 0x13, 0x51, 0x7D, 0x61,
		    0xE5, 0xE0, 0x7B, 0x2B, 0xD0, 0x9E},
	/* User Plane w/AES enc. DL for 18-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/ZUC enc. UL LONG SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/ZUC enc. DL LONG SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/ZUC enc. UL SHORT SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/ZUC enc. DL SHORT SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/ZUC enc. UL 15 BIT SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/ZUC enc. DL 15 BIT SN */
	(uint8_t[]){0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d, 0x51, 0x20, 0x4e, 0xa5,
		    0xf1, 0x45, 0x10, 0x10, 0xd8, 0x52},
	/* User Plane w/ZUC enc. UL for 18-bit SN*/
	(uint8_t[]){0x60, 0x4C, 0x8A, 0x22, 0x89, 0x56, 0x13, 0x51, 0x7D, 0x61,
		    0xE5, 0xE0, 0x7B, 0x2B, 0xD0, 0x9E},
	/* User Plane w/ZUC enc. DL for 18-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},

	/******* u-plane for 12-bit SN ***********/
	/* User Plane w/NULL enc. + NULL int. UL for 12-bit SN*/
	NULL,
	/* User Plane w/NULL enc. + NULL int. DL for 12-bit SN*/
	NULL,
	/* User Plane w/NULL enc. + SNOW f9 int. UL for 12-bit SN*/
	NULL,
	/* User Plane w/NULL enc. + SNOW f9 int. DL for 12-bit SN*/
	NULL,
	/* User Plane w/NULL enc. + AES CMAC int. UL for 12-bit SN*/
	NULL,
	/* User Plane w/NULL enc. + AES CMAC int. DL for 12-bit SN*/
	NULL,
	/* User Plane w/NULL enc. + ZUC int. UL for 12-bit SN*/
	NULL,
	/* User Plane w/NULL enc. + ZUC int. DL for 12-bit SN*/
	NULL,
	/* User Plane w/SNOW f8 enc. + NULL int. UL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/SNOW f8 enc. + NULL int. DL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. UL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. DL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/SNOW f8 enc. + AES CMAC int. UL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/SNOW f8 enc. + AES CMAC int. DL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/SNOW f8 enc. + ZUC int. UL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/SNOW f8 enc. + ZUC int. DL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},

	/* User Plane w/AES CTR enc. + NULL int. UL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/AES CTR enc. + NULL int. DL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/AES CTR enc. + SNOW f9 int. UL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/AES CTR enc. + SNOW f9 int. DL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/AES CTR enc. + AES CMAC int. UL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},

	/* User Plane w/AES CTR enc. + AES CMAC int. DL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/AES CTR enc. + ZUC int. UL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/AES CTR enc. + ZUC int. DL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},

	/* User Plane w/ZUC enc. + NULL int. UL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/ZUC enc. + NULL int. DL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/ZUC enc. + SNOW f9 int. UL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/ZUC enc. + SNOW f9 int. DL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/ZUC enc. + AES CMAC int. UL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/ZUC enc. + AES CMAC int. DL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/ZUC enc. + ZUC int. UL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/ZUC enc. + ZUC int. DL for 12-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},

	/******* u-plane for 18-bit SN with integrity ***********/
	/* User Plane w/NULL enc. + NULL int. UL for 18-bit SN*/
	NULL,
	/* User Plane w/NULL enc. + NULL int. DL for 18-bit SN*/
	NULL,
	/* User Plane w/NULL enc. + SNOW f9 int. UL for 18-bit SN*/
	NULL,
	/* User Plane w/NULL enc. + SNOW f9 int. DL for 18-bit SN*/
	NULL,
	/* User Plane w/NULL enc. + AES CMAC int. UL for 18-bit SN*/
	NULL,
	/* User Plane w/NULL enc. + AES CMAC int. DL for 18-bit SN*/
	NULL,
	/* User Plane w/NULL enc. + ZUC int. UL for 18-bit SN*/
	NULL,
	/* User Plane w/NULL enc. + ZUC int. DL for 18-bit SN*/
	NULL,
	/* User Plane w/SNOW f8 enc. + NULL int. UL for 18-bit SN*/
	(uint8_t[]){0x60, 0x4C, 0x8A, 0x22, 0x89, 0x56, 0x13, 0x51, 0x7D, 0x61,
		    0xE5, 0xE0, 0x7B, 0x2B, 0xD0, 0x9E},
	/* User Plane w/SNOW f8 enc. + NULL int. DL for 18-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. UL for 18-bit SN*/
	(uint8_t[]){0x60, 0x4C, 0x8A, 0x22, 0x89, 0x56, 0x13, 0x51, 0x7D, 0x61,
		    0xE5, 0xE0, 0x7B, 0x2B, 0xD0, 0x9E},
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. DL for 18-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/SNOW f8 enc. + AES CMAC int. UL for 18-bit SN*/
	(uint8_t[]){0x60, 0x4C, 0x8A, 0x22, 0x89, 0x56, 0x13, 0x51, 0x7D, 0x61,
		    0xE5, 0xE0, 0x7B, 0x2B, 0xD0, 0x9E},
	/* User Plane w/SNOW f8 enc. + AES CMAC int. DL for 18-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/SNOW f8 enc. + ZUC int. UL for 18-bit SN*/
	(uint8_t[]){0x60, 0x4C, 0x8A, 0x22, 0x89, 0x56, 0x13, 0x51, 0x7D, 0x61,
		    0xE5, 0xE0, 0x7B, 0x2B, 0xD0, 0x9E},
	/* User Plane w/SNOW f8 enc. + ZUC int. DL for 18-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/AES CTR enc. + NULL int. UL for 18-bit SN*/
	(uint8_t[]){0x60, 0x4C, 0x8A, 0x22, 0x89, 0x56, 0x13, 0x51, 0x7D, 0x61,
		    0xE5, 0xE0, 0x7B, 0x2B, 0xD0, 0x9E},
	/* User Plane w/AES CTR enc. + NULL int. DL for 18-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/AES CTR enc. + SNOW f9 int. UL for 18-bit SN*/
	(uint8_t[]){0x60, 0x4C, 0x8A, 0x22, 0x89, 0x56, 0x13, 0x51, 0x7D, 0x61,
		    0xE5, 0xE0, 0x7B, 0x2B, 0xD0, 0x9E},
	/* User Plane w/AES CTR enc. + SNOW f9 int. DL for 18-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/AES CTR enc. + AES CMAC int. UL for 18-bit SN*/
	(uint8_t[]){0x60, 0x4C, 0x8A, 0x22, 0x89, 0x56, 0x13, 0x51, 0x7D, 0x61,
		    0xE5, 0xE0, 0x7B, 0x2B, 0xD0, 0x9E},
	/* User Plane w/AES CTR enc. + AES CMAC int. DL for 18-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/AES CTR enc. + ZUC int. UL for 18-bit SN*/
	(uint8_t[]){0x60, 0x4C, 0x8A, 0x22, 0x89, 0x56, 0x13, 0x51, 0x7D, 0x61,
		    0xE5, 0xE0, 0x7B, 0x2B, 0xD0, 0x9E},
	/* User Plane w/AES CTR enc. + ZUC int. DL for 18-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/ZUC enc. + NULL int. UL for 18-bit SN*/
	(uint8_t[]){0x60, 0x4C, 0x8A, 0x22, 0x89, 0x56, 0x13, 0x51, 0x7D, 0x61,
		    0xE5, 0xE0, 0x7B, 0x2B, 0xD0, 0x9E},
	/* User Plane w/ZUC enc. + NULL int. DL for 18-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/ZUC enc. + SNOW f9 int. UL for 18-bit SN*/
	(uint8_t[]){0x60, 0x4C, 0x8A, 0x22, 0x89, 0x56, 0x13, 0x51, 0x7D, 0x61,
		    0xE5, 0xE0, 0x7B, 0x2B, 0xD0, 0x9E},
	/* User Plane w/ZUC enc. + SNOW f9 int. DL for 18-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/ZUC enc. + AES CMAC int. UL for 18-bit SN*/
	(uint8_t[]){0x60, 0x4C, 0x8A, 0x22, 0x89, 0x56, 0x13, 0x51, 0x7D, 0x61,
		    0xE5, 0xE0, 0x7B, 0x2B, 0xD0, 0x9E},
	/* User Plane w/ZUC enc. + AES CMAC int. DL for 18-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
	/* User Plane w/ZUC enc. + ZUC int. UL for 18-bit SN*/
	(uint8_t[]){0x60, 0x4C, 0x8A, 0x22, 0x89, 0x56, 0x13, 0x51, 0x7D, 0x61,
		    0xE5, 0xE0, 0x7B, 0x2B, 0xD0, 0x9E},
	/* User Plane w/ZUC enc. + ZUC int. DL for 18-bit SN*/
	(uint8_t[]){0x40, 0x19, 0xE2, 0x99, 0x16, 0xC3, 0x7E, 0x9B, 0xA6, 0x8F,
		    0x57, 0xBE, 0x97, 0xFD, 0x02, 0xCB},
};

static uint8_t *pdcp_test_auth_key[] = {
	/* Control Plane w/NULL enc. + NULL int. UL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/NULL enc. + NULL int. DL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/NULL enc. + SNOW f9 int. UL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/NULL enc. + SNOW f9 int. DL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/NULL enc. + AES CMAC int. UL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/NULL enc. + AES CMAC int. DL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/NULL enc. + ZUC int. UL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/NULL enc. + ZUC int. DL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/SNOW f8 enc. + NULL int. UL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/SNOW f8 enc. + NULL int. DL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. UL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. DL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. UL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. DL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/SNOW f8 enc. + ZUC int. UL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/SNOW f8 enc. + ZUC int. DL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/AES CTR enc. + NULL int. UL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/AES CTR enc. + NULL int. DL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/AES CTR enc. + SNOW f9 int. UL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/AES CTR enc. + SNOW f9 int. DL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/AES CTR enc. + AES CMAC int. UL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/AES CTR enc. + AES CMAC int. DL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/AES CTR enc. + ZUC int. UL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/AES CTR enc. + ZUC int. DL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/ZUC enc. + NULL int. UL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/ZUC enc. + NULL int. DL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/ZUC enc. + SNOW f9 int. UL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/ZUC enc. + SNOW f9 int. DL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/ZUC enc. + AES CMAC int. UL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/ZUC enc. + AES CMAC int. DL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/ZUC enc. + ZUC int. UL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},
	/* Control Plane w/ZUC enc. + ZUC int. DL */
	(uint8_t[]){0xc7, 0x36, 0xc6, 0xaa, 0xb2, 0x2b, 0xff, 0xf9, 0x1e, 0x26,
		    0x98, 0xd2, 0xe2, 0x2a, 0xd5, 0x7e},

	/********* 12-bit C-plane **********************/
	/* Control Plane w/NULL enc. + NULL int. UL */
	NULL,
	/* Control Plane w/NULL enc. + NULL int. DL */
	NULL,
	/* Control Plane w/NULL enc. + SNOW f9 int. UL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/NULL enc. + SNOW f9 int. DL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/NULL enc. + AES CMAC int. UL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/NULL enc. + AES CMAC int. DL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/NULL enc. + ZUC int. UL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/NULL enc. + ZUC int. DL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},

	/* Control Plane w/SNOW f8 enc. + NULL int. UL */
	NULL,
	/* Control Plane w/SNOW f8 enc. + NULL int. DL */
	NULL,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. UL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. DL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. UL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. DL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/SNOW f8 enc. + ZUC int. UL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/SNOW f8 enc. + ZUC int. DL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/AES CTR enc. + NULL int. UL */
	NULL,
	/* Control Plane w/AES CTR enc. + NULL int. DL */
	NULL,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. UL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/AES CTR enc. + SNOW f9 int. DL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/AES CTR enc. + AES CMAC int. UL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/AES CTR enc. + AES CMAC int. DL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/AES CTR enc. + ZUC int. UL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/AES CTR enc. + ZUC int. DL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},

	/* Control Plane w/ZUC enc. + NULL int. UL */
	NULL,
	/* Control Plane w/ZUC enc. + NULL int. DL */
	NULL,
	/* Control Plane w/ZUC enc. + SNOW f9 int. UL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/ZUC enc. + SNOW f9 int. DL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/ZUC enc. + AES CMAC int. UL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/ZUC enc. + AES CMAC int. DL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/ZUC enc. + ZUC int. UL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* Control Plane w/ZUC enc. + ZUC int. DL */
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},

	/* User Plane w/NULL enc. UL LONG SN */
	NULL,
	/* User Plane w/NULL enc. DL LONG SN */
	NULL,
	/* User Plane w/NULL enc. UL SHORT SN */
	NULL,
	/* User Plane w/NULL enc. DL SHORT SN */
	NULL,
	/* User Plane w/NULL enc. UL 15 BIT SN */
	NULL,
	/* User Plane w/NULL enc. DL 15 BIT SN */
	NULL,
	/* User Plane w/NULL enc. UL 18 BIT SN */
	NULL,
	/* User Plane w/NULL enc. DL 18 BIT SN */
	NULL,
	/* User Plane w/SNOW f8 enc. UL LONG SN */
	NULL,
	/* User Plane w/SNOW f8 enc. DL LONG SN */
	NULL,
	/* User Plane w/SNOW f8 enc. UL SHORT SN */
	NULL,
	/* User Plane w/SNOW f8 enc. DL SHORT SN */
	NULL,
	/* User Plane w/SNOW f8 enc. UL 15 BIT SN */
	NULL,
	/* User Plane w/SNOW f8 enc. DL 15 BIT SN */
	NULL,
	/* User Plane w/SNOW f8 enc. UL 18 BIT SN */
	NULL,
	/* User Plane w/SNOW f8 enc. DL 18 BIT SN */
	NULL,
	/* User Plane w/AES CTR enc. UL LONG SN */
	NULL,
	/* User Plane w/AES CTR enc. DL LONG SN */
	NULL,
	/* User Plane w/AES CTR enc. UL SHORT SN */
	NULL,
	/* User Plane w/AES CTR enc. DL SHORT SN */
	NULL,
	/* User Plane w/AES CTR enc. UL 15 BIT SN */
	NULL,
	/* User Plane w/AES CTR enc. DL 15 BIT SN */
	NULL,
	/* User Plane w/AES CTR enc. UL 18 BIT SN */
	NULL,
	/* User Plane w/AES CTR enc. DL 18 BIT SN */
	NULL,
	/* User Plane w/ZUC enc. UL LONG SN */
	NULL,
	/* User Plane w/ZUC enc. DL LONG SN */
	NULL,
	/* User Plane w/ZUC enc. UL SHORT SN */
	NULL,
	/* User Plane w/ZUC enc. DL SHORT SN */
	NULL,
	/* User Plane w/ZUC enc. UL 15 BIT SN */
	NULL,
	/* User Plane w/ZUC enc. DL 15 BIT SN */
	NULL,
	/* User Plane w/ZUC enc. UL 18 BIT SN */
	NULL,
	/* User Plane w/ZUC enc. DL 18 BIT SN */
	NULL,

	/***** u-plane with integrity for 12-bit SN ******/
	/* User Plane w/NULL enc. + NULL int. UL for 12-bit SN*/
	NULL,
	/* User Plane w/NULL enc. + NULL int. DL for 12-bit SN*/
	NULL,
	/* User Plane w/NULL enc. + SNOW f9 int. UL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},

	/* User Plane w/NULL enc. + SNOW f9 int. DL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/NULL enc. + AES CMAC int. UL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/NULL enc. + AES CMAC int. DL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/NULL enc. + ZUC int. UL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/NULL enc. + ZUC int. DL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},

	/* User Plane w/SNOW f8 enc. + NULL int. UL for 12-bit SN*/
	NULL,
	/* User Plane w/SNOW f8 enc. + NULL int. DL for 12-bit SN*/
	NULL,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. UL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. DL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/SNOW f8 enc. + AES CMAC int. UL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/SNOW f8 enc. + AES CMAC int. DL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/SNOW f8 enc. + ZUC int. UL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/SNOW f8 enc. + ZUC int. DL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},

	/* User Plane w/AES CTR enc. + NULL int. UL for 12-bit SN*/
	NULL,
	/* User Plane w/AES CTR enc. + NULL int. DL for 12-bit SN*/
	NULL,
	/* User Plane w/AES CTR enc. + SNOW f9 int. UL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/AES CTR enc. + SNOW f9 int. DL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/AES CTR enc. + AES CMAC int. UL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/AES CTR enc. + AES CMAC int. DL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/AES CTR enc. + ZUC int. UL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/AES CTR enc. + ZUC int. DL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},

	/* User Plane w/ZUC enc. + NULL int. UL for 12-bit SN*/
	NULL,
	/* User Plane w/ZUC enc. + NULL int. DL for 12-bit SN*/
	NULL,
	/* User Plane w/ZUC enc. + SNOW f9 int. UL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/ZUC enc. + SNOW f9 int. DL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/ZUC enc. + AES CMAC int. UL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/ZUC enc. + AES CMAC int. DL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/ZUC enc. + ZUC int. UL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/ZUC enc. + ZUC int. DL for 12-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},

	/***** u-plane with integrity for 18-bit SN ******/
	/* User Plane w/NULL enc. + NULL int. UL for 18-bit SN*/
	NULL,
	/* User Plane w/NULL enc. + NULL int. DL for 18-bit SN*/
	NULL,
	/* User Plane w/NULL enc. + SNOW f9 int. UL for 18-bit SN*/
	(uint8_t[]){0x1A, 0xEF, 0xBB, 0xFF, 0x00, 0xF5, 0x4B, 0x32, 0x87, 0xF9,
		    0xDB, 0xE0, 0x31, 0x5F, 0x3A, 0x15},
	/* User Plane w/NULL enc. + SNOW f9 int. DL for 18-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/NULL enc. + AES CMAC int. UL for 18-bit SN*/
	(uint8_t[]){0x1A, 0xEF, 0xBB, 0xFF, 0x00, 0xF5, 0x4B, 0x32, 0x87, 0xF9,
		    0xDB, 0xE0, 0x31, 0x5F, 0x3A, 0x15},
	/* User Plane w/NULL enc. + AES CMAC int. DL for 18-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/NULL enc. + ZUC int. UL for 18-bit SN*/
	(uint8_t[]){0x1A, 0xEF, 0xBB, 0xFF, 0x00, 0xF5, 0x4B, 0x32, 0x87, 0xF9,
		    0xDB, 0xE0, 0x31, 0x5F, 0x3A, 0x15},
	/* User Plane w/NULL enc. + ZUC int. DL for 18-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/SNOW f8 enc. + NULL int. UL for 18-bit SN*/
	NULL,
	/* User Plane w/SNOW f8 enc. + NULL int. DL for 18-bit SN*/
	NULL,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. UL for 18-bit SN*/
	(uint8_t[]){0x1A, 0xEF, 0xBB, 0xFF, 0x00, 0xF5, 0x4B, 0x32, 0x87, 0xF9,
		    0xDB, 0xE0, 0x31, 0x5F, 0x3A, 0x15},
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. DL for 18-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/SNOW f8 enc. + AES CMAC int. UL for 18-bit SN*/
	(uint8_t[]){0x1A, 0xEF, 0xBB, 0xFF, 0x00, 0xF5, 0x4B, 0x32, 0x87, 0xF9,
		    0xDB, 0xE0, 0x31, 0x5F, 0x3A, 0x15},
	/* User Plane w/SNOW f8 enc. + AES CMAC int. DL for 18-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/SNOW f8 enc. + ZUC int. UL for 18-bit SN*/
	(uint8_t[]){0x1A, 0xEF, 0xBB, 0xFF, 0x00, 0xF5, 0x4B, 0x32, 0x87, 0xF9,
		    0xDB, 0xE0, 0x31, 0x5F, 0x3A, 0x15},
	/* User Plane w/SNOW f8 enc. + ZUC int. DL for 18-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/AES CTR enc. + NULL int. UL for 18-bit SN*/
	NULL,
	/* User Plane w/AES CTR enc. + NULL int. DL for 18-bit SN*/
	NULL,
	/* User Plane w/AES CTR enc. + SNOW f9 int. UL for 18-bit SN*/
	(uint8_t[]){0x1A, 0xEF, 0xBB, 0xFF, 0x00, 0xF5, 0x4B, 0x32, 0x87, 0xF9,
		    0xDB, 0xE0, 0x31, 0x5F, 0x3A, 0x15},
	/* User Plane w/AES CTR enc. + SNOW f9 int. DL for 18-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/AES CTR enc. + AES CMAC int. UL for 18-bit SN*/
	(uint8_t[]){0x1A, 0xEF, 0xBB, 0xFF, 0x00, 0xF5, 0x4B, 0x32, 0x87, 0xF9,
		    0xDB, 0xE0, 0x31, 0x5F, 0x3A, 0x15},
	/* User Plane w/AES CTR enc. + AES CMAC int. DL for 18-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/AES CTR enc. + ZUC int. UL for 18-bit SN*/
	(uint8_t[]){0x1A, 0xEF, 0xBB, 0xFF, 0x00, 0xF5, 0x4B, 0x32, 0x87, 0xF9,
		    0xDB, 0xE0, 0x31, 0x5F, 0x3A, 0x15},
	/* User Plane w/AES CTR enc. + ZUC int. DL for 18-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/ZUC enc. + NULL int. UL for 18-bit SN*/
	NULL,
	/* User Plane w/ZUC enc. + NULL int. DL for 18-bit SN*/
	NULL,
	/* User Plane w/ZUC enc. + SNOW f9 int. UL for 18-bit SN*/
	(uint8_t[]){0x1A, 0xEF, 0xBB, 0xFF, 0x00, 0xF5, 0x4B, 0x32, 0x87, 0xF9,
		    0xDB, 0xE0, 0x31, 0x5F, 0x3A, 0x15},
	/* User Plane w/ZUC enc. + SNOW f9 int. DL for 18-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/ZUC enc. + AES CMAC int. UL for 18-bit SN*/
	(uint8_t[]){0x1A, 0xEF, 0xBB, 0xFF, 0x00, 0xF5, 0x4B, 0x32, 0x87, 0xF9,
		    0xDB, 0xE0, 0x31, 0x5F, 0x3A, 0x15},
	/* User Plane w/ZUC enc. + AES CMAC int. DL for 18-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
	/* User Plane w/ZUC enc. + ZUC int. UL for 18-bit SN*/
	(uint8_t[]){0x1A, 0xEF, 0xBB, 0xFF, 0x00, 0xF5, 0x4B, 0x32, 0x87, 0xF9,
		    0xDB, 0xE0, 0x31, 0x5F, 0x3A, 0x15},
	/* User Plane w/ZUC enc. + ZUC int. DL for 18-bit SN*/
	(uint8_t[]){0x4D, 0x4B, 0x2B, 0x1E, 0x39, 0x60, 0x0A, 0x98, 0xC1, 0x3C,
		    0x98, 0x82, 0xDC, 0xB6, 0xC2, 0x36},
};

static uint8_t *pdcp_test_data_in[] = {
	/* Control Plane w/NULL enc. + NULL int. UL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/NULL enc. + NULL int. DL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/NULL enc. + SNOW f9 int. UL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/NULL enc. + SNOW f9 int. DL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/NULL enc. + AES CMAC int. UL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/NULL enc. + AES CMAC int. DL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/NULL enc. + ZUC int. UL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/NULL enc. + ZUC int. DL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/SNOW f8 enc. + NULL int. UL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/SNOW f8 enc. + NULL int. DL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. UL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. DL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. UL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. DL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/SNOW f8 enc. + ZUC int. UL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/SNOW f8 enc. + ZUC int. DL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/AES CTR enc. + NULL int. UL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/AES CTR enc. + NULL int. DL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/AES CTR enc. + SNOW f9 int. UL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/AES CTR enc. + SNOW f9 int. DL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/AES CTR enc. + AES CMAC int. UL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/AES CTR enc. + AES CMAC int. DL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/AES CTR enc. + ZUC int. UL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/AES CTR enc. + ZUC int. DL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/ZUC enc. + NULL int. UL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/ZUC enc. + NULL int. DL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/ZUC enc. + SNOW f9 int. UL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/ZUC enc. + SNOW f9 int. DL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/ZUC enc. + AES CMAC int. UL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/ZUC enc. + AES CMAC int. DL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/ZUC enc. + ZUC int. UL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* Control Plane w/ZUC enc. + ZUC int. DL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},

	/*************** 12-bit C-plane ****************/
	/* Control Plane w/NULL enc. + NULL int. UL */
	(uint8_t[]){0x00, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/NULL enc. + NULL int. DL */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/NULL enc. + SNOW f9 int. UL */
	(uint8_t[]){0x00, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/NULL enc. + SNOW f9 int. DL */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/NULL enc. + AES CMAC int. UL */
	(uint8_t[]){0x00, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/NULL enc. + AES CMAC int. DL */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/NULL enc. + ZUC int. UL */
	(uint8_t[]){0x00, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/NULL enc. + ZUC int. DL */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},

	/* Control Plane w/SNOW f8 enc. + NULL int. UL */
	(uint8_t[]){0x00, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/SNOW f8 enc. + NULL int. DL */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. UL */
	(uint8_t[]){0x00, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. DL */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. UL */
	(uint8_t[]){0x00, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. DL */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/SNOW f8 enc. + ZUC int. UL */
	(uint8_t[]){0x00, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/SNOW f8 enc. + ZUC int. DL */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},

	/* Control Plane w/AES CTR enc. + NULL int. UL */
	(uint8_t[]){0x00, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/AES CTR enc. + NULL int. DL */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/AES CTR enc. + SNOW f9 int. UL */
	(uint8_t[]){0x00, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/AES CTR enc. + SNOW f9 int. DL */
	 (uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		     0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		     0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		     0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		     0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		     0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/AES CTR enc. + AES CMAC int. UL */
	(uint8_t[]){0x00, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/AES CTR enc. + AES CMAC int. DL */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/AES CTR enc. + ZUC int. UL */
	(uint8_t[]){0x00, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/AES CTR enc. + ZUC int. DL */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},

	/* Control Plane w/ZUC enc. + NULL int. UL */
	(uint8_t[]){0x00, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/ZUC enc. + NULL int. DL */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/ZUC enc. + SNOW f9 int. UL */
	(uint8_t[]){0x00, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/ZUC enc. + SNOW f9 int. DL */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/ZUC enc. + AES CMAC int. UL */
	(uint8_t[]){0x00, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/ZUC enc. + AES CMAC int. DL */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/ZUC enc. + ZUC int. UL */
	(uint8_t[]){0x00, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* Control Plane w/ZUC enc. + ZUC int. DL */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},

	/* User Plane w/NULL enc. UL LONG SN */
	(uint8_t[]){0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4,
		    0x57, 0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/NULL enc. DL LONG SN */
	(uint8_t[]){0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4,
		    0x57, 0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/NULL enc. UL SHORT SN */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/NULL enc. DL SHORT SN */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/NULL enc. UL 15 BIT SN */
	(uint8_t[]){0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4,
		    0x57, 0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/NULL enc. DL 15 BIT SN */
	(uint8_t[]){0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4,
		    0x57, 0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/NULL enc. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69, 0x00, 0x00, 0x00, 0x00},
	/* User Plane w/NULL enc. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69, 0x00, 0x00, 0x00, 0x00},
	/* User Plane w/SNOW f8 enc. UL LONG SN */
	(uint8_t[]){0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4,
		    0x57, 0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/SNOW f8 enc. DL LONG SN */
	(uint8_t[]){0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4,
		    0x57, 0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/SNOW f8 enc. UL SHORT SN */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/SNOW f8 enc. DL SHORT SN */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/SNOW f8 enc. UL 15 BIT SN */
	(uint8_t[]){0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4,
		    0x57, 0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/SNOW f8 enc. DL 15 BIT SN */
	(uint8_t[]){0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4,
		    0x57, 0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/SNOW enc. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		    0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9,
		    0x68, 0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13,
		    0x52, 0x08, 0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62,
		    0x31, 0xA2, 0x76, 0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A,
		    0xD2, 0xEE, 0xD6, 0x93, 0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA,
		    0x91, 0x00, 0x00, 0x00, 0x00},
	/* User Plane w/SNOW enc. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69, 0x00, 0x00, 0x00, 0x00},
	/* User Plane w/AES CTR enc. UL LONG SN */
	(uint8_t[]){0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4,
		    0x57, 0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/AES CTR enc. DL LONG SN */
	(uint8_t[]){0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4,
		    0x57, 0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/AES CTR enc. UL SHORT SN */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/AES CTR enc. DL SHORT SN */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/AES CTR enc. UL 15 BIT SN */
	(uint8_t[]){0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4,
		    0x57, 0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/AES CTR enc. DL 15 BIT SN */
	(uint8_t[]){0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4,
		    0x57, 0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/AES enc. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		    0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9,
		    0x68, 0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13,
		    0x52, 0x08, 0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62,
		    0x31, 0xA2, 0x76, 0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A,
		    0xD2, 0xEE, 0xD6, 0x93, 0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA,
		    0x91, 0x00, 0x00, 0x00, 0x00},
	/* User Plane w/AES enc. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69, 0x00, 0x00, 0x00, 0x00},
	/* User Plane w/ZUC enc. UL LONG SN */
	(uint8_t[]){0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4,
		    0x57, 0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/ZUC enc. DL LONG SN */
	(uint8_t[]){0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4,
		    0x57, 0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/ZUC enc. UL SHORT SN */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/ZUC enc. DL SHORT SN */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/ZUC enc. UL 15 BIT SN */
	(uint8_t[]){0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4,
		    0x57, 0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/ZUC enc. DL 15 BIT SN */
	(uint8_t[]){0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4,
		    0x57, 0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/ZUC enc. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		    0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9,
		    0x68, 0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13,
		    0x52, 0x08, 0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62,
		    0x31, 0xA2, 0x76, 0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A,
		    0xD2, 0xEE, 0xD6, 0x93, 0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA,
		    0x91, 0x00, 0x00, 0x00, 0x00},
	/* User Plane w/ZUC enc. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69, 0x00, 0x00, 0x00, 0x00},

	/*************** u-plane with integrity for 12-bit SN *****/
	/* User Plane w/NULL enc. + NULL int. UL for 12-bit SN*/
	(uint8_t[]){0x80, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/NULL enc. + NULL int. DL for 12-bit SN*/
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/NULL enc. + SNOW f9 int. UL for 12-bit SN*/
	(uint8_t[]){0x80, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/NULL enc. + SNOW f9 int. DL for 12-bit SN*/
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/NULL enc. + AES CMAC int. UL for 12-bit SN*/
	(uint8_t[]){0x80, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/NULL enc. + AES CMAC int. DL for 12-bit SN*/
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/NULL enc. + ZUC int. UL for 12-bit SN*/
	(uint8_t[]){0x80, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/NULL enc. + ZUC int. DL for 12-bit SN*/
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},

	/* User Plane w/SNOW f8 enc. + NULL int. UL for 12-bit SN*/
	(uint8_t[]){0x80, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/SNOW f8 enc. + NULL int. DL for 12-bit SN*/
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. UL for 12-bit SN*/
	(uint8_t[]){0x80, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. DL for 12-bit SN*/
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/SNOW f8 enc. + AES CMAC int. UL for 12-bit SN*/
	(uint8_t[]){0x80, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/SNOW f8 enc. + AES CMAC int. DL for 12-bit SN*/
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/SNOW f8 enc. + ZUC int. UL for 12-bit SN*/
	(uint8_t[]){0x80, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/SNOW f8 enc. + ZUC int. DL for 12-bit SN*/
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},

	/* User Plane w/AES CTR enc. + NULL int. UL for 12-bit SN*/
	(uint8_t[]){0x80, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/AES CTR enc. + NULL int. DL for 12-bit SN*/
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/AES CTR enc. + SNOW f9 int. UL for 12-bit SN*/
	(uint8_t[]){0x80, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/AES CTR enc. + SNOW f9 int. DL for 12-bit SN*/
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/AES CTR enc. + AES CMAC int. UL for 12-bit SN*/
	(uint8_t[]){0x80, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/AES CTR enc. + AES CMAC int. DL for 12-bit SN*/
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/AES CTR enc. + ZUC int. UL for 12-bit SN*/
	(uint8_t[]){0x80, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/AES CTR enc. + ZUC int. DL for 12-bit SN*/
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},

	/* User Plane w/ZUC enc. + NULL int. UL for 12-bit SN*/
	(uint8_t[]){0x80, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/ZUC enc. + NULL int. DL for 12-bit SN*/
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/ZUC enc. + SNOW f9 int. UL for 12-bit SN*/
	(uint8_t[]){0x80, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/ZUC enc. + SNOW f9 int. DL for 12-bit SN*/
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/ZUC enc. + AES CMAC int. UL for 12-bit SN*/
	(uint8_t[]){0x80, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/ZUC enc. + AES CMAC int. DL for 12-bit SN*/
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/ZUC enc. + ZUC int. UL for 12-bit SN*/
	(uint8_t[]){0x80, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},
	/* User Plane w/ZUC enc. + ZUC int. DL for 12-bit SN*/
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53,
		    0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
		    0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91,
		    0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0,
		    0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70,
		    0x1B, 0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD},

	/*************** u-plane with integrity for 18-bit SN *****/
	/* User Plane w/NULL enc. + NULL int. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69},
	/* User Plane w/NULL enc. + NULL int. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69},
	/* User Plane w/NULL enc. + SNOW f9 int. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		    0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9,
		    0x68, 0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13,
		    0x52, 0x08, 0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62,
		    0x31, 0xA2, 0x76, 0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A,
		    0xD2, 0xEE, 0xD6, 0x93, 0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA,
		    0x91},
	/* User Plane w/NULL enc. + SNOW f9 int. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69},
	/* User Plane w/NULL enc. + AES CMAC int. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		    0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9,
		    0x68, 0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13,
		    0x52, 0x08, 0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62,
		    0x31, 0xA2, 0x76, 0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A,
		    0xD2, 0xEE, 0xD6, 0x93, 0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA,
		    0x91},
	/* User Plane w/NULL enc. + AES CMAC int. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69},
	/* User Plane w/NULL enc. + ZUC int. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		    0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9,
		    0x68, 0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13,
		    0x52, 0x08, 0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62,
		    0x31, 0xA2, 0x76, 0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A,
		    0xD2, 0xEE, 0xD6, 0x93, 0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA,
		    0x91},
	/* User Plane w/NULL enc. + ZUC int. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69},
	/* User Plane w/SNOW f8 enc. + NULL int. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		    0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9,
		    0x68, 0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13,
		    0x52, 0x08, 0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62,
		    0x31, 0xA2, 0x76, 0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A,
		    0xD2, 0xEE, 0xD6, 0x93, 0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA,
		    0x91},
	/* User Plane w/SNOW f8 enc. + NULL int. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69},
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		    0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9,
		    0x68, 0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13,
		    0x52, 0x08, 0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62,
		    0x31, 0xA2, 0x76, 0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A,
		    0xD2, 0xEE, 0xD6, 0x93, 0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA,
		    0x91},
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69},
	/* User Plane w/SNOW f8 enc. + AES CMAC int. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		    0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9,
		    0x68, 0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13,
		    0x52, 0x08, 0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62,
		    0x31, 0xA2, 0x76, 0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A,
		    0xD2, 0xEE, 0xD6, 0x93, 0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA,
		    0x91},
	/* User Plane w/SNOW f8 enc. + AES CMAC int. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69},
	/* User Plane w/SNOW f8 enc. + ZUC int. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		    0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9,
		    0x68, 0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13,
		    0x52, 0x08, 0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62,
		    0x31, 0xA2, 0x76, 0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A,
		    0xD2, 0xEE, 0xD6, 0x93, 0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA,
		    0x91},
	/* User Plane w/SNOW f8 enc. + ZUC int. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69},
	/* User Plane w/AES CTR enc. + NULL int. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		    0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9,
		    0x68, 0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13,
		    0x52, 0x08, 0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62,
		    0x31, 0xA2, 0x76, 0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A,
		    0xD2, 0xEE, 0xD6, 0x93, 0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA,
		    0x91},
	/* User Plane w/AES CTR enc. + NULL int. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69},
	/* User Plane w/AES CTR enc. + SNOW f9 int. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		    0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9,
		    0x68, 0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13,
		    0x52, 0x08, 0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62,
		    0x31, 0xA2, 0x76, 0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A,
		    0xD2, 0xEE, 0xD6, 0x93, 0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA,
		    0x91},
	/* User Plane w/AES CTR enc. + SNOW f9 int. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69},
	/* User Plane w/AES CTR enc. + AES CMAC int. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		    0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9,
		    0x68, 0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13,
		    0x52, 0x08, 0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62,
		    0x31, 0xA2, 0x76, 0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A,
		    0xD2, 0xEE, 0xD6, 0x93, 0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA,
		    0x91},
	/* User Plane w/AES CTR enc. + AES CMAC int. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69},
	/* User Plane w/AES CTR enc. + ZUC int. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		    0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9,
		    0x68, 0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13,
		    0x52, 0x08, 0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62,
		    0x31, 0xA2, 0x76, 0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A,
		    0xD2, 0xEE, 0xD6, 0x93, 0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA,
		    0x91},
	/* User Plane w/AES CTR enc. + ZUC int. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69},
	/* User Plane w/ZUC enc. + NULL int. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		    0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9,
		    0x68, 0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13,
		    0x52, 0x08, 0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62,
		    0x31, 0xA2, 0x76, 0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A,
		    0xD2, 0xEE, 0xD6, 0x93, 0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA,
		    0x91},
	/* User Plane w/ZUC enc. + NULL int. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69},
	/* User Plane w/ZUC enc. + SNOW f9 int. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		    0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9,
		    0x68, 0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13,
		    0x52, 0x08, 0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62,
		    0x31, 0xA2, 0x76, 0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A,
		    0xD2, 0xEE, 0xD6, 0x93, 0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA,
		    0x91},
	/* User Plane w/ZUC enc. + SNOW f9 int. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69},
	/* User Plane w/ZUC enc. + AES CMAC int. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		    0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9,
		    0x68, 0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13,
		    0x52, 0x08, 0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62,
		    0x31, 0xA2, 0x76, 0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A,
		    0xD2, 0xEE, 0xD6, 0x93, 0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA,
		    0x91},
	/* User Plane w/ZUC enc. + AES CMAC int. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69},
	/* User Plane w/ZUC enc. + ZUC int. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		    0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9,
		    0x68, 0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13,
		    0x52, 0x08, 0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62,
		    0x31, 0xA2, 0x76, 0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A,
		    0xD2, 0xEE, 0xD6, 0x93, 0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA,
		    0x91},
	/* User Plane w/ZUC enc. + ZUC int. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69},
};

static uint32_t pdcp_test_data_in_len[] = {
	/* Control Plane w/NULL enc. + NULL int. UL */
	16,
	/* Control Plane w/NULL enc. + NULL int. DL */
	16,
	/* Control Plane w/NULL enc. + SNOW f9 int. UL */
	16,
	/* Control Plane w/NULL enc. + SNOW f9 int. DL */
	16,
	/* Control Plane w/NULL enc. + AES CMAC int. UL */
	16,
	/* Control Plane w/NULL enc. + AES CMAC int. DL */
	16,
	/* Control Plane w/NULL enc. + ZUC int. UL */
	16,
	/* Control Plane w/NULL enc. + ZUC int. DL */
	16,
	/* Control Plane w/SNOW f8 enc. + NULL int. UL */
	16,
	/* Control Plane w/SNOW f8 enc. + NULL int. DL */
	16,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. UL */
	16,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. DL */
	16,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. UL */
	16,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. DL */
	16,
	/* Control Plane w/SNOW f8 enc. + ZUC int. UL */
	16,
	/* Control Plane w/SNOW f8 enc. + ZUC int. DL */
	16,
	/* Control Plane w/AES CTR enc. + NULL int. UL */
	16,
	/* Control Plane w/AES CTR enc. + NULL int. DL */
	16,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. UL */
	16,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. DL */
	16,
	/* Control Plane w/AES CTR enc. + AES CMAC int. UL */
	16,
	/* Control Plane w/AES CTR enc. + AES CMAC int. DL */
	16,
	/* Control Plane w/AES CTR enc. + ZUC int. UL */
	16,
	/* Control Plane w/AES CTR enc. + ZUC int. DL */
	16,
	/* Control Plane w/ZUC enc. + NULL int. UL */
	16,
	/* Control Plane w/ZUC enc. + NULL int. DL */
	16,
	/* Control Plane w/ZUC enc. + SNOW f9 int. UL */
	16,
	/* Control Plane w/ZUC enc. + SNOW f9 int. DL */
	16,
	/* Control Plane w/ZUC enc. + AES CMAC int. UL */
	16,
	/* Control Plane w/ZUC enc. + AES CMAC int. DL */
	16,
	/* Control Plane w/ZUC enc. + ZUC int. UL */
	16,
	/* Control Plane w/ZUC enc. + ZUC int. DL */
	16,

	/****************** C-plane 12-bit SN ***********/
	/* Control Plane w/NULL enc. + NULL int. UL LONG SN */
	66,
	/* Control Plane w/NULL enc. + NULL int. DL LONG SN */
	66,
	/* Control Plane w/NULL enc. + SNOW f9 int. UL LONG SN */
	66,
	/* Control Plane w/NULL enc. + SNOW f9 int. DL LONG SN */
	66,
	/* Control Plane w/NULL enc. + AES CMAC int. UL LONG SN */
	66,
	/* Control Plane w/NULL enc. + AES CMAC int. DL LONG SN */
	66,
	/* Control Plane w/NULL enc. + ZUC int. UL LONG SN */
	66,
	/* Control Plane w/NULL enc. + ZUC int. DL LONG SN */
	66,

	/* Control Plane w/SNOW f8 enc. + NULL int. UL LONG SN */
	66,
	/* Control Plane w/SNOW f8 enc. + NULL int. DL LONG SN */
	66,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. UL LONG SN */
	66,
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. DL LONG SN */
	66,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. UL LONG SN */
	66,
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. DL */
	66,
	/* Control Plane w/SNOW f8 enc. + ZUC int. UL */
	66,
	/* Control Plane w/SNOW f8 enc. + ZUC int. DL */
	66,

	/* Control Plane w/AES CTR enc. + NULL int. UL */
	66,
	/* Control Plane w/AES CTR enc. + NULL int. DL */
	66,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. UL */
	66,
	/* Control Plane w/AES CTR enc. + SNOW f9 int. DL */
	66,
	/* Control Plane w/AES CTR enc. + AES CMAC int. UL */
	66,
	/* Control Plane w/AES CTR enc. + AES CMAC int. DL */
	66,
	/* Control Plane w/AES CTR enc. + ZUC int. UL */
	66,
	/* Control Plane w/AES CTR enc. + ZUC int. DL */
	66,

	/* Control Plane w/ZUC enc. + NULL int. UL */
	66,
	/* Control Plane w/ZUC enc. + NULL int. DL */
	66,
	/* Control Plane w/ZUC enc. + SNOW f9 int. UL */
	66,
	/* Control Plane w/ZUC enc. + SNOW f9 int. DL */
	66,
	/* Control Plane w/ZUC enc. + AES CMAC int. UL */
	66,
	/* Control Plane w/ZUC enc. + AES CMAC int. DL */
	66,
	/* Control Plane w/ZUC enc. + ZUC int. UL */
	66,
	/* Control Plane w/ZUC enc. + ZUC int. DL */
	66,


	/* User Plane w/NULL enc. UL LONG SN */
	17,
	/* User Plane w/NULL enc. DL LONG SN */
	17,
	/* User Plane w/NULL enc. UL SHORT SN */
	16,
	/* User Plane w/NULL enc. DL SHORT SN */
	16,
	/* User Plane w/NULL enc. UL 15 BIT SN */
	17,
	/* User Plane w/NULL enc. DL 15 BIT SN */
	17,
	/* User Plane w/NULL enc. UL 18 BIT SN */
	71,
	/* User Plane w/NULL enc. DL 18 BIT SN */
	71,
	/* User Plane w/SNOW f8 enc. UL LONG SN */
	17,
	/* User Plane w/SNOW f8 enc. DL LONG SN */
	17,
	/* User Plane w/SNOW f8 enc. UL SHORT SN */
	16,
	/* User Plane w/SNOW f8 enc. DL SHORT SN */
	16,
	/* User Plane w/SNOW f8 enc. UL 15 BIT SN */
	17,
	/* User Plane w/SNOW f8 enc. DL 15 BIT SN */
	17,
	/* User Plane w/SNOW f8 enc. UL 18 BIT SN */
	71,
	/* User Plane w/SNOW f8 enc. DL 18 BIT SN */
	71,
	/* User Plane w/AES CTR enc. UL LONG SN */
	17,
	/* User Plane w/AES CTR enc. DL LONG SN */
	17,
	/* User Plane w/AES CTR enc. UL SHORT SN */
	16,
	/* User Plane w/AES CTR enc. DL SHORT SN */
	16,
	/* User Plane w/AES CTR enc. UL 15 BIT SN */
	17,
	/* User Plane w/AES CTR enc. DL 15 BIT SN */
	17,
	/* User Plane w/AES CTR enc. UL 18 BIT SN */
	71,
	/* User Plane w/AES CTR enc. DL 18 BIT SN */
	71,
	/* User Plane w/ZUC enc. UL LONG SN */
	17,
	/* User Plane w/ZUC enc. DL LONG SN */
	17,
	/* User Plane w/ZUC enc. UL SHORT SN */
	16,
	/* User Plane w/ZUC enc. DL SHORT SN */
	16,
	/* User Plane w/ZUC enc. UL 15 BIT SN */
	17,
	/* User Plane w/ZUC enc. DL 15 BIT SN */
	17,
	/* User Plane w/ZUC enc. UL 18 BIT SN */
	71,
	/* User Plane w/ZUC enc. DL 18 BIT SN */
	71,

	/***** u-plane with integrity for 12-bit SN *******/

	/* User Plane w/NULL enc. + NULL int. UL for 12-bit SN */
	66,
	/* User Plane w/NULL enc. + NULL int. DL for 12-bit SN */
	66,
	/* User Plane w/NULL enc. + SNOW f9 int. UL for 12-bit SN */
	66,
	/* User Plane w/NULL enc. + SNOW f9 int. DL for 12-bit SN */
	66,
	/* User Plane w/NULL enc. + AES CMAC int. UL for 12-bit SN */
	66,
	/* User Plane w/NULL enc. + AES CMAC int. DL for 12-bit SN */
	66,
	/* User Plane w/NULL enc. + ZUC int. UL for 12-bit SN */
	66,
	/* User Plane w/NULL enc. + ZUC int. DL for 12-bit SN */
	66,

	/* User Plane w/SNOW f8 enc. + NULL int. UL for 12-bit SN */
	66,
	/* User Plane w/SNOW f8 enc. + NULL int. DL for 12-bit SN */
	66,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. UL for 12-bit SN */
	66,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. DL for 12-bit SN */
	66,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. UL for 12-bit SN */
	66,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. DL for 12-bit SN*/
	66,
	/* User Plane w/SNOW f8 enc. + ZUC int. UL for 12-bit SN*/
	66,
	/* User Plane w/SNOW f8 enc. + ZUC int. DL for 12-bit SN*/
	66,

	/* User Plane w/AES CTR enc. + NULL int. UL for 12-bit SN*/
	66,
	/* User Plane w/AES CTR enc. + NULL int. DL for 12-bit SN*/
	66,
	/* User Plane w/AES CTR enc. + SNOW f9 int. UL for 12-bit SN*/
	66,
	/* User Plane w/AES CTR enc. + SNOW f9 int. DL for 12-bit SN*/
	66,
	/* User Plane w/AES CTR enc. + AES CMAC int. UL for 12-bit SN*/
	66,
	/* User Plane w/AES CTR enc. + AES CMAC int. DL for 12-bit SN*/
	66,
	/* User Plane w/AES CTR enc. + ZUC int. UL for 12-bit SN*/
	66,
	/* User Plane w/AES CTR enc. + ZUC int. DL for 12-bit SN*/
	66,

	/* User Plane w/ZUC enc. + NULL int. UL for 12-bit SN*/
	66,
	/* User Plane w/ZUC enc. + NULL int. DL for 12-bit SN*/
	66,
	/* User Plane w/ZUC enc. + SNOW f9 int. UL for 12-bit SN*/
	66,
	/* User Plane w/ZUC enc. + SNOW f9 int. DL for 12-bit SN*/
	66,
	/* User Plane w/ZUC enc. + AES CMAC int. UL for 12-bit SN*/
	66,
	/* User Plane w/ZUC enc. + AES CMAC int. DL for 12-bit SN*/
	66,
	/* User Plane w/ZUC enc. + ZUC int. UL for 12-bit SN*/
	66,
	/* User Plane w/ZUC enc. + ZUC int. DL for 12-bit SN*/
	66,

	/***** u-plane with integrity for 18-bit SN *******/
	/* User Plane w/NULL enc. + NULL int. UL for 18-bit SN */
	67,
	/* User Plane w/NULL enc. + NULL int. DL for 18-bit SN */
	67,
	/* User Plane w/NULL enc. + SNOW f9 int. UL for 18-bit SN */
	67,
	/* User Plane w/NULL enc. + SNOW f9 int. DL for 18-bit SN */
	67,
	/* User Plane w/NULL enc. + AES CMAC int. UL for 18-bit SN */
	67,
	/* User Plane w/NULL enc. + AES CMAC int. DL for 18-bit SN */
	67,
	/* User Plane w/NULL enc. + ZUC int. UL for 18-bit SN */
	67,
	/* User Plane w/NULL enc. + ZUC int. DL for 18-bit SN */
	67,
	/* User Plane w/SNOW f8 enc. + NULL int. UL for 18-bit SN */
	67,
	/* User Plane w/SNOW f8 enc. + NULL int. DL for 18-bit SN */
	67,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. UL for 18-bit SN */
	67,
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. DL for 18-bit SN */
	67,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. UL for 18-bit SN */
	67,
	/* User Plane w/SNOW f8 enc. + AES CMAC int. DL for 18-bit SN*/
	67,
	/* User Plane w/SNOW f8 enc. + ZUC int. UL for 18-bit SN*/
	67,
	/* User Plane w/SNOW f8 enc. + ZUC int. DL for 18-bit SN*/
	67,
	/* User Plane w/AES CTR enc. + NULL int. UL for 18-bit SN*/
	67,
	/* User Plane w/AES CTR enc. + NULL int. DL for 18-bit SN*/
	67,
	/* User Plane w/AES CTR enc. + SNOW f9 int. UL for 18-bit SN*/
	67,
	/* User Plane w/AES CTR enc. + SNOW f9 int. DL for 18-bit SN*/
	67,
	/* User Plane w/AES CTR enc. + AES CMAC int. UL for 18-bit SN*/
	67,
	/* User Plane w/AES CTR enc. + AES CMAC int. DL for 18-bit SN*/
	67,
	/* User Plane w/AES CTR enc. + ZUC int. UL for 18-bit SN*/
	67,
	/* User Plane w/AES CTR enc. + ZUC int. DL for 18-bit SN*/
	67,
	/* User Plane w/ZUC enc. + NULL int. UL for 18-bit SN*/
	67,
	/* User Plane w/ZUC enc. + NULL int. DL for 18-bit SN*/
	67,
	/* User Plane w/ZUC enc. + SNOW f9 int. UL for 18-bit SN*/
	67,
	/* User Plane w/ZUC enc. + SNOW f9 int. DL for 18-bit SN*/
	67,
	/* User Plane w/ZUC enc. + AES CMAC int. UL for 18-bit SN*/
	67,
	/* User Plane w/ZUC enc. + AES CMAC int. DL for 18-bit SN*/
	67,
	/* User Plane w/ZUC enc. + ZUC int. UL for 18-bit SN*/
	67,
	/* User Plane w/ZUC enc. + ZUC int. DL for 18-bit SN*/
	67,
};

static uint8_t *pdcp_test_data_out[] = {
	/* Control Plane w/NULL enc. + NULL int. UL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8, 0x00, 0x00, 0x00, 0x00},
	/* Control Plane w/NULL enc. + NULL int. DL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8, 0x00, 0x00, 0x00, 0x00},
	/* Control Plane w/NULL enc. + SNOW f9 int. UL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8, 0x88, 0x7f, 0x4e, 0x59},
	/* Control Plane w/NULL enc. + SNOW f9 int. DL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8, 0x9d, 0x9e, 0x45, 0x36},
	/* Control Plane w/NULL enc. + AES CMAC int. UL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8, 0xf3, 0xdd, 0x01, 0xdf},
	/* Control Plane w/NULL enc. + AES CMAC int. DL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8, 0x5d, 0x8e, 0x5d, 0x05},
	/* Control Plane w/NULL enc. + ZUC int. UL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8, 0x18, 0xc3, 0x2e, 0x66},
	/* Control Plane w/NULL enc. + ZUC int. DL */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8, 0x72, 0x44, 0xab, 0x64},
	/* Control Plane w/SNOW f8 enc. + NULL int. UL */
	(uint8_t[]){0x8b, 0x39, 0xd1, 0x2b, 0xbd, 0x2a, 0x4c, 0x91, 0x59, 0xff,
		    0xfa, 0xce, 0x68, 0xc0, 0x7c, 0x30, 0xd0, 0xc5, 0x08, 0x58},
	/* Control Plane w/SNOW f8 enc. + NULL int. DL */
	(uint8_t[]){0x8b, 0x26, 0xf3, 0x67, 0xf1, 0x42, 0x50, 0x1a, 0x85, 0x02,
		    0xb9, 0x00, 0xa8, 0x9b, 0xcf, 0x06, 0xd1, 0x2c, 0x86, 0x7c},
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. UL */
	(uint8_t[]){0x8b, 0x39, 0xd1, 0x2b, 0xbd, 0x2a, 0x4c, 0x91, 0x59, 0xff,
		    0xfa, 0xce, 0x68, 0xc0, 0x7c, 0x30, 0x58, 0xba, 0x46, 0x01},
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. DL */
	(uint8_t[]){0x8b, 0x26, 0xf3, 0x67, 0xf1, 0x42, 0x50, 0x1a, 0x85, 0x02,
		    0xb9, 0x00, 0xa8, 0x9b, 0xcf, 0x06, 0x4c, 0xb2, 0xc3, 0x4a},
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. UL */
	(uint8_t[]){0x8b, 0x39, 0xd1, 0x2b, 0xbd, 0x2a, 0x4c, 0x91, 0x59, 0xff,
		    0xfa, 0xce, 0x68, 0xc0, 0x7c, 0x30, 0x23, 0x18, 0x09, 0x87},
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. DL */
	(uint8_t[]){0x8b, 0x26, 0xf3, 0x67, 0xf1, 0x42, 0x50, 0x1a, 0x85, 0x02,
		    0xb9, 0x00, 0xa8, 0x9b, 0xcf, 0x06, 0x8c, 0xa2, 0xdb, 0x79},
	/* Control Plane w/SNOW f8 enc. + ZUC int. UL */
	(uint8_t[]){0x8b, 0x39, 0xd1, 0x2b, 0xbd, 0x2a, 0x4c, 0x91, 0x59, 0xff,
		    0xfa, 0xce, 0x68, 0xc0, 0x7c, 0x30, 0xc8, 0x06, 0x26, 0x3e},
	/* Control Plane w/SNOW f8 enc. + ZUC int. DL */
	(uint8_t[]){0x8b, 0x26, 0xf3, 0x67, 0xf1, 0x42, 0x50, 0x1a, 0x85, 0x02,
		    0xb9, 0x00, 0xa8, 0x9b, 0xcf, 0x06, 0xa3, 0x68, 0x2d, 0x18},
	/* Control Plane w/AES CTR enc. + NULL int. UL */
	(uint8_t[]){0x8b, 0x2c, 0x59, 0x74, 0xab, 0xdc, 0xd8, 0x36, 0xf6, 0x1b,
		    0x54, 0x8d, 0x46, 0x93, 0x1c, 0xff, 0x32, 0x4f, 0x1a, 0x6b},
	/* Control Plane w/AES CTR enc. + NULL int. DL */
	(uint8_t[]){0x8b, 0xf2, 0xb9, 0x9d, 0x96, 0x51, 0xcc, 0x1e, 0xe8, 0x55,
		    0x3e, 0x98, 0xc5, 0x58, 0xec, 0x4c, 0x92, 0x40, 0x52, 0x8e},
	/* Control Plane w/AES CTR enc. + SNOW f9 int. UL */
	(uint8_t[]){0x8b, 0x2c, 0x59, 0x74, 0xab, 0xdc, 0xd8, 0x36, 0xf6, 0x1b,
		    0x54, 0x8d, 0x46, 0x93, 0x1c, 0xff, 0xba, 0x30, 0x54, 0x32},
	/* Control Plane w/AES CTR enc. + SNOW f9 int. DL */
	(uint8_t[]){0x8b, 0xf2, 0xb9, 0x9d, 0x96, 0x51, 0xcc, 0x1e, 0xe8, 0x55,
		    0x3e, 0x98, 0xc5, 0x58, 0xec, 0x4c, 0x0f, 0xde, 0x17, 0xb8},
	/* Control Plane w/AES CTR enc. + AES CMAC int. UL */
	(uint8_t[]){0x8b, 0x2c, 0x59, 0x74, 0xab, 0xdc, 0xd8, 0x36, 0xf6, 0x1b,
		    0x54, 0x8d, 0x46, 0x93, 0x1c, 0xff, 0xc1, 0x92, 0x1b, 0xb4},
	/* Control Plane w/AES CTR enc. + AES CMAC int. DL */
	(uint8_t[]){0x8b, 0xf2, 0xb9, 0x9d, 0x96, 0x51, 0xcc, 0x1e, 0xe8, 0x55,
		    0x3e, 0x98, 0xc5, 0x58, 0xec, 0x4c, 0xcf, 0xce, 0x0f, 0x8b},
	/* Control Plane w/AES CTR enc. + ZUC int. UL */
	(uint8_t[]){0x8b, 0x2c, 0x59, 0x74, 0xab, 0xdc, 0xd8, 0x36, 0xf6, 0x1b,
		    0x54, 0x8d, 0x46, 0x93, 0x1c, 0xff, 0x2a, 0x8c, 0x34, 0x0d},
	/* Control Plane w/AES CTR enc. + ZUC int. DL */
	(uint8_t[]){0x8b, 0xf2, 0xb9, 0x9d, 0x96, 0x51, 0xcc, 0x1e, 0xe8, 0x55,
		    0x3e, 0x98, 0xc5, 0x58, 0xec, 0x4c, 0xe0, 0x04, 0xf9, 0xea},
	/* Control Plane w/ZUC enc. + NULL int. UL */
	(uint8_t[]){0x8b, 0xa6, 0x23, 0xf8, 0xca, 0x98, 0x03, 0x33, 0x81, 0x8a,
		    0x6b, 0xfe, 0x37, 0xf2, 0x20, 0xd6, 0x68, 0x82, 0xb9, 0x06},
	/* Control Plane w/ZUC enc. + NULL int. DL */
	(uint8_t[]){0x8b, 0x3b, 0x42, 0xfc, 0x73, 0x83, 0x09, 0xb1, 0x3f, 0x66,
		    0x86, 0x3a, 0x5d, 0xe7, 0x47, 0xf4, 0x44, 0x81, 0x49, 0x0e},
	/* Control Plane w/ZUC enc. + SNOW f9 int. UL */
	(uint8_t[]){0x8b, 0xa6, 0x23, 0xf8, 0xca, 0x98, 0x03, 0x33, 0x81, 0x8a,
		    0x6b, 0xfe, 0x37, 0xf2, 0x20, 0xd6, 0xe0, 0xfd, 0xf7, 0x5f},
	/* Control Plane w/ZUC enc. + SNOW f9 int. DL */
	(uint8_t[]){0x8b, 0x3b, 0x42, 0xfc, 0x73, 0x83, 0x09, 0xb1, 0x3f, 0x66,
		    0x86, 0x3a, 0x5d, 0xe7, 0x47, 0xf4, 0xd9, 0x1f, 0x0c, 0x38},
	/* Control Plane w/ZUC enc. + AES CMAC int. UL */
	(uint8_t[]){0x8b, 0xa6, 0x23, 0xf8, 0xca, 0x98, 0x03, 0x33, 0x81, 0x8a,
		    0x6b, 0xfe, 0x37, 0xf2, 0x20, 0xd6, 0x9b, 0x5f, 0xb8, 0xd9},
	/* Control Plane w/ZUC enc. + AES CMAC int. DL */
	(uint8_t[]){0x8b, 0x3b, 0x42, 0xfc, 0x73, 0x83, 0x09, 0xb1, 0x3f, 0x66,
		    0x86, 0x3a, 0x5d, 0xe7, 0x47, 0xf4, 0x19, 0x0f, 0x14, 0x0b},
	/* Control Plane w/ZUC enc. + ZUC int. UL */
	(uint8_t[]){0x8b, 0xa6, 0x23, 0xf8, 0xca, 0x98, 0x03, 0x33, 0x81, 0x8a,
		    0x6b, 0xfe, 0x37, 0xf2, 0x20, 0xd6, 0x70, 0x41, 0x97, 0x60},
	/* Control Plane w/ZUC enc. + ZUC int. DL */
	(uint8_t[]){0x8b, 0x3b, 0x42, 0xfc, 0x73, 0x83, 0x09, 0xb1, 0x3f, 0x66,
		    0x86, 0x3a, 0x5d, 0xe7, 0x47, 0xf4, 0x36, 0xc5, 0xe2, 0x6a},

	/************ C-plane 12-bit ****************************/
	/* Control Plane w/NULL enc. + NULL int. UL LONG SN */
	(uint8_t[]){0x00, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82,
		    0x53, 0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71,
		    0xFB, 0xEB, 0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57,
		    0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70,
		    0x33, 0x8A, 0x15, 0xD0, 0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C,
		    0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01, 0x7F, 0x96, 0x46,
		    0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0x00, 0x00, 0x00, 0x00},
	/* Control Plane w/NULL enc. + NULL int. DL LONG SN */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82,
		    0x53, 0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71,
		    0xFB, 0xEB, 0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57,
		    0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70,
		    0x33, 0x8A, 0x15, 0xD0, 0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C,
		    0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01, 0x7F, 0x96, 0x46,
		    0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0x00, 0x00, 0x00, 0x00},
	/* Control Plane w/NULL enc. + SNOW f9 int. UL LONG SN */
	(uint8_t[]){0x00, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82,
		    0x53, 0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71,
		    0xFB, 0xEB, 0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57,
		    0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70,
		    0x33, 0x8A, 0x15, 0xD0, 0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C,
		    0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01, 0x7F, 0x96, 0x46,
		    0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0x33, 0x22, 0x02, 0x10},
	/* Control Plane w/NULL enc. + SNOW f9 int. DL LONG SN */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82,
		    0x53, 0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71,
		    0xFB, 0xEB, 0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57,
		    0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70,
		    0x33, 0x8A, 0x15, 0xD0, 0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C,
		    0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01, 0x7F, 0x96, 0x46,
		    0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0x97, 0x50, 0x3F, 0xF7},
	/* Control Plane w/NULL enc. + AES CMAC int. UL LONG SN */
	(uint8_t[]){0x00, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82,
		    0x53, 0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71,
		    0xFB, 0xEB, 0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57,
		    0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70,
		    0x33, 0x8A, 0x15, 0xD0, 0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C,
		    0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01, 0x7F, 0x96, 0x46,
		    0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0x1B, 0xB0, 0x4A, 0xBF},
	/* Control Plane w/NULL enc. + AES CMAC int. DL LONG SN */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82,
		    0x53, 0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71,
		    0xFB, 0xEB, 0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57,
		    0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70,
		    0x33, 0x8A, 0x15, 0xD0, 0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C,
		    0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01, 0x7F, 0x96, 0x46,
		    0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xE8, 0xBB, 0xE9, 0x36},
	/* Control Plane w/NULL enc. + ZUC int. UL LONG SN */
	(uint8_t[]){0x00, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82,
		    0x53, 0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71,
		    0xFB, 0xEB, 0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57,
		    0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70,
		    0x33, 0x8A, 0x15, 0xD0, 0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C,
		    0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01, 0x7F, 0x96, 0x46,
		    0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0x28, 0x41, 0xAB, 0x16},
	/* Control Plane w/NULL enc. + ZUC int. DL LONG SN */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82,
		    0x53, 0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71,
		    0xFB, 0xEB, 0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57,
		    0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70,
		    0x33, 0x8A, 0x15, 0xD0, 0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C,
		    0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01, 0x7F, 0x96, 0x46,
		    0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0x76, 0xD0, 0x5B, 0x2C},

	/* Control Plane w/SNOW f8 enc. + NULL int. UL LONG SN */
	(uint8_t[]){0x00, 0x01, 0xD6, 0xCC, 0xB5, 0xCE, 0x7C, 0xF8, 0xBE, 0x68,
		    0x2B, 0xAB, 0xC7, 0x32, 0xDA, 0x49, 0xD0, 0xC7, 0x54, 0xCA,
		    0x18, 0xBB, 0x05, 0x6D, 0xC5, 0x5F, 0xD3, 0xA7, 0xE6, 0xD8,
		    0xE1, 0xDF, 0x7C, 0x4F, 0x3C, 0x8B, 0x86, 0xC6, 0x8E, 0x24,
		    0xF7, 0xBC, 0x45, 0x2A, 0x2E, 0xB4, 0xF5, 0xD0, 0x39, 0x5B,
		    0x70, 0xB4, 0x53, 0x90, 0x98, 0x8A, 0x7C, 0x87, 0x21, 0xED,
		    0x76, 0x83, 0x63, 0x39, 0x2C, 0xDB, 0x12, 0x07, 0xAC, 0x93},
	/* Control Plane w/SNOW f8 enc. + NULL int. DL LONG SN */
	(uint8_t[]){0xA0, 0x00, 0xC1, 0x3A, 0x28, 0xBC, 0xEB, 0xAC, 0x49, 0xB9,
		    0xA1, 0xFC, 0xD6, 0x83, 0xEC, 0xA2, 0x89, 0xE6, 0x8F, 0xCA,
		    0x77, 0x62, 0xF8, 0x55, 0xC6, 0x8B, 0x25, 0x7B, 0xA3, 0xAF,
		    0x67, 0x6A, 0xEB, 0x45, 0x18, 0x0B, 0xD6, 0x03, 0xDD, 0xFC,
		    0xDE, 0x74, 0x3C, 0x4C, 0x7F, 0x5E, 0x67, 0x25, 0x9F, 0xC9,
		    0x0F, 0xD8, 0x38, 0xE6, 0x3F, 0xD4, 0x59, 0x7A, 0x9A, 0xB7,
		    0xF4, 0x52, 0xC6, 0x66, 0xC2, 0x73, 0xDC, 0x32, 0x96, 0x65},
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. UL LONG SN */
	(uint8_t[]){0x00, 0x01, 0xD6, 0xCC, 0xB5, 0xCE, 0x7C, 0xF8, 0xBE, 0x68,
		    0x2B, 0xAB, 0xC7, 0x32, 0xDA, 0x49, 0xD0, 0xC7, 0x54, 0xCA,
		    0x18, 0xBB, 0x05, 0x6D, 0xC5, 0x5F, 0xD3, 0xA7, 0xE6, 0xD8,
		    0xE1, 0xDF, 0x7C, 0x4F, 0x3C, 0x8B, 0x86, 0xC6, 0x8E, 0x24,
		    0xF7, 0xBC, 0x45, 0x2A, 0x2E, 0xB4, 0xF5, 0xD0, 0x39, 0x5B,
		    0x70, 0xB4, 0x53, 0x90, 0x98, 0x8A, 0x7C, 0x87, 0x21, 0xED,
		    0x76, 0x83, 0x63, 0x39, 0x2C, 0xDB, 0x21, 0x25, 0xAE, 0x83},
	/* Control Plane w/SNOW f8 enc. + SNOW f9 int. DL LONG SN */
	(uint8_t[]){0xA0, 0x00, 0xC1, 0x3A, 0x28, 0xBC, 0xEB, 0xAC, 0x49, 0xB9,
		    0xA1, 0xFC, 0xD6, 0x83, 0xEC, 0xA2, 0x89, 0xE6, 0x8F, 0xCA,
		    0x77, 0x62, 0xF8, 0x55, 0xC6, 0x8B, 0x25, 0x7B, 0xA3, 0xAF,
		    0x67, 0x6A, 0xEB, 0x45, 0x18, 0x0B, 0xD6, 0x03, 0xDD, 0xFC,
		    0xDE, 0x74, 0x3C, 0x4C, 0x7F, 0x5E, 0x67, 0x25, 0x9F, 0xC9,
		    0x0F, 0xD8, 0x38, 0xE6, 0x3F, 0xD4, 0x59, 0x7A, 0x9A, 0xB7,
		    0xF4, 0x52, 0xC6, 0x66, 0xC2, 0x73, 0x4B, 0x62, 0xA9, 0x92},
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. UL LONG SN */
	(uint8_t[]){0x00, 0x01, 0xD6, 0xCC, 0xB5, 0xCE, 0x7C, 0xF8, 0xBE, 0x68,
		    0x2B, 0xAB, 0xC7, 0x32, 0xDA, 0x49, 0xD0, 0xC7, 0x54, 0xCA,
		    0x18, 0xBB, 0x05, 0x6D, 0xC5, 0x5F, 0xD3, 0xA7, 0xE6, 0xD8,
		    0xE1, 0xDF, 0x7C, 0x4F, 0x3C, 0x8B, 0x86, 0xC6, 0x8E, 0x24,
		    0xF7, 0xBC, 0x45, 0x2A, 0x2E, 0xB4, 0xF5, 0xD0, 0x39, 0x5B,
		    0x70, 0xB4, 0x53, 0x90, 0x98, 0x8A, 0x7C, 0x87, 0x21, 0xED,
		    0x76, 0x83, 0x63, 0x39, 0x2C, 0xDB, 0x09, 0xB7, 0xE6, 0x2C},
	/* Control Plane w/SNOW f8 enc. + AES CMAC int. DL LONG SN */
	(uint8_t[]){0xA0, 0x00, 0xC1, 0x3A, 0x28, 0xBC, 0xEB, 0xAC, 0x49, 0xB9,
		    0xA1, 0xFC, 0xD6, 0x83, 0xEC, 0xA2, 0x89, 0xE6, 0x8F, 0xCA,
		    0x77, 0x62, 0xF8, 0x55, 0xC6, 0x8B, 0x25, 0x7B, 0xA3, 0xAF,
		    0x67, 0x6A, 0xEB, 0x45, 0x18, 0x0B, 0xD6, 0x03, 0xDD, 0xFC,
		    0xDE, 0x74, 0x3C, 0x4C, 0x7F, 0x5E, 0x67, 0x25, 0x9F, 0xC9,
		    0x0F, 0xD8, 0x38, 0xE6, 0x3F, 0xD4, 0x59, 0x7A, 0x9A, 0xB7,
		    0xF4, 0x52, 0xC6, 0x66, 0xC2, 0x73, 0x34, 0x89, 0x7F, 0x53},
	/* Control Plane w/SNOW f8 enc. + ZUC int. UL LONG SN */
	(uint8_t[]){0x00, 0x01, 0xD6, 0xCC, 0xB5, 0xCE, 0x7C, 0xF8, 0xBE, 0x68,
		    0x2B, 0xAB, 0xC7, 0x32, 0xDA, 0x49, 0xD0, 0xC7, 0x54, 0xCA,
		    0x18, 0xBB, 0x05, 0x6D, 0xC5, 0x5F, 0xD3, 0xA7, 0xE6, 0xD8,
		    0xE1, 0xDF, 0x7C, 0x4F, 0x3C, 0x8B, 0x86, 0xC6, 0x8E, 0x24,
		    0xF7, 0xBC, 0x45, 0x2A, 0x2E, 0xB4, 0xF5, 0xD0, 0x39, 0x5B,
		    0x70, 0xB4, 0x53, 0x90, 0x98, 0x8A, 0x7C, 0x87, 0x21, 0xED,
		    0x76, 0x83, 0x63, 0x39, 0x2C, 0xDB, 0x3A, 0x46, 0x07, 0x85},
	/* Control Plane w/SNOW f8 enc. + ZUC int. DL LONG SN */
	(uint8_t[]){0xA0, 0x00, 0xC1, 0x3A, 0x28, 0xBC, 0xEB, 0xAC, 0x49, 0xB9,
		    0xA1, 0xFC, 0xD6, 0x83, 0xEC, 0xA2, 0x89, 0xE6, 0x8F, 0xCA,
		    0x77, 0x62, 0xF8, 0x55, 0xC6, 0x8B, 0x25, 0x7B, 0xA3, 0xAF,
		    0x67, 0x6A, 0xEB, 0x45, 0x18, 0x0B, 0xD6, 0x03, 0xDD, 0xFC,
		    0xDE, 0x74, 0x3C, 0x4C, 0x7F, 0x5E, 0x67, 0x25, 0x9F, 0xC9,
		    0x0F, 0xD8, 0x38, 0xE6, 0x3F, 0xD4, 0x59, 0x7A, 0x9A, 0xB7,
		    0xF4, 0x52, 0xC6, 0x66, 0xC2, 0x73, 0xAA, 0xE2, 0xCD, 0x49},

	/* Control Plane w/AES CTR enc. + NULL int. UL LONG SN */
	(uint8_t[]){0x00, 0x01, 0x57, 0xB2, 0x7E, 0x21, 0xE7, 0xDD, 0x56, 0xCF,
		    0xE9, 0x97, 0x27, 0xE8, 0xA3, 0xDE, 0x4C, 0xF6, 0xD1, 0x10,
		    0x4A, 0x7D, 0xC0, 0xD0, 0xF7, 0x1B, 0x3E, 0x16, 0xF0, 0xA8,
		    0x4F, 0xBC, 0x17, 0x73, 0x9A, 0x69, 0x73, 0x6C, 0x83, 0xE5,
		    0x9D, 0x56, 0xBA, 0xF7, 0x08, 0x6D, 0xC5, 0x89, 0xFB, 0xAB,
		    0x99, 0xD1, 0x37, 0x42, 0x89, 0x8F, 0xE1, 0xAE, 0xA3, 0x22,
		    0x60, 0x98, 0xFD, 0x79, 0x32, 0xDB, 0x86, 0x33, 0x3F, 0x3C},

	/* Control Plane w/AES CTR enc. + NULL int. DL LONG SN */
	(uint8_t[]){0xA0, 0x00, 0x84, 0x3D, 0x5A, 0x2C, 0xBA, 0x02, 0xC1, 0x6C,
		    0x8D, 0x78, 0xB5, 0x1F, 0x51, 0x70, 0x18, 0x61, 0x92, 0x10,
		    0x18, 0xD1, 0x25, 0xB4, 0x4B, 0x24, 0x94, 0xAE, 0x6D, 0x45,
		    0xA7, 0x00, 0x01, 0xF8, 0x97, 0x9F, 0xF9, 0x58, 0xD6, 0x62,
		    0x30, 0x7D, 0xE9, 0x41, 0x69, 0x65, 0x1C, 0xBA, 0x79, 0x54,
		    0x7E, 0xF5, 0xBD, 0x60, 0xEB, 0x9E, 0xC2, 0xC9, 0x54, 0x65,
		    0x7D, 0xAC, 0xB6, 0x47, 0xFF, 0x1C, 0x87, 0x7A, 0x32, 0x1B},
	/* Control Plane w/AES CTR enc. + SNOW f9 int. UL LONG SN */
	(uint8_t[]){0x00, 0x01, 0x57, 0xB2, 0x7E, 0x21, 0xE7, 0xDD, 0x56, 0xCF,
		    0xE9, 0x97, 0x27, 0xE8, 0xA3, 0xDE, 0x4C, 0xF6, 0xD1, 0x10,
		    0x4A, 0x7D, 0xC0, 0xD0, 0xF7, 0x1B, 0x3E, 0x16, 0xF0, 0xA8,
		    0x4F, 0xBC, 0x17, 0x73, 0x9A, 0x69, 0x73, 0x6C, 0x83, 0xE5,
		    0x9D, 0x56, 0xBA, 0xF7, 0x08, 0x6D, 0xC5, 0x89, 0xFB, 0xAB,
		    0x99, 0xD1, 0x37, 0x42, 0x89, 0x8F, 0xE1, 0xAE, 0xA3, 0x22,
		    0x60, 0x98, 0xFD, 0x79, 0x32, 0xDB, 0xB5, 0x11, 0x3D, 0x2C},

	/* Control Plane w/AES CTR enc. + SNOW f9 int. DL LONG SN */
	(uint8_t[]){0xA0, 0x00, 0x84, 0x3D, 0x5A, 0x2C, 0xBA, 0x02, 0xC1, 0x6C,
		    0x8D, 0x78, 0xB5, 0x1F, 0x51, 0x70, 0x18, 0x61, 0x92, 0x10,
		    0x18, 0xD1, 0x25, 0xB4, 0x4B, 0x24, 0x94, 0xAE, 0x6D, 0x45,
		    0xA7, 0x00, 0x01, 0xF8, 0x97, 0x9F, 0xF9, 0x58, 0xD6, 0x62,
		    0x30, 0x7D, 0xE9, 0x41, 0x69, 0x65, 0x1C, 0xBA, 0x79, 0x54,
		    0x7E, 0xF5, 0xBD, 0x60, 0xEB, 0x9E, 0xC2, 0xC9, 0x54, 0x65,
		    0x7D, 0xAC, 0xB6, 0x47, 0xFF, 0x1C, 0x10, 0x2A, 0x0D, 0xEC},
	/* Control Plane w/AES CTR enc. + AES CMAC int. UL LONG SN */
	(uint8_t[]){0x00, 0x01, 0x57, 0xB2, 0x7E, 0x21, 0xE7, 0xDD, 0x56, 0xCF,
		    0xE9, 0x97, 0x27, 0xE8, 0xA3, 0xDE, 0x4C, 0xF6, 0xD1, 0x10,
		    0x4A, 0x7D, 0xC0, 0xD0, 0xF7, 0x1B, 0x3E, 0x16, 0xF0, 0xA8,
		    0x4F, 0xBC, 0x17, 0x73, 0x9A, 0x69, 0x73, 0x6C, 0x83, 0xE5,
		    0x9D, 0x56, 0xBA, 0xF7, 0x08, 0x6D, 0xC5, 0x89, 0xFB, 0xAB,
		    0x99, 0xD1, 0x37, 0x42, 0x89, 0x8F, 0xE1, 0xAE, 0xA3, 0x22,
		    0x60, 0x98, 0xFD, 0x79, 0x32, 0xDB, 0x9D, 0x83, 0x75, 0x83},
	/* Control Plane w/AES CTR enc. + AES CMAC int. DL LONG SN */
	(uint8_t[]){0xA0, 0x00, 0x84, 0x3D, 0x5A, 0x2C, 0xBA, 0x02, 0xC1, 0x6C,
		    0x8D, 0x78, 0xB5, 0x1F, 0x51, 0x70, 0x18, 0x61, 0x92, 0x10,
		    0x18, 0xD1, 0x25, 0xB4, 0x4B, 0x24, 0x94, 0xAE, 0x6D, 0x45,
		    0xA7, 0x00, 0x01, 0xF8, 0x97, 0x9F, 0xF9, 0x58, 0xD6, 0x62,
		    0x30, 0x7D, 0xE9, 0x41, 0x69, 0x65, 0x1C, 0xBA, 0x79, 0x54,
		    0x7E, 0xF5, 0xBD, 0x60, 0xEB, 0x9E, 0xC2, 0xC9, 0x54, 0x65,
		    0x7D, 0xAC, 0xB6, 0x47, 0xFF, 0x1C, 0x6F, 0xC1, 0xDB, 0x2D},
	/* Control Plane w/AES CTR enc. + ZUC int. UL LONG SN */
	(uint8_t[]){0x00, 0x01, 0x57, 0xB2, 0x7E, 0x21, 0xE7, 0xDD, 0x56, 0xCF,
		    0xE9, 0x97, 0x27, 0xE8, 0xA3, 0xDE, 0x4C, 0xF6, 0xD1, 0x10,
		    0x4A, 0x7D, 0xC0, 0xD0, 0xF7, 0x1B, 0x3E, 0x16, 0xF0, 0xA8,
		    0x4F, 0xBC, 0x17, 0x73, 0x9A, 0x69, 0x73, 0x6C, 0x83, 0xE5,
		    0x9D, 0x56, 0xBA, 0xF7, 0x08, 0x6D, 0xC5, 0x89, 0xFB, 0xAB,
		    0x99, 0xD1, 0x37, 0x42, 0x89, 0x8F, 0xE1, 0xAE, 0xA3, 0x22,
		    0x60, 0x98, 0xFD, 0x79, 0x32, 0xDB, 0xAE, 0x72, 0x94, 0x2A},
	/* Control Plane w/AES CTR enc. + ZUC int. DL LONG SN */
	(uint8_t[]){0xA0, 0x00, 0x84, 0x3D, 0x5A, 0x2C, 0xBA, 0x02, 0xC1, 0x6C,
		    0x8D, 0x78, 0xB5, 0x1F, 0x51, 0x70, 0x18, 0x61, 0x92, 0x10,
		    0x18, 0xD1, 0x25, 0xB4, 0x4B, 0x24, 0x94, 0xAE, 0x6D, 0x45,
		    0xA7, 0x00, 0x01, 0xF8, 0x97, 0x9F, 0xF9, 0x58, 0xD6, 0x62,
		    0x30, 0x7D, 0xE9, 0x41, 0x69, 0x65, 0x1C, 0xBA, 0x79, 0x54,
		    0x7E, 0xF5, 0xBD, 0x60, 0xEB, 0x9E, 0xC2, 0xC9, 0x54, 0x65,
		    0x7D, 0xAC, 0xB6, 0x47, 0xFF, 0x1C, 0xF1, 0xAA, 0x69, 0x37},
	/* Control Plane w/ZUC enc. + NULL int. UL LONG SN */
	(uint8_t[]){0x00, 0x01, 0x47, 0x9B, 0x21, 0xD1, 0xB2, 0x99, 0x23, 0x56,
		    0xC5, 0xFF, 0xC2, 0xB7, 0x7D, 0x30, 0xBA, 0xFB, 0x43, 0xED,
		    0x79, 0xC9, 0x9D, 0x9D, 0x38, 0x35, 0xC6, 0x7B, 0xD0, 0xAA,
		    0x33, 0x08, 0x88, 0x72, 0x16, 0x1D, 0xF7, 0xA0, 0xD9, 0xEC,
		    0x73, 0x45, 0x51, 0x87, 0xFF, 0x64, 0xFB, 0x3C, 0xA6, 0xB5,
		    0xD0, 0x1C, 0xD6, 0x90, 0x3D, 0x40, 0x54, 0x22, 0x2F, 0x6C,
		    0xE4, 0xB1, 0x71, 0x15, 0x78, 0x54, 0x1D, 0xCD, 0x3A, 0xE0},
	/* Control Plane w/ZUC enc. + NULL int. DL LONG SN */
	(uint8_t[]){0xA0, 0x00, 0x3F, 0x01, 0xCE, 0xBD, 0x8A, 0x98, 0x7B, 0x26,
		    0xF1, 0x28, 0x74, 0xDC, 0x26, 0x2B, 0x02, 0xE8, 0x9C, 0xBC,
		    0x98, 0x41, 0xC5, 0x03, 0x57, 0x48, 0x83, 0xBB, 0x8E, 0xCA,
		    0x87, 0xCD, 0x8B, 0xE9, 0x96, 0x2A, 0x35, 0x5C, 0xD3, 0x32,
		    0x53, 0xA7, 0x12, 0xC2, 0xBC, 0x0C, 0x75, 0x98, 0x8E, 0x29,
		    0x85, 0xAF, 0x0A, 0xFF, 0xAC, 0x6A, 0x00, 0x19, 0xC1, 0x51,
		    0x53, 0xDE, 0x78, 0x07, 0x6D, 0x10, 0x90, 0xF5, 0xBD, 0x56},
	/* Control Plane w/ZUC enc. + SNOW f9 int. UL LONG SN */
	(uint8_t[]){0x00, 0x01, 0x47, 0x9B, 0x21, 0xD1, 0xB2, 0x99, 0x23, 0x56,
		    0xC5, 0xFF, 0xC2, 0xB7, 0x7D, 0x30, 0xBA, 0xFB, 0x43, 0xED,
		    0x79, 0xC9, 0x9D, 0x9D, 0x38, 0x35, 0xC6, 0x7B, 0xD0, 0xAA,
		    0x33, 0x08, 0x88, 0x72, 0x16, 0x1D, 0xF7, 0xA0, 0xD9, 0xEC,
		    0x73, 0x45, 0x51, 0x87, 0xFF, 0x64, 0xFB, 0x3C, 0xA6, 0xB5,
		    0xD0, 0x1C, 0xD6, 0x90, 0x3D, 0x40, 0x54, 0x22, 0x2F, 0x6C,
		    0xE4, 0xB1, 0x71, 0x15, 0x78, 0x54, 0x2E, 0xEF, 0x38, 0xF0},
	/* Control Plane w/ZUC enc. + SNOW f9 int. DL LONG SN */
	(uint8_t[]){0xA0, 0x00, 0x3F, 0x01, 0xCE, 0xBD, 0x8A, 0x98, 0x7B, 0x26,
		    0xF1, 0x28, 0x74, 0xDC, 0x26, 0x2B, 0x02, 0xE8, 0x9C, 0xBC,
		    0x98, 0x41, 0xC5, 0x03, 0x57, 0x48, 0x83, 0xBB, 0x8E, 0xCA,
		    0x87, 0xCD, 0x8B, 0xE9, 0x96, 0x2A, 0x35, 0x5C, 0xD3, 0x32,
		    0x53, 0xA7, 0x12, 0xC2, 0xBC, 0x0C, 0x75, 0x98, 0x8E, 0x29,
		    0x85, 0xAF, 0x0A, 0xFF, 0xAC, 0x6A, 0x00, 0x19, 0xC1, 0x51,
		    0x53, 0xDE, 0x78, 0x07, 0x6D, 0x10, 0x07, 0xA5, 0x82, 0xA1},
	/* Control Plane w/ZUC enc. + AES CMAC int. UL LONG SN */
	(uint8_t[]){0x00, 0x01, 0x47, 0x9B, 0x21, 0xD1, 0xB2, 0x99, 0x23, 0x56,
		    0xC5, 0xFF, 0xC2, 0xB7, 0x7D, 0x30, 0xBA, 0xFB, 0x43, 0xED,
		    0x79, 0xC9, 0x9D, 0x9D, 0x38, 0x35, 0xC6, 0x7B, 0xD0, 0xAA,
		    0x33, 0x08, 0x88, 0x72, 0x16, 0x1D, 0xF7, 0xA0, 0xD9, 0xEC,
		    0x73, 0x45, 0x51, 0x87, 0xFF, 0x64, 0xFB, 0x3C, 0xA6, 0xB5,
		    0xD0, 0x1C, 0xD6, 0x90, 0x3D, 0x40, 0x54, 0x22, 0x2F, 0x6C,
		    0xE4, 0xB1, 0x71, 0x15, 0x78, 0x54, 0x06, 0x7D, 0x70, 0x5F},
	/* Control Plane w/ZUC enc. + AES CMAC int. DL LONG SN */
	(uint8_t[]){0xA0, 0x00, 0x3F, 0x01, 0xCE, 0xBD, 0x8A, 0x98, 0x7B, 0x26,
		    0xF1, 0x28, 0x74, 0xDC, 0x26, 0x2B, 0x02, 0xE8, 0x9C, 0xBC,
		    0x98, 0x41, 0xC5, 0x03, 0x57, 0x48, 0x83, 0xBB, 0x8E, 0xCA,
		    0x87, 0xCD, 0x8B, 0xE9, 0x96, 0x2A, 0x35, 0x5C, 0xD3, 0x32,
		    0x53, 0xA7, 0x12, 0xC2, 0xBC, 0x0C, 0x75, 0x98, 0x8E, 0x29,
		    0x85, 0xAF, 0x0A, 0xFF, 0xAC, 0x6A, 0x00, 0x19, 0xC1, 0x51,
		    0x53, 0xDE, 0x78, 0x07, 0x6D, 0x10, 0x78, 0x4E, 0x54, 0x60},
	/* Control Plane w/ZUC enc. + ZUC int. UL LONG SN */
	(uint8_t[]){0x00, 0x01, 0x47, 0x9B, 0x21, 0xD1, 0xB2, 0x99, 0x23, 0x56,
		    0xC5, 0xFF, 0xC2, 0xB7, 0x7D, 0x30, 0xBA, 0xFB, 0x43, 0xED,
		    0x79, 0xC9, 0x9D, 0x9D, 0x38, 0x35, 0xC6, 0x7B, 0xD0, 0xAA,
		    0x33, 0x08, 0x88, 0x72, 0x16, 0x1D, 0xF7, 0xA0, 0xD9, 0xEC,
		    0x73, 0x45, 0x51, 0x87, 0xFF, 0x64, 0xFB, 0x3C, 0xA6, 0xB5,
		    0xD0, 0x1C, 0xD6, 0x90, 0x3D, 0x40, 0x54, 0x22, 0x2F, 0x6C,
		    0xE4, 0xB1, 0x71, 0x15, 0x78, 0x54, 0x35, 0x8C, 0x91, 0xF6},
	/* Control Plane w/ZUC enc. + ZUC int. DL LONG SN */
	(uint8_t[]){0xA0, 0x00, 0x3F, 0x01, 0xCE, 0xBD, 0x8A, 0x98, 0x7B, 0x26,
		    0xF1, 0x28, 0x74, 0xDC, 0x26, 0x2B, 0x02, 0xE8, 0x9C, 0xBC,
		    0x98, 0x41, 0xC5, 0x03, 0x57, 0x48, 0x83, 0xBB, 0x8E, 0xCA,
		    0x87, 0xCD, 0x8B, 0xE9, 0x96, 0x2A, 0x35, 0x5C, 0xD3, 0x32,
		    0x53, 0xA7, 0x12, 0xC2, 0xBC, 0x0C, 0x75, 0x98, 0x8E, 0x29,
		    0x85, 0xAF, 0x0A, 0xFF, 0xAC, 0x6A, 0x00, 0x19, 0xC1, 0x51,
		    0x53, 0xDE, 0x78, 0x07, 0x6D, 0x10, 0xE6, 0x25, 0xE6, 0x7A},

	/* User Plane w/NULL enc. UL LONG SN */
	(uint8_t[]){0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4,
		    0x57, 0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/NULL enc. DL LONG SN */
	(uint8_t[]){0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4,
		    0x57, 0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/NULL enc. UL SHORT SN */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/NULL enc. DL SHORT SN */
	(uint8_t[]){0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4, 0x57,
		    0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/NULL enc. UL 15 BIT SN */
	(uint8_t[]){0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4,
		    0x57, 0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/NULL enc. DL 15 BIT SN */
	(uint8_t[]){0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f, 0x89, 0x0b, 0x38, 0xc4,
		    0x57, 0xa4, 0x9d, 0x42, 0x14, 0x07, 0xe8},
	/* User Plane w/NULL enc. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69, 0x00, 0x00, 0x00, 0x00},
	/* User Plane w/NULL enc. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69, 0x00, 0x00, 0x00, 0x00},
	/* User Plane w/SNOW f8 enc. UL LONG SN */
	(uint8_t[]){0x8b, 0x26, 0x7a, 0xe0, 0x00, 0x07, 0x2a, 0xa6, 0xef, 0xdc,
		    0x75, 0xef, 0x2e, 0x27, 0x0f, 0x69, 0x3d},
	/* User Plane w/SNOW f8 enc. DL LONG SN */
	(uint8_t[]){0x8b, 0x26, 0x7e, 0xbb, 0x80, 0x20, 0xba, 0xef, 0xe7, 0xf7,
		    0xef, 0x69, 0x51, 0x85, 0x09, 0xa5, 0xab},
	/* User Plane w/SNOW f8 enc. UL SHORT SN */
	(uint8_t[]){0x8b, 0x80, 0xcf, 0xe5, 0x27, 0xe2, 0x88, 0x2a, 0xac, 0xc5,
		    0xaf, 0x49, 0x9b, 0x3e, 0x48, 0x89},
	/* User Plane w/SNOW f8 enc. DL SHORT SN */
	(uint8_t[]){0x8b, 0xe2, 0x51, 0x58, 0x88, 0xff, 0x1a, 0x00, 0xe4, 0x67,
		    0x05, 0x46, 0x24, 0x2f, 0x07, 0xb7},
	/* User Plane w/SNOW f8 enc. UL 15 BIT SN */
	(uint8_t[]){0x8b, 0x26, 0xbe, 0x72, 0x05, 0x78, 0x92, 0xec, 0xb1, 0x4f,
		    0xdd, 0x5d, 0xfc, 0x60, 0x2c, 0x9a, 0x85},
	/* User Plane w/SNOW f8 enc. DL 15 BIT SN */
	(uint8_t[]){0x8b, 0x26, 0x0b, 0x50, 0xf3, 0xff, 0x37, 0xe3, 0x6b, 0xaf,
		    0x08, 0xd8, 0xf6, 0x1f, 0xca, 0x6f, 0xbc},
	/* User Plane w/SNOW enc. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0x9A, 0xAF, 0x1D, 0x21, 0x2F, 0x48, 0xB2, 0x30,
		    0xCF, 0xBB, 0x8A, 0x2C, 0xB7, 0x57, 0xB6, 0x27, 0x89, 0x0D, 0x91,
		    0x03, 0x2C, 0x2B, 0x8D, 0x29, 0x4A, 0xBD, 0x8D, 0x48, 0xD2, 0x69,
		    0x37, 0xB1, 0xA1, 0x97, 0x12, 0xBD, 0x0A, 0x91, 0x4D, 0xEB, 0x76,
		    0xC8, 0x96, 0x7A, 0x0A, 0x25, 0x08, 0xEB, 0x41, 0x30, 0x00, 0x33,
		    0xC7, 0xFF, 0x33, 0x4E, 0xC1, 0xFE, 0x5C, 0x0F, 0x15, 0xE7, 0x9F,
		    0x31, 0x55, 0xDA, 0x18, 0x4D},
	/* User Plane w/SNOW enc. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0x22, 0x2D, 0x15, 0xBA, 0x95, 0xAC, 0x47, 0x5A,
		0xE3, 0x90, 0x82, 0xEA, 0xC2, 0x93, 0x80, 0x23, 0xE9, 0xAC, 0xEA, 0x5D,
		0xAA, 0x70, 0x42, 0x04, 0x7C, 0xE6, 0xA6, 0x1D, 0x91, 0xAE, 0x2E, 0x43,
		0x60, 0x39, 0x23, 0x06, 0xD2, 0x31, 0x73, 0x98, 0xF0, 0x61, 0x47, 0xB5,
		0xC4, 0xB0, 0xB8, 0x31, 0x50, 0x9E, 0x37, 0x15, 0x0E, 0x0D, 0x29, 0x9D,
		0xB3, 0x78, 0xFB, 0x9D, 0x5C, 0x90, 0xF8, 0x80, 0x53, 0x93, 0xEF, 0x7C},
	/* User Plane w/AES CTR enc. UL LONG SN */
	(uint8_t[]){0x8b, 0x26, 0xde, 0x0a, 0x59, 0xca, 0x7d, 0x93, 0xa3, 0xb5,
		    0xd2, 0x88, 0xb3, 0x04, 0xa2, 0x12, 0x09},
	/* User Plane w/AES CTR enc. DL LONG SN */
	(uint8_t[]){0x8b, 0x26, 0x69, 0x92, 0x25, 0xd8, 0xe9, 0xd5, 0xe9, 0x53,
		    0x60, 0x49, 0x9f, 0xe9, 0x8f, 0xbe, 0x6a},
	/* User Plane w/AES CTR enc. UL SHORT SN */
	(uint8_t[]){0x8b, 0x0f, 0xa1, 0xf2, 0x56, 0x6e, 0xee, 0x62, 0x1c, 0x62,
		    0x06, 0x7e, 0x38, 0x4a, 0x02, 0xa4},
	/* User Plane w/AES CTR enc. DL SHORT SN */
	(uint8_t[]){0x8b, 0x00, 0x8d, 0x50, 0x80, 0x30, 0xda, 0xc7, 0x14, 0xc5,
		    0xe0, 0xc8, 0xfb, 0x83, 0xd0, 0x73},
	/* User Plane w/AES CTR enc. UL 15 BIT SN */
	(uint8_t[]){0x8b, 0x26, 0xa1, 0x2e, 0xa3, 0x64, 0xa9, 0x81, 0xbc, 0xd3,
		    0x6f, 0xef, 0xee, 0x30, 0x71, 0x23, 0x85},
	/* User Plane w/AES CTR enc. DL 15 BIT SN */
	(uint8_t[]){0x8b, 0x26, 0xc7, 0xf2, 0x23, 0xb3, 0xbe, 0xc0, 0xdf, 0xc5,
		    0xed, 0x37, 0x35, 0x7c, 0x66, 0xa3, 0xf9},
	/* User Plane w/AES enc. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0xBF, 0x31, 0x94, 0xCF, 0x6E, 0x99, 0x84, 0x08,
		0xF1, 0x90, 0xC2, 0x22, 0xD0, 0xD2, 0x3D, 0x44, 0x75, 0x7F, 0xC5, 0x0F,
		0xAC, 0x7C, 0x18, 0x46, 0xA5, 0x3E, 0x2F, 0x0F, 0x26, 0x9E, 0x5A, 0x49,
		0xF7, 0xCB, 0x70, 0x17, 0xBC, 0x01, 0x1D, 0xA3, 0x65, 0x0E, 0x4B, 0x53,
		0x14, 0x73, 0x76, 0xDE, 0x54, 0xA0, 0xF9, 0x4C, 0xC2, 0x8F, 0x02, 0x88,
		0x36, 0xC7, 0xC4, 0x5A, 0x57, 0x7D, 0xA1, 0x0D, 0xC1, 0x66, 0x96, 0xC8},
	/* User Plane w/AES enc. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0x01, 0x0D, 0x4B, 0x5E, 0xD3, 0xCE, 0x96, 0xE1,
		0x9A, 0x9D, 0xB3, 0x01, 0xD6, 0x40, 0x50, 0x00, 0x6C, 0x63, 0xFD, 0x37,
		0xD9, 0xBB, 0x3B, 0x76, 0xE5, 0x7D, 0x3C, 0xFC, 0xE3, 0x9D, 0x45, 0x4A,
		0x07, 0x14, 0xD3, 0x14, 0xBC, 0x7E, 0x57, 0xAB, 0xB0, 0x8D, 0x8F, 0x42,
		0x39, 0x22, 0xB2, 0xF6, 0x5F, 0xBD, 0x58, 0xE3, 0xE0, 0xDB, 0xD5, 0x7F,
		0xFB, 0x78, 0x95, 0xE1, 0x5E, 0x36, 0xF8, 0x52, 0x98, 0x15, 0x68, 0x35},
	/* User Plane w/ZUC enc. UL LONG SN */
	(uint8_t[]){0x8b, 0x26, 0xfb, 0xb6, 0x0e, 0x81, 0xa1, 0x9e, 0xc8, 0xeb,
		    0x90, 0xa8, 0xc7, 0x0e, 0x27, 0xcb, 0xb0},
	/* User Plane w/ZUC enc. DL LONG SN */
	(uint8_t[]){0x8b, 0x26, 0x2f, 0x5d, 0xa4, 0x82, 0xfb, 0xce, 0x1f, 0x3a,
		    0xb5, 0x66, 0x60, 0x40, 0x65, 0x2b, 0x40},
	/* User Plane w/ZUC enc. UL SHORT SN */
	(uint8_t[]){0x8b, 0xcb, 0x75, 0x03, 0xd5, 0xed, 0xea, 0x73, 0x39, 0xf5,
		    0x07, 0x03, 0x04, 0x51, 0xc9, 0x5e},
	/* User Plane w/ZUC enc. DL SHORT SN */
	(uint8_t[]){0x8b, 0xe9, 0xd2, 0x49, 0x7f, 0xfd, 0x98, 0x9f, 0xc4, 0x6a,
		    0xcb, 0xe6, 0x4e, 0x21, 0x33, 0xd2},
	/* User Plane w/ZUC enc. UL 15 BIT SN */
	(uint8_t[]){0x8b, 0x26, 0x01, 0x0a, 0xba, 0x79, 0xf8, 0xe5, 0x9f, 0x22,
		    0x37, 0xab, 0x5c, 0x7e, 0xad, 0xd6, 0x6b},
	/* User Plane w/ZUC enc. DL 15 BIT SN */
	(uint8_t[]){0x8b, 0x26, 0xa3, 0x1a, 0x1e, 0x22, 0xf7, 0x17, 0x8a, 0xb5,
		    0x59, 0xd8, 0x2b, 0x13, 0xdd, 0x12, 0x4e},
	/* User Plane w/ZUC enc. UL for 18-bit SN*/
	(uint8_t[]){0x80, 0x00, 0x01, 0x32, 0xF9, 0x21, 0x1D, 0xBB, 0xF8, 0xE5, 0x7C,
		0x74, 0xC2, 0xD7, 0xFF, 0x74, 0x59, 0x3A, 0x69, 0xD1, 0x8B, 0x65, 0x98,
		0xB9, 0x3C, 0xFB, 0x63, 0xB1, 0x9E, 0xB7, 0xCA, 0x04, 0x68, 0xB9, 0xAB,
		0xA2, 0x5A, 0xAF, 0x15, 0x8E, 0x71, 0xED, 0xE4, 0xFA, 0x99, 0x79, 0xF9,
		0x51, 0x54, 0x82, 0x69, 0x4C, 0x45, 0x0B, 0xFA, 0x87, 0x4D, 0x97, 0x6E,
		0xB0, 0xC9, 0x06, 0x08, 0x6B, 0xFC, 0x4A, 0x85, 0x43, 0x62, 0x73, 0xD8},
	/* User Plane w/ZUC enc. DL for 18-bit SN*/
	(uint8_t[]){0xF8, 0x00, 0x00, 0x30, 0x62, 0x48, 0xC0, 0xB1, 0xED, 0x1F, 0x13,
		0x8A, 0x7A, 0x62, 0x40, 0x12, 0x35, 0x54, 0x03, 0x93, 0xBD, 0xE5, 0x88,
		0x51, 0x38, 0xB5, 0x89, 0xC6, 0xD3, 0xB5, 0x44, 0xC2, 0xB9, 0xB9, 0x59,
		0x7C, 0xEC, 0x71, 0xD8, 0x42, 0x01, 0x03, 0x3C, 0x0E, 0xBB, 0x7B, 0xDD,
		0x7D, 0x2D, 0xE0, 0x3C, 0xE3, 0x81, 0xAA, 0xEA, 0xCC, 0xD7, 0xFC, 0x46,
		0x07, 0x7C, 0x8E, 0x8E, 0x0E, 0x99, 0xB8, 0x31, 0x65, 0x17, 0xF6, 0xE3},

	/************************* 12-bit u-plane with int ************/
	/* User Plane w/NULL enc. + NULL int. UL for 12-bit SN */
	(uint8_t[]){0x80, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82,
		    0x53, 0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71,
		    0xFB, 0xEB, 0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57,
		    0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70,
		    0x33, 0x8A, 0x15, 0xD0, 0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C,
		    0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01, 0x7F, 0x96, 0x46,
		    0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0x00, 0x00, 0x00, 0x00},
	/* User Plane w/NULL enc. + NULL int. DL for 12-bit SN */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82,
		    0x53, 0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71,
		    0xFB, 0xEB, 0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57,
		    0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70,
		    0x33, 0x8A, 0x15, 0xD0, 0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C,
		    0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01, 0x7F, 0x96, 0x46,
		    0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0x00, 0x00, 0x00, 0x00},
	/* User Plane w/NULL enc. + SNOW f9 int. UL for 12-bit SN */
	(uint8_t[]){0x80, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82,
		    0x53, 0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71,
		    0xFB, 0xEB, 0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57,
		    0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70,
		    0x33, 0x8A, 0x15, 0xD0, 0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C,
		    0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01, 0x7F, 0x96, 0x46,
		    0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0x6A, 0x4D, 0xA1, 0xE0},
	/* User Plane w/NULL enc. + SNOW f9 int. DL for 12-bit SN */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82,
		    0x53, 0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71,
		    0xFB, 0xEB, 0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57,
		    0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70,
		    0x33, 0x8A, 0x15, 0xD0, 0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C,
		    0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01, 0x7F, 0x96, 0x46,
		    0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0x97, 0x50, 0x3F, 0xF7},
	/* User Plane w/NULL enc. + AES CMAC int. UL for 12-bit SN */
	(uint8_t[]){0x80, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82,
		    0x53, 0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71,
		    0xFB, 0xEB, 0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57,
		    0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70,
		    0x33, 0x8A, 0x15, 0xD0, 0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C,
		    0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01, 0x7F, 0x96, 0x46,
		    0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xB4, 0x36, 0x24, 0x75},
	/* User Plane w/NULL enc. + AES CMAC int. DL for 12-bit SN */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82,
		    0x53, 0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71,
		    0xFB, 0xEB, 0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57,
		    0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70,
		    0x33, 0x8A, 0x15, 0xD0, 0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C,
		    0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01, 0x7F, 0x96, 0x46,
		    0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xE8, 0xBB, 0xE9, 0x36},
	/* User Plane w/NULL enc. + ZUC int. UL for 12-bit SN */
	(uint8_t[]){0x80, 0x01, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82,
		    0x53, 0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71,
		    0xFB, 0xEB, 0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57,
		    0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70,
		    0x33, 0x8A, 0x15, 0xD0, 0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C,
		    0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01, 0x7F, 0x96, 0x46,
		    0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0x5B, 0x05, 0x40, 0x0B},
	/* User Plane w/NULL enc. + ZUC int. DL for 12-bit SN */
	(uint8_t[]){0xA0, 0x00, 0x86, 0xB8, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82,
		    0x53, 0xFD, 0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71,
		    0xFB, 0xEB, 0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57,
		    0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70,
		    0x33, 0x8A, 0x15, 0xD0, 0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C,
		    0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01, 0x7F, 0x96, 0x46,
		    0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0x76, 0xD0, 0x5B, 0x2C},

	/* User Plane w/SNOW f8 enc. + NULL int. UL for 12-bit SN */
	(uint8_t[]){0x80, 0x01, 0xD6, 0xCC, 0xB5, 0xCE, 0x7C, 0xF8, 0xBE, 0x68,
		    0x2B, 0xAB, 0xC7, 0x32, 0xDA, 0x49, 0xD0, 0xC7, 0x54, 0xCA,
		    0x18, 0xBB, 0x05, 0x6D, 0xC5, 0x5F, 0xD3, 0xA7, 0xE6, 0xD8,
		    0xE1, 0xDF, 0x7C, 0x4F, 0x3C, 0x8B, 0x86, 0xC6, 0x8E, 0x24,
		    0xF7, 0xBC, 0x45, 0x2A, 0x2E, 0xB4, 0xF5, 0xD0, 0x39, 0x5B,
		    0x70, 0xB4, 0x53, 0x90, 0x98, 0x8A, 0x7C, 0x87, 0x21, 0xED,
		    0x76, 0x83, 0x63, 0x39, 0x2C, 0xDB, 0x12, 0x07, 0xAC, 0x93},
	/* User Plane w/SNOW f8 enc. + NULL int. DL for 12-bit SN */
	(uint8_t[]){0xA0, 0x00, 0xC1, 0x3A, 0x28, 0xBC, 0xEB, 0xAC, 0x49, 0xB9,
		    0xA1, 0xFC, 0xD6, 0x83, 0xEC, 0xA2, 0x89, 0xE6, 0x8F, 0xCA,
		    0x77, 0x62, 0xF8, 0x55, 0xC6, 0x8B, 0x25, 0x7B, 0xA3, 0xAF,
		    0x67, 0x6A, 0xEB, 0x45, 0x18, 0x0B, 0xD6, 0x03, 0xDD, 0xFC,
		    0xDE, 0x74, 0x3C, 0x4C, 0x7F, 0x5E, 0x67, 0x25, 0x9F, 0xC9,
		    0x0F, 0xD8, 0x38, 0xE6, 0x3F, 0xD4, 0x59, 0x7A, 0x9A, 0xB7,
		    0xF4, 0x52, 0xC6, 0x66, 0xC2, 0x73, 0xDC, 0x32, 0x96, 0x65},
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. UL for 12-bit SN */
	(uint8_t[]){0x80, 0x01, 0xD6, 0xCC, 0xB5, 0xCE, 0x7C, 0xF8, 0xBE, 0x68,
		    0x2B, 0xAB, 0xC7, 0x32, 0xDA, 0x49, 0xD0, 0xC7, 0x54, 0xCA,
		    0x18, 0xBB, 0x05, 0x6D, 0xC5, 0x5F, 0xD3, 0xA7, 0xE6, 0xD8,
		    0xE1, 0xDF, 0x7C, 0x4F, 0x3C, 0x8B, 0x86, 0xC6, 0x8E, 0x24,
		    0xF7, 0xBC, 0x45, 0x2A, 0x2E, 0xB4, 0xF5, 0xD0, 0x39, 0x5B,
		    0x70, 0xB4, 0x53, 0x90, 0x98, 0x8A, 0x7C, 0x87, 0x21, 0xED,
		    0x76, 0x83, 0x63, 0x39, 0x2C, 0xDB, 0x78, 0x4A, 0x0D, 0x73},
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. DL for 12-bit SN */
	(uint8_t[]){0xA0, 0x00, 0xC1, 0x3A, 0x28, 0xBC, 0xEB, 0xAC, 0x49, 0xB9,
		    0xA1, 0xFC, 0xD6, 0x83, 0xEC, 0xA2, 0x89, 0xE6, 0x8F, 0xCA,
		    0x77, 0x62, 0xF8, 0x55, 0xC6, 0x8B, 0x25, 0x7B, 0xA3, 0xAF,
		    0x67, 0x6A, 0xEB, 0x45, 0x18, 0x0B, 0xD6, 0x03, 0xDD, 0xFC,
		    0xDE, 0x74, 0x3C, 0x4C, 0x7F, 0x5E, 0x67, 0x25, 0x9F, 0xC9,
		    0x0F, 0xD8, 0x38, 0xE6, 0x3F, 0xD4, 0x59, 0x7A, 0x9A, 0xB7,
		    0xF4, 0x52, 0xC6, 0x66, 0xC2, 0x73, 0x4B, 0x62, 0xA9, 0x92},
	/* User Plane w/SNOW f8 enc. + AES CMAC int. UL for 12-bit SN */
	(uint8_t[]){0x80, 0x01, 0xD6, 0xCC, 0xB5, 0xCE, 0x7C, 0xF8, 0xBE, 0x68,
		    0x2B, 0xAB, 0xC7, 0x32, 0xDA, 0x49, 0xD0, 0xC7, 0x54, 0xCA,
		    0x18, 0xBB, 0x05, 0x6D, 0xC5, 0x5F, 0xD3, 0xA7, 0xE6, 0xD8,
		    0xE1, 0xDF, 0x7C, 0x4F, 0x3C, 0x8B, 0x86, 0xC6, 0x8E, 0x24,
		    0xF7, 0xBC, 0x45, 0x2A, 0x2E, 0xB4, 0xF5, 0xD0, 0x39, 0x5B,
		    0x70, 0xB4, 0x53, 0x90, 0x98, 0x8A, 0x7C, 0x87, 0x21, 0xED,
		    0x76, 0x83, 0x63, 0x39, 0x2C, 0xDB, 0xA6, 0x31, 0x88, 0xE6},
	/* User Plane w/SNOW f8 enc. + AES CMAC int. DL for 12-bit SN */
	(uint8_t[]){0xA0, 0x00, 0xC1, 0x3A, 0x28, 0xBC, 0xEB, 0xAC, 0x49, 0xB9,
		    0xA1, 0xFC, 0xD6, 0x83, 0xEC, 0xA2, 0x89, 0xE6, 0x8F, 0xCA,
		    0x77, 0x62, 0xF8, 0x55, 0xC6, 0x8B, 0x25, 0x7B, 0xA3, 0xAF,
		    0x67, 0x6A, 0xEB, 0x45, 0x18, 0x0B, 0xD6, 0x03, 0xDD, 0xFC,
		    0xDE, 0x74, 0x3C, 0x4C, 0x7F, 0x5E, 0x67, 0x25, 0x9F, 0xC9,
		    0x0F, 0xD8, 0x38, 0xE6, 0x3F, 0xD4, 0x59, 0x7A, 0x9A, 0xB7,
		    0xF4, 0x52, 0xC6, 0x66, 0xC2, 0x73, 0x34, 0x89, 0x7F, 0x53},
	/* User Plane w/SNOW f8 enc. + ZUC int. UL for 12-bit SN */
	(uint8_t[]){0x80, 0x01, 0xD6, 0xCC, 0xB5, 0xCE, 0x7C, 0xF8, 0xBE, 0x68,
		    0x2B, 0xAB, 0xC7, 0x32, 0xDA, 0x49, 0xD0, 0xC7, 0x54, 0xCA,
		    0x18, 0xBB, 0x05, 0x6D, 0xC5, 0x5F, 0xD3, 0xA7, 0xE6, 0xD8,
		    0xE1, 0xDF, 0x7C, 0x4F, 0x3C, 0x8B, 0x86, 0xC6, 0x8E, 0x24,
		    0xF7, 0xBC, 0x45, 0x2A, 0x2E, 0xB4, 0xF5, 0xD0, 0x39, 0x5B,
		    0x70, 0xB4, 0x53, 0x90, 0x98, 0x8A, 0x7C, 0x87, 0x21, 0xED,
		    0x76, 0x83, 0x63, 0x39, 0x2C, 0xDB, 0x49, 0x02, 0xEC, 0x98},
	/* User Plane w/SNOW f8 enc. + ZUC int. DL for 12-bit SN */
	(uint8_t[]){0xA0, 0x00, 0xC1, 0x3A, 0x28, 0xBC, 0xEB, 0xAC, 0x49, 0xB9,
		    0xA1, 0xFC, 0xD6, 0x83, 0xEC, 0xA2, 0x89, 0xE6, 0x8F, 0xCA,
		    0x77, 0x62, 0xF8, 0x55, 0xC6, 0x8B, 0x25, 0x7B, 0xA3, 0xAF,
		    0x67, 0x6A, 0xEB, 0x45, 0x18, 0x0B, 0xD6, 0x03, 0xDD, 0xFC,
		    0xDE, 0x74, 0x3C, 0x4C, 0x7F, 0x5E, 0x67, 0x25, 0x9F, 0xC9,
		    0x0F, 0xD8, 0x38, 0xE6, 0x3F, 0xD4, 0x59, 0x7A, 0x9A, 0xB7,
		    0xF4, 0x52, 0xC6, 0x66, 0xC2, 0x73, 0xAA, 0xE2, 0xCD, 0x49},
	/* User Plane w/AES CTR enc. + NULL int. UL for 12-bit SN  */
	(uint8_t[]){0x80, 0x01, 0x57, 0xB2, 0x7E, 0x21, 0xE7, 0xDD, 0x56, 0xCF,
		    0xE9, 0x97, 0x27, 0xE8, 0xA3, 0xDE, 0x4C, 0xF6, 0xD1, 0x10,
		    0x4A, 0x7D, 0xC0, 0xD0, 0xF7, 0x1B, 0x3E, 0x16, 0xF0, 0xA8,
		    0x4F, 0xBC, 0x17, 0x73, 0x9A, 0x69, 0x73, 0x6C, 0x83, 0xE5,
		    0x9D, 0x56, 0xBA, 0xF7, 0x08, 0x6D, 0xC5, 0x89, 0xFB, 0xAB,
		    0x99, 0xD1, 0x37, 0x42, 0x89, 0x8F, 0xE1, 0xAE, 0xA3, 0x22,
		    0x60, 0x98, 0xFD, 0x79, 0x32, 0xDB, 0x86, 0x33, 0x3F, 0x3C},

	/* User Plane w/AES CTR enc. + NULL int. DL for 12-bit SN */
	(uint8_t[]){0xA0, 0x00, 0x84, 0x3D, 0x5A, 0x2C, 0xBA, 0x02, 0xC1, 0x6C,
		    0x8D, 0x78, 0xB5, 0x1F, 0x51, 0x70, 0x18, 0x61, 0x92, 0x10,
		    0x18, 0xD1, 0x25, 0xB4, 0x4B, 0x24, 0x94, 0xAE, 0x6D, 0x45,
		    0xA7, 0x00, 0x01, 0xF8, 0x97, 0x9F, 0xF9, 0x58, 0xD6, 0x62,
		    0x30, 0x7D, 0xE9, 0x41, 0x69, 0x65, 0x1C, 0xBA, 0x79, 0x54,
		    0x7E, 0xF5, 0xBD, 0x60, 0xEB, 0x9E, 0xC2, 0xC9, 0x54, 0x65,
		    0x7D, 0xAC, 0xB6, 0x47, 0xFF, 0x1C, 0x87, 0x7A, 0x32, 0x1B},
	/* User Plane w/AES CTR enc. + SNOW f9 int. UL for 12-bit SN */
	(uint8_t[]){0x80, 0x01, 0x57, 0xB2, 0x7E, 0x21, 0xE7, 0xDD, 0x56, 0xCF,
		    0xE9, 0x97, 0x27, 0xE8, 0xA3, 0xDE, 0x4C, 0xF6, 0xD1, 0x10,
		    0x4A, 0x7D, 0xC0, 0xD0, 0xF7, 0x1B, 0x3E, 0x16, 0xF0, 0xA8,
		    0x4F, 0xBC, 0x17, 0x73, 0x9A, 0x69, 0x73, 0x6C, 0x83, 0xE5,
		    0x9D, 0x56, 0xBA, 0xF7, 0x08, 0x6D, 0xC5, 0x89, 0xFB, 0xAB,
		    0x99, 0xD1, 0x37, 0x42, 0x89, 0x8F, 0xE1, 0xAE, 0xA3, 0x22,
		    0x60, 0x98, 0xFD, 0x79, 0x32, 0xDB, 0xEC, 0x7E, 0x9E, 0xDC},

	/* User Plane w/AES CTR enc. + SNOW f9 int. DL for 12-bit SN */
	(uint8_t[]){0xA0, 0x00, 0x84, 0x3D, 0x5A, 0x2C, 0xBA, 0x02, 0xC1, 0x6C,
		    0x8D, 0x78, 0xB5, 0x1F, 0x51, 0x70, 0x18, 0x61, 0x92, 0x10,
		    0x18, 0xD1, 0x25, 0xB4, 0x4B, 0x24, 0x94, 0xAE, 0x6D, 0x45,
		    0xA7, 0x00, 0x01, 0xF8, 0x97, 0x9F, 0xF9, 0x58, 0xD6, 0x62,
		    0x30, 0x7D, 0xE9, 0x41, 0x69, 0x65, 0x1C, 0xBA, 0x79, 0x54,
		    0x7E, 0xF5, 0xBD, 0x60, 0xEB, 0x9E, 0xC2, 0xC9, 0x54, 0x65,
		    0x7D, 0xAC, 0xB6, 0x47, 0xFF, 0x1C, 0x10, 0x2A, 0x0D, 0xEC},
	/* User Plane w/AES CTR enc. + AES CMAC int. UL for 12-bit SN */
	(uint8_t[]){0x80, 0x01, 0x57, 0xB2, 0x7E, 0x21, 0xE7, 0xDD, 0x56, 0xCF,
		    0xE9, 0x97, 0x27, 0xE8, 0xA3, 0xDE, 0x4C, 0xF6, 0xD1, 0x10,
		    0x4A, 0x7D, 0xC0, 0xD0, 0xF7, 0x1B, 0x3E, 0x16, 0xF0, 0xA8,
		    0x4F, 0xBC, 0x17, 0x73, 0x9A, 0x69, 0x73, 0x6C, 0x83, 0xE5,
		    0x9D, 0x56, 0xBA, 0xF7, 0x08, 0x6D, 0xC5, 0x89, 0xFB, 0xAB,
		    0x99, 0xD1, 0x37, 0x42, 0x89, 0x8F, 0xE1, 0xAE, 0xA3, 0x22,
		    0x60, 0x98, 0xFD, 0x79, 0x32, 0xDB, 0x32, 0x05, 0x1B, 0x49},
	/* User Plane w/AES CTR enc. + AES CMAC int. DL for 12-bit SN */
	(uint8_t[]){0xA0, 0x00, 0x84, 0x3D, 0x5A, 0x2C, 0xBA, 0x02, 0xC1, 0x6C,
		    0x8D, 0x78, 0xB5, 0x1F, 0x51, 0x70, 0x18, 0x61, 0x92, 0x10,
		    0x18, 0xD1, 0x25, 0xB4, 0x4B, 0x24, 0x94, 0xAE, 0x6D, 0x45,
		    0xA7, 0x00, 0x01, 0xF8, 0x97, 0x9F, 0xF9, 0x58, 0xD6, 0x62,
		    0x30, 0x7D, 0xE9, 0x41, 0x69, 0x65, 0x1C, 0xBA, 0x79, 0x54,
		    0x7E, 0xF5, 0xBD, 0x60, 0xEB, 0x9E, 0xC2, 0xC9, 0x54, 0x65,
		    0x7D, 0xAC, 0xB6, 0x47, 0xFF, 0x1C, 0x6F, 0xC1, 0xDB, 0x2D},
	/* User Plane w/AES CTR enc. + ZUC int. UL for 12-bit SN */
	(uint8_t[]){0x80, 0x01, 0x57, 0xB2, 0x7E, 0x21, 0xE7, 0xDD, 0x56, 0xCF,
		    0xE9, 0x97, 0x27, 0xE8, 0xA3, 0xDE, 0x4C, 0xF6, 0xD1, 0x10,
		    0x4A, 0x7D, 0xC0, 0xD0, 0xF7, 0x1B, 0x3E, 0x16, 0xF0, 0xA8,
		    0x4F, 0xBC, 0x17, 0x73, 0x9A, 0x69, 0x73, 0x6C, 0x83, 0xE5,
		    0x9D, 0x56, 0xBA, 0xF7, 0x08, 0x6D, 0xC5, 0x89, 0xFB, 0xAB,
		    0x99, 0xD1, 0x37, 0x42, 0x89, 0x8F, 0xE1, 0xAE, 0xA3, 0x22,
		    0x60, 0x98, 0xFD, 0x79, 0x32, 0xDB, 0xDD, 0x36, 0x7F, 0x37},
	/* User Plane w/AES CTR enc. + ZUC int. DL for 12-bit SN */
	(uint8_t[]){0xA0, 0x00, 0x84, 0x3D, 0x5A, 0x2C, 0xBA, 0x02, 0xC1, 0x6C,
		    0x8D, 0x78, 0xB5, 0x1F, 0x51, 0x70, 0x18, 0x61, 0x92, 0x10,
		    0x18, 0xD1, 0x25, 0xB4, 0x4B, 0x24, 0x94, 0xAE, 0x6D, 0x45,
		    0xA7, 0x00, 0x01, 0xF8, 0x97, 0x9F, 0xF9, 0x58, 0xD6, 0x62,
		    0x30, 0x7D, 0xE9, 0x41, 0x69, 0x65, 0x1C, 0xBA, 0x79, 0x54,
		    0x7E, 0xF5, 0xBD, 0x60, 0xEB, 0x9E, 0xC2, 0xC9, 0x54, 0x65,
		    0x7D, 0xAC, 0xB6, 0x47, 0xFF, 0x1C, 0xF1, 0xAA, 0x69, 0x37},

	/* User Plane w/ZUC enc. + NULL int. UL for 12-bit SN */
	(uint8_t[]){0x80, 0x01, 0x47, 0x9B, 0x21, 0xD1, 0xB2, 0x99, 0x23, 0x56,
		    0xC5, 0xFF, 0xC2, 0xB7, 0x7D, 0x30, 0xBA, 0xFB, 0x43, 0xED,
		    0x79, 0xC9, 0x9D, 0x9D, 0x38, 0x35, 0xC6, 0x7B, 0xD0, 0xAA,
		    0x33, 0x08, 0x88, 0x72, 0x16, 0x1D, 0xF7, 0xA0, 0xD9, 0xEC,
		    0x73, 0x45, 0x51, 0x87, 0xFF, 0x64, 0xFB, 0x3C, 0xA6, 0xB5,
		    0xD0, 0x1C, 0xD6, 0x90, 0x3D, 0x40, 0x54, 0x22, 0x2F, 0x6C,
		    0xE4, 0xB1, 0x71, 0x15, 0x78, 0x54, 0x1D, 0xCD, 0x3A, 0xE0},
	/* User Plane w/ZUC enc. + NULL int. DL for 12-bit SN */
	(uint8_t[]){0xA0, 0x00, 0x3F, 0x01, 0xCE, 0xBD, 0x8A, 0x98, 0x7B, 0x26,
		    0xF1, 0x28, 0x74, 0xDC, 0x26, 0x2B, 0x02, 0xE8, 0x9C, 0xBC,
		    0x98, 0x41, 0xC5, 0x03, 0x57, 0x48, 0x83, 0xBB, 0x8E, 0xCA,
		    0x87, 0xCD, 0x8B, 0xE9, 0x96, 0x2A, 0x35, 0x5C, 0xD3, 0x32,
		    0x53, 0xA7, 0x12, 0xC2, 0xBC, 0x0C, 0x75, 0x98, 0x8E, 0x29,
		    0x85, 0xAF, 0x0A, 0xFF, 0xAC, 0x6A, 0x00, 0x19, 0xC1, 0x51,
		    0x53, 0xDE, 0x78, 0x07, 0x6D, 0x10, 0x90, 0xF5, 0xBD, 0x56},
	/* User Plane w/ZUC enc. + SNOW f9 int. UL for 12-bit SN */
	(uint8_t[]){0x80, 0x01, 0x47, 0x9B, 0x21, 0xD1, 0xB2, 0x99, 0x23, 0x56,
		    0xC5, 0xFF, 0xC2, 0xB7, 0x7D, 0x30, 0xBA, 0xFB, 0x43, 0xED,
		    0x79, 0xC9, 0x9D, 0x9D, 0x38, 0x35, 0xC6, 0x7B, 0xD0, 0xAA,
		    0x33, 0x08, 0x88, 0x72, 0x16, 0x1D, 0xF7, 0xA0, 0xD9, 0xEC,
		    0x73, 0x45, 0x51, 0x87, 0xFF, 0x64, 0xFB, 0x3C, 0xA6, 0xB5,
		    0xD0, 0x1C, 0xD6, 0x90, 0x3D, 0x40, 0x54, 0x22, 0x2F, 0x6C,
		    0xE4, 0xB1, 0x71, 0x15, 0x78, 0x54, 0x77, 0x80, 0x9B, 0x00},
	/* User Plane w/ZUC enc. + SNOW f9 int. DL for 12-bit SN */
	(uint8_t[]){0xA0, 0x00, 0x3F, 0x01, 0xCE, 0xBD, 0x8A, 0x98, 0x7B, 0x26,
		    0xF1, 0x28, 0x74, 0xDC, 0x26, 0x2B, 0x02, 0xE8, 0x9C, 0xBC,
		    0x98, 0x41, 0xC5, 0x03, 0x57, 0x48, 0x83, 0xBB, 0x8E, 0xCA,
		    0x87, 0xCD, 0x8B, 0xE9, 0x96, 0x2A, 0x35, 0x5C, 0xD3, 0x32,
		    0x53, 0xA7, 0x12, 0xC2, 0xBC, 0x0C, 0x75, 0x98, 0x8E, 0x29,
		    0x85, 0xAF, 0x0A, 0xFF, 0xAC, 0x6A, 0x00, 0x19, 0xC1, 0x51,
		    0x53, 0xDE, 0x78, 0x07, 0x6D, 0x10, 0x07, 0xA5, 0x82, 0xA1},
	/* User Plane w/ZUC enc. + AES CMAC int. UL for 12-bit SN */
	(uint8_t[]){0x80, 0x01, 0x47, 0x9B, 0x21, 0xD1, 0xB2, 0x99, 0x23, 0x56,
		    0xC5, 0xFF, 0xC2, 0xB7, 0x7D, 0x30, 0xBA, 0xFB, 0x43, 0xED,
		    0x79, 0xC9, 0x9D, 0x9D, 0x38, 0x35, 0xC6, 0x7B, 0xD0, 0xAA,
		    0x33, 0x08, 0x88, 0x72, 0x16, 0x1D, 0xF7, 0xA0, 0xD9, 0xEC,
		    0x73, 0x45, 0x51, 0x87, 0xFF, 0x64, 0xFB, 0x3C, 0xA6, 0xB5,
		    0xD0, 0x1C, 0xD6, 0x90, 0x3D, 0x40, 0x54, 0x22, 0x2F, 0x6C,
		    0xE4, 0xB1, 0x71, 0x15, 0x78, 0x54, 0xA9, 0xFB, 0x1E, 0x95},
	/* User Plane w/ZUC enc. + AES CMAC int. DL for 12-bit SN */
	(uint8_t[]){0xA0, 0x00, 0x3F, 0x01, 0xCE, 0xBD, 0x8A, 0x98, 0x7B, 0x26,
		    0xF1, 0x28, 0x74, 0xDC, 0x26, 0x2B, 0x02, 0xE8, 0x9C, 0xBC,
		    0x98, 0x41, 0xC5, 0x03, 0x57, 0x48, 0x83, 0xBB, 0x8E, 0xCA,
		    0x87, 0xCD, 0x8B, 0xE9, 0x96, 0x2A, 0x35, 0x5C, 0xD3, 0x32,
		    0x53, 0xA7, 0x12, 0xC2, 0xBC, 0x0C, 0x75, 0x98, 0x8E, 0x29,
		    0x85, 0xAF, 0x0A, 0xFF, 0xAC, 0x6A, 0x00, 0x19, 0xC1, 0x51,
		    0x53, 0xDE, 0x78, 0x07, 0x6D, 0x10, 0x78, 0x4E, 0x54, 0x60},
	/* User Plane w/ZUC enc. + ZUC int. UL for 12-bit SN */
	(uint8_t[]){0x80, 0x01, 0x47, 0x9B, 0x21, 0xD1, 0xB2, 0x99, 0x23, 0x56,
		    0xC5, 0xFF, 0xC2, 0xB7, 0x7D, 0x30, 0xBA, 0xFB, 0x43, 0xED,
		    0x79, 0xC9, 0x9D, 0x9D, 0x38, 0x35, 0xC6, 0x7B, 0xD0, 0xAA,
		    0x33, 0x08, 0x88, 0x72, 0x16, 0x1D, 0xF7, 0xA0, 0xD9, 0xEC,
		    0x73, 0x45, 0x51, 0x87, 0xFF, 0x64, 0xFB, 0x3C, 0xA6, 0xB5,
		    0xD0, 0x1C, 0xD6, 0x90, 0x3D, 0x40, 0x54, 0x22, 0x2F, 0x6C,
		    0xE4, 0xB1, 0x71, 0x15, 0x78, 0x54, 0x46, 0xC8, 0x7A, 0xEB},
	/* User Plane w/ZUC enc. + ZUC int. DL for 12-bit SN */
	(uint8_t[]){0xA0, 0x00, 0x3F, 0x01, 0xCE, 0xBD, 0x8A, 0x98, 0x7B, 0x26,
		    0xF1, 0x28, 0x74, 0xDC, 0x26, 0x2B, 0x02, 0xE8, 0x9C, 0xBC,
		    0x98, 0x41, 0xC5, 0x03, 0x57, 0x48, 0x83, 0xBB, 0x8E, 0xCA,
		    0x87, 0xCD, 0x8B, 0xE9, 0x96, 0x2A, 0x35, 0x5C, 0xD3, 0x32,
		    0x53, 0xA7, 0x12, 0xC2, 0xBC, 0x0C, 0x75, 0x98, 0x8E, 0x29,
		    0x85, 0xAF, 0x0A, 0xFF, 0xAC, 0x6A, 0x00, 0x19, 0xC1, 0x51,
		    0x53, 0xDE, 0x78, 0x07, 0x6D, 0x10, 0xE6, 0x25, 0xE6, 0x7A},

	/************************* 18-bit u-plane with int ************/
	/* User Plane w/NULL enc. + NULL int. UL for 18-bit SN */
	(uint8_t[]){0x80, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69, 0x00, 0x00, 0x00, 0x00},
	/* User Plane w/NULL enc. + NULL int. DL for 18-bit SN */
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		    0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35,
		    0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91,
		    0xA3, 0x9C, 0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36,
		    0x47, 0x0E, 0x8F, 0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B,
		    0x01, 0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC,
		    0x69, 0x00, 0x00, 0x00, 0x00},
	/* User Plane w/NULL enc. + SNOW f9 int. UL for 18-bit SN */
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		    0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9,
		    0x68, 0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13,
		    0x52, 0x08, 0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62,
		    0x31, 0xA2, 0x76, 0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A,
		    0xD2, 0xEE, 0xD6, 0x93, 0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA,
		    0x91, 0x7F, 0x58, 0x24, 0x17},
	/* User Plane w/NULL enc. + SNOW f9 int. DL for 18-bit SN */
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35, 0xF3,
		0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C,
		0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36, 0x47, 0x0E, 0x8F,
		0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01, 0x7F, 0x96, 0x46,
		0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC, 0x69, 0x84, 0x45, 0xA8, 0x88},
	/* User Plane w/NULL enc. + AES CMAC int. UL for 18-bit SN */
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9, 0x68,
		0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13, 0x52, 0x08,
		0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62, 0x31, 0xA2, 0x76,
		0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A, 0xD2, 0xEE, 0xD6, 0x93,
		0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA, 0x91, 0x83, 0xB7, 0xF2, 0x0B},
	/* User Plane w/NULL enc. + AES CMAC int. DL for 18-bit SN */
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35, 0xF3,
		0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C,
		0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36, 0x47, 0x0E, 0x8F,
		0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01, 0x7F, 0x96, 0x46,
		0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC, 0x69, 0xD9, 0x0B, 0x89, 0x7F},
	/* User Plane w/NULL enc. + ZUC int. UL for 18-bit SN */
	(uint8_t[]){0x80, 0x00, 0x01, 0xB8, 0x33, 0x4F, 0x85, 0x8C, 0x2C, 0x65, 0x7D,
		0x8F, 0x5D, 0x40, 0x57, 0x60, 0x52, 0x4F, 0xB9, 0xF1, 0x69, 0xE9, 0x68,
		0x04, 0xFC, 0x7A, 0xBE, 0xD2, 0x5B, 0x4A, 0x21, 0x7F, 0x13, 0x52, 0x08,
		0xBA, 0xBD, 0x69, 0x51, 0xC9, 0x63, 0xCF, 0x06, 0x62, 0x31, 0xA2, 0x76,
		0xBA, 0xFC, 0x5A, 0xDB, 0xAA, 0xA3, 0x0B, 0x6A, 0xD2, 0xEE, 0xD6, 0x93,
		0xE4, 0x1B, 0x11, 0x4F, 0xC4, 0xD7, 0xDA, 0x91, 0xAB, 0x98, 0xC0, 0x1A},
	/* User Plane w/NULL enc. + ZUC int. DL for 18-bit SN */
	(uint8_t[]){0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F, 0x23, 0x82, 0x53, 0xFD,
		0x37, 0xDE, 0x88, 0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB, 0x35, 0xF3,
		0x64, 0xD3, 0x5E, 0xAF, 0x3F, 0x57, 0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C,
		0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15, 0xD0, 0x36, 0x47, 0x0E, 0x8F,
		0xEE, 0x2C, 0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01, 0x7F, 0x96, 0x46,
		0x53, 0xB0, 0xA4, 0x7A, 0xF9, 0xDD, 0xCC, 0x69, 0xDA, 0xE9, 0x17, 0x96},
	/* User Plane w/SNOW f8 enc. + NULL int. UL for 18-bit SN */
	(uint8_t[]){0x80, 0x00, 0x01, 0x9A, 0xAF, 0x1D, 0x21, 0x2F, 0x48, 0xB2, 0x30,
		    0xCF, 0xBB, 0x8A, 0x2C, 0xB7, 0x57, 0xB6, 0x27, 0x89, 0x0D, 0x91,
		    0x03, 0x2C, 0x2B, 0x8D, 0x29, 0x4A, 0xBD, 0x8D, 0x48, 0xD2, 0x69,
		    0x37, 0xB1, 0xA1, 0x97, 0x12, 0xBD, 0x0A, 0x91, 0x4D, 0xEB, 0x76,
		    0xC8, 0x96, 0x7A, 0x0A, 0x25, 0x08, 0xEB, 0x41, 0x30, 0x00, 0x33,
		    0xC7, 0xFF, 0x33, 0x4E, 0xC1, 0xFE, 0x5C, 0x0F, 0x15, 0xE7, 0x9F,
		    0x31, 0x55, 0xDA, 0x18, 0x4D},
	/* User Plane w/SNOW f8 enc. + NULL int. DL for 18-bit SN */
	(uint8_t[]){0xF8, 0x00, 0x00, 0x22, 0x2D, 0x15, 0xBA, 0x95, 0xAC, 0x47, 0x5A,
		0xE3, 0x90, 0x82, 0xEA, 0xC2, 0x93, 0x80, 0x23, 0xE9, 0xAC, 0xEA, 0x5D,
		0xAA, 0x70, 0x42, 0x04, 0x7C, 0xE6, 0xA6, 0x1D, 0x91, 0xAE, 0x2E, 0x43,
		0x60, 0x39, 0x23, 0x06, 0xD2, 0x31, 0x73, 0x98, 0xF0, 0x61, 0x47, 0xB5,
		0xC4, 0xB0, 0xB8, 0x31, 0x50, 0x9E, 0x37, 0x15, 0x0E, 0x0D, 0x29, 0x9D,
		0xB3, 0x78, 0xFB, 0x9D, 0x5C, 0x90, 0xF8, 0x80, 0x53, 0x93, 0xEF, 0x7C},
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. UL for 18-bit SN */
	(uint8_t[]){0x80, 0x00, 0x01, 0x9A, 0xAF, 0x1D, 0x21, 0x2F, 0x48, 0xB2, 0x30,
		0xCF, 0xBB, 0x8A, 0x2C, 0xB7, 0x57, 0xB6, 0x27, 0x89, 0x0D, 0x91, 0x03,
		0x2C, 0x2B, 0x8D, 0x29, 0x4A, 0xBD, 0x8D, 0x48, 0xD2, 0x69, 0x37, 0xB1,
		0xA1, 0x97, 0x12, 0xBD, 0x0A, 0x91, 0x4D, 0xEB, 0x76, 0xC8, 0x96, 0x7A,
		0x0A, 0x25, 0x08, 0xEB, 0x41, 0x30, 0x00, 0x33, 0xC7, 0xFF, 0x33, 0x4E,
		0xC1, 0xFE, 0x5C, 0x0F, 0x15, 0xE7, 0x9F, 0x31, 0x2A, 0x82, 0x3C, 0x5A},
	/* User Plane w/SNOW f8 enc. + SNOW f9 int. DL for 18-bit SN */
	(uint8_t[]){0xF8, 0x00, 0x00, 0x22, 0x2D, 0x15, 0xBA, 0x95, 0xAC, 0x47, 0x5A,
		0xE3, 0x90, 0x82, 0xEA, 0xC2, 0x93, 0x80, 0x23, 0xE9, 0xAC, 0xEA, 0x5D,
		0xAA, 0x70, 0x42, 0x04, 0x7C, 0xE6, 0xA6, 0x1D, 0x91, 0xAE, 0x2E, 0x43,
		0x60, 0x39, 0x23, 0x06, 0xD2, 0x31, 0x73, 0x98, 0xF0, 0x61, 0x47, 0xB5,
		0xC4, 0xB0, 0xB8, 0x31, 0x50, 0x9E, 0x37, 0x15, 0x0E, 0x0D, 0x29, 0x9D,
		0xB3, 0x78, 0xFB, 0x9D, 0x5C, 0x90, 0xF8, 0x80, 0xD7, 0xD6, 0x47, 0xF4},
	/* User Plane w/SNOW f8 enc. + AES CMAC int. UL for 18-bit SN */
	(uint8_t[]){0x80, 0x00, 0x01, 0x9A, 0xAF, 0x1D, 0x21, 0x2F, 0x48, 0xB2, 0x30,
		0xCF, 0xBB, 0x8A, 0x2C, 0xB7, 0x57, 0xB6, 0x27, 0x89, 0x0D, 0x91, 0x03,
		0x2C, 0x2B, 0x8D, 0x29, 0x4A, 0xBD, 0x8D, 0x48, 0xD2, 0x69, 0x37, 0xB1,
		0xA1, 0x97, 0x12, 0xBD, 0x0A, 0x91, 0x4D, 0xEB, 0x76, 0xC8, 0x96, 0x7A,
		0x0A, 0x25, 0x08, 0xEB, 0x41, 0x30, 0x00, 0x33, 0xC7, 0xFF, 0x33, 0x4E,
		0xC1, 0xFE, 0x5C, 0x0F, 0x15, 0xE7, 0x9F, 0x31, 0xD6, 0x6D, 0xEA, 0x46},
	/* User Plane w/SNOW f8 enc. + AES CMAC int. DL for 18-bit SN */
	(uint8_t[]){0xF8, 0x00, 0x00, 0x22, 0x2D, 0x15, 0xBA, 0x95, 0xAC, 0x47, 0x5A,
		0xE3, 0x90, 0x82, 0xEA, 0xC2, 0x93, 0x80, 0x23, 0xE9, 0xAC, 0xEA, 0x5D,
		0xAA, 0x70, 0x42, 0x04, 0x7C, 0xE6, 0xA6, 0x1D, 0x91, 0xAE, 0x2E, 0x43,
		0x60, 0x39, 0x23, 0x06, 0xD2, 0x31, 0x73, 0x98, 0xF0, 0x61, 0x47, 0xB5,
		0xC4, 0xB0, 0xB8, 0x31, 0x50, 0x9E, 0x37, 0x15, 0x0E, 0x0D, 0x29, 0x9D,
		0xB3, 0x78, 0xFB, 0x9D, 0x5C, 0x90, 0xF8, 0x80, 0x8A, 0x98, 0x66, 0x03},
	/* User Plane w/SNOW f8 enc. + ZUC int. UL for 18-bit SN */
	(uint8_t[]){0x80, 0x00, 0x01, 0x9A, 0xAF, 0x1D, 0x21, 0x2F, 0x48, 0xB2, 0x30,
		0xCF, 0xBB, 0x8A, 0x2C, 0xB7, 0x57, 0xB6, 0x27, 0x89, 0x0D, 0x91, 0x03,
		0x2C, 0x2B, 0x8D, 0x29, 0x4A, 0xBD, 0x8D, 0x48, 0xD2, 0x69, 0x37, 0xB1,
		0xA1, 0x97, 0x12, 0xBD, 0x0A, 0x91, 0x4D, 0xEB, 0x76, 0xC8, 0x96, 0x7A,
		0x0A, 0x25, 0x08, 0xEB, 0x41, 0x30, 0x00, 0x33, 0xC7, 0xFF, 0x33, 0x4E,
		0xC1, 0xFE, 0x5C, 0x0F, 0x15, 0xE7, 0x9F, 0x31, 0xFE, 0x42, 0xD8, 0x57},
	/* User Plane w/SNOW f8 enc. + ZUC int. DL for 18-bit SN */
	(uint8_t[]){0xF8, 0x00, 0x00, 0x22, 0x2D, 0x15, 0xBA, 0x95, 0xAC, 0x47, 0x5A,
		0xE3, 0x90, 0x82, 0xEA, 0xC2, 0x93, 0x80, 0x23, 0xE9, 0xAC, 0xEA, 0x5D,
		0xAA, 0x70, 0x42, 0x04, 0x7C, 0xE6, 0xA6, 0x1D, 0x91, 0xAE, 0x2E, 0x43,
		0x60, 0x39, 0x23, 0x06, 0xD2, 0x31, 0x73, 0x98, 0xF0, 0x61, 0x47, 0xB5,
		0xC4, 0xB0, 0xB8, 0x31, 0x50, 0x9E, 0x37, 0x15, 0x0E, 0x0D, 0x29, 0x9D,
		0xB3, 0x78, 0xFB, 0x9D, 0x5C, 0x90, 0xF8, 0x80, 0x89, 0x7A, 0xF8, 0xEA},
	/* User Plane w/AES CTR enc. + NULL int. UL for 18-bit SN  */
	(uint8_t[]){0x80, 0x00, 0x01, 0xBF, 0x31, 0x94, 0xCF, 0x6E, 0x99, 0x84, 0x08,
		0xF1, 0x90, 0xC2, 0x22, 0xD0, 0xD2, 0x3D, 0x44, 0x75, 0x7F, 0xC5, 0x0F,
		0xAC, 0x7C, 0x18, 0x46, 0xA5, 0x3E, 0x2F, 0x0F, 0x26, 0x9E, 0x5A, 0x49,
		0xF7, 0xCB, 0x70, 0x17, 0xBC, 0x01, 0x1D, 0xA3, 0x65, 0x0E, 0x4B, 0x53,
		0x14, 0x73, 0x76, 0xDE, 0x54, 0xA0, 0xF9, 0x4C, 0xC2, 0x8F, 0x02, 0x88,
		0x36, 0xC7, 0xC4, 0x5A, 0x57, 0x7D, 0xA1, 0x0D, 0xC1, 0x66, 0x96, 0xC8},
	/* User Plane w/AES CTR enc. + NULL int. DL for 18-bit SN */
	(uint8_t[]){0xF8, 0x00, 0x00, 0x01, 0x0D, 0x4B, 0x5E, 0xD3, 0xCE, 0x96, 0xE1,
		0x9A, 0x9D, 0xB3, 0x01, 0xD6, 0x40, 0x50, 0x00, 0x6C, 0x63, 0xFD, 0x37,
		0xD9, 0xBB, 0x3B, 0x76, 0xE5, 0x7D, 0x3C, 0xFC, 0xE3, 0x9D, 0x45, 0x4A,
		0x07, 0x14, 0xD3, 0x14, 0xBC, 0x7E, 0x57, 0xAB, 0xB0, 0x8D, 0x8F, 0x42,
		0x39, 0x22, 0xB2, 0xF6, 0x5F, 0xBD, 0x58, 0xE3, 0xE0, 0xDB, 0xD5, 0x7F,
		0xFB, 0x78, 0x95, 0xE1, 0x5E, 0x36, 0xF8, 0x52, 0x98, 0x15, 0x68, 0x35},
	/* User Plane w/AES CTR enc. + SNOW f9 int. UL for 18-bit SN */
	(uint8_t[]){0x80, 0x00, 0x01, 0xBF, 0x31, 0x94, 0xCF, 0x6E, 0x99, 0x84, 0x08,
		0xF1, 0x90, 0xC2, 0x22, 0xD0, 0xD2, 0x3D, 0x44, 0x75, 0x7F, 0xC5, 0x0F,
		0xAC, 0x7C, 0x18, 0x46, 0xA5, 0x3E, 0x2F, 0x0F, 0x26, 0x9E, 0x5A, 0x49,
		0xF7, 0xCB, 0x70, 0x17, 0xBC, 0x01, 0x1D, 0xA3, 0x65, 0x0E, 0x4B, 0x53,
		0x14, 0x73, 0x76, 0xDE, 0x54, 0xA0, 0xF9, 0x4C, 0xC2, 0x8F, 0x02, 0x88,
		0x36, 0xC7, 0xC4, 0x5A, 0x57, 0x7D, 0xA1, 0x0D, 0xBE, 0x3E, 0xB2, 0xDF},
	/* User Plane w/AES CTR enc. + SNOW f9 int. DL for 18-bit SN */
	(uint8_t[]){0xF8, 0x00, 0x00, 0x01, 0x0D, 0x4B, 0x5E, 0xD3, 0xCE, 0x96, 0xE1,
		0x9A, 0x9D, 0xB3, 0x01, 0xD6, 0x40, 0x50, 0x00, 0x6C, 0x63, 0xFD, 0x37,
		0xD9, 0xBB, 0x3B, 0x76, 0xE5, 0x7D, 0x3C, 0xFC, 0xE3, 0x9D, 0x45, 0x4A,
		0x07, 0x14, 0xD3, 0x14, 0xBC, 0x7E, 0x57, 0xAB, 0xB0, 0x8D, 0x8F, 0x42,
		0x39, 0x22, 0xB2, 0xF6, 0x5F, 0xBD, 0x58, 0xE3, 0xE0, 0xDB, 0xD5, 0x7F,
		0xFB, 0x78, 0x95, 0xE1, 0x5E, 0x36, 0xF8, 0x52, 0x1C, 0x50, 0xC0, 0xBD},
	/* User Plane w/AES CTR enc. + AES CMAC int. UL for 18-bit SN */
	(uint8_t[]){0x80, 0x00, 0x01, 0xBF, 0x31, 0x94, 0xCF, 0x6E, 0x99, 0x84, 0x08,
		0xF1, 0x90, 0xC2, 0x22, 0xD0, 0xD2, 0x3D, 0x44, 0x75, 0x7F, 0xC5, 0x0F,
		0xAC, 0x7C, 0x18, 0x46, 0xA5, 0x3E, 0x2F, 0x0F, 0x26, 0x9E, 0x5A, 0x49,
		0xF7, 0xCB, 0x70, 0x17, 0xBC, 0x01, 0x1D, 0xA3, 0x65, 0x0E, 0x4B, 0x53,
		0x14, 0x73, 0x76, 0xDE, 0x54, 0xA0, 0xF9, 0x4C, 0xC2, 0x8F, 0x02, 0x88,
		0x36, 0xC7, 0xC4, 0x5A, 0x57, 0x7D, 0xA1, 0x0D, 0x42, 0xD1, 0x64, 0xC3},
	/* User Plane w/AES CTR enc. + AES CMAC int. DL for 18-bit SN */
	(uint8_t[]){0xF8, 0x00, 0x00, 0x01, 0x0D, 0x4B, 0x5E, 0xD3, 0xCE, 0x96, 0xE1,
		0x9A, 0x9D, 0xB3, 0x01, 0xD6, 0x40, 0x50, 0x00, 0x6C, 0x63, 0xFD, 0x37,
		0xD9, 0xBB, 0x3B, 0x76, 0xE5, 0x7D, 0x3C, 0xFC, 0xE3, 0x9D, 0x45, 0x4A,
		0x07, 0x14, 0xD3, 0x14, 0xBC, 0x7E, 0x57, 0xAB, 0xB0, 0x8D, 0x8F, 0x42,
		0x39, 0x22, 0xB2, 0xF6, 0x5F, 0xBD, 0x58, 0xE3, 0xE0, 0xDB, 0xD5, 0x7F,
		0xFB, 0x78, 0x95, 0xE1, 0x5E, 0x36, 0xF8, 0x52, 0x41, 0x1E, 0xE1, 0x4A},
	/* User Plane w/AES CTR enc. + ZUC int. UL for 18-bit SN */
	(uint8_t[]){0x80, 0x00, 0x01, 0xBF, 0x31, 0x94, 0xCF, 0x6E, 0x99, 0x84, 0x08,
		0xF1, 0x90, 0xC2, 0x22, 0xD0, 0xD2, 0x3D, 0x44, 0x75, 0x7F, 0xC5, 0x0F,
		0xAC, 0x7C, 0x18, 0x46, 0xA5, 0x3E, 0x2F, 0x0F, 0x26, 0x9E, 0x5A, 0x49,
		0xF7, 0xCB, 0x70, 0x17, 0xBC, 0x01, 0x1D, 0xA3, 0x65, 0x0E, 0x4B, 0x53,
		0x14, 0x73, 0x76, 0xDE, 0x54, 0xA0, 0xF9, 0x4C, 0xC2, 0x8F, 0x02, 0x88,
		0x36, 0xC7, 0xC4, 0x5A, 0x57, 0x7D, 0xA1, 0x0D, 0x6A, 0xFE, 0x56, 0xD2},
	/* User Plane w/AES CTR enc. + ZUC int. DL for 18-bit SN */
	(uint8_t[]){0xF8, 0x00, 0x00, 0x01, 0x0D, 0x4B, 0x5E, 0xD3, 0xCE, 0x96, 0xE1,
		0x9A, 0x9D, 0xB3, 0x01, 0xD6, 0x40, 0x50, 0x00, 0x6C, 0x63, 0xFD, 0x37,
		0xD9, 0xBB, 0x3B, 0x76, 0xE5, 0x7D, 0x3C, 0xFC, 0xE3, 0x9D, 0x45, 0x4A,
		0x07, 0x14, 0xD3, 0x14, 0xBC, 0x7E, 0x57, 0xAB, 0xB0, 0x8D, 0x8F, 0x42,
		0x39, 0x22, 0xB2, 0xF6, 0x5F, 0xBD, 0x58, 0xE3, 0xE0, 0xDB, 0xD5, 0x7F,
		0xFB, 0x78, 0x95, 0xE1, 0x5E, 0x36, 0xF8, 0x52, 0x42, 0xFC, 0x7F, 0xA3},
	/* User Plane w/ZUC enc. + NULL int. UL for 18-bit SN */
	(uint8_t[]){0x80, 0x00, 0x01, 0x32, 0xF9, 0x21, 0x1D, 0xBB, 0xF8, 0xE5, 0x7C,
		0x74, 0xC2, 0xD7, 0xFF, 0x74, 0x59, 0x3A, 0x69, 0xD1, 0x8B, 0x65, 0x98,
		0xB9, 0x3C, 0xFB, 0x63, 0xB1, 0x9E, 0xB7, 0xCA, 0x04, 0x68, 0xB9, 0xAB,
		0xA2, 0x5A, 0xAF, 0x15, 0x8E, 0x71, 0xED, 0xE4, 0xFA, 0x99, 0x79, 0xF9,
		0x51, 0x54, 0x82, 0x69, 0x4C, 0x45, 0x0B, 0xFA, 0x87, 0x4D, 0x97, 0x6E,
		0xB0, 0xC9, 0x06, 0x08, 0x6B, 0xFC, 0x4A, 0x85, 0x43, 0x62, 0x73, 0xD8},
	/* User Plane w/ZUC enc. + NULL int. DL for 18-bit SN */
	(uint8_t[]){0xF8, 0x00, 0x00, 0x30, 0x62, 0x48, 0xC0, 0xB1, 0xED, 0x1F, 0x13,
		0x8A, 0x7A, 0x62, 0x40, 0x12, 0x35, 0x54, 0x03, 0x93, 0xBD, 0xE5, 0x88,
		0x51, 0x38, 0xB5, 0x89, 0xC6, 0xD3, 0xB5, 0x44, 0xC2, 0xB9, 0xB9, 0x59,
		0x7C, 0xEC, 0x71, 0xD8, 0x42, 0x01, 0x03, 0x3C, 0x0E, 0xBB, 0x7B, 0xDD,
		0x7D, 0x2D, 0xE0, 0x3C, 0xE3, 0x81, 0xAA, 0xEA, 0xCC, 0xD7, 0xFC, 0x46,
		0x07, 0x7C, 0x8E, 0x8E, 0x0E, 0x99, 0xB8, 0x31, 0x65, 0x17, 0xF6, 0xE3},
	/* User Plane w/ZUC enc. + SNOW f9 int. UL for 18-bit SN */
	(uint8_t[]){0x80, 0x00, 0x01, 0x32, 0xF9, 0x21, 0x1D, 0xBB, 0xF8, 0xE5, 0x7C,
		0x74, 0xC2, 0xD7, 0xFF, 0x74, 0x59, 0x3A, 0x69, 0xD1, 0x8B, 0x65, 0x98,
		0xB9, 0x3C, 0xFB, 0x63, 0xB1, 0x9E, 0xB7, 0xCA, 0x04, 0x68, 0xB9, 0xAB,
		0xA2, 0x5A, 0xAF, 0x15, 0x8E, 0x71, 0xED, 0xE4, 0xFA, 0x99, 0x79, 0xF9,
		0x51, 0x54, 0x82, 0x69, 0x4C, 0x45, 0x0B, 0xFA, 0x87, 0x4D, 0x97, 0x6E,
		0xB0, 0xC9, 0x06, 0x08, 0x6B, 0xFC, 0x4A, 0x85, 0x3C, 0x3A, 0x57, 0xCF},
	/* User Plane w/ZUC enc. + SNOW f9 int. DL for 18-bit SN */
	(uint8_t[]){0xF8, 0x00, 0x00, 0x30, 0x62, 0x48, 0xC0, 0xB1, 0xED, 0x1F, 0x13,
		0x8A, 0x7A, 0x62, 0x40, 0x12, 0x35, 0x54, 0x03, 0x93, 0xBD, 0xE5, 0x88,
		0x51, 0x38, 0xB5, 0x89, 0xC6, 0xD3, 0xB5, 0x44, 0xC2, 0xB9, 0xB9, 0x59,
		0x7C, 0xEC, 0x71, 0xD8, 0x42, 0x01, 0x03, 0x3C, 0x0E, 0xBB, 0x7B, 0xDD,
		0x7D, 0x2D, 0xE0, 0x3C, 0xE3, 0x81, 0xAA, 0xEA, 0xCC, 0xD7, 0xFC, 0x46,
		0x07, 0x7C, 0x8E, 0x8E, 0x0E, 0x99, 0xB8, 0x31, 0xE1, 0x52, 0x5E, 0x6B},
	/* User Plane w/ZUC enc. + AES CMAC int. UL for 18-bit SN */
	(uint8_t[]){0x80, 0x00, 0x01, 0x32, 0xF9, 0x21, 0x1D, 0xBB, 0xF8, 0xE5, 0x7C,
		0x74, 0xC2, 0xD7, 0xFF, 0x74, 0x59, 0x3A, 0x69, 0xD1, 0x8B, 0x65, 0x98,
		0xB9, 0x3C, 0xFB, 0x63, 0xB1, 0x9E, 0xB7, 0xCA, 0x04, 0x68, 0xB9, 0xAB,
		0xA2, 0x5A, 0xAF, 0x15, 0x8E, 0x71, 0xED, 0xE4, 0xFA, 0x99, 0x79, 0xF9,
		0x51, 0x54, 0x82, 0x69, 0x4C, 0x45, 0x0B, 0xFA, 0x87, 0x4D, 0x97, 0x6E,
		0xB0, 0xC9, 0x06, 0x08, 0x6B, 0xFC, 0x4A, 0x85, 0xC0, 0xD5, 0x81, 0xD3},
	/* User Plane w/ZUC enc. + AES CMAC int. DL for 18-bit SN */
	(uint8_t[]){0xF8, 0x00, 0x00, 0x30, 0x62, 0x48, 0xC0, 0xB1, 0xED, 0x1F, 0x13,
		0x8A, 0x7A, 0x62, 0x40, 0x12, 0x35, 0x54, 0x03, 0x93, 0xBD, 0xE5, 0x88,
		0x51, 0x38, 0xB5, 0x89, 0xC6, 0xD3, 0xB5, 0x44, 0xC2, 0xB9, 0xB9, 0x59,
		0x7C, 0xEC, 0x71, 0xD8, 0x42, 0x01, 0x03, 0x3C, 0x0E, 0xBB, 0x7B, 0xDD,
		0x7D, 0x2D, 0xE0, 0x3C, 0xE3, 0x81, 0xAA, 0xEA, 0xCC, 0xD7, 0xFC, 0x46,
		0x07, 0x7C, 0x8E, 0x8E, 0x0E, 0x99, 0xB8, 0x31, 0xBC, 0x1C, 0x7F, 0x9C},
	/* User Plane w/ZUC enc. + ZUC int. UL for 18-bit SN */
	(uint8_t[]){0x80, 0x00, 0x01, 0x32, 0xF9, 0x21, 0x1D, 0xBB, 0xF8, 0xE5, 0x7C,
		0x74, 0xC2, 0xD7, 0xFF, 0x74, 0x59, 0x3A, 0x69, 0xD1, 0x8B, 0x65, 0x98,
		0xB9, 0x3C, 0xFB, 0x63, 0xB1, 0x9E, 0xB7, 0xCA, 0x04, 0x68, 0xB9, 0xAB,
		0xA2, 0x5A, 0xAF, 0x15, 0x8E, 0x71, 0xED, 0xE4, 0xFA, 0x99, 0x79, 0xF9,
		0x51, 0x54, 0x82, 0x69, 0x4C, 0x45, 0x0B, 0xFA, 0x87, 0x4D, 0x97, 0x6E,
		0xB0, 0xC9, 0x06, 0x08, 0x6B, 0xFC, 0x4A, 0x85, 0xE8, 0xFA, 0xB3, 0xC2},
	/* User Plane w/ZUC enc. + ZUC int. DL for 18-bit SN */
	(uint8_t[]){0xF8, 0x00, 0x00, 0x30, 0x62, 0x48, 0xC0, 0xB1, 0xED, 0x1F, 0x13,
		0x8A, 0x7A, 0x62, 0x40, 0x12, 0x35, 0x54, 0x03, 0x93, 0xBD, 0xE5, 0x88,
		0x51, 0x38, 0xB5, 0x89, 0xC6, 0xD3, 0xB5, 0x44, 0xC2, 0xB9, 0xB9, 0x59,
		0x7C, 0xEC, 0x71, 0xD8, 0x42, 0x01, 0x03, 0x3C, 0x0E, 0xBB, 0x7B, 0xDD,
		0x7D, 0x2D, 0xE0, 0x3C, 0xE3, 0x81, 0xAA, 0xEA, 0xCC, 0xD7, 0xFC, 0x46,
		0x07, 0x7C, 0x8E, 0x8E, 0x0E, 0x99, 0xB8, 0x31, 0xBF, 0xFE, 0xE1, 0x75},
};

#endif /* SECURITY_PDCP_TEST_VECTOR_H_ */
