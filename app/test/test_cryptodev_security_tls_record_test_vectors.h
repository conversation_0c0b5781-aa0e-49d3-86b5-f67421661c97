/* SPDX-License-Identifier: BSD-3-Clause
 * Copyright(c) 2023 Marvell
 */

#ifndef _TEST_CRYPTODEV_SECURITY_TLS_RECORD_TEST_VECTORS_H_
#define _TEST_CRYPTODEV_SECURITY_TLS_RECORD_TEST_VECTORS_H_

#include <rte_crypto.h>
#include <rte_security.h>

#include "test_cryptodev.h"
#include "test_cryptodev_security_tls_record.h"

/* TLS 1.2 AES-GCM 128 vector generated with kTLS. */
struct tls_record_test_data tls_test_data_aes_128_gcm_v1 = {
	.key = {
		.data = {
			0x77, 0xc4, 0x9b, 0x0c, 0x2b, 0xe2, 0xd2, 0x4e,
			0xf5, 0x36, 0xb4, 0xea, 0x16, 0xb0, 0xed, 0x1f
		},
	},
	.input_text = {
		.data = {
			0x61, 0x62, 0x63, 0x64, 0x31, 0x32, 0x33, 0x34,
			0x61, 0x62, 0x63, 0x64, 0x31, 0x32, 0x33, 0x34,
			0xa
		},
		.len = 17,
	},
	.output_text = {
		.data = {
			0x17, 0x03, 0x03, 0x00, 0x29,
			0x3a, 0xcd, 0x41, 0x5d, 0x42, 0xac, 0xce, 0x36,
			0xad, 0xd1, 0x83, 0xa0, 0x16, 0x84, 0xce, 0x84,
			0xfc, 0xc2, 0xa7, 0x6b, 0xa1, 0x89, 0x5d, 0xf2,
			0xeb,
			0x9b, 0x90, 0x01, 0x56, 0xf8, 0x9d, 0xac, 0x0c,
			0xa8, 0x95, 0xba, 0xcc, 0x1f, 0xf3, 0x36, 0x2b
		},
		.len = 46,
	},
	.imp_nonce = {
		.data = {
			0xda, 0xf8, 0x8f, 0x12
		},
		.len = 4,
	},

	.iv = {
		.data = {
			0x3a, 0xcd, 0x41, 0x5d, 0x42, 0xac, 0xce, 0x36
		},
	},

	.xform = {
		.aead = {
			.next = NULL,
			.type = RTE_CRYPTO_SYM_XFORM_AEAD,
			.aead = {
				.op = RTE_CRYPTO_AEAD_OP_ENCRYPT,
				.algo = RTE_CRYPTO_AEAD_AES_GCM,
				.key.length = 16,
				.iv.length = 12,
				.iv.offset = IV_OFFSET,
				.digest_length = 16,
				.aad_length = 13,
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_TLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.tls_1_2.seq_no = 0x1,
	},

	.aead = true,
	.app_type = 0x17,
};

/* TLS 1.2 AES-128-GCM */
struct tls_record_test_data tls_test_data_aes_128_gcm_v2 = {
	.key = {
		.data = {
			0x64, 0xda, 0xc6, 0x34, 0xd4, 0x88, 0x59, 0x57,
			0x2d, 0x7c, 0xcc, 0x6d, 0xb2, 0x88, 0x40, 0x4c,
		},
	},
	.input_text = {
		.data = {
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x32, 0x20,
			0x41, 0x45, 0x53, 0x2d, 0x31, 0x32, 0x38, 0x2d,
			0x47, 0x43, 0x4d, 0x20, 0x65, 0x78, 0x61, 0x6d,
			0x70, 0x6c, 0x65, 0xa,
		},
		.len = 36,
	},
	.output_text = {
		.data = {
			0x17, 0x03, 0x03, 0x00, 0x3c,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01,
			0xb2, 0x8f, 0x1e, 0x8d, 0xe3, 0x67, 0xfd, 0xfa,
			0x68, 0x5a, 0xf4, 0x0f, 0x68, 0xdc, 0x5b, 0x6f,
			0x88, 0xd0, 0x1b, 0x3c, 0x84, 0xad, 0x01, 0xcf,
			0xd2, 0xab, 0x8b, 0x59, 0xbd, 0x66, 0x93, 0xd2,
			0x6f, 0xbd, 0xb2, 0xf0, 0xc7, 0xa3, 0x26, 0x78,
			0x35, 0xda, 0xe1, 0x4b, 0xb3, 0x3b, 0x21, 0x08,
			0xd2, 0x9b, 0x5c, 0x16,
		},
		.len = 65,
	},
	.imp_nonce = {
		.data = {
			0xdf, 0x13, 0xc5, 0x7c,
		},
		.len = 4,
	},
	.iv = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01
		},
	},

	.xform = {
		.aead = {
			.next = NULL,
			.type = RTE_CRYPTO_SYM_XFORM_AEAD,
			.aead = {
				.op = RTE_CRYPTO_AEAD_OP_ENCRYPT,
				.algo = RTE_CRYPTO_AEAD_AES_GCM,
				.key.length = 16,
				.iv.length = 12,
				.iv.offset = IV_OFFSET,
				.digest_length = 16,
				.aad_length = 13,
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_TLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.tls_1_2.seq_no = 0x1,
	},

	.aead = true,
	.app_type = 0x17,
};

/* TLS 1.2 AES-256-GCM */
struct tls_record_test_data tls_test_data_aes_256_gcm = {
	.key = {
		.data = {
			0x14, 0x8d, 0x98, 0x26, 0xc3, 0x9f, 0xae, 0x1a,
			0x52, 0x38, 0x7c, 0xfe, 0xcd, 0x2e, 0x7f, 0x28,
			0x1f, 0x11, 0x07, 0x02, 0x83, 0xa0, 0x24, 0x78,
			0x09, 0x83, 0x65, 0xb9, 0x99, 0x1b, 0x2c, 0x3a,
		},
	},
	.input_text = {
		.data = {
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x41, 0x45, 0x53, 0x2d, 0x32, 0x35, 0x36, 0x2d,
			0x47, 0x43, 0x4d, 0x20, 0x54, 0x4c, 0x53, 0x20,
			0x31, 0x2e, 0x32, 0x20, 0x65, 0x78, 0x61, 0x6d,
			0x70, 0x6c, 0x65, 0xa,
		},
		.len = 36,
	},
	.output_text = {
		.data = {
			0x17, 0x03, 0x03, 0x00, 0x3c,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01,
			0x37, 0xed, 0x76, 0x33, 0xb3, 0xcd, 0x66, 0x76,
			0x68, 0x2d, 0xc2, 0xb2, 0x83, 0x91, 0x15, 0xdf,
			0xc3, 0x13, 0x14, 0x83, 0x99, 0x4c, 0x0c, 0xcc,
			0xc0, 0x41, 0x6f, 0x54, 0x8d, 0xd5, 0x6f, 0xe7,
			0xe9, 0x7d, 0x9c, 0xed, 0xe8, 0xd6, 0x92, 0xac,
			0xa5, 0xc2, 0x93, 0xdf, 0xdc, 0xf7, 0x80, 0x3c,
			0xc6, 0xc3, 0x6e, 0x7f,
		},
		.len = 65,
	},
	.imp_nonce = {
		.data = {
			0xff, 0x9d, 0x56, 0x96,
		},
		.len = 4,
	},
	.iv = {
		.data = {
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01
		},
	},

	.xform = {
		.aead = {
			.next = NULL,
			.type = RTE_CRYPTO_SYM_XFORM_AEAD,
			.aead = {
				.op = RTE_CRYPTO_AEAD_OP_ENCRYPT,
				.algo = RTE_CRYPTO_AEAD_AES_GCM,
				.key.length = 32,
				.iv.length = 12,
				.iv.offset = IV_OFFSET,
				.digest_length = 16,
				.aad_length = 13,
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_TLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.tls_1_2.seq_no = 0x1,
	},

	.aead = true,
	.app_type = 0x17,
};

struct tls_record_test_data tls_test_data_aes_128_cbc_sha1_hmac = {
	.key = {
		.data = {
			0xee, 0x2b, 0x2a, 0x59, 0xbf, 0x60, 0x36, 0xba,
			0xe4, 0xdc, 0x7d, 0x8b, 0xc7, 0x0d, 0xbb, 0x85,
		},
	},
	.auth_key = {
		.data = {
			0xcf, 0x4f, 0xc1, 0x7d, 0x6d, 0x4c, 0x0d, 0x13,
			0x3d, 0x8f, 0x95, 0xb5, 0xdd, 0xb0, 0x85, 0x08,
			0x38, 0x2c, 0xa1, 0xa0,
		},
	},
	.input_text = {
		.data = {
			/* plain text data */
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x32, 0x20,
			0x41, 0x45, 0x53, 0x2d, 0x31, 0x32, 0x38, 0x2d,
			0x43, 0x42, 0x43, 0x20, 0x53, 0x48, 0x41, 0x31,
			0x20, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65,
			0x20, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x0a,
		},
		.len = 48,
	},
	.output_text = {
		.data = {
			/* TLS header */
			0x17, 0x03, 0x03, 0x00, 0x60,
			/* Encrypted data */
			0x30, 0x76, 0x10, 0x2e, 0xb2, 0xe3, 0xb6, 0x93,
			0x70, 0xd4, 0xdf, 0x2a, 0x4f, 0x92, 0x1d, 0x3c,
			0x9a, 0x87, 0x38, 0xb0, 0x8e, 0x82, 0xf1, 0x33,
			0xa2, 0x5e, 0xce, 0xaf, 0x51, 0xf1, 0x51, 0x17,
			0x4f, 0x63, 0x55, 0x60, 0xa5, 0x3f, 0xc5, 0x42,
			0x9a, 0x80, 0x7a, 0x19, 0xb9, 0x77, 0x5b, 0x6f,
			0x1b, 0x2e, 0x2c, 0x0a, 0xa1, 0xa9, 0xf4, 0xb1,
			0xc7, 0x12, 0x7d, 0xfc, 0x92, 0x2f, 0xf4, 0xfc,
			0x20, 0x16, 0x1a, 0xa3, 0x94, 0xc6, 0xfe, 0x64,
			0x8d, 0x16, 0xd2, 0x52, 0x56, 0x83, 0x75, 0x3f,
			0x5b, 0x89, 0xf4, 0x29, 0x72, 0x46, 0x19, 0xd2,
			0xae, 0xea, 0xe3, 0xfc, 0x23, 0xd9, 0x00, 0x6c,
		},
		.len = 101,
	},
	.iv = {
		.data = {
			0x30, 0x76, 0x10, 0x2e, 0xb2, 0xe3, 0xb6, 0x93,
			0x70, 0xd4, 0xdf, 0x2a, 0x4f, 0x92, 0x1d, 0x3c,
		},
	},

	.xform = {
		.chain = {
			.auth = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_AUTH,
				.auth = {
					.op = RTE_CRYPTO_AUTH_OP_GENERATE,
					.algo = RTE_CRYPTO_AUTH_SHA1_HMAC,
					.key.length = 20,
					.iv.length = 0,
					.iv.offset = 0,
					.digest_length = 20,
				},
			},
			.cipher = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_CIPHER,
				.cipher = {
					.op = RTE_CRYPTO_CIPHER_OP_ENCRYPT,
					.algo = RTE_CRYPTO_CIPHER_AES_CBC,
					.key.length = 16,
					.iv.length = 16,
					.iv.offset = IV_OFFSET,
				},
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_TLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.tls_1_2.seq_no = 0x1,
	},

	.aead = false,
	.app_type = 0x17,
};

/* DTLS 1.2 AES-128-GCM */
struct tls_record_test_data dtls_test_data_aes_128_gcm = {
	.key = {
		.data = {
			0xde, 0x1f, 0xf3, 0x6f, 0x76, 0x9a, 0x99, 0x71,
			0x19, 0x5f, 0x03, 0x36, 0x64, 0x7d, 0xb2, 0x1e,
		},
	},
	.output_text = {
		.data = {
			0x17, 0xfe, 0xfd, 0x00, 0x01, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x01, 0x00, 0x3d, 0x00, 0x01, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x01, 0xe8, 0x02, 0xd6,
			0xa6, 0x78, 0x71, 0x50, 0x8f, 0x26, 0x43, 0x5d,
			0xf7, 0x50, 0x5a, 0x45, 0x8b, 0xc9, 0x7f, 0xa7,
			0x12, 0x51, 0x3a, 0x72, 0x0c, 0xfa, 0x21, 0xff,
			0x8c, 0xc9, 0x3f, 0x25, 0x08, 0xb5, 0xe8, 0x7d,
			0x5f, 0xbc, 0xa8, 0xb9, 0xd3, 0xe3, 0x15, 0xb0,
			0x9b, 0xbd, 0x82, 0x9b, 0x77, 0x14, 0x17, 0x2a,
			0x28, 0xbb,
		},
		.len = 74,
	},
	.input_text = {
		.data = {
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x44, 0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x32,
			0x20, 0x41, 0x45, 0x53, 0x2d, 0x31, 0x32, 0x38,
			0x2d, 0x47, 0x43, 0x4d, 0x20, 0x65, 0x78, 0x61,
			0x6d, 0x70, 0x6c, 0x65, 0xa,
		},
		.len = 37,
	},
	.imp_nonce = {
		.data = {
			0x66, 0xb2, 0x04, 0x1d,
		},
		.len = 4,
	},
	.iv = {
		.data = {
			0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01,
		},
	},

	.xform = {
		.aead = {
			.next = NULL,
			.type = RTE_CRYPTO_SYM_XFORM_AEAD,
			.aead = {
				.op = RTE_CRYPTO_AEAD_OP_ENCRYPT,
				.algo = RTE_CRYPTO_AEAD_AES_GCM,
				.key.length = 16,
				.iv.length = 12,
				.iv.offset = IV_OFFSET,
				.digest_length = 16,
				.aad_length = 13,
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_DTLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.dtls_1_2.epoch = 1,
		.dtls_1_2.seq_no = 1,
	},

	.aead = true,
	.app_type = 0x17,
};

/* DTLS 1.2 AES-256-GCM */
struct tls_record_test_data dtls_test_data_aes_256_gcm = {
	.key = {
		.data = {
			0x9e, 0x0d, 0xe9, 0x57, 0x4d, 0x24, 0xa7, 0x15,
			0x0d, 0x49, 0xf0, 0x87, 0xa6, 0xf1, 0x3e, 0x33,
			0x8c, 0xb8, 0x86, 0xb6, 0xbb, 0x72, 0x93, 0xa2,
			0x5e, 0xcb, 0x31, 0xe3, 0x60, 0xc6, 0x7e, 0x11,
		},
	},
	.output_text = {
		.data = {
			0x17, 0xfe, 0xfd, 0x00, 0x01, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x01, 0x00, 0x39, 0x00, 0x01, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x01, 0xf9, 0xed, 0xe7,
			0xe2, 0x53, 0x97, 0x9a, 0x3c, 0xc7, 0x66, 0x21,
			0x62, 0xfb, 0x29, 0x27, 0x8d, 0xc6, 0x11, 0xa3,
			0x69, 0x89, 0xdc, 0x34, 0x50, 0x6d, 0x2f, 0xf7,
			0x58, 0xd5, 0xc7, 0x71, 0xf4, 0x6d, 0x07, 0xae,
			0x35, 0x39, 0x7b, 0xa8, 0xc3, 0x38, 0x71, 0xb8,
			0xee, 0xb0, 0x18, 0xc6, 0xb5, 0x49,
		},
		.len = 70,
	},
	.input_text = {
		.data = {
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x44, 0x54, 0x4c, 0x53, 0x20, 0x65, 0x78, 0x61,
			0x6d, 0x70, 0x6c, 0x65, 0x20, 0x74, 0x65, 0x73,
			0x74, 0x20, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0xa,
		},
		.len = 33,
	},
	.imp_nonce = {
		.data = {
			0x24, 0x58, 0x29, 0x71,
		},
		.len = 4,
	},
	.iv = {
		.data = {
			0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01,
		},
	},

	.xform = {
		.aead = {
			.next = NULL,
			.type = RTE_CRYPTO_SYM_XFORM_AEAD,
			.aead = {
				.op = RTE_CRYPTO_AEAD_OP_ENCRYPT,
				.algo = RTE_CRYPTO_AEAD_AES_GCM,
				.key.length = 32,
				.iv.length = 12,
				.iv.offset = IV_OFFSET,
				.digest_length = 16,
				.aad_length = 13,
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_DTLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.dtls_1_2.epoch = 1,
		.dtls_1_2.seq_no = 1,
	},

	.aead = true,
	.app_type = 0x17,
};

struct tls_record_test_data tls_test_data_null_cipher_sha1_hmac = {
	.auth_key = {
		.data = {
			0xd7, 0x8c, 0xe2, 0xda, 0x51, 0x2a, 0xd3, 0x9c,
			0xb4, 0x6c, 0xa1, 0xcf, 0x3f, 0x13, 0xf6, 0x1d,
			0x9e, 0x40, 0x6f, 0x38,
		},
	},
	.input_text = {
		.data = {
			/* actual plain text */
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x32, 0x20,
			0x4e, 0x55, 0x4c, 0x4c, 0x20, 0x53, 0x48, 0x41,
			0x31, 0x20, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c,
			0x65, 0x20, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0xa,
		},
		.len = 41,
	},
	.output_text = {
		.data = {
			0x17, 0x03, 0x03, 0x00, 0x3d,
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x32, 0x20,
			0x4e, 0x55, 0x4c, 0x4c, 0x20, 0x53, 0x48, 0x41,
			0x31, 0x20, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c,
			0x65, 0x20, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72,
			0x0a, 0x80, 0xa3, 0x85, 0x16, 0x85, 0xbf, 0x31,
			0xca, 0xac, 0x18, 0x8c, 0x4e, 0xd3, 0x02, 0x75,
			0x4e, 0xc9, 0xeb, 0x26, 0xab,
		},
		.len = 66,
	},

	.xform = {
		.chain = {
			.auth = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_AUTH,
				.auth = {
					.op = RTE_CRYPTO_AUTH_OP_GENERATE,
					.algo = RTE_CRYPTO_AUTH_SHA1_HMAC,
					.key.length = 20,
					.iv.length = 0,
					.iv.offset = 0,
					.digest_length = 20,
				},
			},
			.cipher = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_CIPHER,
				.cipher = {
					.op = RTE_CRYPTO_CIPHER_OP_ENCRYPT,
					.algo = RTE_CRYPTO_CIPHER_NULL,
					.key.length = 0,
					.iv.length = 0,
					.iv.offset = IV_OFFSET,
				},
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_TLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.tls_1_2.seq_no = 0x1,
	},

	.aead = false,
	.app_type = 0x17,
};

struct tls_record_test_data tls_test_data_chacha20_poly1305 = {
	.key = {
		.data = {
			0xe3, 0x5b, 0x21, 0x49, 0x5d, 0xfa, 0x6d, 0xb5,
			0x93, 0xbf, 0xd3, 0xde, 0xa0, 0xef, 0x8b, 0xb2,
			0x86, 0xf3, 0xae, 0x20, 0x7c, 0x4d, 0x98, 0xda,
			0x20, 0x03, 0x3f, 0x57, 0x9f, 0x45, 0xdc, 0x8f,
		},
	},
	.input_text = {
		.data = {
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x54, 0x4c, 0x53, 0x31, 0x2e, 0x32, 0x20, 0x43,
			0x48, 0x41, 0x43, 0x48, 0x41, 0x32, 0x30, 0x2d,
			0x50, 0x4f, 0x4c, 0x59, 0x31, 0x33, 0x30, 0x35,
			0x20, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65,
			0x20, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x0a,
		},
		.len = 48,
	},
	.output_text = {
		.data = {
			0x17, 0x03, 0x03, 0x00, 0x40,
			0xdc, 0x3d, 0x81, 0xa1, 0xda, 0x88, 0x24, 0xcf,
			0xeb, 0xd3, 0x38, 0x99, 0x3d, 0x0f, 0xfa, 0xdc,
			0xac, 0x17, 0x8f, 0xa4, 0x20, 0xff, 0x08, 0x3a,
			0x04, 0x70, 0x52, 0xcf, 0x9e, 0x25, 0xf5, 0x98,
			0xf0, 0x5b, 0x0f, 0x80, 0xb1, 0x77, 0x6d, 0x7a,
			0x20, 0x73, 0xf6, 0x10, 0x1b, 0x5e, 0xfd, 0xc4,
			0x3c, 0xe1, 0x40, 0xa1, 0x7c, 0x43, 0xde, 0x13,
			0x6e, 0x43, 0xe2, 0x81, 0x16, 0x9c, 0xf0, 0x4a,
		},
		.len = 69,
	},
	.imp_nonce = {
		.data = {
			0x2e, 0x86, 0x13, 0x09, 0x34, 0xe8, 0xa7, 0x3f,
			0xda, 0x87, 0x68, 0xcb
		},
		.len = 12,
	},
	.iv = {
		.data = {
			0xdc, 0x3d, 0x81, 0xa1, 0xda, 0x88, 0x24, 0xcf,
		},
	},

	.xform = {
		.aead = {
			.next = NULL,
			.type = RTE_CRYPTO_SYM_XFORM_AEAD,
			.aead = {
				.op = RTE_CRYPTO_AEAD_OP_ENCRYPT,
				.algo = RTE_CRYPTO_AEAD_CHACHA20_POLY1305,
				.key.length = 32,
				.iv.length = 8,
				.iv.offset = IV_OFFSET,
				.digest_length = 16,
				.aad_length = 13,
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_TLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.tls_1_2.seq_no = 0x1,
	},

	.aead = true,
	.app_type = 0x17,
};

struct tls_record_test_data dtls_test_data_chacha20_poly1305 = {
	.key = {
		.data = {
			0x11, 0x6b, 0x4e, 0x91, 0x74, 0x57, 0x15, 0x94,
			0xca, 0x11, 0x2e, 0xe8, 0xb2, 0x48, 0xf1, 0x9b,
			0x49, 0xbd, 0xbf, 0x91, 0x13, 0x4c, 0x83, 0xd7,
			0x24, 0xb1, 0xa1, 0x0e, 0xc1, 0x54, 0xe6, 0xe2,
		},
	},
	.input_text = {
		.data = {
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x44, 0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x32,
			0x20, 0x43, 0x48, 0x41, 0x43, 0x48, 0x41, 0x32,
			0x30, 0x2d, 0x50, 0x4f, 0x4c, 0x59, 0x31, 0x33,
			0x30, 0x35, 0x20, 0x65, 0x78, 0x61, 0x6d, 0x70,
			0x6c, 0x65, 0x20, 0x76, 0x65, 0x63, 0x74, 0x6f,
			0x72, 0x0a,
		},
		.len = 50,
	},
	.output_text = {
		.data = {
			0x17, 0xfe, 0xfd, 0x00, 0x01, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x01, 0x00, 0x42,
			0xd5, 0x38, 0xd0, 0x1e, 0x7e, 0x9d, 0x69, 0xa1,
			0xf9, 0x7c, 0x1f, 0xad, 0x64, 0x24, 0xac, 0x07,
			0x37, 0x3d, 0x7b, 0x8b, 0x63, 0x6e, 0x9d, 0x63,
			0xa2, 0xd2, 0x05, 0x91, 0x1c, 0xa2, 0x5f, 0xbd,
			0x3e, 0x5d, 0x82, 0x37, 0xb4, 0x46, 0x8d, 0x7f,
			0x1c, 0x20, 0xb4, 0x5c, 0xb2, 0x05, 0xdb, 0x84,
			0x77, 0xad, 0x9c, 0xbd, 0x09, 0x14, 0x50, 0x9e,
			0xad, 0xc3, 0xc6, 0xfe, 0x2f, 0x13, 0xf6, 0x79,
			0x65, 0x70,
		},
		.len = 79,
	},
	.imp_nonce = {
		.data = {
			0x5b, 0xb2, 0x29, 0x02, 0x14, 0x1d, 0xf1, 0x86,
			0x5a, 0xe5, 0x70, 0x3f,
		},
		.len = 12,
	},
	.iv = {
		.data = {
			0xd5, 0x38, 0xd0, 0x1e, 0x7e, 0x9d, 0x69, 0xa1,
		},
	},

	.xform = {
		.aead = {
			.next = NULL,
			.type = RTE_CRYPTO_SYM_XFORM_AEAD,
			.aead = {
				.op = RTE_CRYPTO_AEAD_OP_ENCRYPT,
				.algo = RTE_CRYPTO_AEAD_CHACHA20_POLY1305,
				.key.length = 32,
				.iv.length = 8,
				.iv.offset = IV_OFFSET,
				.digest_length = 16,
				.aad_length = 21,
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_DTLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.dtls_1_2.epoch = 1,
		.dtls_1_2.seq_no = 1,
	},

	.aead = true,
	.app_type = 0x17,
};

struct tls_record_test_data dtls_test_data_null_cipher_sha1_hmac = {
	.auth_key = {
		.data = {
			0xb1, 0x1d, 0x4f, 0x46, 0x23, 0x42, 0x51, 0x51,
			0xda, 0x91, 0x59, 0x01, 0xde, 0x9e, 0x0c, 0x27,
			0xe6, 0x8a, 0xdc, 0xf8,
		},
	},
	.input_text = {
		.data = {
			/* actual plain text */
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x44, 0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x32,
			0x20, 0x4e, 0x55, 0x4c, 0x4c, 0x20, 0x63, 0x69,
			0x70, 0x68, 0x65, 0x72, 0x20, 0x53, 0x48, 0x41,
			0x31, 0x20, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c,
			0x65, 0x20, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x0a,
		},
		.len = 49,
	},
	.output_text = {
		.data = {
			0x17, 0xfe, 0xfd, 0x00, 0x01, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x01, 0x00, 0x45,
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x44, 0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x32,
			0x20, 0x4e, 0x55, 0x4c, 0x4c, 0x20, 0x63, 0x69,
			0x70, 0x68, 0x65, 0x72, 0x20, 0x53, 0x48, 0x41,
			0x31, 0x20, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c,
			0x65, 0x20, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72,
			0x0a, 0xfc, 0x24, 0x2f, 0x1c, 0x74, 0xda, 0x3d,
			0xb3, 0xa9, 0x81, 0x51, 0xcb, 0x9b, 0xb6, 0x7e,
			0x25, 0xa6, 0x53, 0x04, 0x2f,
		},
		.len = 82,
	},

	.xform = {
		.chain = {
			.auth = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_AUTH,
				.auth = {
					.op = RTE_CRYPTO_AUTH_OP_GENERATE,
					.algo = RTE_CRYPTO_AUTH_SHA1_HMAC,
					.key.length = 20,
					.iv.length = 0,
					.iv.offset = 0,
					.digest_length = 20,
				},
			},
			.cipher = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_CIPHER,
				.cipher = {
					.op = RTE_CRYPTO_CIPHER_OP_ENCRYPT,
					.algo = RTE_CRYPTO_CIPHER_NULL,
					.key.length = 0,
					.iv.length = 0,
					.iv.offset = IV_OFFSET,
				},
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_DTLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.dtls_1_2.epoch = 1,
		.dtls_1_2.seq_no = 1,
	},

	.aead = false,
	.app_type = 0x17,
};

struct tls_record_test_data dtls_test_data_aes_128_cbc_sha1_hmac = {
	.key = {
		.data = {
			0x6f, 0x78, 0x95, 0x1f, 0x08, 0xff, 0xd7, 0xa6,
			0x7f, 0x64, 0x70, 0xa8, 0x10, 0xde, 0xce, 0xca,
		},
	},
	.auth_key = {
		.data = {
			0x8e, 0x8a, 0xae, 0xa2, 0xe9, 0x8e, 0x4a, 0xa2,
			0x7a, 0xa7, 0x24, 0x76, 0xf1, 0x48, 0x20, 0x25,
			0x8b, 0x04, 0xd7, 0x2d,
		},
	},
	.input_text = {
		.data = {
			/* actual plain text */
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x44, 0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x32,
			0x20, 0x41, 0x45, 0x53, 0x2d, 0x31, 0x32, 0x38,
			0x2d, 0x43, 0x42, 0x43, 0x20, 0x53, 0x48, 0x41,
			0x31, 0x20, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c,
			0x65, 0x20, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0xa,
		},
		.len = 49,
	},
	.output_text = {
		.data = {
			0x17, 0xfe, 0xfd, 0x00, 0x01, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x01, 0x00, 0x60,
			0x3a, 0x0f, 0xaf, 0x47, 0x0d, 0x9e, 0x0f, 0x47,
			0x88, 0x23, 0x4e, 0xa0, 0x05, 0x1e, 0x35, 0x99,
			0x03, 0xa8, 0x32, 0x8f, 0x3d, 0x5d, 0xd6, 0xb1,
			0xf1, 0x93, 0x40, 0x16, 0xed, 0x3a, 0xea, 0x0d,
			0xb0, 0x03, 0xcd, 0x56, 0x8f, 0x96, 0x26, 0x00,
			0xb3, 0x54, 0xaf, 0x18, 0xbc, 0x95, 0xfe, 0x3e,
			0x5d, 0xfa, 0x57, 0xa0, 0x8d, 0x95, 0x8f, 0xd2,
			0xab, 0x2e, 0x58, 0x5c, 0xe8, 0x37, 0xff, 0xc5,
			0x00, 0x62, 0xfa, 0x19, 0xfd, 0x39, 0x4f, 0xc2,
			0x10, 0xd3, 0x98, 0x72, 0xec, 0x2f, 0x00, 0x2b,
			0x1c, 0xb9, 0xf4, 0xe2, 0xf4, 0xd4, 0xbf, 0x5b,
			0x77, 0x45, 0x42, 0x57, 0x77, 0x26, 0xff, 0x41,
		},
		.len = 109,
	},
	.iv = {
		.data = {
			0x3a, 0x0f, 0xaf, 0x47, 0x0d, 0x9e, 0x0f, 0x47,
			0x88, 0x23, 0x4e, 0xa0, 0x05, 0x1e, 0x35, 0x99,
		},
	},

	.xform = {
		.chain = {
			.auth = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_AUTH,
				.auth = {
					.op = RTE_CRYPTO_AUTH_OP_GENERATE,
					.algo = RTE_CRYPTO_AUTH_SHA1_HMAC,
					.key.length = 20,
					.iv.length = 0,
					.iv.offset = 0,
					.digest_length = 20,
				},
			},
			.cipher = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_CIPHER,
				.cipher = {
					.op = RTE_CRYPTO_CIPHER_OP_ENCRYPT,
					.algo = RTE_CRYPTO_CIPHER_AES_CBC,
					.key.length = 16,
					.iv.length = 16,
					.iv.offset = IV_OFFSET,
				},
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_DTLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.dtls_1_2.epoch = 1,
		.dtls_1_2.seq_no = 1,
	},

	.aead = false,
	.app_type = 0x17,
};

struct tls_record_test_data dtls_test_data_aes_128_cbc_sha256_hmac = {
	.key = {
		.data = {
			0x4c, 0x3f, 0x7e, 0x56, 0xdf, 0x60, 0x0e, 0xfa,
			0x47, 0x8b, 0xb5, 0x44, 0x7f, 0x47, 0xfa, 0x22,
		},
	},
	.auth_key = {
		.data = {
			0x5b, 0xaa, 0x3c, 0x1a, 0x60, 0x9b, 0x15, 0x04,
			0x02, 0xda, 0xdb, 0x96, 0x5e, 0xd9, 0x7b, 0x7a,
			0xa4, 0xca, 0xd6, 0xab, 0x0b, 0x55, 0x87, 0xef,
			0x20, 0xcd, 0x2e, 0xd5, 0xe4, 0x70, 0xae, 0x36,
		},
	},
	.input_text = {
		.data = {
			/* actual plain text */
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x44, 0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x32,
			0x20, 0x41, 0x45, 0x53, 0x2d, 0x31, 0x32, 0x38,
			0x2d, 0x43, 0x42, 0x43, 0x20, 0x53, 0x48, 0x41,
			0x32, 0x35, 0x36, 0x20, 0x65, 0x78, 0x61, 0x6d,
			0x70, 0x6c, 0x65, 0x20, 0x76, 0x65, 0x63, 0x74,
			0x6f, 0x72, 0xa,
		},
		.len = 51,
	},
	.output_text = {
		.data = {
			0x17, 0xfe, 0xfd, 0x00, 0x01, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x01, 0x00, 0x70,
			0x9c, 0xfe, 0xdf, 0x4f, 0xaf, 0x86, 0x46, 0xc7,
			0x97, 0xaa, 0x2e, 0xe4, 0xd8, 0x51, 0xa8, 0xbb,
			0x22, 0x25, 0x09, 0xa7, 0x25, 0xb1, 0xa1, 0xe1,
			0xcc, 0xcb, 0x3f, 0x08, 0xae, 0xa9, 0xae, 0xa1,
			0x43, 0xe0, 0xf5, 0x6b, 0x98, 0xda, 0x98, 0x09,
			0x99, 0xfa, 0x8c, 0x01, 0xd8, 0x90, 0x0d, 0x42,
			0xcc, 0x5f, 0x1d, 0x52, 0x48, 0xbe, 0xd2, 0x4e,
			0x62, 0xf5, 0x64, 0x89, 0x2f, 0x05, 0x91, 0x4e,
			0x54, 0x3a, 0x58, 0x1c, 0x99, 0xb0, 0xa5, 0x4a,
			0xc1, 0x42, 0xa1, 0x87, 0xa3, 0x00, 0x17, 0x6f,
			0xa8, 0xf8, 0x9d, 0xae, 0xaf, 0x40, 0xf8, 0x72,
			0xbf, 0xad, 0xbe, 0x68, 0x74, 0x20, 0xe5, 0xe9,
			0x1b, 0x1d, 0x17, 0x28, 0xf7, 0xb2, 0xf8, 0x01,
			0xc5, 0xcb, 0xca, 0xc5, 0xda, 0x70, 0x1c, 0x7d,
		},
		.len = 125,
	},
	.iv = {
		.data = {
			0x9c, 0xfe, 0xdf, 0x4f, 0xaf, 0x86, 0x46, 0xc7,
			0x97, 0xaa, 0x2e, 0xe4, 0xd8, 0x51, 0xa8, 0xbb,
		},
	},

	.xform = {
		.chain = {
			.auth = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_AUTH,
				.auth = {
					.op = RTE_CRYPTO_AUTH_OP_GENERATE,
					.algo = RTE_CRYPTO_AUTH_SHA256_HMAC,
					.key.length = 32,
					.iv.length = 0,
					.iv.offset = 0,
					.digest_length = 32,
				},
			},
			.cipher = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_CIPHER,
				.cipher = {
					.op = RTE_CRYPTO_CIPHER_OP_ENCRYPT,
					.algo = RTE_CRYPTO_CIPHER_AES_CBC,
					.key.length = 16,
					.iv.length = 16,
					.iv.offset = IV_OFFSET,
				},
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_DTLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.dtls_1_2.epoch = 1,
		.dtls_1_2.seq_no = 1,
	},

	.aead = false,
	.app_type = 0x17,
};

struct tls_record_test_data dtls_test_data_aes_256_cbc_sha1_hmac = {
	.key = {
		.data = {
			0xd5, 0x5d, 0xae, 0xb4, 0xb4, 0x6f, 0xef, 0xd9,
			0x08, 0xbd, 0x61, 0xf7, 0x95, 0x50, 0x72, 0x85,
			0x4f, 0x4f, 0x3a, 0x1a, 0x55, 0x5b, 0xa9, 0x65,
			0xcb, 0x54, 0x29, 0xdb, 0xdb, 0xdc, 0xb9, 0x66,
		},
	},
	.auth_key = {
		.data = {
			0x9f, 0xb0, 0xcb, 0xe1, 0xca, 0xc4, 0x13, 0xa0,
			0x57, 0xa8, 0x5b, 0xd6, 0x5e, 0xe7, 0x8d, 0xfb,
			0xe9, 0x99, 0x05, 0x88,
		},
	},
	.input_text = {
		.data = {
			/* actual plain text */
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x44, 0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x32,
			0x20, 0x41, 0x45, 0x53, 0x2d, 0x32, 0x35, 0x36,
			0x2d, 0x43, 0x42, 0x43, 0x20, 0x53, 0x48, 0x41,
			0x31, 0x20, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c,
			0x65, 0x20, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0xa,
		},
		.len = 49,
	},
	.output_text = {
		.data = {
			0x17, 0xfe, 0xfd, 0x00, 0x01, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x01, 0x00, 0x60,
			0x55, 0xdd, 0x04, 0x79, 0xa9, 0xc2, 0x62, 0x2c,
			0xc1, 0x89, 0x8e, 0xc4, 0x0a, 0x3f, 0x80, 0x2c,
			0x14, 0xcc, 0xa4, 0x01, 0xf3, 0x4f, 0x33, 0x10,
			0xe0, 0x7a, 0xc1, 0x5b, 0xeb, 0x51, 0xd6, 0x34,
			0x51, 0xaf, 0x81, 0x6c, 0xeb, 0xce, 0x97, 0xa2,
			0x27, 0x02, 0x66, 0xb6, 0x35, 0x4c, 0x1c, 0xbd,
			0x5e, 0x8d, 0x1b, 0x12, 0x42, 0xad, 0xf3, 0x43,
			0x52, 0x6d, 0x1b, 0xb5, 0x4f, 0x7c, 0x48, 0xd8,
			0xae, 0xf2, 0x5e, 0xe0, 0xe6, 0x57, 0x3f, 0x61,
			0xa0, 0x8d, 0x3d, 0x42, 0x3c, 0x33, 0xb9, 0x39,
			0x84, 0x06, 0x49, 0xd8, 0xd4, 0xfc, 0x7d, 0x0f,
			0x30, 0x6d, 0x7b, 0x98, 0xee, 0xd3, 0xbd, 0x35,
		},
		.len = 109,
	},
	.iv = {
		.data = {
			0x55, 0xdd, 0x04, 0x79, 0xa9, 0xc2, 0x62, 0x2c,
			0xc1, 0x89, 0x8e, 0xc4, 0x0a, 0x3f, 0x80, 0x2c,
		},
	},

	.xform = {
		.chain = {
			.auth = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_AUTH,
				.auth = {
					.op = RTE_CRYPTO_AUTH_OP_GENERATE,
					.algo = RTE_CRYPTO_AUTH_SHA1_HMAC,
					.key.length = 20,
					.iv.length = 0,
					.iv.offset = 0,
					.digest_length = 20,
				},
			},
			.cipher = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_CIPHER,
				.cipher = {
					.op = RTE_CRYPTO_CIPHER_OP_ENCRYPT,
					.algo = RTE_CRYPTO_CIPHER_AES_CBC,
					.key.length = 32,
					.iv.length = 16,
					.iv.offset = IV_OFFSET,
				},
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_DTLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.dtls_1_2.epoch = 1,
		.dtls_1_2.seq_no = 1,
	},

	.aead = false,
	.app_type = 0x17,
};

struct tls_record_test_data dtls_test_data_aes_256_cbc_sha256_hmac = {
	.key = {
		.data = {
			0xaa, 0x18, 0xb6, 0x7b, 0xf1, 0x74, 0xf5, 0x8d,
			0xab, 0x6e, 0xfc, 0x12, 0x59, 0x5b, 0xab, 0x6f,
			0xe7, 0x23, 0xc9, 0x80, 0x2e, 0x5d, 0xba, 0x73,
			0x93, 0xd7, 0xe3, 0xc9, 0x86, 0xd7, 0x9c, 0x8e,
		},
	},
	.auth_key = {
		.data = {
			0x78, 0x92, 0x52, 0xb3, 0x2a, 0x2f, 0x3e, 0xdf,
			0xf0, 0xdd, 0x91, 0xd3, 0xf4, 0xeb, 0xd5, 0xdd,
			0x97, 0x7c, 0x24, 0xb4, 0xec, 0x28, 0xf6, 0xbd,
			0x8f, 0x7b, 0xdf, 0x97, 0x32, 0xdf, 0x81, 0xde,
		},
	},
	.input_text = {
		.data = {
			/* actual plain text */
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x44, 0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x32,
			0x20, 0x41, 0x45, 0x53, 0x2d, 0x32, 0x35, 0x36,
			0x2d, 0x43, 0x42, 0x43, 0x20, 0x53, 0x48, 0x41,
			0x32, 0x35, 0x36, 0x20, 0x65, 0x78, 0x61, 0x6d,
			0x70, 0x6c, 0x65, 0x20, 0x76, 0x65, 0x63, 0x74,
			0x6f, 0x72, 0xa,
		},
		.len = 51,
	},
	.output_text = {
		.data = {
			0x17, 0xfe, 0xfd, 0x00, 0x01, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x01, 0x00, 0x70,
			0x8f, 0x07, 0x1c, 0x60, 0x41, 0xbd, 0x77, 0x0c,
			0x0b, 0x42, 0xbe, 0x95, 0x80, 0xc5, 0xdb, 0x1c,
			0x46, 0xe4, 0x16, 0x83, 0xf7, 0x89, 0xe4, 0x82,
			0xae, 0xaa, 0x8b, 0xc6, 0x91, 0x1a, 0x34, 0x9c,
			0x23, 0x48, 0x83, 0x4c, 0x21, 0xf4, 0xed, 0xf8,
			0x74, 0x1e, 0xd6, 0x9a, 0x76, 0x6d, 0xa3, 0x53,
			0x56, 0xda, 0x78, 0x22, 0xea, 0x77, 0x87, 0xd0,
			0xb5, 0x73, 0x7c, 0x53, 0x14, 0xdf, 0xc1, 0x84,
			0x93, 0x97, 0xa4, 0x1d, 0x32, 0x1c, 0x57, 0x9a,
			0xdd, 0x29, 0x0d, 0x41, 0x4d, 0x44, 0x4c, 0x7f,
			0x7c, 0xd7, 0xa2, 0x8b, 0xaf, 0xfd, 0x76, 0x24,
			0x81, 0x03, 0x11, 0x9e, 0xd0, 0x62, 0xc1, 0xfd,
			0xc4, 0xe1, 0xf9, 0x88, 0xae, 0xf8, 0x7e, 0x25,
			0xbf, 0xfb, 0xea, 0xa6, 0xe9, 0x70, 0x33, 0x6a,
		},
		.len = 125,
	},
	.iv = {
		.data = {
			0x8f, 0x07, 0x1c, 0x60, 0x41, 0xbd, 0x77, 0x0c,
			0x0b, 0x42, 0xbe, 0x95, 0x80, 0xc5, 0xdb, 0x1c,
		},
	},

	.xform = {
		.chain = {
			.auth = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_AUTH,
				.auth = {
					.op = RTE_CRYPTO_AUTH_OP_GENERATE,
					.algo = RTE_CRYPTO_AUTH_SHA256_HMAC,
					.key.length = 32,
					.iv.length = 0,
					.iv.offset = 0,
					.digest_length = 32,
				},
			},
			.cipher = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_CIPHER,
				.cipher = {
					.op = RTE_CRYPTO_CIPHER_OP_ENCRYPT,
					.algo = RTE_CRYPTO_CIPHER_AES_CBC,
					.key.length = 32,
					.iv.length = 16,
					.iv.offset = IV_OFFSET,
				},
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_DTLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.dtls_1_2.epoch = 1,
		.dtls_1_2.seq_no = 1,
	},

	.aead = false,
	.app_type = 0x17,
};

struct tls_record_test_data dtls_test_data_aes_256_cbc_sha384_hmac = {
	.key = {
		.data = {
			0x1d, 0xe5, 0x19, 0x18, 0x57, 0xa0, 0xee, 0x79,
			0x84, 0x61, 0x92, 0x9d, 0x3d, 0xce, 0x42, 0x92,
			0x4a, 0x98, 0x23, 0x3b, 0xf8, 0xec, 0x29, 0x47,
			0xb3, 0xae, 0x1f, 0x22, 0xd2, 0x8d, 0xbe, 0x2c,
		},
	},
	.auth_key = {
		.data = {
			0xf8, 0xbd, 0x28, 0xf9, 0x4a, 0xde, 0x1d, 0xde,
			0x8c, 0xf5, 0xe9, 0x49, 0x34, 0x2a, 0x1a, 0xd0,
			0x0d, 0xe3, 0x64, 0xb2, 0x54, 0xd6, 0xd6, 0x40,
			0x90, 0x5d, 0x16, 0xc1, 0xf2, 0x77, 0x14, 0x90,
			0xe6, 0xfa, 0xbc, 0x9d, 0xe2, 0x72, 0x12, 0xec,
			0xb6, 0x05, 0xec, 0xdd, 0x1d, 0x23, 0xb3, 0x8e,
		},
	},
	.input_text = {
		.data = {
			/* actual plain text */
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x44, 0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x32,
			0x20, 0x41, 0x45, 0x53, 0x2d, 0x32, 0x35, 0x36,
			0x2d, 0x43, 0x42, 0x43, 0x20, 0x53, 0x48, 0x41,
			0x33, 0x38, 0x34, 0x20, 0x65, 0x78, 0x61, 0x6d,
			0x70, 0x6c, 0x65, 0x20, 0x76, 0x65, 0x63, 0x74,
			0x6f, 0x72, 0x0a,
		},
		.len = 51,
	},
	.output_text = {
		.data = {
			0x17, 0xfe, 0xfd, 0x00, 0x01, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x01, 0x00, 0x80,
			0x63, 0xe2, 0x70, 0xce, 0x45, 0x93, 0x18, 0xa3,
			0xa6, 0xd6, 0xf5, 0x50, 0x9f, 0x07, 0x9d, 0xab,
			0x88, 0x41, 0xee, 0x5b, 0x32, 0x1e, 0x85, 0xaf,
			0x33, 0x7b, 0x59, 0x8a, 0xe9, 0x41, 0x11, 0x6a,
			0xbb, 0x7d, 0x16, 0x6c, 0xbb, 0x66, 0x5e, 0xf5,
			0xfb, 0x5f, 0x03, 0xf9, 0x75, 0x7d, 0xb9, 0xff,
			0x70, 0xc3, 0x4a, 0x19, 0xe5, 0x25, 0xa0, 0x5d,
			0xad, 0x45, 0xef, 0xce, 0xd8, 0x2a, 0xde, 0xf0,
			0x0c, 0xca, 0x1c, 0x6b, 0x5a, 0x31, 0x8f, 0x49,
			0xff, 0x7d, 0xf3, 0x71, 0x15, 0x06, 0x07, 0x9d,
			0x6a, 0x5f, 0x5c, 0xd8, 0x2a, 0xa5, 0x0e, 0x61,
			0xde, 0x18, 0x6b, 0x7d, 0xc8, 0x74, 0x58, 0x18,
			0xf1, 0xac, 0xde, 0xb9, 0x6d, 0x8a, 0x44, 0xad,
			0x10, 0xf8, 0x63, 0x15, 0xcf, 0x25, 0x2f, 0x82,
			0x2f, 0xda, 0x74, 0x45, 0x02, 0xda, 0x61, 0x3c,
			0x2f, 0xf9, 0xa5, 0x92, 0x2a, 0x7c, 0x5e, 0x5d,
		},
		.len = 141,
	},
	.iv = {
		.data = {
			0x63, 0xe2, 0x70, 0xce, 0x45, 0x93, 0x18, 0xa3,
			0xa6, 0xd6, 0xf5, 0x50, 0x9f, 0x07, 0x9d, 0xab,
		},
	},

	.xform = {
		.chain = {
			.auth = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_AUTH,
				.auth = {
					.op = RTE_CRYPTO_AUTH_OP_GENERATE,
					.algo = RTE_CRYPTO_AUTH_SHA384_HMAC,
					.key.length = 48,
					.iv.length = 0,
					.iv.offset = 0,
					.digest_length = 48,
				},
			},
			.cipher = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_CIPHER,
				.cipher = {
					.op = RTE_CRYPTO_CIPHER_OP_ENCRYPT,
					.algo = RTE_CRYPTO_CIPHER_AES_CBC,
					.key.length = 32,
					.iv.length = 16,
					.iv.offset = IV_OFFSET,
				},
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_DTLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.dtls_1_2.epoch = 1,
		.dtls_1_2.seq_no = 1,
	},

	.aead = false,
	.app_type = 0x17,
};

struct tls_record_test_data dtls_test_data_3des_cbc_sha1_hmac = {
	.key = {
		.data = {
			0x2f, 0x60, 0xdc, 0xe6, 0x75, 0x71, 0xaa, 0x11,
			0xb6, 0x37, 0xc3, 0x8e, 0xc4, 0x72, 0x19, 0xec,
			0xc7, 0xd5, 0x43, 0x9b, 0x35, 0xe9, 0x5f, 0xc4,
		},
	},
	.auth_key = {
		.data = {
			0x3e, 0xaa, 0x47, 0xfc, 0x45, 0x1c, 0x49, 0xe0,
			0xf1, 0x76, 0x82, 0x70, 0xb2, 0xbd, 0x43, 0x49,
			0xc5, 0x31, 0x2b, 0xf0,
		},
	},
	.input_text = {
		.data = {
			/* actual plain text */
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x44, 0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x32,
			0x20, 0x33, 0x44, 0x45, 0x53, 0x2d, 0x43, 0x42,
			0x43, 0x20, 0x53, 0x48, 0x41, 0x31, 0x20, 0x65,
			0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x20, 0x76,
			0x65, 0x63, 0x74, 0x6f, 0x72, 0xa,
		},
		.len = 46,
	},
	.output_text = {
		.data = {
			0x17, 0xfe, 0xfd, 0x00, 0x01, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x01, 0x00, 0x50,
			0xe1, 0xae, 0x8c, 0x7d, 0x1c, 0x50, 0x3d, 0xb4,
			0xf6, 0xd0, 0x63, 0xc2, 0x70, 0x4e, 0x01, 0x2d,
			0x78, 0x0e, 0xbc, 0x88, 0xf3, 0xf6, 0xaa, 0x75,
			0xf8, 0x72, 0xe3, 0x4b, 0x3f, 0x3e, 0xf7, 0x91,
			0x5c, 0x20, 0xd9, 0x66, 0x1d, 0x76, 0xa7, 0x4e,
			0x14, 0x50, 0x51, 0x79, 0x37, 0xc2, 0xbb, 0x7a,
			0xdb, 0x89, 0x3d, 0xaa, 0x98, 0x68, 0x67, 0x17,
			0xb2, 0x68, 0x30, 0x25, 0x98, 0xa4, 0x26, 0xa1,
			0xaf, 0x91, 0x18, 0x16, 0x2e, 0x66, 0x75, 0xb7,
			0xd5, 0x59, 0x66, 0x13, 0x40, 0xce, 0x5e, 0xe0,
		},
		.len = 93,
	},
	.iv = {
		.data = {
			0xe1, 0xae, 0x8c, 0x7d, 0x1c, 0x50, 0x3d, 0xb4,
		},
	},

	.xform = {
		.chain = {
			.auth = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_AUTH,
				.auth = {
					.op = RTE_CRYPTO_AUTH_OP_GENERATE,
					.algo = RTE_CRYPTO_AUTH_SHA1_HMAC,
					.key.length = 20,
					.iv.length = 0,
					.iv.offset = 0,
					.digest_length = 20,
				},
			},
			.cipher = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_CIPHER,
				.cipher = {
					.op = RTE_CRYPTO_CIPHER_OP_ENCRYPT,
					.algo = RTE_CRYPTO_CIPHER_3DES_CBC,
					.key.length = 24,
					.iv.length = 8,
					.iv.offset = IV_OFFSET,
				},
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_DTLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.dtls_1_2.epoch = 1,
		.dtls_1_2.seq_no = 1,
	},

	.aead = false,
	.app_type = 0x17,
};

struct tls_record_test_data tls_test_data_aes_128_cbc_sha256_hmac = {
	.key = {
		.data = {
			0x77, 0xe2, 0xad, 0x5f, 0x3a, 0xac, 0xae, 0xaf,
			0x29, 0x4c, 0x94, 0xe0, 0x27, 0x99, 0x1a, 0x6c,
		},
	},
	.auth_key = {
		.data = {
			0x1e, 0xbb, 0x70, 0xaf, 0x09, 0x9d, 0x1b, 0xbd,
			0xd9, 0xce, 0x48, 0x9b, 0x42, 0x52, 0x53, 0x1b,
			0x16, 0x37, 0xdb, 0xc5, 0x0a, 0x0f, 0x01, 0xa4,
			0x57, 0x22, 0x84, 0x95, 0xa5, 0xea, 0x33, 0x61,
		},
	},
	.input_text = {
		.data = {
			/* actual plain text */
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x32, 0x20,
			0x41, 0x45, 0x53, 0x2d, 0x31, 0x32, 0x38, 0x2d,
			0x43, 0x42, 0x43, 0x20, 0x53, 0x48, 0x41, 0x32,
			0x35, 0x36, 0x20, 0x65, 0x78, 0x61, 0x6d, 0x70,
			0x6c, 0x65, 0x20, 0x76, 0x65, 0x63, 0x74, 0x6f,
			0x72, 0xa,
		},
		.len = 50,
	},
	.output_text = {
		.data = {
			0x17, 0x03, 0x03, 0x00, 0x70,
			0xe4, 0xe7, 0xdf, 0x4b, 0x35, 0x8d, 0xcd, 0xc2,
			0x0c, 0x83, 0x54, 0xb9, 0x7c, 0x11, 0x39, 0xcc,
			0xb8, 0x52, 0xd8, 0x79, 0x39, 0x0e, 0x5e, 0xe8,
			0x13, 0x5e, 0xd0, 0x7e, 0x19, 0xd1, 0xbb, 0x5c,
			0x33, 0xdd, 0x95, 0xc9, 0x71, 0x22, 0xa1, 0xae,
			0x21, 0x99, 0x89, 0xde, 0x9a, 0x88, 0xdd, 0x53,
			0x46, 0x7c, 0xa4, 0xdd, 0x21, 0xb6, 0xe1, 0x19,
			0x41, 0xc9, 0xd2, 0x27, 0xa3, 0xec, 0x3f, 0x9a,
			0xd5, 0x53, 0x07, 0x0c, 0x1f, 0xb8, 0x3d, 0xdf,
			0xaf, 0x87, 0xa2, 0x67, 0x97, 0xd0, 0x3f, 0xf5,
			0x25, 0x9c, 0xfd, 0x9d, 0x40, 0x22, 0x91, 0xb7,
			0xed, 0xb0, 0xfc, 0x93, 0xd9, 0xa6, 0x1c, 0x04,
			0x5f, 0xc8, 0x55, 0x4c, 0x32, 0x5f, 0x0b, 0x65,
			0x87, 0xd1, 0x79, 0x72, 0xc3, 0x44, 0x89, 0x2e,
		},
		.len = 117,
	},
	.iv = {
		.data = {
			0xe4, 0xe7, 0xdf, 0x4b, 0x35, 0x8d, 0xcd, 0xc2,
			0x0c, 0x83, 0x54, 0xb9, 0x7c, 0x11, 0x39, 0xcc,
		},
	},

	.xform = {
		.chain = {
			.auth = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_AUTH,
				.auth = {
					.op = RTE_CRYPTO_AUTH_OP_GENERATE,
					.algo = RTE_CRYPTO_AUTH_SHA256_HMAC,
					.key.length = 32,
					.iv.length = 0,
					.iv.offset = 0,
					.digest_length = 32,
				},
			},
			.cipher = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_CIPHER,
				.cipher = {
					.op = RTE_CRYPTO_CIPHER_OP_ENCRYPT,
					.algo = RTE_CRYPTO_CIPHER_AES_CBC,
					.key.length = 16,
					.iv.length = 16,
					.iv.offset = IV_OFFSET,
				},
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_TLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.tls_1_2.seq_no = 0x1,
	},

	.aead = false,
	.app_type = 0x17,
};

struct tls_record_test_data tls_test_data_aes_256_cbc_sha1_hmac = {
	.key = {
		.data = {
			0xa3, 0x43, 0x65, 0xb7, 0xb1, 0x86, 0x96, 0xc9,
			0xa7, 0x98, 0xe9, 0xcc, 0x3e, 0x97, 0x7b, 0xba,
			0x52, 0x5f, 0x9c, 0x0d, 0x25, 0x1d, 0x07, 0x1f,
			0x97, 0xf8, 0xfa, 0x06, 0x1c, 0x1c, 0x23, 0xed,
		},
	},
	.auth_key = {
		.data = {
			0x8e, 0xec, 0x48, 0x7b, 0xe9, 0xf2, 0xbb, 0x47,
			0x82, 0x01, 0x49, 0xa3, 0x79, 0x30, 0x47, 0xb8,
			0xd7, 0x28, 0x94, 0x08,
		},
	},
	.input_text = {
		.data = {
			/* actual plain text */
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x32, 0x20,
			0x41, 0x45, 0x53, 0x2d, 0x32, 0x35, 0x36, 0x2d,
			0x43, 0x42, 0x43, 0x20, 0x53, 0x48, 0x41, 0x31,
			0x20, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0xa,
		},
		.len = 41,
	},
	.output_text = {
		.data = {
			0x17, 0x03, 0x03, 0x00, 0x50,
			0x7a, 0xc6, 0x7e, 0x16, 0x63, 0x2f, 0x11, 0xd4,
			0x14, 0x22, 0x8f, 0xfa, 0x0a, 0x60, 0xe1, 0x58,
			0x11, 0xa3, 0xa3, 0xef, 0x8a, 0x04, 0xab, 0x87,
			0x20, 0x67, 0xc1, 0x99, 0xde, 0x8a, 0xe9, 0x5a,
			0x1c, 0xd6, 0xbd, 0x46, 0x99, 0xeb, 0x60, 0xad,
			0x06, 0xf4, 0xfe, 0x8f, 0x50, 0x5f, 0x18, 0x52,
			0x0c, 0x70, 0x25, 0xf2, 0xcc, 0x65, 0xff, 0x88,
			0x17, 0xe9, 0xe6, 0xf9, 0x5e, 0x6f, 0xf8, 0xaa,
			0x58, 0xf0, 0x49, 0xb5, 0xb3, 0x5c, 0x0f, 0x62,
			0x06, 0x45, 0x08, 0x4e, 0x98, 0x2e, 0x71, 0x26,
		},
		.len = 85,
	},
	.iv = {
		.data = {
			0x7a, 0xc6, 0x7e, 0x16, 0x63, 0x2f, 0x11, 0xd4,
			0x14, 0x22, 0x8f, 0xfa, 0x0a, 0x60, 0xe1, 0x58,
		},
	},

	.xform = {
		.chain = {
			.auth = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_AUTH,
				.auth = {
					.op = RTE_CRYPTO_AUTH_OP_GENERATE,
					.algo = RTE_CRYPTO_AUTH_SHA1_HMAC,
					.key.length = 20,
					.iv.length = 0,
					.iv.offset = 0,
					.digest_length = 20,
				},
			},
			.cipher = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_CIPHER,
				.cipher = {
					.op = RTE_CRYPTO_CIPHER_OP_ENCRYPT,
					.algo = RTE_CRYPTO_CIPHER_AES_CBC,
					.key.length = 32,
					.iv.length = 16,
					.iv.offset = IV_OFFSET,
				},
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_TLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.tls_1_2.seq_no = 0x1,
	},

	.aead = false,
	.app_type = 0x17,
};

struct tls_record_test_data tls_test_data_aes_256_cbc_sha256_hmac = {
	.key = {
		.data = {
			0xed, 0x63, 0xb2, 0xad, 0xa5, 0xb7, 0x95, 0xf1,
			0xf2, 0x64, 0x83, 0xac, 0xd4, 0xbb, 0x37, 0x31,
			0xec, 0xf7, 0x8a, 0xa9, 0xdd, 0x19, 0x6d, 0x47,
			0x12, 0xb4, 0xa0, 0x5b, 0x8f, 0x6f, 0xa7, 0x5d,
		},
	},
	.auth_key = {
		.data = {
			0xe3, 0x0f, 0x07, 0x9c, 0xee, 0x5a, 0xe6, 0xdd,
			0xd1, 0xc5, 0x49, 0x9a, 0x94, 0x22, 0xd3, 0xee,
			0x13, 0x41, 0x5b, 0x2b, 0x2a, 0x88, 0x89, 0xc9,
			0xd8, 0xeb, 0xbf, 0x16, 0x4d, 0x89, 0xcb, 0x5a,
		},
	},
	.input_text = {
		.data = {
			/* actual plain text */
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x32, 0x20,
			0x41, 0x45, 0x53, 0x2d, 0x32, 0x35, 0x36, 0x2d,
			0x43, 0x42, 0x43, 0x20, 0x53, 0x48, 0x41, 0x32,
			0x35, 0x36, 0x20, 0x65, 0x78, 0x61, 0x6d, 0x70,
			0x6c, 0x65, 0x20, 0x76, 0x65, 0x63, 0x74, 0x6f,
			0x72, 0xa,
		},
		.len = 50,
	},
	.output_text = {
		.data = {
			0x17, 0x03, 0x03, 0x00, 0x70,
			0x83, 0x7c, 0x06, 0xa9, 0x9f, 0x57, 0xf3, 0x53,
			0xc2, 0xca, 0xd5, 0xe0, 0x8d, 0xd0, 0x6d, 0x13,
			0xe9, 0x09, 0xff, 0xf7, 0x56, 0xd6, 0x9b, 0x73,
			0xd5, 0xea, 0x43, 0x55, 0xd1, 0xab, 0x25, 0x66,
			0x6e, 0x06, 0x0a, 0x98, 0xa2, 0x83, 0x4d, 0x91,
			0xef, 0x36, 0x2c, 0xde, 0x69, 0xf5, 0xbc, 0x7f,
			0xf6, 0x98, 0x85, 0xab, 0x89, 0x0f, 0x1e, 0xcb,
			0x54, 0x87, 0xae, 0xab, 0xc6, 0x2e, 0xb9, 0x4c,
			0xa5, 0x16, 0x7f, 0x04, 0x92, 0x2a, 0x59, 0x61,
			0x3c, 0x2e, 0x40, 0x6b, 0xb6, 0xb1, 0x2f, 0x04,
			0x66, 0x55, 0xd1, 0x12, 0x9c, 0x35, 0xb5, 0xbd,
			0xbc, 0x0e, 0xa1, 0xb1, 0x88, 0xe5, 0x4f, 0xdd,
			0x11, 0x47, 0x61, 0xed, 0x85, 0xca, 0xbb, 0x3e,
			0xec, 0xf5, 0xea, 0x49, 0x07, 0x86, 0x1f, 0x2a,
		},
		.len = 117,
	},
	.iv = {
		.data = {
			0x83, 0x7c, 0x06, 0xa9, 0x9f, 0x57, 0xf3, 0x53,
			0xc2, 0xca, 0xd5, 0xe0, 0x8d, 0xd0, 0x6d, 0x13,
		},
	},

	.xform = {
		.chain = {
			.auth = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_AUTH,
				.auth = {
					.op = RTE_CRYPTO_AUTH_OP_GENERATE,
					.algo = RTE_CRYPTO_AUTH_SHA256_HMAC,
					.key.length = 32,
					.iv.length = 0,
					.iv.offset = 0,
					.digest_length = 32,
				},
			},
			.cipher = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_CIPHER,
				.cipher = {
					.op = RTE_CRYPTO_CIPHER_OP_ENCRYPT,
					.algo = RTE_CRYPTO_CIPHER_AES_CBC,
					.key.length = 32,
					.iv.length = 16,
					.iv.offset = IV_OFFSET,
				},
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_TLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.tls_1_2.seq_no = 0x1,
	},

	.aead = false,
	.app_type = 0x17,
};

struct tls_record_test_data tls_test_data_aes_256_cbc_sha384_hmac = {
	.key = {
		.data = {
			0x48, 0x70, 0xc7, 0x93, 0x77, 0xe3, 0x4c, 0x8c,
			0x27, 0x00, 0x64, 0x06, 0x3e, 0xc6, 0x47, 0x64,
			0xcc, 0xee, 0xa4, 0x9a, 0x1a, 0xe7, 0x3a, 0xc6,
			0xef, 0xe5, 0xe6, 0x2c, 0x15, 0xe3, 0xac, 0x16,
		},
	},
	.auth_key = {
		.data = {
			0x23, 0x95, 0x84, 0x30, 0xaf, 0x2b, 0x07, 0xfe,
			0x12, 0x83, 0x87, 0x28, 0x2b, 0x38, 0xb9, 0x02,
			0xc0, 0x27, 0x59, 0x3e, 0xa7, 0xbd, 0xce, 0xcb,
			0xe1, 0x8a, 0xe9, 0x43, 0x5d, 0xed, 0xb4, 0xf2,
			0x11, 0x4d, 0x19, 0xbb, 0x0f, 0x1b, 0x76, 0x86,
			0xfb, 0xb5, 0xda, 0xfd, 0x38, 0xfe, 0x7d, 0x02,
		},
	},
	.input_text = {
		.data = {
			/* actual plain text */
			0x20, 0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73,
			0x20, 0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x32,
			0x20, 0x41, 0x45, 0x53, 0x2d, 0x32, 0x35, 0x36,
			0x2d, 0x43, 0x42, 0x43, 0x20, 0x53, 0x48, 0x41,
			0x33, 0x38, 0x34, 0x20, 0x65, 0x78, 0x61, 0x6d,
			0x70, 0x6c, 0x65, 0x20, 0x76, 0x65, 0x63, 0x74,
			0x6f, 0x72, 0x0a,
		},
		.len = 51,
	},
	.output_text = {
		.data = {
			0x17, 0x03, 0x03, 0x00, 0x80,
			0xc4, 0x90, 0xd7, 0x74, 0x5e, 0x26, 0xc8, 0x43,
			0x12, 0x45, 0x48, 0xc1, 0x0f, 0xb1, 0x18, 0xd8,
			0x1e, 0x5b, 0x1e, 0x50, 0x3e, 0x19, 0x25, 0x41,
			0x35, 0xc7, 0x7c, 0x14, 0x99, 0x7b, 0x97, 0x80,
			0x60, 0x9d, 0xf8, 0xf1, 0xac, 0x43, 0x7b, 0x5c,
			0xb6, 0xe2, 0xc4, 0x8f, 0x3f, 0xd7, 0x1b, 0xd4,
			0x61, 0x90, 0x40, 0xe3, 0xd5, 0x60, 0xac, 0xee,
			0x62, 0x53, 0x1f, 0x1f, 0x75, 0xf6, 0x2c, 0xda,
			0x1a, 0xed, 0x4a, 0x6a, 0x11, 0xeb, 0x9b, 0x1c,
			0x39, 0x0d, 0x6e, 0x8a, 0xf8, 0x3d, 0x45, 0x08,
			0x3e, 0x24, 0x17, 0x3e, 0xcf, 0x74, 0xcf, 0x6a,
			0xcb, 0x37, 0xdf, 0x25, 0xc6, 0xa5, 0xe4, 0x1e,
			0x53, 0x28, 0x71, 0xcf, 0xac, 0x1e, 0xad, 0x77,
			0x8c, 0xfc, 0x80, 0x19, 0x9c, 0xcc, 0x00, 0x60,
			0xc6, 0x82, 0xa0, 0xb8, 0x5e, 0x42, 0xd1, 0xff,
			0x14, 0x0a, 0x92, 0x5c, 0xde, 0x8a, 0x15, 0x7a,
		},
		.len = 133,
	},
	.iv = {
		.data = {
			0xc4, 0x90, 0xd7, 0x74, 0x5e, 0x26, 0xc8, 0x43,
			0x12, 0x45, 0x48, 0xc1, 0x0f, 0xb1, 0x18, 0xd8,
		},
	},

	.xform = {
		.chain = {
			.auth = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_AUTH,
				.auth = {
					.op = RTE_CRYPTO_AUTH_OP_GENERATE,
					.algo = RTE_CRYPTO_AUTH_SHA384_HMAC,
					.key.length = 48,
					.iv.length = 0,
					.iv.offset = 0,
					.digest_length = 48,
				},
			},
			.cipher = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_CIPHER,
				.cipher = {
					.op = RTE_CRYPTO_CIPHER_OP_ENCRYPT,
					.algo = RTE_CRYPTO_CIPHER_AES_CBC,
					.key.length = 32,
					.iv.length = 16,
					.iv.offset = IV_OFFSET,
				},
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_TLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.tls_1_2.seq_no = 0x1,
	},

	.aead = false,
	.app_type = 0x17,
};

struct tls_record_test_data tls_test_data_3des_cbc_sha1_hmac = {
	.key = {
		.data = {
			0x71, 0x61, 0x3c, 0x0f, 0xbd, 0x54, 0x25, 0xea,
			0x18, 0x1e, 0xae, 0xb8, 0x6d, 0xed, 0xc7, 0xff,
			0x0e, 0x8d, 0x19, 0x20, 0x9e, 0xba, 0x88, 0xbb,
		},
	},
	.auth_key = {
		.data = {
			0x32, 0x06, 0xea, 0x9c, 0xa8, 0x7a, 0xf5, 0x75,
			0xc5, 0xea, 0xd2, 0x4e, 0x2b, 0x7a, 0xf9, 0x20,
			0x0b, 0xa5, 0x83, 0x53,
		},
	},
	.input_text = {
		.data = {
			/* actual plain text */
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x32, 0x20,
			0x33, 0x44, 0x45, 0x53, 0x2d, 0x43, 0x42, 0x43,
			0x20, 0x53, 0x48, 0x41, 0x31, 0x20, 0x65, 0x78,
			0x61, 0x6d, 0x70, 0x6c, 0x65, 0x20, 0x76, 0x65,
			0x63, 0x74, 0x6f, 0x72, 0xa,
		},
		.len = 45,
	},
	.output_text = {
		.data = {
			0x17, 0x03, 0x03, 0x00, 0x50,
			0x6f, 0xcf, 0x77, 0x13, 0xc3, 0xa2, 0x31, 0x30,
			0x3f, 0xe7, 0x1d, 0x7a, 0xaa, 0xaa, 0x1f, 0x19,
			0xc1, 0xb7, 0x6b, 0xa3, 0x54, 0x24, 0x9c, 0xa6,
			0xe6, 0x8c, 0xf3, 0xef, 0xc5, 0xd4, 0x47, 0xde,
			0x13, 0xe0, 0xf3, 0x2f, 0x91, 0xa1, 0xb9, 0xb0,
			0x4f, 0x16, 0xd3, 0xa9, 0x45, 0xba, 0xd9, 0x37,
			0x07, 0x8d, 0xc6, 0x05, 0x83, 0x07, 0x75, 0x8c,
			0x4b, 0xf5, 0xa4, 0xa0, 0xf8, 0x36, 0xfe, 0x9f,
			0x18, 0xb5, 0x83, 0xb5, 0x47, 0x33, 0x64, 0xdc,
			0x2f, 0xac, 0x95, 0x2a, 0x72, 0xa9, 0x3c, 0xc6,
		},
		.len = 85,
	},
	.iv = {
		.data = {
			0x6f, 0xcf, 0x77, 0x13, 0xc3, 0xa2, 0x31, 0x30,
		},
	},

	.xform = {
		.chain = {
			.auth = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_AUTH,
				.auth = {
					.op = RTE_CRYPTO_AUTH_OP_GENERATE,
					.algo = RTE_CRYPTO_AUTH_SHA1_HMAC,
					.key.length = 20,
					.iv.length = 0,
					.iv.offset = 0,
					.digest_length = 20,
				},
			},
			.cipher = {
				.next = NULL,
				.type = RTE_CRYPTO_SYM_XFORM_CIPHER,
				.cipher = {
					.op = RTE_CRYPTO_CIPHER_OP_ENCRYPT,
					.algo = RTE_CRYPTO_CIPHER_3DES_CBC,
					.key.length = 24,
					.iv.length = 8,
					.iv.offset = IV_OFFSET,
				},
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_TLS_1_2,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.tls_1_2.seq_no = 0x1,
	},

	.aead = false,
	.app_type = 0x17,
};

/* TLS 1.3 AES-128-GCM */
struct tls_record_test_data tls13_test_data_aes_128_gcm = {
	.key = {
		.data = {
			0x03, 0x12, 0xf5, 0x86, 0xe4, 0xd0, 0x27, 0xc7,
			0x47, 0x82, 0x44, 0xca, 0xd3, 0xce, 0x06, 0x6c,
		},
	},
	.input_text = {
		.data = {
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x33, 0x20,
			0x41, 0x45, 0x53, 0x2d, 0x31, 0x32, 0x38, 0x2d,
			0x47, 0x43, 0x4d, 0x20, 0x65, 0x78, 0x61, 0x6d,
			0x70, 0x6c, 0x65, 0x20, 0x76, 0x65, 0x63, 0x74,
			0x6f, 0x72, 0xa,
		},
		.len = 43,
	},
	.output_text = {
		.data = {
			0x17, 0x03, 0x03, 0x00, 0x3c,
			0x52, 0xb5, 0x24, 0xce, 0x5c, 0x29, 0x0f, 0x0a,
			0x3a, 0xc0, 0x60, 0xaf, 0xba, 0xe3, 0x0d, 0x28,
			0x6c, 0xbb, 0x3e, 0x5f, 0xde, 0x4a, 0xcd, 0xf1,
			0x30, 0x12, 0xa9, 0x42, 0x95, 0x55, 0xf5, 0x2c,
			0xb7, 0xb6, 0x60, 0x82, 0xa2, 0x1d, 0x34, 0x33,
			0x0a, 0xd7, 0x48, 0x40, 0xef, 0xab, 0x70, 0xa7,
			0xb2, 0x58, 0x41, 0xdb, 0xf6, 0x37, 0xe4, 0x6d,
			0xa3, 0x1e, 0xbf, 0x6f,
		},
		.len = 65,
	},
	.imp_nonce = {
		.data = {
			0x8d, 0x1f, 0xa0, 0x14, 0xc7, 0x66, 0x9f, 0x93,
			0x74, 0x3f, 0x46, 0x52,
		},
		.len = 12,
	},

	.xform = {
		.aead = {
			.next = NULL,
			.type = RTE_CRYPTO_SYM_XFORM_AEAD,
			.aead = {
				.op = RTE_CRYPTO_AEAD_OP_ENCRYPT,
				.algo = RTE_CRYPTO_AEAD_AES_GCM,
				.key.length = 16,
				.iv.length = 0,
				.iv.offset = 0,
				.digest_length = 16,
				.aad_length = 5,
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_TLS_1_3,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.tls_1_3.seq_no = 0x0,
	},

	.aead = true,
	.app_type = 0x17,
};

/* TLS 1.3 AES-256-GCM */
struct tls_record_test_data tls13_test_data_aes_256_gcm = {
	.key = {
		.data = {
			0xc9, 0xc2, 0xa2, 0x4c, 0x4e, 0x36, 0x19, 0x6e,
			0xd8, 0xf5, 0xb9, 0x14, 0x30, 0xfc, 0xe0, 0xef,
			0x29, 0xb0, 0x00, 0xd1, 0x2d, 0xfc, 0x5a, 0x76,
			0x50, 0xf4, 0xf3, 0xb1, 0x82, 0x21, 0x57, 0x82,
		},
	},
	.input_text = {
		.data = {
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x33, 0x20,
			0x41, 0x45, 0x53, 0x2d, 0x32, 0x35, 0x36, 0x2d,
			0x47, 0x43, 0x4d, 0x20, 0x65, 0x78, 0x61, 0x6d,
			0x70, 0x6c, 0x65, 0x20, 0x76, 0x65, 0x63, 0x74,
			0x6f, 0x72, 0xa,
		},
		.len = 43,
	},
	.output_text = {
		.data = {
			0x17, 0x03, 0x03, 0x00, 0x3c,
			0xc9, 0xb0, 0x44, 0x23, 0xd5, 0xe2, 0xbd, 0x1d,
			0xf1, 0x96, 0x53, 0x2c, 0x8c, 0xe2, 0xa3, 0x00,
			0x7b, 0x2a, 0xb1, 0xa1, 0xd6, 0x79, 0x58, 0xb5,
			0x35, 0x1f, 0xbb, 0x98, 0x03, 0xe1, 0x82, 0xa2,
			0x1e, 0x26, 0x81, 0xbe, 0x77, 0x65, 0xaf, 0x7d,
			0x9f, 0x52, 0xec, 0x3a, 0x18, 0x2d, 0x36, 0xab,
			0xdc, 0xa9, 0xfb, 0xd3, 0xa8, 0xd5, 0xbc, 0x98,
			0xa4, 0xab, 0x70, 0xe9,
		},
		.len = 65,
	},
	.imp_nonce = {
		.data = {
			0xd4, 0x78, 0xf2, 0x90, 0x61, 0x5d, 0x8c, 0x63,
			0x4b, 0xf4, 0x72, 0xf3,
		},
		.len = 12,
	},

	.xform = {
		.aead = {
			.next = NULL,
			.type = RTE_CRYPTO_SYM_XFORM_AEAD,
			.aead = {
				.op = RTE_CRYPTO_AEAD_OP_ENCRYPT,
				.algo = RTE_CRYPTO_AEAD_AES_GCM,
				.key.length = 32,
				.iv.length = 0,
				.iv.offset = 0,
				.digest_length = 16,
				.aad_length = 5,
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_TLS_1_3,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.tls_1_3.seq_no = 0x0,
	},

	.aead = true,
	.app_type = 0x17,
};

struct tls_record_test_data tls13_test_data_chacha20_poly1305 = {
	.key = {
		.data = {
			0xec, 0x7d, 0x7a, 0x3f, 0x91, 0xdd, 0xb9, 0x70,
			0x95, 0x3b, 0x99, 0xb0, 0xe7, 0x66, 0xda, 0xdc,
			0x85, 0xbb, 0xfc, 0xc8, 0x50, 0xe9, 0x61, 0x88,
			0xc8, 0x1e, 0xf0, 0x61, 0xb0, 0xcd, 0x6c, 0x3d,
		},
	},
	.input_text = {
		.data = {
			0x54, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
			0x54, 0x4c, 0x53, 0x20, 0x31, 0x2e, 0x33, 0x20,
			0x43, 0x48, 0x41, 0x43, 0x48, 0x41, 0x32, 0x30,
			0x2d, 0x50, 0x4f, 0x4c, 0x59, 0x31, 0x33, 0x30,
			0x35, 0x20, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c,
			0x65, 0x20, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0xa,
		},
		.len = 49,
	},
	.output_text = {
		.data = {
			0x17, 0x03, 0x03, 0x00, 0x42,
			0x9a, 0xc1, 0xd9, 0x0e, 0xf9, 0x4c, 0x51, 0x8c,
			0xb4, 0xa7, 0x54, 0x57, 0x56, 0xba, 0xbb, 0xf7,
			0xd7, 0x1d, 0x49, 0x5a, 0x42, 0xd2, 0xab, 0x75,
			0x3f, 0xb1, 0x5f, 0xb5, 0x2b, 0x2b, 0xa3, 0xc5,
			0x61, 0x32, 0x7e, 0x62, 0x1e, 0xf5, 0x56, 0xff,
			0x84, 0x8e, 0x9a, 0x99, 0x06, 0xba, 0x3b, 0xc0,
			0x15, 0x4c, 0xf5, 0xb1, 0x5e, 0xcc, 0xff, 0x42,
			0x79, 0x4b, 0xa9, 0x23, 0x16, 0x08, 0xc3, 0x9a,
			0x52, 0x2a,
		},
		.len = 71,
	},
	.imp_nonce = {
		.data = {
			0x3c, 0x28, 0xa0, 0xb8, 0xf8, 0x74, 0x35, 0xfe,
			0xd2, 0xa0, 0x31, 0x28,
		},
		.len = 12,
	},

	.xform = {
		.aead = {
			.next = NULL,
			.type = RTE_CRYPTO_SYM_XFORM_AEAD,
			.aead = {
				.op = RTE_CRYPTO_AEAD_OP_ENCRYPT,
				.algo = RTE_CRYPTO_AEAD_CHACHA20_POLY1305,
				.key.length = 32,
				.iv.length = 0,
				.iv.offset = 0,
				.digest_length = 16,
				.aad_length = 5,
			},
		},
	},

	.tls_record_xform = {
		.ver = RTE_SECURITY_VERSION_TLS_1_3,
		.type = RTE_SECURITY_TLS_SESS_TYPE_WRITE,
		.tls_1_3.seq_no = 0x0,
	},

	.aead = true,
	.app_type = 0x17,
};

#endif
