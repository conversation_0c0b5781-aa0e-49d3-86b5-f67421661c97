/* SPDX-License-Identifier: BSD-3-Clause
 * Copyright(c) 2016-2019 Intel Corporation
 */

#ifndef TEST_CRYPTODEV_SM4_TEST_VECTORS_H_
#define TEST_CRYPTODEV_SM4_TEST_VECTORS_H_

static const uint8_t plaintext_sm4[] = {
	0x81, 0x70, 0x99, 0x44, 0xE0, 0xCB, 0x2E, 0x1D,
	0xB5, 0xB0, 0xA4, 0x77, 0xD1, 0xA8, 0x53, 0x9B,
	0x0A, 0x87, 0x86, 0xE3, 0x4E, 0xAA, 0xED, 0x99,
	0x30, 0x3E, 0xA6, 0x97, 0x55, 0x95, 0xB2, 0x45,
	0x4D, 0x5D, 0x7F, 0x91, 0xEB, 0xBD, 0x4A, 0xCD,
	0x72, 0x6C, 0x0E, 0x0E, 0x5E, 0x3E, 0xB5, 0x5E,
	0xF6, 0xB1, 0x5A, 0x13, 0x8E, 0x22, 0x6E, 0xCD,
	0x1B, 0x23, 0x5A, 0xB5, 0xBB, 0x52, 0x51, 0xC1,
	0x33, 0x76, 0xB2, 0x64, 0x48, 0xA9, 0xAC, 0x1D,
	0xE8, 0xBD, 0x52, 0x64, 0x8C, 0x0B, 0x5F, 0xFA,
	0x94, 0x44, 0x86, 0x82, 0xE3, 0xCB, 0x4D, 0xE9,
	0xCB, 0x8A, 0xE7, 0xF4, 0xBD, 0x41, 0x0E, 0xD5,
	0x02, 0xB1, 0x25, 0x3A, 0xD0, 0x8B, 0xB2, 0x79,
	0x69, 0xB5, 0xF0, 0x2B, 0x10, 0x02, 0x9D, 0x67,
	0xD0, 0x7E, 0x18, 0x64, 0xD9, 0x4D, 0x4F, 0xCA,
	0x20, 0x81, 0x51, 0xE2, 0x6F, 0x5F, 0xEE, 0x26
};

static const uint8_t ciphertext_sm4_ecb[] = {
	0xCC, 0x62, 0x37, 0xA6, 0xA1, 0x35, 0x39, 0x75,
	0xFF, 0xF5, 0xEE, 0x6A, 0xFD, 0xD7, 0x70, 0x15,
	0xE1, 0x32, 0x23, 0x1F, 0x18, 0xB8, 0xC9, 0x16,
	0x07, 0x27, 0x9C, 0x6C, 0x7F, 0x8F, 0x7F, 0xF6,
	0xFD, 0xF1, 0xE4, 0x01, 0xEC, 0x7E, 0xD2, 0x60,
	0xFD, 0xE7, 0x5C, 0xE5, 0xCF, 0x6E, 0xE7, 0x87,
	0x97, 0x13, 0xCC, 0x01, 0x92, 0x1A, 0xC5, 0x62,
	0xB5, 0x0C, 0x45, 0xE4, 0xDC, 0x9A, 0x30, 0xC2,
	0x35, 0xED, 0x7D, 0x1A, 0x93, 0x0D, 0x33, 0x96,
	0xD6, 0xE1, 0x8B, 0x77, 0x64, 0x40, 0x25, 0x3D,
	0x9F, 0x4B, 0x8E, 0xFF, 0x3F, 0x11, 0x41, 0x58,
	0x3B, 0x55, 0x4F, 0x59, 0x38, 0x2E, 0xAA, 0xBB,
	0x8B, 0x42, 0xFA, 0x30, 0x38, 0x0D, 0x05, 0x3B,
	0x86, 0x7C, 0xB0, 0xF2, 0x77, 0xCE, 0xEE, 0x1B,
	0x78, 0xE8, 0x64, 0x7D, 0x59, 0xE3, 0xDA, 0x61,
	0x05, 0x27, 0x56, 0x12, 0x95, 0x6D, 0x34, 0x9C
};

static const uint8_t ciphertext_sm4_cbc[] = {
	0x60, 0x7A, 0xBE, 0xC9, 0xDA, 0xD7, 0x90, 0x73,
	0xC7, 0x96, 0xDB, 0x34, 0x26, 0xFD, 0x2C, 0x2F,
	0x8E, 0x39, 0xC7, 0x0B, 0x60, 0xB2, 0x3D, 0xBE,
	0xF3, 0xA9, 0xA5, 0x46, 0x65, 0x26, 0x41, 0xB7,
	0xAE, 0xC9, 0xC3, 0xAD, 0x8C, 0x9B, 0x95, 0x8D,
	0x17, 0x53, 0x15, 0x35, 0x40, 0x2A, 0x8C, 0x6B,
	0x02, 0x5C, 0xBD, 0x13, 0xA6, 0x7E, 0xB7, 0x63,
	0xC0, 0x3F, 0xA8, 0xBC, 0x73, 0xDD, 0x0B, 0x7A,
	0x88, 0x0E, 0xF8, 0xC5, 0x5B, 0x00, 0x07, 0xFF,
	0x53, 0x7B, 0xF1, 0x6A, 0xA0, 0xFD, 0x0B, 0x89,
	0x03, 0x91, 0x4D, 0xD8, 0xC4, 0xB3, 0xC0, 0x12,
	0x41, 0xEB, 0xF7, 0xCB, 0x0A, 0xFB, 0x68, 0xE7,
	0x8E, 0x0C, 0x14, 0x33, 0x1A, 0x34, 0x55, 0xDA,
	0x04, 0xE2, 0xA3, 0xFC, 0xBE, 0xB6, 0xDF, 0x2B,
	0x61, 0x33, 0x05, 0xBD, 0xBC, 0x0A, 0xB5, 0x8B,
	0x6D, 0x0F, 0x1B, 0x7D, 0x5F, 0x24, 0x46, 0x0E
};

static const uint8_t ciphertext_sm4_cfb[] = {
	0xC1, 0x27, 0x47, 0xC7, 0x44, 0x0C, 0x9A, 0x5C,
	0x7D, 0x51, 0x26, 0x0D, 0x1B, 0xDB, 0x0D, 0x9D,
	0x52, 0x59, 0xAD, 0x56, 0x05, 0xBE, 0x92, 0xD2,
	0xB7, 0x62, 0xF5, 0xD7, 0x53, 0xD3, 0x12, 0x2A,
	0x3C, 0x9A, 0x6E, 0x75, 0x80, 0xAB, 0x18, 0xE5,
	0x72, 0x49, 0x9A, 0xD9, 0x80, 0x99, 0xC2, 0xE7,
	0xCA, 0xD9, 0xDC, 0xD1, 0x45, 0x2F, 0xDD, 0xFC,
	0x01, 0x7F, 0xB8, 0x01, 0x51, 0xCF, 0x43, 0x74,
	0xC0, 0xBA, 0xFE, 0xB0, 0x28, 0xFE, 0xA4, 0xCD,
	0x35, 0x0E, 0xEC, 0xE5, 0x70, 0xA2, 0x7F, 0x5D,
	0x38, 0x1B, 0x50, 0xEB, 0x46, 0xBE, 0x61, 0x6E,
	0x6C, 0x76, 0xF3, 0x65, 0x75, 0xCD, 0xA1, 0xBB,
	0x9F, 0xFA, 0x7B, 0x86, 0x12, 0x87, 0x04, 0xEB,
	0x00, 0x24, 0x81, 0xE7, 0x91, 0xFC, 0x1B, 0xC7,
	0xA6, 0xB2, 0x67, 0xE2, 0x6E, 0x88, 0x8F, 0xB6,
	0x4C, 0x45, 0x96, 0xEF, 0xBF, 0x4C, 0x26, 0x69
};

static const uint8_t ciphertext_sm4_ofb[] = {
	0xC1, 0x27, 0x47, 0xC7, 0x44, 0x0C, 0x9A, 0x5C,
	0x7D, 0x51, 0x26, 0x0D, 0x1B, 0xDB, 0x0D, 0x9D,
	0x0F, 0x0C, 0xAD, 0xA0, 0x2D, 0x18, 0x0B, 0x3C,
	0x54, 0xA9, 0x87, 0x86, 0xBC, 0x6B, 0xF9, 0xFB,
	0x18, 0x68, 0x51, 0x1E, 0xB2, 0x53, 0x1D, 0xD5,
	0x7F, 0x4B, 0xED, 0xB8, 0xCA, 0x8E, 0x81, 0xCE,
	0xE1, 0x16, 0x7F, 0x84, 0x69, 0xD1, 0x15, 0xCE,
	0x84, 0xF0, 0xB0, 0x3A, 0x21, 0xF2, 0x85, 0xA2,
	0xEB, 0x2F, 0xDF, 0x34, 0x52, 0x62, 0x42, 0x87,
	0xFA, 0x7F, 0x02, 0x2A, 0xC2, 0xD9, 0xE4, 0xB0,
	0x8D, 0xC5, 0x52, 0xEC, 0x3D, 0x96, 0x3F, 0xD3,
	0x8C, 0x39, 0x9C, 0x0F, 0xD9, 0x66, 0xDD, 0x29,
	0x90, 0x00, 0x5D, 0x4F, 0x4D, 0x82, 0x2A, 0x47,
	0x9E, 0x7E, 0x46, 0x87, 0x84, 0xE8, 0xDD, 0xAE,
	0xB3, 0x03, 0xF8, 0xE8, 0x7B, 0xA6, 0xC9, 0x9A,
	0x56, 0x9C, 0xC7, 0x82, 0x1E, 0x9A, 0x9D, 0x13
};

static const uint8_t ciphertext_sm4_ctr[] = {
	0xC1, 0x27, 0x47, 0xC7, 0x44, 0x0C, 0x9A, 0x5C,
	0x7D, 0x51, 0x26, 0x0D, 0x1B, 0xDB, 0x0D, 0x9D,
	0xC3, 0x75, 0xCE, 0xBB, 0x63, 0x9A, 0x5B, 0x0C,
	0xED, 0x64, 0x3F, 0x33, 0x80, 0x8F, 0x97, 0x40,
	0xB7, 0x5C, 0xA7, 0xFE, 0x2F, 0x7F, 0xFB, 0x20,
	0x13, 0xEC, 0xDC, 0xBC, 0x96, 0xC8, 0x05, 0xF0,
	0xA4, 0x95, 0xC4, 0x0A, 0xB7, 0x1B, 0x18, 0xB4,
	0xDA, 0x35, 0xFF, 0xA5, 0xB5, 0x90, 0x1B, 0x07,
	0x5C, 0x5B, 0x91, 0x36, 0xF0, 0xC9, 0xFE, 0xFB,
	0xC4, 0x71, 0xD6, 0x3B, 0x03, 0x28, 0x62, 0xB9,
	0x22, 0x7A, 0x97, 0xC9, 0x54, 0xC0, 0x8C, 0x71,
	0xEC, 0x8F, 0xE1, 0xBB, 0x56, 0xAE, 0xAB, 0x16,
	0xF6, 0x57, 0x76, 0x65, 0xC2, 0x4B, 0xE0, 0x46,
	0x4E, 0x13, 0x77, 0x50, 0x91, 0x24, 0x76, 0xD9,
	0xB7, 0x16, 0xFF, 0x9E, 0xD0, 0x2E, 0x14, 0x23,
	0x27, 0xF4, 0x99, 0x03, 0xDA, 0x1C, 0x52, 0x04
};

static const uint8_t ciphertext_sm4_xts[] = {
	0xEB, 0x4E, 0x0F, 0x8B, 0x44, 0x75, 0x9A, 0xE4,
	0xCD, 0xAF, 0x1F, 0x69, 0xCA, 0x90, 0x62, 0x58,
	0x91, 0x7A, 0xA8, 0x14, 0x5D, 0xF3, 0x4E, 0xBC,
	0xFC, 0xA6, 0xFE, 0x36, 0x48, 0x8D, 0x4D, 0x55,
	0x10, 0x00, 0xF0, 0xA5, 0xB2, 0x6D, 0xAB, 0x61,
	0x54, 0x14, 0x8C, 0x9A, 0xFA, 0x8B, 0xDA, 0xA2,
	0x00, 0x12, 0xFE, 0xDF, 0x4A, 0x26, 0x61, 0xE8,
	0x6E, 0x67, 0x8F, 0xE1, 0xBA, 0xAC, 0x27, 0x72,
	0xD8, 0xA1, 0x84, 0xF4, 0xD2, 0x3C, 0xFA, 0xB5,
	0x59, 0xE2, 0x0E, 0xC0, 0x7B, 0xCF, 0x25, 0x78,
	0x1C, 0x02, 0xDE, 0xB7, 0x17, 0xF8, 0x9E, 0x22,
	0x8B, 0x79, 0xF8, 0xA2, 0xFC, 0x12, 0xF9, 0x4A,
	0x5E, 0x48, 0x82, 0xBF, 0x87, 0x57, 0x5E, 0xDC,
	0xF3, 0xA7, 0x47, 0x96, 0x56, 0x00, 0xDD, 0x04,
	0x0E, 0x0E, 0x1B, 0x9E, 0x6B, 0x5C, 0xD0, 0xA6,
	0xB5, 0x7B, 0x9E, 0xB5, 0x5A, 0x19, 0xD9, 0x52,
};

static const struct blockcipher_test_data
sm4_test_data_cbc = {
	.crypto_algo = RTE_CRYPTO_CIPHER_SM4_CBC,
	.cipher_key = {
		.data = {
			0xE0, 0x70, 0x99, 0xF1, 0xBF, 0xAF, 0xFD, 0x7F,
			0x24, 0x0C, 0xD7, 0x90, 0xCA, 0x4F, 0xE1, 0x34
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xC7, 0x2B, 0x65, 0x91, 0xA0, 0xD7, 0xDE, 0x8F,
			0x6B, 0x40, 0x72, 0x33, 0xAD, 0x35, 0x81, 0xD6
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_sm4,
		.len = 128
	},
	.ciphertext = {
		.data = ciphertext_sm4_cbc,
		.len = 128
	},
};

static const struct blockcipher_test_data
sm4_test_data_ctr = {
	.crypto_algo = RTE_CRYPTO_CIPHER_SM4_CTR,
	.cipher_key = {
		.data = {
			0xE0, 0x70, 0x99, 0xF1, 0xBF, 0xAF, 0xFD, 0x7F,
			0x24, 0x0C, 0xD7, 0x90, 0xCA, 0x4F, 0xE1, 0x34
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xC7, 0x2B, 0x65, 0x91, 0xA0, 0xD7, 0xDE, 0x8F,
			0x6B, 0x40, 0x72, 0x33, 0xAD, 0x35, 0x81, 0xD6
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_sm4,
		.len = 128
	},
	.ciphertext = {
		.data = ciphertext_sm4_ctr,
		.len = 128
	},
};

static const struct blockcipher_test_data
sm4_test_data_ecb = {
	.crypto_algo = RTE_CRYPTO_CIPHER_SM4_ECB,
	.cipher_key = {
		.data = {
			0xE0, 0x70, 0x99, 0xF1, 0xBF, 0xAF, 0xFD, 0x7F,
			0x24, 0x0C, 0xD7, 0x90, 0xCA, 0x4F, 0xE1, 0x34
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_sm4,
		.len = 128
	},
	.ciphertext = {
		.data = ciphertext_sm4_ecb,
		.len = 128
	},
};

static const struct blockcipher_test_data
sm4_test_data_ofb = {
	.crypto_algo = RTE_CRYPTO_CIPHER_SM4_OFB,
	.cipher_key = {
		.data = {
			0xE0, 0x70, 0x99, 0xF1, 0xBF, 0xAF, 0xFD, 0x7F,
			0x24, 0x0C, 0xD7, 0x90, 0xCA, 0x4F, 0xE1, 0x34
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xC7, 0x2B, 0x65, 0x91, 0xA0, 0xD7, 0xDE, 0x8F,
			0x6B, 0x40, 0x72, 0x33, 0xAD, 0x35, 0x81, 0xD6
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_sm4,
		.len = 128
	},
	.ciphertext = {
		.data = ciphertext_sm4_ofb,
		.len = 128
	},
};

static const struct blockcipher_test_data
sm4_test_data_cfb = {
	.crypto_algo = RTE_CRYPTO_CIPHER_SM4_CFB,
	.cipher_key = {
		.data = {
			0xE0, 0x70, 0x99, 0xF1, 0xBF, 0xAF, 0xFD, 0x7F,
			0x24, 0x0C, 0xD7, 0x90, 0xCA, 0x4F, 0xE1, 0x34
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xC7, 0x2B, 0x65, 0x91, 0xA0, 0xD7, 0xDE, 0x8F,
			0x6B, 0x40, 0x72, 0x33, 0xAD, 0x35, 0x81, 0xD6
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_sm4,
		.len = 128
	},
	.ciphertext = {
		.data = ciphertext_sm4_cfb,
		.len = 128
	},
};

static const struct blockcipher_test_data
sm4_test_data_xts = {
	.crypto_algo = RTE_CRYPTO_CIPHER_SM4_XTS,
	.cipher_key = {
		.data = {
			0x59, 0x32, 0x43, 0x97, 0x5c, 0xce, 0x7c, 0x8a,
			0x32, 0xac, 0x6b, 0x3c, 0xaf, 0x8a, 0x19, 0xc5,
			0x90, 0xb4, 0x46, 0x18, 0xc8, 0xbf, 0x7a, 0x18,
			0x23, 0x26, 0xc3, 0xb2, 0xb0, 0xa9, 0x93, 0x1c
		},
		.len = 32
	},
	.iv = {
		.data = {
			0xC7, 0x2B, 0x65, 0x91, 0xA0, 0xD7, 0xDE, 0x8F,
			0x6B, 0x40, 0x72, 0x33, 0xAD, 0x35, 0x81, 0xD6
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_sm4,
		.len = 128
	},
	.ciphertext = {
		.data = ciphertext_sm4_xts,
		.len = 128
	},
};

static const struct blockcipher_test_case sm4_cipheronly_test_cases[] = {
	{
		.test_descr = "SM4-CBC Encryption",
		.test_data = &sm4_test_data_cbc,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "SM4-CBC Decryption",
		.test_data = &sm4_test_data_cbc,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "SM4-CTR Encryption",
		.test_data = &sm4_test_data_ctr,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "SM4-CTR Decryption",
		.test_data = &sm4_test_data_ctr,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "SM4-ECB Encryption",
		.test_data = &sm4_test_data_ecb,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "SM4-ECB Decryption",
		.test_data = &sm4_test_data_ecb,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "SM4-OFB Encryption",
		.test_data = &sm4_test_data_ofb,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "SM4-OFB Decryption",
		.test_data = &sm4_test_data_ofb,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "SM4-CFB Encryption",
		.test_data = &sm4_test_data_cfb,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "SM4-CFB Decryption",
		.test_data = &sm4_test_data_cfb,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
	{
		.test_descr = "SM4-XTS Encryption",
		.test_data = &sm4_test_data_xts,
		.op_mask = BLOCKCIPHER_TEST_OP_ENCRYPT,
	},
	{
		.test_descr = "SM4-XTS Decryption",
		.test_data = &sm4_test_data_xts,
		.op_mask = BLOCKCIPHER_TEST_OP_DECRYPT,
	},
};

static const uint8_t plaintext_sm4_common[] = {
	"What a lousy earth! He wondered how many people were destitute that"
};

static const uint8_t ciphertext64_sm4_cbc[] = {
	0x5D, 0x93, 0x4E, 0x50, 0xDC, 0x26, 0x03, 0xB5,
	0x6C, 0xFC, 0xB7, 0x09, 0x11, 0x28, 0x2F, 0xF7,
	0x6C, 0xB6, 0xE6, 0xCC, 0xB8, 0xC5, 0x01, 0x5F,
	0xBE, 0x89, 0xF8, 0xD3, 0xE8, 0xCD, 0x51, 0xC6,
	0x17, 0x31, 0x07, 0xDE, 0xA5, 0x8F, 0xFD, 0x79,
	0x2A, 0xAC, 0xAC, 0x90, 0x47, 0x8D, 0x0F, 0x16,
	0x66, 0xCF, 0x16, 0xF1, 0xE2, 0x4C, 0x79, 0x4A,
	0x6B, 0x5D, 0x6C, 0xAB, 0x98, 0x2B, 0xDB, 0xF8
};

/* test vectors */
static const uint8_t plaintext64_sm4_ctr[] = {
	0x6B, 0xC1, 0xBE, 0xE2, 0x2E, 0x40, 0x9F, 0x96,
	0xE9, 0x3D, 0x7E, 0x11, 0x73, 0x93, 0x17, 0x2A,
	0xAE, 0x2D, 0x8A, 0x57, 0x1E, 0x03, 0xAC, 0x9C,
	0x9E, 0xB7, 0x6F, 0xAC, 0x45, 0xAF, 0x8E, 0x51,
	0x30, 0xC8, 0x1C, 0x46, 0xA3, 0x5C, 0xE4, 0x11,
	0xE5, 0xFB, 0xC1, 0x19, 0x1A, 0x0A, 0x52, 0xEF,
	0xF6, 0x9F, 0x24, 0x45, 0xDF, 0x4F, 0x9B, 0x17,
	0xAD, 0x2B, 0x41, 0x7B, 0xE6, 0x6C, 0x37, 0x10
};

static const uint8_t ciphertext64_sm4_ctr[] = {
	0x14, 0xAE, 0x4A, 0x72, 0xB9, 0x7A, 0x93, 0xCE,
	0x12, 0x16, 0xCC, 0xD9, 0x98, 0xE3, 0x71, 0xC1,
	0x60, 0xF7, 0xEF, 0x8B, 0x63, 0x44, 0xBD, 0x6D,
	0xA1, 0x99, 0x25, 0x05, 0xE5, 0xFC, 0x21, 0x9B,
	0x0B, 0xF0, 0x57, 0xF8, 0x6C, 0x5D, 0x75, 0x10,
	0x3C, 0x0F, 0x46, 0x51, 0x9C, 0x7F, 0xB2, 0xE7,
	0x29, 0x28, 0x05, 0x03, 0x5A, 0xDB, 0x9A, 0x90,
	0xEC, 0xEF, 0x14, 0x53, 0x59, 0xD7, 0xCF, 0x0E
};

static const struct blockcipher_test_data sm4_test_data_1 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_SM4_CTR,
	.cipher_key = {
		.data = {
			0x2B, 0x7E, 0x15, 0x16, 0x28, 0xAE, 0xD2, 0xA6,
			0xAB, 0xF7, 0x15, 0x88, 0x09, 0xCF, 0x4F, 0x3C
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xF0, 0xF1, 0xF2, 0xF3, 0xF4, 0xF5, 0xF6, 0xF7,
			0xF8, 0xF9, 0xFA, 0xFB, 0xFC, 0xFD, 0xFE, 0xFF
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext64_sm4_ctr,
		.len = 64
	},
	.ciphertext = {
		.data = ciphertext64_sm4_ctr,
		.len = 64
	},
	.auth_algo = RTE_CRYPTO_AUTH_SM3_HMAC,
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD
		},
		.len = 20
	},
	.digest = {
		.data = {
			0x3A, 0x90, 0x8E, 0x9E, 0x07, 0x48, 0xBE, 0xFE,
			0x29, 0xB9, 0x61, 0xD8, 0x1A, 0x01, 0x5E, 0x34,
			0x98, 0x1A, 0x35, 0xE5, 0x26, 0x1A, 0x28, 0x1E,
			0xB1, 0xDC, 0xDB, 0xEB, 0xC7, 0x16, 0xF9, 0x9E
		},
		.len = 32,
		.truncated_len = 16
	}
};

/* test vectors */
static const uint8_t plaintext64_sm4_cfb[] = {
	0x6B, 0xC1, 0xBE, 0xE2, 0x2E, 0x40, 0x9F, 0x96,
	0xE9, 0x3D, 0x7E, 0x11, 0x73, 0x93, 0x17, 0x2A,
	0xAE, 0x2D, 0x8A, 0x57, 0x1E, 0x03, 0xAC, 0x9C,
	0x9E, 0xB7, 0x6F, 0xAC, 0x45, 0xAF, 0x8E, 0x51,
	0x30, 0xC8, 0x1C, 0x46, 0xA3, 0x5C, 0xE4, 0x11,
	0xE5, 0xFB, 0xC1, 0x19, 0x1A, 0x0A, 0x52, 0xEF,
	0xF6, 0x9F, 0x24, 0x45, 0xDF, 0x4F, 0x9B, 0x17,
	0xAD, 0x2B, 0x41, 0x7B, 0xE6, 0x6C, 0x37, 0x10
};

static const uint8_t ciphertext64_sm4_cfb[] = {
	0x14, 0xAE, 0x4A, 0x72, 0xB9, 0x7A, 0x93, 0xCE,
	0x12, 0x16, 0xCC, 0xD9, 0x98, 0xE3, 0x71, 0xC1,
	0xAA, 0x8A, 0x2C, 0x8C, 0x61, 0x89, 0x6B, 0x8B,
	0x7C, 0xF8, 0x32, 0x6A, 0xEB, 0xAC, 0xD4, 0x0C,
	0xD1, 0x5D, 0xFA, 0x1D, 0xFD, 0x17, 0x67, 0x02,
	0x06, 0xCD, 0x38, 0x2C, 0x4D, 0x04, 0xF3, 0x96,
	0x2F, 0x85, 0x5C, 0x3F, 0xBB, 0x79, 0x56, 0xA9,
	0xC4, 0x6F, 0x34, 0xB3, 0xBE, 0xEC, 0xCA, 0x40
};

static const struct blockcipher_test_data sm4_test_data_2 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_SM4_CFB,
	.cipher_key = {
		.data = {
			0x2B, 0x7E, 0x15, 0x16, 0x28, 0xAE, 0xD2, 0xA6,
			0xAB, 0xF7, 0x15, 0x88, 0x09, 0xCF, 0x4F, 0x3C
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xF0, 0xF1, 0xF2, 0xF3, 0xF4, 0xF5, 0xF6, 0xF7,
			0xF8, 0xF9, 0xFA, 0xFB, 0xFC, 0xFD, 0xFE, 0xFF
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext64_sm4_cfb,
		.len = 64
	},
	.ciphertext = {
		.data = ciphertext64_sm4_cfb,
		.len = 64
	},
	.auth_algo = RTE_CRYPTO_AUTH_SM3_HMAC,
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD
		},
		.len = 20
	},
	.digest = {
		.data = {
			0x6F, 0x7C, 0xFA, 0x6D, 0x02, 0x5D, 0xE2, 0x4B,
			0x4A, 0xEE, 0xC6, 0x67, 0x0B, 0xD3, 0xCF, 0xBD,
			0x64, 0x8F, 0x01, 0x17, 0x19, 0xD6, 0xFB, 0xEC,
			0x3A, 0x27, 0xE8, 0x0F, 0x51, 0xA3, 0xE2, 0x3F
		},
		.len = 32,
		.truncated_len = 16
	}
};

/* test vectors */
static const uint8_t plaintext64_sm4_ofb[] = {
	0x6B, 0xC1, 0xBE, 0xE2, 0x2E, 0x40, 0x9F, 0x96,
	0xE9, 0x3D, 0x7E, 0x11, 0x73, 0x93, 0x17, 0x2A,
	0xAE, 0x2D, 0x8A, 0x57, 0x1E, 0x03, 0xAC, 0x9C,
	0x9E, 0xB7, 0x6F, 0xAC, 0x45, 0xAF, 0x8E, 0x51,
	0x30, 0xC8, 0x1C, 0x46, 0xA3, 0x5C, 0xE4, 0x11,
	0xE5, 0xFB, 0xC1, 0x19, 0x1A, 0x0A, 0x52, 0xEF,
	0xF6, 0x9F, 0x24, 0x45, 0xDF, 0x4F, 0x9B, 0x17,
	0xAD, 0x2B, 0x41, 0x7B, 0xE6, 0x6C, 0x37, 0x10
};

static const uint8_t ciphertext64_sm4_ofb[] = {
	0x14, 0xAE, 0x4A, 0x72, 0xB9, 0x7A, 0x93, 0xCE,
	0x12, 0x16, 0xCC, 0xD9, 0x98, 0xE3, 0x71, 0xC1,
	0x94, 0x54, 0x3E, 0x9A, 0x3A, 0x0A, 0x22, 0x63,
	0x22, 0xE8, 0xA5, 0xF3, 0x82, 0xB9, 0x14, 0x2D,
	0xEF, 0x5F, 0x94, 0xCB, 0x49, 0x17, 0xEF, 0xA4,
	0xD9, 0xCF, 0x3F, 0xC3, 0x32, 0x30, 0x90, 0x11,
	0x12, 0x61, 0x52, 0xCC, 0xEC, 0x43, 0x61, 0xF2,
	0x6F, 0x1E, 0x1A, 0x8D, 0xB4, 0x9D, 0x2F, 0xC4
};

static const struct blockcipher_test_data sm4_test_data_3 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_SM4_OFB,
	.cipher_key = {
		.data = {
			0x2B, 0x7E, 0x15, 0x16, 0x28, 0xAE, 0xD2, 0xA6,
			0xAB, 0xF7, 0x15, 0x88, 0x09, 0xCF, 0x4F, 0x3C
		},
		.len = 16
	},
	.iv = {
		.data = {
			0xF0, 0xF1, 0xF2, 0xF3, 0xF4, 0xF5, 0xF6, 0xF7,
			0xF8, 0xF9, 0xFA, 0xFB, 0xFC, 0xFD, 0xFE, 0xFF
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext64_sm4_ofb,
		.len = 64
	},
	.ciphertext = {
		.data = ciphertext64_sm4_ofb,
		.len = 64
	},
	.auth_algo = RTE_CRYPTO_AUTH_SM3_HMAC,
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD
		},
		.len = 20
	},
	.digest = {
		.data = {
			0xA7, 0x6F, 0x7D, 0xB5, 0xFB, 0x35, 0x92, 0x82,
			0x97, 0xB7, 0x9B, 0xBF, 0xA9, 0xCE, 0xA8, 0x4C,
			0x5A, 0xC5, 0x53, 0x62, 0x9F, 0x03, 0x54, 0x88,
			0x7D, 0xFE, 0x2D, 0xD3, 0x9C, 0xFB, 0x56, 0xED
		},
		.len = 32,
		.truncated_len = 16
	}
};

static const struct blockcipher_test_data sm4_test_data_4 = {
	.crypto_algo = RTE_CRYPTO_CIPHER_SM4_CBC,
	.cipher_key = {
		.data = {
			0xE4, 0x23, 0x33, 0x8A, 0x35, 0x64, 0x61, 0xE2,
			0x49, 0x03, 0xDD, 0xC6, 0xB8, 0xCA, 0x55, 0x7A
		},
		.len = 16
	},
	.iv = {
		.data = {
			0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
			0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
		},
		.len = 16
	},
	.plaintext = {
		.data = plaintext_sm4_common,
		.len = 64
	},
	.ciphertext = {
		.data = ciphertext64_sm4_cbc,
		.len = 64
	},
	.auth_algo = RTE_CRYPTO_AUTH_SM3_HMAC,
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD
		},
		.len = 20
	},
	.digest = {
		.data = {
			0xA7, 0x87, 0x94, 0x53, 0x17, 0xAE, 0xF9, 0xCD,
			0x8A, 0x60, 0x34, 0xFB, 0x9F, 0x07, 0xED, 0x28,
			0x04, 0x13, 0x8C, 0xD4, 0x48, 0x83, 0x4A, 0xBA,
			0xB8, 0xE8, 0x45, 0xDB, 0xE0, 0x66, 0xF9, 0xDA
		},
		.len = 32,
		.truncated_len = 16
	}
};

static const struct blockcipher_test_case sm4_chain_test_cases[] = {
	{
		.test_descr = "SM4-CBC HMAC-SM3 Encryption Digest",
		.test_data = &sm4_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
	},
	{
		.test_descr = "SM4-CBC HMAC-SM3 Decryption Digest Verify",
		.test_data = &sm4_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
	},
	{
		.test_descr = "SM4-CBC HMAC-SM3 Encryption Digest Scatter Gather",
		.test_data = &sm4_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "SM4-CBC HMAC-SM3 Decryption Digest Verify Scatter Gather",
		.test_data = &sm4_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG,
	},
	{
		.test_descr = "SM4-CBC HMAC-SM3 Encryption Digest OOP",
		.test_data = &sm4_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "SM4-CBC HMAC-SM3 Decryption Digest Verify OOP",
		.test_data = &sm4_test_data_4,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "SM4-CTR HMAC-SM3 Encryption Digest",
		.test_data = &sm4_test_data_1,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
	},
	{
		.test_descr = "SM4-CTR HMAC-SM3 Decryption Digest Verify",
		.test_data = &sm4_test_data_1,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
	},
	{
		.test_descr = "SM4-CTR HMAC-SM3 Encryption Digest Scatter Gather",
		.test_data = &sm4_test_data_1,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "SM4-CTR HMAC-SM3 Decryption Digest Verify Scatter Gather",
		.test_data = &sm4_test_data_1,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG,
	},
	{
		.test_descr = "SM4-CTR HMAC-SM3 Encryption Digest OOP",
		.test_data = &sm4_test_data_1,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "SM4-CTR HMAC-SM3 Decryption Digest Verify OOP",
		.test_data = &sm4_test_data_1,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "SM4-CFB HMAC-SM3 Encryption Digest",
		.test_data = &sm4_test_data_2,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
	},
	{
		.test_descr = "SM4-CFB HMAC-SM3 Decryption Digest Verify",
		.test_data = &sm4_test_data_2,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
	},
	{
		.test_descr = "SM4-CFB HMAC-SM3 Encryption Digest Scatter Gather",
		.test_data = &sm4_test_data_2,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "SM4-CFB HMAC-SM3 Decryption Digest Verify Scatter Gather",
		.test_data = &sm4_test_data_2,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG,
	},
	{
		.test_descr = "SM4-CFB HMAC-SM3 Encryption Digest OOP",
		.test_data = &sm4_test_data_2,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "SM4-CFB HMAC-SM3 Decryption Digest Verify OOP",
		.test_data = &sm4_test_data_2,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "SM4-OFB HMAC-SM3 Encryption Digest",
		.test_data = &sm4_test_data_3,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
	},
	{
		.test_descr = "SM4-OFB HMAC-SM3 Decryption Digest Verify",
		.test_data = &sm4_test_data_3,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
	},
	{
		.test_descr = "SM4-OFB HMAC-SM3 Encryption Digest Scatter Gather",
		.test_data = &sm4_test_data_3,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG |
			BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "SM4-OFB HMAC-SM3 Decryption Digest Verify Scatter Gather",
		.test_data = &sm4_test_data_3,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG,
	},
	{
		.test_descr = "SM4-OFB HMAC-SM3 Encryption Digest OOP",
		.test_data = &sm4_test_data_3,
		.op_mask = BLOCKCIPHER_TEST_OP_ENC_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "SM4-OFB HMAC-SM3 Decryption Digest Verify OOP",
		.test_data = &sm4_test_data_3,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY_DEC,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
};

#endif
