/* SPDX-License-Identifier: BSD-3-Clause
 *
 * Copyright 2020 NXP
 */

#ifndef SECURITY_PDCP_SDAP_TEST_VECTOR_H_
#define SECURITY_PDCP_SDAP_TEST_VECTOR_H_

#include <rte_security.h>

struct pdcp_sdap_test {
	uint32_t test_idx;
	struct pdcp_sdap_test_param {
		uint8_t type;
		enum rte_security_pdcp_domain domain;
		enum rte_crypto_cipher_algorithm cipher_alg;
		uint8_t cipher_key_len;
		enum rte_crypto_auth_algorithm auth_alg;
		uint8_t auth_key_len;
		const char *name;
	} param;
	const uint8_t *cipher_key;
	const uint8_t *auth_key;
	const uint8_t *data_in;
	uint32_t in_len;
	const uint8_t *data_out;
	uint8_t sn_size;
	uint32_t hfn;
	uint32_t hfn_threshold;
	uint8_t bearer;
	uint8_t packet_direction;
};

static const struct pdcp_sdap_test list_pdcp_sdap_tests[] = {
	/**************************** 7bit ************************************/

	/************************ null + null ****************************/

	{
		.test_idx = 1,
		.param = {.name = "PDCP-SDAP Uplane: 7b SN, NULL_ENC Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_NULL,
			.auth_alg = 0,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d,
					   0x51, 0x20, 0x4e, 0xa5, 0xf1, 0x45,
					   0x10, 0x10, 0xd8, 0x52 },
		.auth_key = NULL,
		.data_in = (uint8_t[]){ 0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89,
					0x0b, 0x38, 0xc4, 0x57, 0xa4, 0x9d,
					0x42, 0x14, 0x07, 0xe8 },
		.in_len = 16,
		.data_out = (uint8_t[]){ 0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89,
					 0x0b, 0x38, 0xc4, 0x57, 0xa4, 0x9d,
					 0x42, 0x14, 0x07, 0xe8 },
		.sn_size = 7,
		.hfn = 0xfa557,
		.hfn_threshold = 0xfa558,
		.bearer = 0x3,
		.packet_direction = 0,
	},

	/**************************** null + null ****************************/

	{
		.test_idx = 2,
		.param = {.name = "PDCP-SDAP Uplane: 7b SN, NULL_ENC Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_NULL,
			.auth_alg = 0,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d,
					   0x51, 0x20, 0x4e, 0xa5, 0xf1, 0x45,
					   0x10, 0x10, 0xd8, 0x52 },
		.auth_key = NULL,
		.data_in = (uint8_t[]){ 0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89,
					0x0b, 0x38, 0xc4, 0x57, 0xa4, 0x9d,
					0x42, 0x14, 0x07, 0xe8 },
		.in_len = 16,
		.data_out = (uint8_t[]){ 0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89,
					 0x0b, 0x38, 0xc4, 0x57, 0xa4, 0x9d,
					 0x42, 0x14, 0x07, 0xe8 },
		.sn_size = 7,
		.hfn = 0xfa557,
		.hfn_threshold = 0xfa558,
		.bearer = 0x3,
		.packet_direction = 1,
	},

	/************************ aes-ctr + null ****************************/

	{
		.test_idx = 3,
		.param = {.name = "PDCP-SDAP Uplane: 7b SN, AES-CTR_ENC Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
			.auth_alg = 0,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d,
					   0x51, 0x20, 0x4e, 0xa5, 0xf1, 0x45,
					   0x10, 0x10, 0xd8, 0x52 },
		.auth_key = NULL,
		.data_in = (uint8_t[]){ 0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89,
					0x0b, 0x38, 0xc4, 0x57, 0xa4, 0x9d,
					0x42, 0x14, 0x07, 0xe8 },
		.in_len = 16,
		.data_out = (uint8_t[]){ 0x8b, 0xad, 0x3e, 0x79, 0xa9, 0xc0,
					 0xec, 0xdd, 0x9e, 0x8f, 0x91, 0x3f,
					 0xa1, 0x6e, 0x59, 0xed },
		.sn_size = 7,
		.hfn = 0xfa557,
		.hfn_threshold = 0xfa558,
		.bearer = 0x3,
		.packet_direction = 0,
	},

	/************************ aes-ctr + null ****************************/

	{
		.test_idx = 4,
		.param = {.name =
				"PDCP-SDAP Uplane: 7b SN, AES-CTR_ENC Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
			.auth_alg = 0,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d,
					   0x51, 0x20, 0x4e, 0xa5, 0xf1, 0x45,
					   0x10, 0x10, 0xd8, 0x52 },
		.auth_key = NULL,
		.data_in = (uint8_t[]){ 0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89,
					0x0b, 0x38, 0xc4, 0x57, 0xa4, 0x9d,
					0x42, 0x14, 0x07, 0xe8 },
		.in_len = 16,
		.data_out = (uint8_t[]){ 0x8b, 0xad, 0x31, 0x55, 0x0b, 0x16,
					 0xb2, 0xe9, 0x3b, 0x87, 0x36, 0xd9,
					 0x17, 0xad, 0x90, 0x3f },
		.sn_size = 7,
		.hfn = 0xfa557,
		.hfn_threshold = 0xfa558,
		.bearer = 0x3,
		.packet_direction = 1,
	},

	/**************************** snow + null ****************************/

	{
		.test_idx = 5,
		.param = {.name = "PDCP-SDAP Uplane: 7b SN, SNOW-f8_ENC Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
			.auth_alg = 0,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d,
					   0x51, 0x20, 0x4e, 0xa5, 0xf1, 0x45,
					   0x10, 0x10, 0xd8, 0x52 },
		.auth_key = NULL,
		.data_in = (uint8_t[]){ 0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89,
					0x0b, 0x38, 0xc4, 0x57, 0xa4, 0x9d,
					0x42, 0x14, 0x07, 0xe8 },
		.in_len = 16,
		.data_out = (uint8_t[]){ 0x8b, 0xad, 0xb1, 0x17, 0xbe, 0xb1,
					 0x60, 0xbb, 0xd6, 0x3f, 0x36, 0x96,
					 0x96, 0xcd, 0x2d, 0xa7 },
		.sn_size = 7,
		.hfn = 0xfa557,
		.hfn_threshold = 0xfa558,
		.bearer = 0x3,
		.packet_direction = 0,
	},

	/**************************** snow + null ****************************/

	{
		.test_idx = 6,
		.param = {.name =
				"PDCP-SDAP Uplane: 7b SN, SNOW-f8_ENC Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
			.auth_alg = 0,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d,
					   0x51, 0x20, 0x4e, 0xa5, 0xf1, 0x45,
					   0x10, 0x10, 0xd8, 0x52 },
		.auth_key = NULL,
		.data_in = (uint8_t[]){ 0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89,
					0x0b, 0x38, 0xc4, 0x57, 0xa4, 0x9d,
					0x42, 0x14, 0x07, 0xe8 },
		.in_len = 16,
		.data_out = (uint8_t[]){ 0x8b, 0xad, 0xd3, 0x89, 0x03, 0x1e,
					 0x7d, 0x29, 0xfc, 0x77, 0x94, 0x3c,
					 0x99, 0x72, 0x3c, 0xe8 },
		.sn_size = 7,
		.hfn = 0xfa557,
		.hfn_threshold = 0xfa558,
		.bearer = 0x3,
		.packet_direction = 1,
	},

	/**************************** zuc + null ****************************/

	{
		.test_idx = 7,
		.param = {.name = "PDCP-SDAP Uplane: 7b SN, ZUC_ENC Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
			.auth_alg = 0,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d,
					   0x51, 0x20, 0x4e, 0xa5, 0xf1, 0x45,
					   0x10, 0x10, 0xd8, 0x52 },
		.auth_key = NULL,
		.data_in = (uint8_t[]){ 0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89,
					0x0b, 0x38, 0xc4, 0x57, 0xa4, 0x9d,
					0x42, 0x14, 0x07, 0xe8 },
		.in_len = 16,
		.data_out = (uint8_t[]){ 0x8b, 0xad, 0xfa, 0xad, 0x58, 0x43,
					 0x6f, 0xd9, 0x8f, 0xaa, 0x06, 0x3e,
					 0xdc, 0x52, 0x42, 0x26 },
		.sn_size = 7,
		.hfn = 0xfa557,
		.hfn_threshold = 0xfa558,
		.bearer = 0x3,
		.packet_direction = 0,
	},

	/**************************** zuc + null ****************************/

	{
		.test_idx = 8,
		.param = {.name = "PDCP-SDAP Uplane: 7b SN, ZUC_ENC Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
			.auth_alg = 0,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d,
					   0x51, 0x20, 0x4e, 0xa5, 0xf1, 0x45,
					   0x10, 0x10, 0xd8, 0x52 },
		.auth_key = NULL,
		.data_in = (uint8_t[]){ 0x8b, 0xad, 0x9c, 0x44, 0x1f, 0x89,
					0x0b, 0x38, 0xc4, 0x57, 0xa4, 0x9d,
					0x42, 0x14, 0x07, 0xe8 },
		.in_len = 16,
		.data_out = (uint8_t[]){ 0x8b, 0xad, 0xd8, 0x0a, 0x12, 0xe9,
					 0x7f, 0xab, 0x63, 0x57, 0x99, 0xf2,
					 0x39, 0x18, 0x32, 0xdc },
		.sn_size = 7,
		.hfn = 0xfa557,
		.hfn_threshold = 0xfa558,
		.bearer = 0x3,
		.packet_direction = 1,
	},

	/***************************** 12bit **********************************/

	/**************************** null + null ****************************/

	{
		.test_idx = 9,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, NULL_ENC and NULL_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_NULL,
			.auth_alg = RTE_CRYPTO_AUTH_NULL,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** null + null ****************************/

	{
		.test_idx = 10,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, NULL_ENC and NULL_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_NULL,
			.auth_alg = RTE_CRYPTO_AUTH_NULL,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/************************ null + aes-cmac ****************************/

	{
		.test_idx = 11,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, NULL_ENC and AES-CMAC_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_NULL,
			.auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd, 0x3f, 0x71, 0x26, 0x2e },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/************************ null + aes-cmac ****************************/

	{
		.test_idx = 12,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, NULL_ENC and AES-CMAC_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_NULL,
			.auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd, 0x92, 0xae, 0xfe, 0xf8 },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** null + snow ****************************/

	{
		.test_idx = 13,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, NULL_ENC and SNOW-f9_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_NULL,
			.auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd, 0x74, 0xb8, 0x27, 0x96 },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** null + snow ****************************/

	{
		.test_idx = 14,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, NULL_ENC and SNOW-f9_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_NULL,
			.auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd, 0x3d, 0x8b, 0x0d, 0xa8 },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** null + zuc ****************************/

	{
		.test_idx = 15,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, NULL_ENC and ZUC_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_NULL,
			.auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd, 0x54, 0xef, 0x25, 0xc3 },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** null + zuc ****************************/

	{
		.test_idx = 16,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, NULL_ENC and ZUC_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_NULL,
			.auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd, 0xf7, 0x16, 0xd4, 0x94 },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** aes-ctr + null *************************/

	{
		.test_idx = 17,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, AES-CTR_ENC and NULL_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
			.auth_alg = RTE_CRYPTO_AUTH_NULL,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0x69, 0xf2, 0x5d, 0xd7,
				     0xf5, 0xc1, 0xf7, 0x1e, 0x47, 0x5d, 0xce,
				     0xbe, 0x48, 0xb5, 0x0b, 0x6a, 0x73, 0x9a,
				     0x5a, 0xa3, 0x06, 0x47, 0x40, 0x96, 0xcf,
				     0x86, 0x98, 0x3d, 0x6f, 0xcf, 0x17, 0x41,
				     0xa5, 0x13, 0xa5, 0x35, 0x9a, 0xa6, 0x24,
				     0xc9, 0x7f, 0x11, 0x79, 0x24, 0x44, 0xe8,
				     0x39, 0x11, 0x03, 0x0a, 0x9d, 0x4f, 0xe2,
				     0x95, 0x9f, 0x47, 0x73, 0x37, 0x83, 0x8c,
				     0x23, 0xfa, 0x16, 0x06, 0x86, 0x33, 0x3f },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/************************* aes-ctr + null ****************************/

	{
		.test_idx = 18,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, AES-CTR_ENC and NULL_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
			.auth_alg = RTE_CRYPTO_AUTH_NULL,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0x22, 0x2a, 0x8f, 0x86,
				     0x25, 0x92, 0xcd, 0xa9, 0xa1, 0xa0, 0xf5,
				     0x86, 0x0d, 0xe8, 0xe4, 0xef, 0xe0, 0x78,
				     0x45, 0x7c, 0x0c, 0x41, 0x5c, 0x8f, 0x07,
				     0x6f, 0xec, 0x2f, 0x99, 0x8b, 0xfd, 0x9c,
				     0x4a, 0x6a, 0xe4, 0x5d, 0xd3, 0x3c, 0x40,
				     0x35, 0xff, 0x7a, 0x42, 0xac, 0x94, 0x21,
				     0x60, 0x1c, 0x13, 0xcc, 0x7e, 0x6b, 0x2f,
				     0x6f, 0x91, 0x89, 0xc6, 0xd4, 0xe6, 0x17,
				     0xae, 0xde, 0xfb, 0x02, 0xcc, 0xa7, 0x33 },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/********************* aes-ctr + aes-cmac ****************************/

	{
		.test_idx = 19,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, AES-CTR_ENC and AES-CMAC_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
			.auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0x69, 0xf2, 0x5d, 0xd7,
				     0xf5, 0xc1, 0xf7, 0x1e, 0x47, 0x5d, 0xce,
				     0xbe, 0x48, 0xb5, 0x0b, 0x6a, 0x73, 0x9a,
				     0x5a, 0xa3, 0x06, 0x47, 0x40, 0x96, 0xcf,
				     0x86, 0x98, 0x3d, 0x6f, 0xcf, 0x17, 0x41,
				     0xa5, 0x13, 0xa5, 0x35, 0x9a, 0xa6, 0x24,
				     0xc9, 0x7f, 0x11, 0x79, 0x24, 0x44, 0xe8,
				     0x39, 0x11, 0x03, 0x0a, 0x9d, 0x4f, 0xe2,
				     0x95, 0x9f, 0x47, 0x73, 0x37, 0x83, 0x8c,
				     0x23, 0xfa, 0x16, 0xb2, 0xb0, 0x17, 0x4a },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/********************* aes-ctr + aes-cmac ****************************/

	{
		.test_idx = 20,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, AES-CTR_ENC and AES-CMAC_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
			.auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0x22, 0x2a, 0x8f, 0x86,
				     0x25, 0x92, 0xcd, 0xa9, 0xa1, 0xa0, 0xf5,
				     0x86, 0x0d, 0xe8, 0xe4, 0xef, 0xe0, 0x78,
				     0x45, 0x7c, 0x0c, 0x41, 0x5c, 0x8f, 0x07,
				     0x6f, 0xec, 0x2f, 0x99, 0x8b, 0xfd, 0x9c,
				     0x4a, 0x6a, 0xe4, 0x5d, 0xd3, 0x3c, 0x40,
				     0x35, 0xff, 0x7a, 0x42, 0xac, 0x94, 0x21,
				     0x60, 0x1c, 0x13, 0xcc, 0x7e, 0x6b, 0x2f,
				     0x6f, 0x91, 0x89, 0xc6, 0xd4, 0xe6, 0x17,
				     0xae, 0xde, 0xfb, 0x19, 0xDa, 0x9a, 0xc2 },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/************************* aes-ctr + snow ****************************/

	{
		.test_idx = 21,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, AES-CTR_ENC and SNOW-f9_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
			.auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0x69, 0xf2, 0x5d, 0xd7,
				     0xf5, 0xc1, 0xf7, 0x1e, 0x47, 0x5d, 0xce,
				     0xbe, 0x48, 0xb5, 0x0b, 0x6a, 0x73, 0x9a,
				     0x5a, 0xa3, 0x06, 0x47, 0x40, 0x96, 0xcf,
				     0x86, 0x98, 0x3d, 0x6f, 0xcf, 0x17, 0x41,
				     0xa5, 0x13, 0xa5, 0x35, 0x9a, 0xa6, 0x24,
				     0xc9, 0x7f, 0x11, 0x79, 0x24, 0x44, 0xe8,
				     0x39, 0x11, 0x03, 0x0a, 0x9d, 0x4f, 0xe2,
				     0x95, 0x9f, 0x47, 0x73, 0x37, 0x83, 0x8c,
				     0x23, 0xfa, 0x16, 0x6c, 0xcb, 0x92, 0xdf },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/************************* aes-ctr + snow ****************************/

	{
		.test_idx = 22,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, AES-CTR_ENC and SNOW-f9_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
			.auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0x22, 0x2a, 0x8f, 0x86,
				     0x25, 0x92, 0xcd, 0xa9, 0xa1, 0xa0, 0xf5,
				     0x86, 0x0d, 0xe8, 0xe4, 0xef, 0xe0, 0x78,
				     0x45, 0x7c, 0x0c, 0x41, 0x5c, 0x8f, 0x07,
				     0x6f, 0xec, 0x2f, 0x99, 0x8b, 0xfd, 0x9c,
				     0x4a, 0x6a, 0xe4, 0x5d, 0xd3, 0x3c, 0x40,
				     0x35, 0xff, 0x7a, 0x42, 0xac, 0x94, 0x21,
				     0x60, 0x1c, 0x13, 0xcc, 0x7e, 0x6b, 0x2f,
				     0x6f, 0x91, 0x89, 0xc6, 0xd4, 0xe6, 0x17,
				     0xae, 0xde, 0xfb, 0x5b, 0xc2, 0x9f, 0x29 },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** aes-ctr + zuc **************************/

	{
		.test_idx = 23,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, AES-CTR_ENC and ZUC_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
			.auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){
				0x80, 0x01, 0x86, 0x69, 0xf2, 0x5d, 0xd7, 0xf5,
				0xc1, 0xf7, 0x1e, 0x47, 0x5d, 0xce, 0xbe, 0x48,
				0xb5, 0x0b, 0x6a, 0x73, 0x9a, 0x5a, 0xa3, 0x06,
				0x47, 0x40, 0x96, 0xcf, 0x86, 0x98, 0x3d, 0x6f,
				0xcf, 0x17, 0x41, 0xa5, 0x13, 0xa5, 0x35, 0x9a,
				0xa6, 0x24, 0xc9, 0x7f, 0x11, 0x79, 0x24, 0x44,
				0xe8, 0x39, 0x11, 0x03, 0x0a, 0x9d, 0x4f, 0xe2,
				0x95, 0x9f, 0x47, 0x73, 0x37, 0x83, 0x8c, 0x23,
				0xfa, 0x16, 0x5d, 0x83, 0x73, 0x34,
			},
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** aes-ctr + zuc **************************/

	{
		.test_idx = 24,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, AES-CTR_ENC and ZUC_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
			.auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0x22, 0x2a, 0x8f, 0x86,
				     0x25, 0x92, 0xcd, 0xa9, 0xa1, 0xa0, 0xf5,
				     0x86, 0x0d, 0xe8, 0xe4, 0xef, 0xe0, 0x78,
				     0x45, 0x7c, 0x0c, 0x41, 0x5c, 0x8f, 0x07,
				     0x6f, 0xec, 0x2f, 0x99, 0x8b, 0xfd, 0x9c,
				     0x4a, 0x6a, 0xe4, 0x5d, 0xd3, 0x3c, 0x40,
				     0x35, 0xff, 0x7a, 0x42, 0xac, 0x94, 0x21,
				     0x60, 0x1c, 0x13, 0xcc, 0x7e, 0x6b, 0x2f,
				     0x6f, 0x91, 0x89, 0xc6, 0xd4, 0xe6, 0x17,
				     0xae, 0xde, 0xfb, 0xff, 0xf9, 0xef, 0xff },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** snow + null ****************************/

	{
		.test_idx = 25,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, SNOW-f8_ENC and NULL_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
			.auth_alg = RTE_CRYPTO_AUTH_NULL,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xe8, 0x8c, 0x96, 0x38,
				     0x6e, 0xe4, 0x1f, 0xb9, 0x85, 0x61, 0x2e,
				     0x64, 0x31, 0x22, 0x97, 0x5b, 0xf6, 0x40,
				     0x08, 0x65, 0xc3, 0xfa, 0x72, 0xd2, 0x22,
				     0x37, 0x8e, 0x4d, 0xc1, 0xac, 0x7c, 0x7d,
				     0x03, 0xf1, 0x50, 0x9f, 0x97, 0x67, 0x4e,
				     0x23, 0x80, 0xcc, 0x5f, 0xfd, 0x74, 0xb1,
				     0xfb, 0xe1, 0xea, 0x6f, 0xf9, 0x9d, 0xf3,
				     0x90, 0x02, 0x6e, 0xf1, 0xf8, 0x95, 0x97,
				     0xbd, 0xba, 0x08, 0x06, 0x12, 0x07, 0xac },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** snow + null ****************************/

	{
		.test_idx = 26,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, SNOW-f8_ENC and NULL_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
			.auth_alg = RTE_CRYPTO_AUTH_NULL,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xd2, 0xc0, 0x3a, 0x41,
				     0xbc, 0x0b, 0x95, 0xa4, 0x57, 0x3e, 0x36,
				     0x10, 0xb2, 0xff, 0x48, 0x1f, 0x10, 0xa8,
				     0x06, 0x42, 0xc5, 0xa2, 0x33, 0xed, 0x2e,
				     0x37, 0x23, 0x04, 0x06, 0x84, 0xb9, 0x4c,
				     0x1b, 0x17, 0xf3, 0x16, 0xc4, 0xd1, 0xa8,
				     0x35, 0xd2, 0x31, 0x1a, 0xb8, 0x33, 0xc7,
				     0x9c, 0xa4, 0xa4, 0x9b, 0x20, 0xc6, 0x47,
				     0xdd, 0xe5, 0xa3, 0x4b, 0x9e, 0x66, 0xde,
				     0xc9, 0x0a, 0x64, 0x1c, 0xd7, 0x20, 0x52 },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** snow + aes-cmac ************************/

	{
		.test_idx = 27,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, SNOW-f8_ENC and AES-CMAC_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
			.auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xe8, 0x8c, 0x96, 0x38,
				     0x6e, 0xe4, 0x1f, 0xb9, 0x85, 0x61, 0x2e,
				     0x64, 0x31, 0x22, 0x97, 0x5b, 0xf6, 0x40,
				     0x08, 0x65, 0xc3, 0xfa, 0x72, 0xd2, 0x22,
				     0x37, 0x8e, 0x4d, 0xc1, 0xac, 0x7c, 0x7d,
				     0x03, 0xf1, 0x50, 0x9f, 0x97, 0x67, 0x4e,
				     0x23, 0x80, 0xcc, 0x5f, 0xfd, 0x74, 0xb1,
				     0xfb, 0xe1, 0xea, 0x6f, 0xf9, 0x9d, 0xf3,
				     0x90, 0x02, 0x6e, 0xf1, 0xf8, 0x95, 0x97,
				     0xbd, 0xba, 0x08, 0xb2, 0x24, 0x23, 0xd9 },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** snow + aes-cmac ************************/

	{
		.test_idx = 28,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, SNOW-f8_ENC and AES-CMAC_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
			.auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xd2, 0xc0, 0x3a, 0x41,
				     0xbc, 0x0b, 0x95, 0xa4, 0x57, 0x3e, 0x36,
				     0x10, 0xb2, 0xff, 0x48, 0x1f, 0x10, 0xa8,
				     0x06, 0x42, 0xc5, 0xa2, 0x33, 0xed, 0x2e,
				     0x37, 0x23, 0x04, 0x06, 0x84, 0xb9, 0x4c,
				     0x1b, 0x17, 0xf3, 0x16, 0xc4, 0xd1, 0xa8,
				     0x35, 0xd2, 0x31, 0x1a, 0xb8, 0x33, 0xc7,
				     0x9c, 0xa4, 0xa4, 0x9b, 0x20, 0xc6, 0x47,
				     0xdd, 0xe5, 0xa3, 0x4b, 0x9e, 0x66, 0xde,
				     0xc9, 0x0a, 0x64, 0x07, 0xc1, 0x1d, 0xa3 },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** snow + snow ****************************/

	{
		.test_idx = 29,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, SNOW-f8_ENC and SNOW-f9_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
			.auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xe8, 0x8c, 0x96, 0x38,
				     0x6e, 0xe4, 0x1f, 0xb9, 0x85, 0x61, 0x2e,
				     0x64, 0x31, 0x22, 0x97, 0x5b, 0xf6, 0x40,
				     0x08, 0x65, 0xc3, 0xfa, 0x72, 0xd2, 0x22,
				     0x37, 0x8e, 0x4d, 0xc1, 0xac, 0x7c, 0x7d,
				     0x03, 0xf1, 0x50, 0x9f, 0x97, 0x67, 0x4e,
				     0x23, 0x80, 0xcc, 0x5f, 0xfd, 0x74, 0xb1,
				     0xfb, 0xe1, 0xea, 0x6f, 0xf9, 0x9d, 0xf3,
				     0x90, 0x02, 0x6e, 0xf1, 0xf8, 0x95, 0x97,
				     0xbd, 0xba, 0x08, 0x6c, 0x5f, 0xa6, 0x4c },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** snow + snow ****************************/

	{
		.test_idx = 30,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, SNOW-f8_ENC and SNOW-f9_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
			.auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xd2, 0xc0, 0x3a, 0x41,
				     0xbc, 0x0b, 0x95, 0xa4, 0x57, 0x3e, 0x36,
				     0x10, 0xb2, 0xff, 0x48, 0x1f, 0x10, 0xa8,
				     0x06, 0x42, 0xc5, 0xa2, 0x33, 0xed, 0x2e,
				     0x37, 0x23, 0x04, 0x06, 0x84, 0xb9, 0x4c,
				     0x1b, 0x17, 0xf3, 0x16, 0xc4, 0xd1, 0xa8,
				     0x35, 0xd2, 0x31, 0x1a, 0xb8, 0x33, 0xc7,
				     0x9c, 0xa4, 0xa4, 0x9b, 0x20, 0xc6, 0x47,
				     0xdd, 0xe5, 0xa3, 0x4b, 0x9e, 0x66, 0xde,
				     0xc9, 0x0a, 0x64, 0x45, 0xd9, 0x18, 0x48 },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** snow + zuc ****************************/

	{
		.test_idx = 31,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, SNOW-f8_ENC and ZUC_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
			.auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xe8, 0x8c, 0x96, 0x38,
				     0x6e, 0xe4, 0x1f, 0xb9, 0x85, 0x61, 0x2e,
				     0x64, 0x31, 0x22, 0x97, 0x5b, 0xf6, 0x40,
				     0x08, 0x65, 0xc3, 0xfa, 0x72, 0xd2, 0x22,
				     0x37, 0x8e, 0x4d, 0xc1, 0xac, 0x7c, 0x7d,
				     0x03, 0xf1, 0x50, 0x9f, 0x97, 0x67, 0x4e,
				     0x23, 0x80, 0xcc, 0x5f, 0xfd, 0x74, 0xb1,
				     0xfb, 0xe1, 0xea, 0x6f, 0xf9, 0x9d, 0xf3,
				     0x90, 0x02, 0x6e, 0xf1, 0xf8, 0x95, 0x97,
				     0xbd, 0xba, 0x08, 0x5d, 0x17, 0x47, 0xa7 },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** snow + zuc ****************************/

	{
		.test_idx = 32,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, SNOW-f8_ENC and ZUC_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
			.auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xd2, 0xc0, 0x3a, 0x41,
				     0xbc, 0x0b, 0x95, 0xa4, 0x57, 0x3e, 0x36,
				     0x10, 0xb2, 0xff, 0x48, 0x1f, 0x10, 0xa8,
				     0x06, 0x42, 0xc5, 0xa2, 0x33, 0xed, 0x2e,
				     0x37, 0x23, 0x04, 0x06, 0x84, 0xb9, 0x4c,
				     0x1b, 0x17, 0xf3, 0x16, 0xc4, 0xd1, 0xa8,
				     0x35, 0xd2, 0x31, 0x1a, 0xb8, 0x33, 0xc7,
				     0x9c, 0xa4, 0xa4, 0x9b, 0x20, 0xc6, 0x47,
				     0xdd, 0xe5, 0xa3, 0x4b, 0x9e, 0x66, 0xde,
				     0xc9, 0x0a, 0x64, 0xe1, 0xe2, 0x68, 0x9e },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** zuc + null ****************************/

	{
		.test_idx = 33,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, ZUC_ENC and NULL_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
			.auth_alg = RTE_CRYPTO_AUTH_NULL,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0x79, 0xdb, 0x02, 0x27,
				     0xa0, 0x85, 0x82, 0x87, 0x6b, 0x35, 0x2b,
				     0xe1, 0x96, 0x5b, 0xfd, 0x67, 0xe1, 0x67,
				     0x69, 0x17, 0x5b, 0x0a, 0x8f, 0xb8, 0x37,
				     0xeb, 0xb8, 0x3f, 0x13, 0x7b, 0x88, 0x40,
				     0x29, 0x67, 0x21, 0xf9, 0xc0, 0xaf, 0xca,
				     0xda, 0x94, 0x61, 0x8e, 0x2d, 0x7a, 0x5d,
				     0x64, 0x0f, 0x4a, 0xc7, 0x7c, 0x9d, 0x56,
				     0x5a, 0x2a, 0xcb, 0xff, 0x79, 0x07, 0xa5,
				     0xaf, 0x96, 0x5c, 0x89, 0x1d, 0xcd, 0x3a },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** zuc + null ****************************/

	{
		.test_idx = 34,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, ZUC_ENC and NULL_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
			.auth_alg = RTE_CRYPTO_AUTH_NULL,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x50, 0x01, 0x86, 0x2c, 0x0f, 0xc1, 0x08,
				     0x2e, 0xa7, 0x97, 0xd2, 0x6c, 0x17, 0x8f,
				     0x22, 0x1f, 0x99, 0x1b, 0x48, 0x04, 0x29,
				     0x54, 0x3f, 0x07, 0x01, 0xda, 0x19, 0x59,
				     0x74, 0x17, 0x45, 0x46, 0x7e, 0x6c, 0x0b,
				     0xc5, 0x6e, 0x90, 0xa7, 0x1e, 0x8d, 0x01,
				     0x04, 0xf4, 0xfe, 0xe6, 0xf7, 0x53, 0x51,
				     0x70, 0x10, 0x56, 0x83, 0x59, 0xb5, 0x64,
				     0x0b, 0xa6, 0x2e, 0xa5, 0x03, 0x62, 0x13,
				     0x91, 0xaf, 0x24, 0x20, 0x2c, 0x05, 0xdf },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** zuc + aes-cmac *************************/

	{
		.test_idx = 35,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, ZUC_ENC and AES-CMAC_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
			.auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0x79, 0xdb, 0x02, 0x27,
				     0xa0, 0x85, 0x82, 0x87, 0x6b, 0x35, 0x2b,
				     0xe1, 0x96, 0x5b, 0xfd, 0x67, 0xe1, 0x67,
				     0x69, 0x17, 0x5b, 0x0a, 0x8f, 0xb8, 0x37,
				     0xeb, 0xb8, 0x3f, 0x13, 0x7b, 0x88, 0x40,
				     0x29, 0x67, 0x21, 0xf9, 0xc0, 0xaf, 0xca,
				     0xda, 0x94, 0x61, 0x8e, 0x2d, 0x7a, 0x5d,
				     0x64, 0x0f, 0x4a, 0xc7, 0x7c, 0x9d, 0x56,
				     0x5a, 0x2a, 0xcb, 0xff, 0x79, 0x07, 0xa5,
				     0xaf, 0x96, 0x5c, 0x3d, 0x2b, 0xe9, 0x4f },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** zuc + aes-cmac *************************/

	{
		.test_idx = 36,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, ZUC_ENC and AES-CMAC_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
			.auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0x2c, 0x0f, 0xc1, 0x08,
				     0x2e, 0xa7, 0x97, 0xd2, 0x6c, 0x17, 0x8f,
				     0x22, 0x1f, 0x99, 0x1b, 0x48, 0x04, 0x29,
				     0x54, 0x3f, 0x07, 0x01, 0xda, 0x19, 0x59,
				     0x74, 0x17, 0x45, 0x46, 0x7e, 0x6c, 0x0b,
				     0xc5, 0x6e, 0x90, 0xa7, 0x1e, 0x8d, 0x01,
				     0x04, 0xf4, 0xfe, 0xe6, 0xf7, 0x53, 0x51,
				     0x70, 0x10, 0x56, 0x83, 0x59, 0xb5, 0x64,
				     0x0b, 0xa6, 0x2e, 0xa5, 0x03, 0x62, 0x13,
				     0x91, 0xaf, 0x24, 0x3b, 0x3a, 0x38, 0x2e },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** zuc + snow ****************************/

	{
		.test_idx = 37,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, ZUC_ENC and SNOW-f9_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
			.auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0x79, 0xdb, 0x02, 0x27,
				     0xa0, 0x85, 0x82, 0x87, 0x6b, 0x35, 0x2b,
				     0xe1, 0x96, 0x5b, 0xfd, 0x67, 0xe1, 0x67,
				     0x69, 0x17, 0x5b, 0x0a, 0x8f, 0xb8, 0x37,
				     0xeb, 0xb8, 0x3f, 0x13, 0x7b, 0x88, 0x40,
				     0x29, 0x67, 0x21, 0xf9, 0xc0, 0xaf, 0xca,
				     0xda, 0x94, 0x61, 0x8e, 0x2d, 0x7a, 0x5d,
				     0x64, 0x0f, 0x4a, 0xc7, 0x7c, 0x9d, 0x56,
				     0x5a, 0x2a, 0xcb, 0xff, 0x79, 0x07, 0xa5,
				     0xaf, 0x96, 0x5c, 0xe3, 0x50, 0x6c, 0xda },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** zuc + snow ****************************/

	{
		.test_idx = 38,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, ZUC_ENC and SNOW-f9_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
			.auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0x2c, 0x0f, 0xc1, 0x08,
				     0x2e, 0xa7, 0x97, 0xd2, 0x6c, 0x17, 0x8f,
				     0x22, 0x1f, 0x99, 0x1b, 0x48, 0x04, 0x29,
				     0x54, 0x3f, 0x07, 0x01, 0xda, 0x19, 0x59,
				     0x74, 0x17, 0x45, 0x46, 0x7e, 0x6c, 0x0b,
				     0xc5, 0x6e, 0x90, 0xa7, 0x1e, 0x8d, 0x01,
				     0x04, 0xf4, 0xfe, 0xe6, 0xf7, 0x53, 0x51,
				     0x70, 0x10, 0x56, 0x83, 0x59, 0xb5, 0x64,
				     0x0b, 0xa6, 0x2e, 0xa5, 0x03, 0x62, 0x13,
				     0x91, 0xaf, 0x24, 0x79, 0x22, 0x3d, 0xc5 },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** zuc + zuc ****************************/

	{
		.test_idx = 39,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, ZUC_ENC and ZUC_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
			.auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0x79, 0xdb, 0x02, 0x27,
				     0xa0, 0x85, 0x82, 0x87, 0x6b, 0x35, 0x2b,
				     0xe1, 0x96, 0x5b, 0xfd, 0x67, 0xe1, 0x67,
				     0x69, 0x17, 0x5b, 0x0a, 0x8f, 0xb8, 0x37,
				     0xeb, 0xb8, 0x3f, 0x13, 0x7b, 0x88, 0x40,
				     0x29, 0x67, 0x21, 0xf9, 0xc0, 0xaf, 0xca,
				     0xda, 0x94, 0x61, 0x8e, 0x2d, 0x7a, 0x5d,
				     0x64, 0x0f, 0x4a, 0xc7, 0x7c, 0x9d, 0x56,
				     0x5a, 0x2a, 0xcb, 0xff, 0x79, 0x07, 0xa5,
				     0xaf, 0x96, 0x5c, 0xd2, 0x18, 0x8d, 0x31 },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** zuc + zuc ****************************/

	{
		.test_idx = 40,
		.param = {.name =
			"PDCP-SDAP Uplane: 12b SN, ZUC_ENC and ZUC_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
			.auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x40, 0x19, 0xe2, 0x99, 0x16, 0xc3,
					   0x7e, 0x9b, 0xa6, 0x8f, 0x57, 0xbe,
					   0x97, 0xfd, 0x02, 0xcb },
		.auth_key = (uint8_t[]){ 0x4d, 0x4b, 0x2b, 0x1e, 0x39, 0x60,
					 0x0a, 0x98, 0xc1, 0x3c, 0x98, 0x82,
					 0xdc, 0xb6, 0xc2, 0x36 },
		.data_in =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0xb8, 0xf8, 0xdb, 0x2d,
				     0x3f, 0x23, 0x82, 0x53, 0xfd, 0x37, 0xde,
				     0x88, 0x63, 0x08, 0x4f, 0xd3, 0x71, 0xfb,
				     0xeb, 0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf,
				     0x3f, 0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3,
				     0x9c, 0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a,
				     0x15, 0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee,
				     0x2c, 0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b,
				     0x01, 0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4,
				     0x7a, 0xf9, 0xdd },
		.in_len = 66,
		.data_out =
			(uint8_t[]){ 0x80, 0x01, 0x86, 0x2c, 0x0f, 0xc1, 0x08,
				     0x2e, 0xa7, 0x97, 0xd2, 0x6c, 0x17, 0x8f,
				     0x22, 0x1f, 0x99, 0x1b, 0x48, 0x04, 0x29,
				     0x54, 0x3f, 0x07, 0x01, 0xda, 0x19, 0x59,
				     0x74, 0x17, 0x45, 0x46, 0x7e, 0x6c, 0x0b,
				     0xc5, 0x6e, 0x90, 0xa7, 0x1e, 0x8d, 0x01,
				     0x04, 0xf4, 0xfe, 0xe6, 0xf7, 0x53, 0x51,
				     0x70, 0x10, 0x56, 0x83, 0x59, 0xb5, 0x64,
				     0x0b, 0xa6, 0x2e, 0xa5, 0x03, 0x62, 0x13,
				     0x91, 0xaf, 0x24, 0xdd, 0x19, 0x4d, 0x13 },
		.sn_size = 12,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/***************************** 15bit **********************************/

	/**************************** null + null ****************************/

	{
		.test_idx = 41,
		.param = {.name = "PDCP-SDAP Uplane: 15b SN, NULL_ENC Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_NULL,
			.auth_alg = 0,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d,
					   0x51, 0x20, 0x4e, 0xa5, 0xf1, 0x45,
					   0x10, 0x10, 0xd8, 0x52 },
		.auth_key = NULL,
		.data_in = (uint8_t[]){ 0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f,
					0x89, 0x0b, 0x38, 0xc4, 0x57, 0xa4,
					0x9d, 0x42, 0x14, 0x07, 0xe8 },
		.in_len = 17,
		.data_out = (uint8_t[]){ 0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f,
					 0x89, 0x0b, 0x38, 0xc4, 0x57, 0xa4,
					 0x9d, 0x42, 0x14, 0x07, 0xe8 },
		.sn_size = 15,
		.hfn = 0xfa557,
		.hfn_threshold = 0xfa558,
		.bearer = 0x3,
		.packet_direction = 0,
	},

	/**************************** null + null ****************************/

	{
		.test_idx = 42,
		.param = {.name =
			"PDCP-SDAP Uplane: 15b SN, NULL_ENC Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_NULL,
			.auth_alg = 0,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d,
					   0x51, 0x20, 0x4e, 0xa5, 0xf1, 0x45,
					   0x10, 0x10, 0xd8, 0x52 },
		.auth_key = NULL,
		.data_in = (uint8_t[]){ 0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f,
					0x89, 0x0b, 0x38, 0xc4, 0x57, 0xa4,
					0x9d, 0x42, 0x14, 0x07, 0xe8 },
		.in_len = 17,
		.data_out = (uint8_t[]){ 0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f,
					 0x89, 0x0b, 0x38, 0xc4, 0x57, 0xa4,
					 0x9d, 0x42, 0x14, 0x07, 0xe8 },
		.sn_size = 15,
		.hfn = 0xfa557,
		.hfn_threshold = 0xfa558,
		.bearer = 0x3,
		.packet_direction = 1,
	},

	/**************************** aes-ctr + null *************************/

	{
		.test_idx = 43,
		.param = {.name =
			"PDCP-SDAP Uplane: 15b SN, AES-CTR_ENC Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
			.auth_alg = 0,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d,
					   0x51, 0x20, 0x4e, 0xa5, 0xf1, 0x45,
					   0x10, 0x10, 0xd8, 0x52 },
		.auth_key = NULL,
		.data_in = (uint8_t[]){ 0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f,
					0x89, 0x0b, 0x38, 0xc4, 0x57, 0xa4,
					0x9d, 0x42, 0x14, 0x07, 0xe8 },
		.in_len = 17,
		.data_out = (uint8_t[]){ 0x8b, 0x26, 0xad, 0x90, 0xf6, 0xf8,
					 0xf2, 0x2b, 0xb2, 0x40, 0x40, 0x9c,
					 0xd6, 0x31, 0x66, 0x62, 0xcc },
		.sn_size = 15,
		.hfn = 0xfa557,
		.hfn_threshold = 0xfa558,
		.bearer = 0x3,
		.packet_direction = 0,
	},

	/**************************** aes-ctr + null *************************/

	{
		.test_idx = 44,
		.param = {.name =
			"PDCP-SDAP Uplane: 15b SN, AES-CTR_ENC Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
			.auth_alg = 0,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d,
					   0x51, 0x20, 0x4e, 0xa5, 0xf1, 0x45,
					   0x10, 0x10, 0xd8, 0x52 },
		.auth_key = NULL,
		.data_in = (uint8_t[]){ 0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f,
					0x89, 0x0b, 0x38, 0xc4, 0x57, 0xa4,
					0x9d, 0x42, 0x14, 0x07, 0xe8 },
		.in_len = 17,
		.data_out = (uint8_t[]){ 0x8b, 0x26, 0xad, 0xf6, 0x2a, 0x78,
					 0x25, 0x3c, 0xf3, 0x23, 0x56, 0x1e,
					 0x0e, 0xea, 0x2a, 0x75, 0x4c },
		.sn_size = 15,
		.hfn = 0xfa557,
		.hfn_threshold = 0xfa558,
		.bearer = 0x3,
		.packet_direction = 1,
	},

	/**************************** snow + null ****************************/

	{
		.test_idx = 45,
		.param = {.name =
			"PDCP-SDAP Uplane: 15b SN, SNOW-f8_ENC Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
			.auth_alg = 0,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d,
					   0x51, 0x20, 0x4e, 0xa5, 0xf1, 0x45,
					   0x10, 0x10, 0xd8, 0x52 },
		.auth_key = NULL,
		.data_in = (uint8_t[]){ 0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f,
					0x89, 0x0b, 0x38, 0xc4, 0x57, 0xa4,
					0x9d, 0x42, 0x14, 0x07, 0xe8 },
		.in_len = 17,
		.data_out = (uint8_t[]){ 0x8b, 0x26, 0xad, 0x8f, 0xaa, 0x5e,
					 0xee, 0x10, 0xdf, 0x4d, 0xdc, 0x2e,
					 0x64, 0x23, 0x36, 0x3f, 0x75 },
		.sn_size = 15,
		.hfn = 0xfa557,
		.hfn_threshold = 0xfa558,
		.bearer = 0x3,
		.packet_direction = 0,
	},

	/**************************** snow + null ****************************/

	{
		.test_idx = 46,
		.param = {.name =
			"PDCP-SDAP Uplane: 15b SN, SNOW-f8_ENC Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
			.auth_alg = 0,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d,
					   0x51, 0x20, 0x4e, 0xa5, 0xf1, 0x45,
					   0x10, 0x10, 0xd8, 0x52 },
		.auth_key = NULL,
		.data_in = (uint8_t[]){ 0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f,
					0x89, 0x0b, 0x38, 0xc4, 0x57, 0xa4,
					0x9d, 0x42, 0x14, 0x07, 0xe8 },
		.in_len = 17,
		.data_out = (uint8_t[]){ 0x8b, 0x26, 0xad, 0x3a, 0x88, 0xa8,
					 0x69, 0xb5, 0xd0, 0x97, 0x3c, 0xfb,
					 0xe1, 0x29, 0x49, 0xd9, 0x80 },
		.sn_size = 15,
		.hfn = 0xfa557,
		.hfn_threshold = 0xfa558,
		.bearer = 0x3,
		.packet_direction = 1,
	},

	/**************************** zuc + null ****************************/

	{
		.test_idx = 47,
		.param = {.name = "PDCP-SDAP Uplane: 15b SN, ZUC_ENC Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
			.auth_alg = 0,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d,
					   0x51, 0x20, 0x4e, 0xa5, 0xf1, 0x45,
					   0x10, 0x10, 0xd8, 0x52 },
		.auth_key = NULL,
		.data_in = (uint8_t[]){ 0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f,
					0x89, 0x0b, 0x38, 0xc4, 0x57, 0xa4,
					0x9d, 0x42, 0x14, 0x07, 0xe8 },
		.in_len = 17,
		.data_out = (uint8_t[]){ 0x8b, 0x26, 0xad, 0x30, 0xd2, 0xe1,
					 0xef, 0x7a, 0xd6, 0x63, 0xb1, 0xc4,
					 0x92, 0x83, 0x28, 0xbe, 0x39 },
		.sn_size = 15,
		.hfn = 0xfa557,
		.hfn_threshold = 0xfa558,
		.bearer = 0x3,
		.packet_direction = 0,
	},

	/**************************** zuc + null ****************************/

	{
		.test_idx = 48,
		.param = {.name =
			"PDCP-SDAP Uplane: 15b SN, ZUC_ENC Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
			.auth_alg = 0,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x5a, 0xcb, 0x1d, 0x64, 0x4c, 0x0d,
					   0x51, 0x20, 0x4e, 0xa5, 0xf1, 0x45,
					   0x10, 0x10, 0xd8, 0x52 },
		.auth_key = NULL,
		.data_in = (uint8_t[]){ 0x8b, 0x26, 0xad, 0x9c, 0x44, 0x1f,
					0x89, 0x0b, 0x38, 0xc4, 0x57, 0xa4,
					0x9d, 0x42, 0x14, 0x07, 0xe8 },
		.in_len = 17,
		.data_out = (uint8_t[]){ 0x8b, 0x26, 0xad, 0x92, 0xc2, 0x45,
					 0xb4, 0x75, 0x24, 0x76, 0x26, 0xaa,
					 0xe1, 0xf4, 0x45, 0xce, 0xfd },
		.sn_size = 15,
		.hfn = 0xfa557,
		.hfn_threshold = 0xfa558,
		.bearer = 0x3,
		.packet_direction = 1,
	},

	/***************************** 18bit **********************************/

	/**************************** null + null ****************************/

	{
		.test_idx = 49,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, NULL_ENC and NULL_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_NULL,
			.auth_alg = RTE_CRYPTO_AUTH_NULL,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F,
				     0x23, 0x82, 0x53, 0xFD, 0x37, 0xDE, 0x88,
				     0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
				     0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F,
				     0x57, 0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C,
				     0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15,
				     0xD0, 0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C,
				     0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01,
				     0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A,
				     0xF9, 0xDD, 0xCC, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0xF8, 0x00, 0x00, 0xF8, 0xDB, 0x2D, 0x3F,
				     0x23, 0x82, 0x53, 0xFD, 0x37, 0xDE, 0x88,
				     0x63, 0x08, 0x4F, 0xD3, 0x71, 0xFB, 0xEB,
				     0x35, 0xF3, 0x64, 0xD3, 0x5E, 0xAF, 0x3F,
				     0x57, 0xC2, 0xE2, 0x91, 0x91, 0xA3, 0x9C,
				     0xE6, 0x30, 0x69, 0x70, 0x33, 0x8A, 0x15,
				     0xD0, 0x36, 0x47, 0x0E, 0x8F, 0xEE, 0x2C,
				     0x96, 0x0C, 0xD7, 0x7D, 0x70, 0x1B, 0x01,
				     0x7F, 0x96, 0x46, 0x53, 0xB0, 0xA4, 0x7A,
				     0xF9, 0xDD, 0xCC, 0x69 },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** null + null ****************************/

	{
		.test_idx = 50,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, NULL_ENC and NULL_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_NULL,
			.auth_alg = RTE_CRYPTO_AUTH_NULL,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** null + aes-cmac ************************/

	{
		.test_idx = 51,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, NULL_ENC and AES-CMAC_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_NULL,
			.auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69, 0x70, 0x3e, 0x09,
				     0xc7 },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** null + aes-cmac ************************/

	{
		.test_idx = 52,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, NULL_ENC and AES-CMAC_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_NULL,
			.auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69, 0x55, 0x89, 0xe3,
				     0x52 },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** null + snow ****************************/

	{
		.test_idx = 53,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, NULL_ENC and SNOW-f9_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_NULL,
			.auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69, 0x00, 0x45, 0x13,
				     0x4c },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** null + snow ****************************/

	{
		.test_idx = 54,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, NULL_ENC and SNOW-f9_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_NULL,
			.auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69, 0x73, 0x53, 0x85,
				     0x5c },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** null + zuc ****************************/

	{
		.test_idx = 55,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, NULL_ENC and ZUC_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_NULL,
			.auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69, 0x00, 0x8b, 0x37,
				     0x29 },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** null + zuc ****************************/

	{
		.test_idx = 56,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, NULL_ENC and ZUC_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_NULL,
			.auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69, 0xa5, 0xd4, 0xca,
				     0xc5 },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** aes-ctr + null ************************/

	{
		.test_idx = 57,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, AES-CTR_ENC and NULL_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
			.auth_alg = RTE_CRYPTO_AUTH_NULL,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0x78, 0xee, 0x01,
				     0x4c, 0x79, 0xc7, 0x14, 0x79, 0x2c, 0x1b,
				     0x8a, 0xd0, 0x17, 0xb4, 0xb4, 0xfe, 0xa7,
				     0x17, 0x9a, 0x93, 0x5d, 0xad, 0x27, 0x8a,
				     0x56, 0xd7, 0x36, 0xec, 0x8d, 0x74, 0xff,
				     0x7c, 0x93, 0x4d, 0x89, 0x00, 0x30, 0x40,
				     0x37, 0xfb, 0x98, 0x1b, 0x06, 0x1a, 0x76,
				     0xf8, 0x07, 0x72, 0xe9, 0xa2, 0x89, 0x47,
				     0x95, 0x8f, 0x8f, 0x4e, 0x45, 0x55, 0xd6,
				     0x0e, 0xf4, 0xe7, 0xe8, 0x08, 0xe3, 0xc8,
				     0x55 },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** aes-ctr + null *************************/

	{
		.test_idx = 58,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, AES-CTR_ENC and NULL_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
			.auth_alg = RTE_CRYPTO_AUTH_NULL,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xbf, 0x21, 0xc9,
				     0x7d, 0x02, 0x8d, 0xf7, 0xf7, 0x80, 0x50,
				     0x60, 0x32, 0x09, 0xb8, 0x69, 0x48, 0xab,
				     0x58, 0xf0, 0xd9, 0x63, 0x63, 0x36, 0x01,
				     0x3f, 0x95, 0xc3, 0xfa, 0xe1, 0xc7, 0x95,
				     0x1f, 0x66, 0x1a, 0xd6, 0x8e, 0xec, 0x0e,
				     0x4c, 0x00, 0xd0, 0x0e, 0x31, 0x1c, 0x87,
				     0xee, 0x1c, 0xc3, 0x81, 0xb4, 0xb6, 0x09,
				     0xb5, 0x3c, 0x85, 0xb9, 0xcc, 0x2d, 0x2e,
				     0x9c, 0x85, 0x0b, 0xf7, 0xe4, 0x09, 0xd3,
				     0xf7 },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** aes-ctr + aes-cmac *********************/

	{
		.test_idx = 59,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, AES-CTR_ENC and AES-CMAC_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
			.auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0x78, 0xee, 0x01,
				     0x4c, 0x79, 0xc7, 0x14, 0x79, 0x2c, 0x1b,
				     0x8a, 0xd0, 0x17, 0xb4, 0xb4, 0xfe, 0xa7,
				     0x17, 0x9a, 0x93, 0x5d, 0xad, 0x27, 0x8a,
				     0x56, 0xd7, 0x36, 0xec, 0x8d, 0x74, 0xff,
				     0x7c, 0x93, 0x4d, 0x89, 0x00, 0x30, 0x40,
				     0x37, 0xfb, 0x98, 0x1b, 0x06, 0x1a, 0x76,
				     0xf8, 0x07, 0x72, 0xe9, 0xa2, 0x89, 0x47,
				     0x95, 0x8f, 0x8f, 0x4e, 0x45, 0x55, 0xd6,
				     0x0e, 0xf4, 0xe7, 0xe8, 0xc0, 0x48, 0x6a,
				     0x7c },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** aes-ctr + aes-cmac *********************/

	{
		.test_idx = 60,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, AES-CTR_ENC and AES-CMAC_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
			.auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xbf, 0x21, 0xc9,
				     0x7d, 0x02, 0x8d, 0xf7, 0xf7, 0x80, 0x50,
				     0x60, 0x32, 0x09, 0xb8, 0x69, 0x48, 0xab,
				     0x58, 0xf0, 0xd9, 0x63, 0x63, 0x36, 0x01,
				     0x3f, 0x95, 0xc3, 0xfa, 0xe1, 0xc7, 0x95,
				     0x1f, 0x66, 0x1a, 0xd6, 0x8e, 0xec, 0x0e,
				     0x4c, 0x00, 0xd0, 0x0e, 0x31, 0x1c, 0x87,
				     0xee, 0x1c, 0xc3, 0x81, 0xb4, 0xb6, 0x09,
				     0xb5, 0x3c, 0x85, 0xb9, 0xcc, 0x2d, 0x2e,
				     0x9c, 0x85, 0x0b, 0xf7, 0x17, 0x28, 0x0f,
				     0x7d },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** aes-ctr + snow ************************/

	{
		.test_idx = 61,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, AES-CTR_ENC and SNOW-f9_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
			.auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0x78, 0xee, 0x01,
				     0x4c, 0x79, 0xc7, 0x14, 0x79, 0x2c, 0x1b,
				     0x8a, 0xd0, 0x17, 0xb4, 0xb4, 0xfe, 0xa7,
				     0x17, 0x9a, 0x93, 0x5d, 0xad, 0x27, 0x8a,
				     0x56, 0xd7, 0x36, 0xec, 0x8d, 0x74, 0xff,
				     0x7c, 0x93, 0x4d, 0x89, 0x00, 0x30, 0x40,
				     0x37, 0xfb, 0x98, 0x1b, 0x06, 0x1a, 0x76,
				     0xf8, 0x07, 0x72, 0xe9, 0xa2, 0x89, 0x47,
				     0x95, 0x8f, 0x8f, 0x4e, 0x45, 0x55, 0xd6,
				     0x0e, 0xf4, 0xe7, 0xe8, 0x8e, 0x76, 0x4a,
				     0x4e },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** aes-ctr + snow ************************/

	{
		.test_idx = 62,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, AES-CTR_ENC and SNOW-f9_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
			.auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xbf, 0x21, 0xc9,
				     0x7d, 0x02, 0x8d, 0xf7, 0xf7, 0x80, 0x50,
				     0x60, 0x32, 0x09, 0xb8, 0x69, 0x48, 0xab,
				     0x58, 0xf0, 0xd9, 0x63, 0x63, 0x36, 0x01,
				     0x3f, 0x95, 0xc3, 0xfa, 0xe1, 0xc7, 0x95,
				     0x1f, 0x66, 0x1a, 0xd6, 0x8e, 0xec, 0x0e,
				     0x4c, 0x00, 0xd0, 0x0e, 0x31, 0x1c, 0x87,
				     0xee, 0x1c, 0xc3, 0x81, 0xb4, 0xb6, 0x09,
				     0xb5, 0x3c, 0x85, 0xb9, 0xcc, 0x2d, 0x2e,
				     0x9c, 0x85, 0x0b, 0xf7, 0xc1, 0x27, 0x82,
				     0xc3 },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** aes-ctr + zuc **************************/

	{
		.test_idx = 63,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, AES-CTR_ENC and ZUC_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
			.auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0x78, 0xee, 0x01,
				     0x4c, 0x79, 0xc7, 0x14, 0x79, 0x2c, 0x1b,
				     0x8a, 0xd0, 0x17, 0xb4, 0xb4, 0xfe, 0xa7,
				     0x17, 0x9a, 0x93, 0x5d, 0xad, 0x27, 0x8a,
				     0x56, 0xd7, 0x36, 0xec, 0x8d, 0x74, 0xff,
				     0x7c, 0x93, 0x4d, 0x89, 0x00, 0x30, 0x40,
				     0x37, 0xfb, 0x98, 0x1b, 0x06, 0x1a, 0x76,
				     0xf8, 0x07, 0x72, 0xe9, 0xa2, 0x89, 0x47,
				     0x95, 0x8f, 0x8f, 0x4e, 0x45, 0x55, 0xd6,
				     0x0e, 0xf4, 0xe7, 0xe8, 0x97, 0x76, 0xce,
				     0xac },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** aes-ctr + zuc **************************/

	{
		.test_idx = 64,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, AES-CTR_ENC and ZUC_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_AES_CTR,
			.auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xbf, 0x21, 0xc9,
				     0x7d, 0x02, 0x8d, 0xf7, 0xf7, 0x80, 0x50,
				     0x60, 0x32, 0x09, 0xb8, 0x69, 0x48, 0xab,
				     0x58, 0xf0, 0xd9, 0x63, 0x63, 0x36, 0x01,
				     0x3f, 0x95, 0xc3, 0xfa, 0xe1, 0xc7, 0x95,
				     0x1f, 0x66, 0x1a, 0xd6, 0x8e, 0xec, 0x0e,
				     0x4c, 0x00, 0xd0, 0x0e, 0x31, 0x1c, 0x87,
				     0xee, 0x1c, 0xc3, 0x81, 0xb4, 0xb6, 0x09,
				     0xb5, 0x3c, 0x85, 0xb9, 0xcc, 0x2d, 0x2e,
				     0x9c, 0x85, 0x0b, 0xf7, 0x69, 0x56, 0x6f,
				     0xaf },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** snow + null ****************************/

	{
		.test_idx = 65,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, SNOW-f8_ENC and NULL_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
			.auth_alg = RTE_CRYPTO_AUTH_NULL,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0x0c, 0xa8, 0x9d,
				     0x9e, 0xcc, 0xf0, 0x1a, 0xc0, 0xf2, 0x9f,
				     0x8c, 0xc9, 0x57, 0xc7, 0x99, 0x4d, 0xde,
				     0xc5, 0x19, 0x69, 0x58, 0x5b, 0x1a, 0x51,
				     0x40, 0xa8, 0xc4, 0x93, 0x4b, 0x47, 0xb8,
				     0xc4, 0x2a, 0x19, 0x07, 0x80, 0x45, 0xbb,
				     0xb3, 0x24, 0x88, 0x68, 0x5f, 0x78, 0x3a,
				     0xd2, 0x6c, 0xcc, 0xa0, 0xb5, 0xf0, 0x95,
				     0x3e, 0xf1, 0xf4, 0x3e, 0x43, 0x8f, 0x6c,
				     0xae, 0x22, 0x59, 0x11, 0x86, 0xa9, 0x02,
				     0xbc },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** snow + null ****************************/

	{
		.test_idx = 66,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, SNOW-f8_ENC and NULL_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
			.auth_alg = RTE_CRYPTO_AUTH_NULL,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0x5a, 0xa9, 0xb5,
				     0x61, 0x8b, 0x8a, 0xb7, 0x6a, 0x98, 0x30,
				     0x6c, 0xed, 0x84, 0x69, 0xff, 0x6b, 0x7e,
				     0x30, 0x59, 0x55, 0x80, 0x32, 0xd0, 0x0f,
				     0x73, 0x43, 0x7c, 0xc3, 0x2e, 0xf4, 0xc3,
				     0x3a, 0x60, 0xe3, 0x7c, 0xf0, 0x31, 0xa2,
				     0x0e, 0x2e, 0x33, 0xe6, 0xa4, 0xea, 0x17,
				     0xd6, 0x56, 0xa2, 0x3b, 0x5f, 0x56, 0x3a,
				     0xa7, 0x6f, 0x4c, 0xc1, 0xca, 0xbd, 0x5f,
				     0xad, 0x3d, 0x99, 0x4a, 0xf6, 0x22, 0x36,
				     0x2e },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** snow + aes-cmac ************************/

	{
		.test_idx = 67,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, SNOW-f8_ENC and AES-CMAC_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
			.auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0x0c, 0xa8, 0x9d,
				     0x9e, 0xcc, 0xf0, 0x1a, 0xc0, 0xf2, 0x9f,
				     0x8c, 0xc9, 0x57, 0xc7, 0x99, 0x4d, 0xde,
				     0xc5, 0x19, 0x69, 0x58, 0x5b, 0x1a, 0x51,
				     0x40, 0xa8, 0xc4, 0x93, 0x4b, 0x47, 0xb8,
				     0xc4, 0x2a, 0x19, 0x07, 0x80, 0x45, 0xbb,
				     0xb3, 0x24, 0x88, 0x68, 0x5f, 0x78, 0x3a,
				     0xd2, 0x6c, 0xcc, 0xa0, 0xb5, 0xf0, 0x95,
				     0x3e, 0xf1, 0xf4, 0x3e, 0x43, 0x8f, 0x6c,
				     0xae, 0x22, 0x59, 0x11, 0x4e, 0x02, 0xa0,
				     0x95 },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** snow + aes-cmac ************************/

	{
		.test_idx = 68,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, SNOW-f8_ENC and AES-CMAC_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
			.auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0x5a, 0xa9, 0xb5,
				     0x61, 0x8b, 0x8a, 0xb7, 0x6a, 0x98, 0x30,
				     0x6c, 0xed, 0x84, 0x69, 0xff, 0x6b, 0x7e,
				     0x30, 0x59, 0x55, 0x80, 0x32, 0xd0, 0x0f,
				     0x73, 0x43, 0x7c, 0xc3, 0x2e, 0xf4, 0xc3,
				     0x3a, 0x60, 0xe3, 0x7c, 0xf0, 0x31, 0xa2,
				     0x0e, 0x2e, 0x33, 0xe6, 0xa4, 0xea, 0x17,
				     0xd6, 0x56, 0xa2, 0x3b, 0x5f, 0x56, 0x3a,
				     0xa7, 0x6f, 0x4c, 0xc1, 0xca, 0xbd, 0x5f,
				     0xad, 0x3d, 0x99, 0x4a, 0x05, 0x03, 0xea,
				     0xa4 },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** snow + snow ****************************/

	{
		.test_idx = 69,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, SNOW-f8_ENC and SNOW-f9_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
			.auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0x0c, 0xa8, 0x9d,
				     0x9e, 0xcc, 0xf0, 0x1a, 0xc0, 0xf2, 0x9f,
				     0x8c, 0xc9, 0x57, 0xc7, 0x99, 0x4d, 0xde,
				     0xc5, 0x19, 0x69, 0x58, 0x5b, 0x1a, 0x51,
				     0x40, 0xa8, 0xc4, 0x93, 0x4b, 0x47, 0xb8,
				     0xc4, 0x2a, 0x19, 0x07, 0x80, 0x45, 0xbb,
				     0xb3, 0x24, 0x88, 0x68, 0x5f, 0x78, 0x3a,
				     0xd2, 0x6c, 0xcc, 0xa0, 0xb5, 0xf0, 0x95,
				     0x3e, 0xf1, 0xf4, 0x3e, 0x43, 0x8f, 0x6c,
				     0xae, 0x22, 0x59, 0x11, 0x00, 0x3c, 0x80,
				     0xa7 },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** snow + snow ****************************/

	{
		.test_idx = 70,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, SNOW-f8_ENC and SNOW-f9_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
			.auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0x5a, 0xa9, 0xb5,
				     0x61, 0x8b, 0x8a, 0xb7, 0x6a, 0x98, 0x30,
				     0x6c, 0xed, 0x84, 0x69, 0xff, 0x6b, 0x7e,
				     0x30, 0x59, 0x55, 0x80, 0x32, 0xd0, 0x0f,
				     0x73, 0x43, 0x7c, 0xc3, 0x2e, 0xf4, 0xc3,
				     0x3a, 0x60, 0xe3, 0x7c, 0xf0, 0x31, 0xa2,
				     0x0e, 0x2e, 0x33, 0xe6, 0xa4, 0xea, 0x17,
				     0xd6, 0x56, 0xa2, 0x3b, 0x5f, 0x56, 0x3a,
				     0xa7, 0x6f, 0x4c, 0xc1, 0xca, 0xbd, 0x5f,
				     0xad, 0x3d, 0x99, 0x4a, 0xd3, 0x0c, 0x67,
				     0x1a },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** snow + zuc ****************************/

	{
		.test_idx = 71,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, SNOW-f8_ENC and ZUC_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
			.auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0x0c, 0xa8, 0x9d,
				     0x9e, 0xcc, 0xf0, 0x1a, 0xc0, 0xf2, 0x9f,
				     0x8c, 0xc9, 0x57, 0xc7, 0x99, 0x4d, 0xde,
				     0xc5, 0x19, 0x69, 0x58, 0x5b, 0x1a, 0x51,
				     0x40, 0xa8, 0xc4, 0x93, 0x4b, 0x47, 0xb8,
				     0xc4, 0x2a, 0x19, 0x07, 0x80, 0x45, 0xbb,
				     0xb3, 0x24, 0x88, 0x68, 0x5f, 0x78, 0x3a,
				     0xd2, 0x6c, 0xcc, 0xa0, 0xb5, 0xf0, 0x95,
				     0x3e, 0xf1, 0xf4, 0x3e, 0x43, 0x8f, 0x6c,
				     0xae, 0x22, 0x59, 0x11, 0x19, 0x3c, 0x04,
				     0x45 },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** snow + zuc ****************************/

	{
		.test_idx = 72,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, SNOW-f8_ENC and ZUC_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_SNOW3G_UEA2,
			.auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0x5a, 0xa9, 0xb5,
				     0x61, 0x8b, 0x8a, 0xb7, 0x6a, 0x98, 0x30,
				     0x6c, 0xed, 0x84, 0x69, 0xff, 0x6b, 0x7e,
				     0x30, 0x59, 0x55, 0x80, 0x32, 0xd0, 0x0f,
				     0x73, 0x43, 0x7c, 0xc3, 0x2e, 0xf4, 0xc3,
				     0x3a, 0x60, 0xe3, 0x7c, 0xf0, 0x31, 0xa2,
				     0x0e, 0x2e, 0x33, 0xe6, 0xa4, 0xea, 0x17,
				     0xd6, 0x56, 0xa2, 0x3b, 0x5f, 0x56, 0x3a,
				     0xa7, 0x6f, 0x4c, 0xc1, 0xca, 0xbd, 0x5f,
				     0xad, 0x3d, 0x99, 0x4a, 0x7b, 0x7d, 0x8a,
				     0x76 },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** zuc + null ****************************/

	{
		.test_idx = 73,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, ZUC_ENC and NULL_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
			.auth_alg = RTE_CRYPTO_AUTH_NULL,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0x9b, 0x9c, 0xde,
				     0xa1, 0x69, 0x9b, 0x27, 0xd3, 0x38, 0x93,
				     0xf2, 0x12, 0xb1, 0xc6, 0x60, 0xac, 0xb7,
				     0xf2, 0x37, 0xf3, 0x72, 0xaf, 0x50, 0x9d,
				     0x97, 0x37, 0x9f, 0xba, 0x25, 0xc8, 0xfc,
				     0xa9, 0x85, 0x42, 0x18, 0x09, 0x12, 0xcb,
				     0xbb, 0x15, 0xf3, 0x6a, 0x5d, 0x61, 0x12,
				     0x6e, 0x6d, 0x1b, 0x92, 0xd8, 0x42, 0x60,
				     0x3e, 0x1f, 0xe0, 0x6c, 0x28, 0x89, 0xf7,
				     0x0c, 0x61, 0x76, 0xdc, 0x55, 0xb4, 0x38,
				     0x2a },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** zuc + null ****************************/

	{
		.test_idx = 74,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, ZUC_ENC and NULL_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
			.auth_alg = RTE_CRYPTO_AUTH_NULL,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0xf8, 0x00, 0x00, 0xf8, 0x0c, 0xef, 0x82,
				     0x59, 0x6b, 0x6b, 0x61, 0xbe, 0x54, 0x23,
				     0x7d, 0x29, 0x6d, 0xa7, 0xd2, 0xfa, 0x26,
				     0xcc, 0x1d, 0x18, 0x39, 0x99, 0xea, 0xac,
				     0xf3, 0x97, 0xb3, 0x18, 0x23, 0x59, 0x5f,
				     0xcb, 0x01, 0x9f, 0x0a, 0x0c, 0x9b, 0xa1,
				     0x7a, 0x8a, 0xe5, 0x00, 0x35, 0x4d, 0x0e,
				     0x69, 0x9f, 0x4b, 0x72, 0x94, 0x8a, 0x25,
				     0x69, 0x43, 0x28, 0xdc, 0x40, 0x60, 0x4f,
				     0x6c, 0xed, 0x6a, 0x50, 0xa6, 0xd7, 0x73,
				     0x10 },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** zuc + aes-cmac *************************/

	{
		.test_idx = 75,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, ZUC_ENC and AES-CMAC_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
			.auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0x9b, 0x9c, 0xde,
				     0xa1, 0x69, 0x9b, 0x27, 0xd3, 0x38, 0x93,
				     0xf2, 0x12, 0xb1, 0xc6, 0x60, 0xac, 0xb7,
				     0xf2, 0x37, 0xf3, 0x72, 0xaf, 0x50, 0x9d,
				     0x97, 0x37, 0x9f, 0xba, 0x25, 0xc8, 0xfc,
				     0xa9, 0x85, 0x42, 0x18, 0x09, 0x12, 0xcb,
				     0xbb, 0x15, 0xf3, 0x6a, 0x5d, 0x61, 0x12,
				     0x6e, 0x6d, 0x1b, 0x92, 0xd8, 0x42, 0x60,
				     0x3e, 0x1f, 0xe0, 0x6c, 0x28, 0x89, 0xf7,
				     0x0c, 0x61, 0x76, 0xdc, 0x9d, 0x1f, 0x9a,
				     0x03 },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** zuc + aes-cmac *************************/

	{
		.test_idx = 76,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, ZUC_ENC and AES-CMAC_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
			.auth_alg = RTE_CRYPTO_AUTH_AES_CMAC,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0x0c, 0xef, 0x82,
				     0x59, 0x6b, 0x6b, 0x61, 0xbe, 0x54, 0x23,
				     0x7d, 0x29, 0x6d, 0xa7, 0xd2, 0xfa, 0x26,
				     0xcc, 0x1d, 0x18, 0x39, 0x99, 0xea, 0xac,
				     0xf3, 0x97, 0xb3, 0x18, 0x23, 0x59, 0x5f,
				     0xcb, 0x01, 0x9f, 0x0a, 0x0c, 0x9b, 0xa1,
				     0x7a, 0x8a, 0xe5, 0x00, 0x35, 0x4d, 0x0e,
				     0x69, 0x9f, 0x4b, 0x72, 0x94, 0x8a, 0x25,
				     0x69, 0x43, 0x28, 0xdc, 0x40, 0x60, 0x4f,
				     0x6c, 0xed, 0x6a, 0x50, 0x55, 0xf6, 0xaf,
				     0x9a },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** zuc + snow ****************************/

	{
		.test_idx = 77,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, ZUC_ENC and SNOW-f9_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
			.auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0x9b, 0x9c, 0xde,
				     0xa1, 0x69, 0x9b, 0x27, 0xd3, 0x38, 0x93,
				     0xf2, 0x12, 0xb1, 0xc6, 0x60, 0xac, 0xb7,
				     0xf2, 0x37, 0xf3, 0x72, 0xaf, 0x50, 0x9d,
				     0x97, 0x37, 0x9f, 0xba, 0x25, 0xc8, 0xfc,
				     0xa9, 0x85, 0x42, 0x18, 0x09, 0x12, 0xcb,
				     0xbb, 0x15, 0xf3, 0x6a, 0x5d, 0x61, 0x12,
				     0x6e, 0x6d, 0x1b, 0x92, 0xd8, 0x42, 0x60,
				     0x3e, 0x1f, 0xe0, 0x6c, 0x28, 0x89, 0xf7,
				     0x0c, 0x61, 0x76, 0xdc, 0xd3, 0x21, 0xba,
				     0x31 },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** zuc + snow ****************************/

	{
		.test_idx = 78,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, ZUC_ENC and SNOW-f9_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
			.auth_alg = RTE_CRYPTO_AUTH_SNOW3G_UIA2,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0x0c, 0xef, 0x82,
				     0x59, 0x6b, 0x6b, 0x61, 0xbe, 0x54, 0x23,
				     0x7d, 0x29, 0x6d, 0xa7, 0xd2, 0xfa, 0x26,
				     0xcc, 0x1d, 0x18, 0x39, 0x99, 0xea, 0xac,
				     0xf3, 0x97, 0xb3, 0x18, 0x23, 0x59, 0x5f,
				     0xcb, 0x01, 0x9f, 0x0a, 0x0c, 0x9b, 0xa1,
				     0x7a, 0x8a, 0xe5, 0x00, 0x35, 0x4d, 0x0e,
				     0x69, 0x9f, 0x4b, 0x72, 0x94, 0x8a, 0x25,
				     0x69, 0x43, 0x28, 0xdc, 0x40, 0x60, 0x4f,
				     0x6c, 0xed, 0x6a, 0x50, 0x83, 0xf9, 0x22,
				     0x24 },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},

	/**************************** zuc + zuc ****************************/

	{
		.test_idx = 79,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, ZUC_ENC and ZUC_AUTH Uplink",
			.cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
			.auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0x9b, 0x9c, 0xde,
				     0xa1, 0x69, 0x9b, 0x27, 0xd3, 0x38, 0x93,
				     0xf2, 0x12, 0xb1, 0xc6, 0x60, 0xac, 0xb7,
				     0xf2, 0x37, 0xf3, 0x72, 0xaf, 0x50, 0x9d,
				     0x97, 0x37, 0x9f, 0xba, 0x25, 0xc8, 0xfc,
				     0xa9, 0x85, 0x42, 0x18, 0x09, 0x12, 0xcb,
				     0xbb, 0x15, 0xf3, 0x6a, 0x5d, 0x61, 0x12,
				     0x6e, 0x6d, 0x1b, 0x92, 0xd8, 0x42, 0x60,
				     0x3e, 0x1f, 0xe0, 0x6c, 0x28, 0x89, 0xf7,
				     0x0c, 0x61, 0x76, 0xdc, 0xca, 0x21, 0x3e,
				     0xd3 },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 0,
	},

	/**************************** zuc + zuc ****************************/

	{
		.test_idx = 80,
		.param = {.name =
			"PDCP-SDAP Uplane: 18b SN, ZUC_ENC and ZUC_AUTH Downlink",
			.cipher_alg = RTE_CRYPTO_CIPHER_ZUC_EEA3,
			.auth_alg = RTE_CRYPTO_AUTH_ZUC_EIA3,
			.domain = RTE_SECURITY_PDCP_MODE_DATA,
			.cipher_key_len = 16,
			.auth_key_len = 16,
		},
		.cipher_key = (uint8_t[]){ 0x60, 0x4c, 0x8a, 0x22, 0x89, 0x56,
					   0x13, 0x51, 0x7d, 0x61, 0xe5, 0xe0,
					   0x7b, 0x2b, 0xd0, 0x9e },
		.auth_key = (uint8_t[]){ 0x1a, 0xef, 0xbb, 0xff, 0x00, 0xf5,
					 0x4b, 0x32, 0x87, 0xf9, 0xdb, 0xe0,
					 0x31, 0x5f, 0x3a, 0x15 },
		.data_in =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0xdb, 0x2d, 0x3f,
				     0x23, 0x82, 0x53, 0xfd, 0x37, 0xde, 0x88,
				     0x63, 0x08, 0x4f, 0xD3, 0x71, 0xfb, 0xeb,
				     0x35, 0xf3, 0x64, 0xd3, 0x5e, 0xaf, 0x3f,
				     0x57, 0xc2, 0xe2, 0x91, 0x91, 0xa3, 0x9c,
				     0xe6, 0x30, 0x69, 0x70, 0x33, 0x8a, 0x15,
				     0xd0, 0x36, 0x47, 0x0e, 0x8f, 0xee, 0x2c,
				     0x96, 0x0c, 0xd7, 0x7d, 0x70, 0x1b, 0x01,
				     0x7f, 0x96, 0x46, 0x53, 0xb0, 0xa4, 0x7a,
				     0xf9, 0xdd, 0xcc, 0x69 },
		.in_len = 67,
		.data_out =
			(uint8_t[]){ 0x80, 0x00, 0x00, 0xf8, 0x0c, 0xef, 0x82,
				     0x59, 0x6b, 0x6b, 0x61, 0xbe, 0x54, 0x23,
				     0x7d, 0x29, 0x6d, 0xa7, 0xd2, 0xfa, 0x26,
				     0xcc, 0x1d, 0x18, 0x39, 0x99, 0xea, 0xac,
				     0xf3, 0x97, 0xb3, 0x18, 0x23, 0x59, 0x5f,
				     0xcb, 0x01, 0x9f, 0x0a, 0x0c, 0x9b, 0xa1,
				     0x7a, 0x8a, 0xe5, 0x00, 0x35, 0x4d, 0x0e,
				     0x69, 0x9f, 0x4b, 0x72, 0x94, 0x8a, 0x25,
				     0x69, 0x43, 0x28, 0xdc, 0x40, 0x60, 0x4f,
				     0x6c, 0xed, 0x6a, 0x50, 0x2b, 0x88, 0xcf,
				     0x48 },
		.sn_size = 18,
		.hfn = 0x1,
		.hfn_threshold = 0xfa558,
		.bearer = 0x16,
		.packet_direction = 1,
	},
};

#endif
