/* SPDX-License-Identifier: BSD-3-Clause
 * Copyright(c) 2016-2017 Intel Corporation
 */

#ifndef TEST_CRYPTODEV_HASH_TEST_VECTORS_H_
#define TEST_CRYPTODEV_HASH_TEST_VECTORS_H_

#ifdef RTE_CRYPTO_AESNI_MB
#if defined(RTE_ARCH_ARM)
#include <ipsec-mb.h>
#else
#include <intel-ipsec-mb.h>
#endif
#endif

static const uint8_t plaintext_hash[] = {
	"What a lousy earth! He wondered how many people "
	"were destitute that same night even in his own "
	"prosperous country, how many homes were "
	"shanties, how many husbands were drunk and "
	"wives socked, and how many children were "
	"bullied, abused, or abandoned. How many "
	"families hungered for food they could not "
	"afford to buy? How many hearts were broken? How "
	"many suicides would take place that same night, "
	"how many people would go insane? How many "
	"cockroaches and landlords would triumph? How "
	"many winners were losers, successes failures, "
	"and rich men poor men? How many wise guys were "
	"stupid? How many happy endings were unhappy "
	"endings? How many honest men were liars, brave "
	"men cowards, loyal men traitors, how many "
	"sainted men were corrupt, how many people in "
	"positions of trust had sold their souls to "
	"bodyguards, how many had never had souls? How "
	"many straight-and-narrow paths were crooked "
	"paths? How many best families were worst "
	"families and how many good people were bad "
	"people? When you added them all up and then "
	"subtracted, you might be left with only the "
	"children, and perhaps with Albert Einstein and "
	"an old violinist or sculptor somewhere."
};

static const struct blockcipher_test_data
md5_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_MD5,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.digest = {
		.data = {
			0xB3, 0xE6, 0xBB, 0x50, 0x41, 0x35, 0x3C, 0x6B,
			0x7A, 0xFF, 0xD2, 0x64, 0xAF, 0xD5, 0x1C, 0xB2
		},
		.len = 16
	}
};

static const struct blockcipher_test_data
hmac_md5_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_MD5_HMAC,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD
		},
		.len = 16
	},
	.digest = {
		.data = {
			0x50, 0xE8, 0xDE, 0xC5, 0xC1, 0x76, 0xAC, 0xAE,
			0x15, 0x4A, 0xF1, 0x7F, 0x7E, 0x04, 0x42, 0x9B
		},
		.len = 16,
		.truncated_len = 12
	}
};

static const struct blockcipher_test_data
sha1_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SHA1,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.digest = {
		.data = {
			0xA2, 0x8D, 0x40, 0x78, 0xDD, 0x9F, 0xBB, 0xD5,
			0x35, 0x62, 0xFB, 0xFA, 0x93, 0xFD, 0x7D, 0x70,
			0xA6, 0x7D, 0x45, 0xCA
		},
		.len = 20,
		.truncated_len = 20
	}
};

static const struct blockcipher_test_data
hmac_sha1_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SHA1_HMAC,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD
		},
		.len = 20
	},
	.digest = {
		.data = {
			0xC4, 0xB7, 0x0E, 0x6B, 0xDE, 0xD1, 0xE7, 0x77,
			0x7E, 0x2E, 0x8F, 0xFC, 0x48, 0x39, 0x46, 0x17,
			0x3F, 0x91, 0x64, 0x59
		},
		.len = 20,
		.truncated_len = 12
	}
};

static const struct blockcipher_test_data
sha224_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SHA224,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.digest = {
		.data = {
			0x91, 0xE7, 0xCD, 0x75, 0x14, 0x9C, 0xA9, 0xE9,
			0x2E, 0x46, 0x12, 0x20, 0x22, 0xF9, 0x68, 0x28,
			0x39, 0x26, 0xDF, 0xB5, 0x78, 0x62, 0xB2, 0x6E,
			0x5E, 0x8F, 0x25, 0x84
		},
		.len = 28,
		.truncated_len = 28
	}
};

static const struct blockcipher_test_data
hmac_sha224_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SHA224_HMAC,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD, 0x26, 0xEB, 0xAB, 0x92,
			0xFB, 0xBF, 0xB0, 0x8C
		},
		.len = 28
	},
	.digest = {
		.data = {
			0x70, 0x0F, 0x04, 0x4D, 0x22, 0x02, 0x7D, 0x31,
			0x36, 0xDA, 0x77, 0x19, 0xB9, 0x66, 0x37, 0x7B,
			0xF1, 0x8A, 0x63, 0xBB, 0x5D, 0x1D, 0xE3, 0x9F,
			0x92, 0xF6, 0xAA, 0x19
		},
		.len = 28,
		.truncated_len = 14
	}
};

static const struct blockcipher_test_data
sha256_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SHA256,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.digest = {
		.data = {
			0x7F, 0xF1, 0x0C, 0xF5, 0x90, 0x97, 0x19, 0x0F,
			0x00, 0xE4, 0x83, 0x01, 0xCA, 0x59, 0x00, 0x2E,
			0x1F, 0xC7, 0x84, 0xEE, 0x76, 0xA6, 0x39, 0x15,
			0x76, 0x2F, 0x87, 0xF9, 0x01, 0x06, 0xF3, 0xB7
		},
		.len = 32,
		.truncated_len = 32
	}
};

static const struct blockcipher_test_data
hmac_sha256_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SHA256_HMAC,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD, 0x26, 0xEB, 0xAB, 0x92,
			0xFB, 0xBF, 0xB0, 0x8C, 0x29, 0x87, 0x90, 0xAC
		},
		.len = 32
	},
	.digest = {
		.data = {
			0xAF, 0x8F, 0x70, 0x1B, 0x4B, 0xAF, 0x34, 0xCB,
			0x02, 0x24, 0x48, 0x45, 0x83, 0x52, 0x8F, 0x22,
			0x06, 0x4D, 0x64, 0x09, 0x0A, 0xCC, 0x02, 0x77,
			0x71, 0x83, 0x48, 0x71, 0x07, 0x02, 0x25, 0x17
		},
		.len = 32,
		.truncated_len = 16
	}
};

static const struct blockcipher_test_data
sha384_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SHA384,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.digest = {
		.data = {
			0x1D, 0xE7, 0x3F, 0x55, 0x86, 0xFE, 0x48, 0x9F,
			0xAC, 0xC6, 0x85, 0x32, 0xFA, 0x8E, 0xA6, 0x77,
			0x25, 0x84, 0xA5, 0x98, 0x8D, 0x0B, 0x80, 0xF4,
			0xEB, 0x2C, 0xFB, 0x6C, 0xEA, 0x7B, 0xFD, 0xD5,
			0xAD, 0x41, 0xAB, 0x15, 0xB0, 0x03, 0x15, 0xEC,
			0x9E, 0x3D, 0xED, 0xCB, 0x80, 0x7B, 0xF4, 0xB6
		},
		.len = 48,
		.truncated_len = 48
	}
};

static const struct blockcipher_test_data
hmac_sha384_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SHA384_HMAC,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD, 0x26, 0xEB, 0xAB, 0x92,
			0xFB, 0xBF, 0xB0, 0x8C, 0x29, 0x87, 0x90, 0xAC,
			0x39, 0x8B, 0x5C, 0x49, 0x68, 0x1E, 0x3A, 0x05,
			0xCC, 0x68, 0x5C, 0x76, 0xCB, 0x3C, 0x71, 0x89
		},
		.len = 48
	},
	.digest = {
		.data = {
			0xE2, 0x83, 0x18, 0x55, 0xB5, 0x8D, 0x94, 0x9B,
			0x01, 0xB6, 0xE2, 0x57, 0x7A, 0x62, 0xF5, 0xF4,
			0xAB, 0x39, 0xF3, 0x3C, 0x28, 0xA0, 0x0F, 0xCC,
			0xEE, 0x1C, 0xF1, 0xF8, 0x69, 0xF1, 0x24, 0x3B,
			0x10, 0x90, 0x0A, 0xE3, 0xF0, 0x59, 0xDD, 0xC0,
			0x6F, 0xE6, 0x8C, 0x84, 0xD5, 0x03, 0xF8, 0x9E
		},
		.len = 48,
		.truncated_len = 24
	}
};

static const struct blockcipher_test_data
sha512_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SHA512,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.digest = {
		.data = {
			0xB9, 0xBA, 0x28, 0x48, 0x3C, 0xC2, 0xD3, 0x65,
			0x4A, 0xD6, 0x00, 0x1D, 0xCE, 0x61, 0x64, 0x54,
			0x45, 0x8C, 0x64, 0x0E, 0xED, 0x0E, 0xD8, 0x1C,
			0x72, 0xCE, 0xD2, 0x44, 0x91, 0xC8, 0xEB, 0xC7,
			0x99, 0xC5, 0xCA, 0x89, 0x72, 0x64, 0x96, 0x41,
			0xC8, 0xEA, 0xB2, 0x4E, 0xD1, 0x21, 0x13, 0x49,
			0x64, 0x4E, 0x15, 0x68, 0x12, 0x67, 0x26, 0x0F,
			0x2C, 0x3C, 0x83, 0x25, 0x27, 0x86, 0xF0, 0xDB
		},
		.len = 64,
		.truncated_len = 64
	}
};

static const struct blockcipher_test_data
hmac_sha512_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SHA512_HMAC,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD, 0x26, 0xEB, 0xAB, 0x92,
			0xFB, 0xBF, 0xB0, 0x8C, 0x29, 0x87, 0x90, 0xAC,
			0x39, 0x8B, 0x5C, 0x49, 0x68, 0x1E, 0x3A, 0x05,
			0xCC, 0x68, 0x5C, 0x76, 0xCB, 0x3C, 0x71, 0x89,
			0xDE, 0xAA, 0x36, 0x44, 0x98, 0x93, 0x97, 0x1E,
			0x6D, 0x53, 0x83, 0x87, 0xB3, 0xB7, 0x56, 0x41
		},
		.len = 64
	},
	.digest = {
		.data = {
			0xB8, 0x0B, 0x35, 0x97, 0x3F, 0x24, 0x3F, 0x05,
			0x2A, 0x7F, 0x2F, 0xD8, 0xD7, 0x56, 0x58, 0xAD,
			0x6F, 0x8D, 0x1F, 0x4C, 0x30, 0xF9, 0xA8, 0x29,
			0x7A, 0xE0, 0x8D, 0x88, 0xF5, 0x2E, 0x94, 0xF5,
			0x06, 0xF7, 0x5D, 0x57, 0x32, 0xA8, 0x49, 0x29,
			0xEA, 0x6B, 0x6D, 0x95, 0xBD, 0x76, 0xF5, 0x79,
			0x97, 0x37, 0x0F, 0xBE, 0xC2, 0x45, 0xA0, 0x87,
			0xAF, 0x24, 0x27, 0x0C, 0x78, 0xBA, 0xBE, 0x20
		},
		.len = 64,
		.truncated_len = 32
	}
};

static const struct blockcipher_test_data
sha3_224_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SHA3_224,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.digest = {
		.data = {
			0xFF, 0x7D, 0xAB, 0xC4, 0xB8, 0xF8, 0x0D, 0x5C,
			0x3A, 0xD3, 0xCD, 0x71, 0x58, 0x62, 0x24, 0x0F,
			0xCC, 0x58, 0xE4, 0x42, 0x1B, 0xA3, 0x6F, 0xE8,
			0x9A, 0x44, 0xBF, 0x45
		},
		.len = 28,
		.truncated_len = 28
	}
};

static const struct blockcipher_test_data
hmac_sha3_224_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SHA3_224_HMAC,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD, 0x26, 0xEB, 0xAB, 0x92,
			0xFB, 0xBF, 0xB0, 0x8C
		},
		.len = 28
	},
	.digest = {
		.data = {
			0x86, 0xDB, 0x99, 0x80, 0xFC, 0x13, 0x75, 0x4E,
			0xB5, 0x30, 0x7A, 0x58, 0xC1, 0x0D, 0xE4, 0x00,
			0x7F, 0xE3, 0xD8, 0xC2, 0x0E, 0x0C, 0xC1, 0xFD,
			0xF9, 0x33, 0x05, 0x40
		},
		.len = 28,
		.truncated_len = 14
	}
};

static const struct blockcipher_test_data
sha3_256_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SHA3_256,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.digest = {
		.data = {
			0xC3, 0x6B, 0x73, 0xF4, 0x97, 0x7F, 0xFA, 0xD9,
			0x35, 0xF1, 0x1F, 0x54, 0x35, 0xC2, 0x19, 0x6C,
			0xA2, 0x24, 0xC7, 0x01, 0xAD, 0xCC, 0xD4, 0x35,
			0x88, 0xB9, 0x0C, 0x15, 0xAE, 0x3F, 0x92, 0x47
		},
		.len = 32,
		.truncated_len = 32
	}
};

static const struct blockcipher_test_data
hmac_sha3_256_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SHA3_256_HMAC,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD, 0x26, 0xEB, 0xAB, 0x92,
			0xFB, 0xBF, 0xB0, 0x8C, 0x29, 0x87, 0x90, 0xAC
		},
		.len = 32
	},
	.digest = {
		.data = {
			0x8E, 0xB1, 0xBD, 0xEE, 0xEF, 0x26, 0xDD, 0xE7,
			0x66, 0xBD, 0x9C, 0x2B, 0xBC, 0x5D, 0x6E, 0xC8,
			0x8C, 0x4A, 0x9C, 0x79, 0xD7, 0x05, 0xDE, 0xFC,
			0x21, 0x48, 0xD5, 0x95, 0x6D, 0x37, 0x0E, 0x00
		},
		.len = 32,
		.truncated_len = 16
	}
};

static const struct blockcipher_test_data
sha3_384_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SHA3_384,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.digest = {
		.data = {
			0x56, 0x18, 0x64, 0x95, 0x70, 0x64, 0x32, 0x80,
			0xB3, 0x67, 0x7B, 0xE3, 0x09, 0x75, 0x92, 0x2B,
			0x56, 0x40, 0xA9, 0xC5, 0x19, 0x47, 0x50, 0x33,
			0xA7, 0xA2, 0x2B, 0x45, 0x46, 0xFD, 0x69, 0xE5,
			0xDE, 0x2B, 0x35, 0xE6, 0x06, 0xC7, 0x0D, 0x28,
			0x5A, 0xFB, 0x37, 0x4A, 0xE5, 0x8F, 0x9F, 0x4A
		},
		.len = 48,
		.truncated_len = 48
	}
};

static const struct blockcipher_test_data
hmac_sha3_384_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SHA3_384_HMAC,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD, 0x26, 0xEB, 0xAB, 0x92,
			0xFB, 0xBF, 0xB0, 0x8C, 0x29, 0x87, 0x90, 0xAC,
			0x39, 0x8B, 0x5C, 0x49, 0x68, 0x1E, 0x3A, 0x05,
			0xCC, 0x68, 0x5C, 0x76, 0xCB, 0x3C, 0x71, 0x89
		},
		.len = 48
	},
	.digest = {
		.data = {
			0x91, 0xCF, 0x69, 0xE5, 0x5A, 0x1A, 0x54, 0x0C,
			0xE2, 0xA9, 0xE7, 0x06, 0xF2, 0x44, 0xA7, 0x8A,
			0x0B, 0x5C, 0xD6, 0x34, 0x8F, 0xF7, 0x08, 0x90,
			0x0D, 0xF1, 0x60, 0xC6, 0xD3, 0x90, 0xA9, 0x10,
			0xAF, 0xA1, 0xEA, 0x83, 0x2E, 0xB8, 0x35, 0xD9,
			0x7B, 0x70, 0xB1, 0x13, 0x08, 0xA8, 0x90, 0x07
		},
		.len = 48,
		.truncated_len = 24
	}
};

static const struct blockcipher_test_data
sha3_512_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SHA3_512,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.digest = {
		.data = {
			0x0B, 0xFF, 0x02, 0x45, 0xC5, 0xE7, 0x72, 0xD6,
			0x1B, 0xD6, 0x7A, 0xB1, 0xFB, 0x34, 0x0E, 0xAA,
			0xAE, 0x85, 0xCD, 0x04, 0xC8, 0x77, 0x97, 0xC7,
			0x99, 0x6A, 0xF8, 0xC1, 0x6A, 0x2A, 0x17, 0xE2,
			0xE2, 0xBC, 0x32, 0x53, 0x69, 0x9C, 0xCF, 0x02,
			0x83, 0x3D, 0x89, 0xEE, 0xC9, 0xDF, 0xBC, 0xEC,
			0xE6, 0xD9, 0x3B, 0x8E, 0xA4, 0xF9, 0x5E, 0x6A,
			0xBB, 0x32, 0x7C, 0x04, 0x5E, 0xEB, 0x18, 0xC7
		},
		.len = 64,
		.truncated_len = 64
	}
};

static const struct blockcipher_test_data
hmac_sha3_512_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SHA3_512_HMAC,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.auth_key = {
		.data = {
			0xF8, 0x2A, 0xC7, 0x54, 0xDB, 0x96, 0x18, 0xAA,
			0xC3, 0xA1, 0x53, 0xF6, 0x1F, 0x17, 0x60, 0xBD,
			0xDE, 0xF4, 0xDE, 0xAD, 0x26, 0xEB, 0xAB, 0x92,
			0xFB, 0xBF, 0xB0, 0x8C, 0x29, 0x87, 0x90, 0xAC,
			0x39, 0x8B, 0x5C, 0x49, 0x68, 0x1E, 0x3A, 0x05,
			0xCC, 0x68, 0x5C, 0x76, 0xCB, 0x3C, 0x71, 0x89,
			0xDE, 0xAA, 0x36, 0x44, 0x98, 0x93, 0x97, 0x1E,
			0x6D, 0x53, 0x83, 0x87, 0xB3, 0xB7, 0x56, 0x41
		},
		.len = 64
	},
	.digest = {
		.data = {
			0x1C, 0x46, 0xD2, 0x95, 0xDC, 0xB4, 0x5F, 0xF8,
			0xB7, 0x63, 0xA7, 0xCD, 0x27, 0x79, 0x04, 0x9B,
			0x19, 0x97, 0xBF, 0x41, 0x5C, 0xCC, 0x02, 0xD3,
			0x19, 0xCB, 0x65, 0x59, 0x43, 0xD8, 0xB7, 0x3B,
			0xF6, 0xAF, 0xAA, 0xA5, 0x6E, 0x24, 0xA4, 0xFA,
			0xC6, 0xE0, 0xC1, 0x1B, 0x61, 0xFD, 0xB6, 0xA2,
			0x3E, 0xF0, 0xD1, 0x64, 0x3B, 0x30, 0x40, 0x67,
			0xE5, 0x41, 0x82, 0xE8, 0xC8, 0x41, 0x42, 0x3D
		},
		.len = 64,
		.truncated_len = 32
	}
};

static const struct blockcipher_test_data
shake_128_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SHAKE_128,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.digest = {
		.data = {
			0x69, 0xBD, 0xD5, 0x14, 0x85, 0xE0, 0x44, 0xA4,
			0x25, 0xE6, 0x9F, 0x81, 0xC8, 0x18, 0x58, 0x3E,
			0xFE, 0xC9, 0x0B, 0xAD, 0x27, 0x98, 0x14, 0x3F,
			0xB9, 0x7D, 0x68, 0x4D, 0x60, 0x77, 0x82, 0x64,
			0x6D, 0x64, 0x66, 0x67, 0xFA, 0xBC, 0x4F, 0xAC,
			0x9C, 0x75, 0x8D, 0x63, 0xBA, 0xBD, 0x2B, 0x03,
			0x0F, 0x2F, 0x46, 0x15, 0x88, 0xF0, 0x8C, 0x09,
			0xAC, 0x87, 0x84, 0x5B, 0xCA, 0x65, 0xA1, 0x14
		},
		.len = 64,
		.truncated_len = 64
	}
};

static const struct blockcipher_test_data
shake_256_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SHAKE_256,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.digest = {
		.data = {
			0x9E, 0xA2, 0x1B, 0x84, 0x15, 0x0E, 0xF0, 0x46,
			0xD9, 0xA9, 0x11, 0x02, 0x11, 0x67, 0x9A, 0xDC,
			0x62, 0x95, 0x8C, 0xD6, 0xEF, 0x89, 0x35, 0x8C,
			0xBF, 0x41, 0x5F, 0x23, 0x16, 0xBD, 0x0C, 0xDE,
			0xEC, 0x1D, 0x47, 0x90, 0xA4, 0xD9, 0x18, 0x63,
			0xE1, 0x60, 0xF8, 0xAF, 0x0F, 0x57, 0xBA, 0xCA,
			0x5D, 0x97, 0x58, 0xF5, 0xFF, 0xE4, 0x85, 0x40,
			0xCA, 0x7D, 0xB8, 0x35, 0x3B, 0x89, 0xCA, 0x34
		},
		.len = 64,
		.truncated_len = 64
	}
};

static const struct blockcipher_test_data
cmac_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_AES_CMAC,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.auth_key = {
		.data = {
			0x2B, 0x7E, 0x15, 0x16, 0x28, 0xAE, 0xD2, 0xA6,
			0xAB, 0xF7, 0x15, 0x88, 0x09, 0xCF, 0x4F, 0x3C
		},
		.len = 16
	},
	.digest = {
		.data = {
			0x4C, 0x77, 0x87, 0xA0, 0x78, 0x8E, 0xEA, 0x96,
			0xC1, 0xEB, 0x1E, 0x4E, 0x95, 0x8F, 0xED, 0x27
		},
		.len = 16,
		.truncated_len = 16
	}
};

static const struct blockcipher_test_data
aes_xcbc_mac_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_AES_XCBC_MAC,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.auth_key = {
		.data = {
			0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
			0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f
		},
		.len = 16
	},
	.digest = {
		.data = {
			0x07, 0xf1, 0xf5, 0x80, 0x5a, 0xbc, 0x1d, 0x1c,
			0x58, 0x43, 0x99, 0xbe

		},
		.len = 12,
		.truncated_len = 12
	}
};

static const struct blockcipher_test_data
null_auth_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_NULL,
	.ciphertext = {		/* arbitrary data - shouldn't be used */
		.data = plaintext_hash,
		.len = 512
	},
	.auth_key = {
		.data = { 0x0 },
		.len = 0
	},
	.digest = {
		.data = { 0x0 },
		.len = 0,
		.truncated_len = 0
	}
};

static const struct blockcipher_test_data
cmac_test_vector_12 = {
	.auth_algo = RTE_CRYPTO_AUTH_AES_CMAC,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.auth_key = {
		.data = {
			0x2B, 0x7E, 0x15, 0x16, 0x28, 0xAE, 0xD2, 0xA6,
			0xAB, 0xF7, 0x15, 0x88, 0x09, 0xCF, 0x4F, 0x3C
		},
		.len = 16
	},
	.digest = {
		.data = {
			0x4C, 0x77, 0x87, 0xA0, 0x78, 0x8E, 0xEA, 0x96,
			0xC1, 0xEB, 0x1E, 0x4E, 0x95, 0x8F, 0xED, 0x27
		},
		.len = 12,
		.truncated_len = 12
	}
};

static const struct blockcipher_test_data
sm3_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SM3,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.digest = {
		.data =  {
			0x9E, 0x86, 0xAB, 0x68, 0xD7, 0x8F, 0xD6, 0xFF,
			0x1C, 0x57, 0x12, 0x48, 0xC4, 0xA8, 0x26, 0xDE,
			0x9E, 0x53, 0xE9, 0xC4, 0xA6, 0xBB, 0xE7, 0x52,
			0x53, 0x9C, 0xDE, 0xC1, 0xD5, 0x7C, 0x43, 0xAB
		},
		.len = 32
	}
};

static const struct blockcipher_test_data
hmac_sm3_test_vector = {
	.auth_algo = RTE_CRYPTO_AUTH_SM3_HMAC,
	.ciphertext = {
		.data = plaintext_hash,
		.len = 512
	},
	.auth_key = {
		.data = {
			0x12, 0x70, 0x87, 0xd9, 0x76, 0x9e, 0xe0, 0xf0,
			0x14, 0xfb, 0xca, 0x5a, 0xb6, 0xcb, 0x8f, 0x9d,
			0x42, 0xd3, 0x1e, 0x09, 0x9a, 0x25, 0x9a, 0x3c,
			0x87, 0xee, 0x55, 0x63, 0x56, 0x33, 0x75, 0x69,
			0xa3, 0xfc, 0x42, 0x19, 0x9a, 0x22, 0x09, 0xae,
			0x1d, 0xd3, 0x09, 0xd3, 0x9e, 0x98, 0x71, 0xe1,
			0x6b, 0x8f, 0xea, 0x05, 0xb4, 0x85, 0x41, 0x3b,
			0x73, 0x97, 0x9e, 0xc9, 0xca, 0x13, 0x32, 0x6d
		},
		.len = 64
	},
	.digest = {
		.data = {
			0xBC, 0x71, 0xF5, 0x3B, 0x43, 0x28, 0x11, 0x7A,
			0x32, 0xD2, 0xC9, 0x2D, 0x3B, 0xFA, 0xE0, 0x47,
			0xA4, 0xD5, 0xEC, 0x81, 0xFB, 0x86, 0x6A, 0x11,
			0x64, 0xF3, 0xF5, 0x82, 0x3B, 0x8E, 0x0A, 0xBB
		},
		.len = 32,
		.truncated_len = 16
	}
};

static const struct blockcipher_test_case hash_test_cases[] = {
	{
		.test_descr = "MD5 Digest",
		.test_data = &md5_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "MD5 Digest Verify",
		.test_data = &md5_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "HMAC-MD5 Digest",
		.test_data = &hmac_md5_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "HMAC-MD5 Digest Verify",
		.test_data = &hmac_md5_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "SHA1 Digest",
		.test_data = &sha1_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "SHA1 Digest Verify",
		.test_data = &sha1_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "HMAC-SHA1 Digest",
		.test_data = &hmac_sha1_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "HMAC-SHA1 Digest Scatter Gather (Inplace)",
		.test_data = &hmac_sha1_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG,
		.sgl_flag = RTE_CRYPTODEV_FF_IN_PLACE_SGL,
		.sgl_segs = 3
	},
	{
		.test_descr = "HMAC-SHA1 Digest Verify",
		.test_data = &hmac_sha1_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "HMAC-SHA1 Digest Verify Scatter Gather (Inplace)",
		.test_data = &hmac_sha1_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG,
		.sgl_flag = RTE_CRYPTODEV_FF_IN_PLACE_SGL,
		.sgl_segs = 3
	},
	{
		.test_descr = "SHA224 Digest",
		.test_data = &sha224_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "SHA224 Digest Verify",
		.test_data = &sha224_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "HMAC-SHA224 Digest",
		.test_data = &hmac_sha224_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "HMAC-SHA224 Digest Verify",
		.test_data = &hmac_sha224_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "SHA256 Digest",
		.test_data = &sha256_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "SHA256 Digest Verify",
		.test_data = &sha256_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "HMAC-SHA256 Digest",
		.test_data = &hmac_sha256_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "HMAC-SHA256 Digest Verify",
		.test_data = &hmac_sha256_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "SHA384 Digest",
		.test_data = &sha384_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "SHA384 Digest Verify",
		.test_data = &sha384_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "HMAC-SHA384 Digest",
		.test_data = &hmac_sha384_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "HMAC-SHA384 Digest Verify",
		.test_data = &hmac_sha384_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "SHA512 Digest",
		.test_data = &sha512_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "SHA512 Digest Verify",
		.test_data = &sha512_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "HMAC-SHA512 Digest",
		.test_data = &hmac_sha512_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "HMAC-SHA512 Digest Verify",
		.test_data = &hmac_sha512_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "SHA3_224 Digest",
		.test_data = &sha3_224_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "SHA3_224 Digest Verify",
		.test_data = &sha3_224_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "HMAC-SHA3_224 Digest",
		.test_data = &hmac_sha3_224_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "HMAC-SHA3_224 Digest Verify",
		.test_data = &hmac_sha3_224_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "SHA3_256 Digest",
		.test_data = &sha3_256_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "SHA3_256 Digest Verify",
		.test_data = &sha3_256_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "HMAC-SHA3_256 Digest",
		.test_data = &hmac_sha3_256_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "HMAC-SHA3_256 Digest Verify",
		.test_data = &hmac_sha3_256_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "SHA3_384 Digest",
		.test_data = &sha3_384_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "SHA3_384 Digest Verify",
		.test_data = &sha3_384_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "HMAC-SHA3_384 Digest",
		.test_data = &hmac_sha3_384_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "HMAC-SHA3_384 Digest Verify",
		.test_data = &hmac_sha3_384_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "SHA3_512 Digest",
		.test_data = &sha3_512_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "SHA3_512 Digest Verify",
		.test_data = &sha3_512_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "HMAC-SHA3_512 Digest",
		.test_data = &hmac_sha3_512_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "HMAC-SHA3_512 Digest Verify",
		.test_data = &hmac_sha3_512_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "SHAKE_128 Digest",
		.test_data = &shake_128_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "SHAKE_128 Digest Verify",
		.test_data = &shake_128_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "SHAKE_256 Digest",
		.test_data = &shake_256_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "SHAKE_256 Digest Verify",
		.test_data = &shake_256_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "CMAC Digest 12B",
		.test_data = &cmac_test_vector_12,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "CMAC Digest Verify 12B",
		.test_data = &cmac_test_vector_12,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "CMAC Digest 16B",
		.test_data = &cmac_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "CMAC Digest Verify 16B",
		.test_data = &cmac_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "NULL algo - auth generate",
		.test_data = &null_auth_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "NULL algo - auth verify",
		.test_data = &null_auth_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "NULL algo - auth generate - OOP",
		.test_data = &null_auth_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "NULL algo - auth verify - OOP",
		.test_data = &null_auth_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_OOP,
	},
	{
		.test_descr = "AES-XCBC-MAC Digest 16B",
		.test_data = &aes_xcbc_mac_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "AES-XCBC-MAC Digest Verify 16B",
		.test_data = &aes_xcbc_mac_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "SM3 Digest",
		.test_data = &sm3_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "SM3 Digest Verify",
		.test_data = &sm3_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "HMAC-SM3 Digest",
		.test_data = &hmac_sm3_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
	},
	{
		.test_descr = "HMAC-SM3 Digest Scatter Gather",
		.test_data = &hmac_sm3_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_GEN,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG,
	},
	{
		.test_descr = "HMAC-SM3 Digest Verify",
		.test_data = &hmac_sm3_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
	},
	{
		.test_descr = "HMAC-SM3 Digest Verify Scatter Gather",
		.test_data = &hmac_sm3_test_vector,
		.op_mask = BLOCKCIPHER_TEST_OP_AUTH_VERIFY,
		.feature_mask = BLOCKCIPHER_TEST_FEATURE_SG,
	},

};

#endif /* TEST_CRYPTODEV_HASH_TEST_VECTORS_H_ */
