# SPDX-License-Identifier: BSD-3-Clause
# Copyright(c) 2019 Intel Corporation

op_type =
RTE_BBDEV_OP_LDPC_DEC

input0 =
0x81817F81, 0x817F7F7F, 0x7F817F7F, 0x7F817F7F, 0x8181817F, 0x817F817F, 0x817F817F, 0x7F818181,
0x7F817F7F, 0x81817F81, 0x81817F7F, 0x7F7F7F81, 0x7F817F81, 0x81817F7F, 0x8181817F, 0x81818181,
0x817F8181, 0x7F7F7F81, 0x7F818181, 0x817F7F7F, 0x7F7F7F7F, 0x7F7F7F81, 0x7F817F81, 0x817F7F81,
0x7F7F7F81, 0x817F8181, 0x7F818181, 0x817F817F, 0x7F7F817F, 0x7F817F7F, 0x7F817F7F, 0x7F81817F,
0x7F818181, 0x7F81817F, 0x81817F7F, 0x81817F7F, 0x7F81817F, 0x817F7F81, 0x81817F81, 0x817F817F,
0x7F7F8181, 0x817F7F81, 0x81818181, 0x7F7F7F7F, 0x7F7F7F7F, 0x817F817F, 0x7F81817F, 0x7F7F817F,
0x8181817F, 0x817F8181, 0x7F817F81, 0x817F8181, 0x8181817F, 0x817F8181, 0x7F817F81, 0x8181817F,
0x7F7F8181, 0x7F7F817F, 0x7F81817F, 0x81817F7F, 0x7F7F8181, 0x8181817F, 0x7F818181, 0x81817F81,
0x7F7F817F, 0x7F7F8181, 0x7F817F81, 0x7F81817F, 0x817F7F81, 0x81818181, 0x7F817F81, 0x7F7F7F7F,
0x7F817F7F, 0x81817F7F, 0x817F7F7F, 0x7F818181, 0x7F7F817F, 0x81817F81, 0x8181817F, 0x7F81817F,
0x8181817F, 0x817F8181, 0x7F817F7F, 0x817F817F, 0x81818181, 0x7F81817F, 0x817F817F, 0x81817F81,
0x7F7F8181, 0x817F7F7F, 0x7F818181, 0x7F7F8181, 0x81817F81, 0x7F81817F, 0x7F817F81, 0x81818181,
0x81817F81, 0x817F7F7F, 0x817F7F81, 0x7F7F817F, 0x7F818181, 0x7F817F7F, 0x817F817F, 0x7F7F817F,
0x81817F7F, 0x7F7F817F, 0x817F8181, 0x817F8181, 0x7F81817F, 0x817F817F, 0x817F7F7F, 0x81817F81,
0x81817F7F, 0x8181817F, 0x817F7F81, 0x81817F81, 0x7F7F817F, 0x7F7F8181, 0x7F7F817F, 0x81817F81,
0x81817F7F, 0x817F7F7F, 0x817F7F7F, 0x7F817F81, 0x8181817F, 0x81818181, 0x7F817F7F, 0x7F7F8181,
0x7F7F7F81, 0x7F7F7F81, 0x817F8181, 0x7F817F81, 0x81818181, 0x8181817F, 0x7F81817F, 0x7F818181,
0x7F7F817F, 0x7F7F7F81, 0x817F7F7F, 0x817F817F, 0x7F817F81, 0x817F7F7F, 0x7F81817F, 0x7F7F7F7F,
0x7F818181, 0x7F817F7F, 0x817F7F81, 0x7F81817F, 0x7F7F8181, 0x7F7F8181, 0x8181817F, 0x7F7F817F,
0x7F81817F, 0x7F81817F, 0x817F8181, 0x7F7F7F7F, 0x817F7F81, 0x7F7F8181, 0x817F8181, 0x817F7F7F,
0x7F7F817F, 0x817F8181, 0x81817F81, 0x81818181, 0x7F7F7F7F, 0x7F818181, 0x7F81817F, 0x81817F81,
0x7F7F817F, 0x8181817F, 0x817F817F, 0x7F7F7F7F, 0x7F818181, 0x7F7F817F, 0x7F818181, 0x817F7F81,
0x7F7F7F7F, 0x7F81817F, 0x817F7F7F, 0x81817F81, 0x7F817F81, 0x81818181, 0x7F7F8181, 0x817F817F,
0x7F817F81, 0x81817F81, 0x7F817F7F, 0x7F7F7F81, 0x7F7F7F7F, 0x81817F81, 0x817F7F81, 0x7F7F817F,
0x81818181, 0x7F7F7F7F, 0x817F817F, 0x817F7F81, 0x7F818181, 0x817F8181, 0x817F817F, 0x7F7F817F,
0x7F817F81

output0 =
0x76332859, 0x417B1254, 0xEC8A8CFE, 0xE7EFCD06, 0x43C5BDA2, 0x2EACD776, 0x6CD515AC, 0x6D6E04AC,
0xBC2D9F85, 0xD3643553, 0xD0C8DF8E, 0x235B434A, 0xD7AB7643, 0xA4D9C420, 0x372FA858, 0xF813CE10,
0xE0C238F6, 0x07853FD4, 0xE04E40F2, 0x0EE765A9, 0x6EEAFCBC, 0xA7059C68, 0xFBBC

basegraph=
2

z_c=
72

n_cb=
3600

q_m=
2

n_filler=
0

e =
804

rv_index =
0

code_block_mode =
1

expected_iter_count =
3

op_flags =
RTE_BBDEV_LDPC_ITERATION_STOP_ENABLE

expected_status =
OK
