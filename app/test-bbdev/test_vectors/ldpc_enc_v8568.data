# SPDX-License-Identifier: BSD-3-Clause
# Copyright(c) 2019 Intel Corporation

# Origin : FEC5g_DL_1/8568

op_type =
RTE_BBDEV_OP_LDPC_ENC

input0 =
0xB6D49CE2, 0x3E96B93F, 0xF02009F6, 0x2DF3D30D, 0x3B06C160, 0x646C69CC, 0x54439F0F, 0xCE0D12C3,
0x66E8BFD5, 0xA9D22B0C, 0xA9E7343B, 0x2B6EEF01, 0x6B6966C0, 0xB98FE144, 0xC3BF7BAD, 0x1B40DF1C,
0x973B12DC, 0x46E25E90, 0xB324ACCA, 0x5F0ED2B9, 0xBB4F

output0 =
0x8F1A7C00, 0x836CF0E5, 0x717CE52D, 0xEE86FB44, 0x21621E25, 0x58425AB5, 0xECA0F645, 0xAE9E63E6,
0x287D84BC, 0x9F19A401, 0xD68C4CA3, 0x354ACA5A, 0xD68D7FF8, 0xFAA84645, 0xCF0DBC28, 0x693C50F0,
0x9A3CD23C, 0x57E97520, 0x929BAF90, 0x8E2BA7D8, 0x5EF0FB8A, 0xFFE7B153, 0x9E164074, 0x4D06F0A2,
0x6BC68E5B, 0xB8274587, 0x69641DFC, 0xF5DC89A0, 0x4F8E741C, 0x1CB682DA, 0xEF36E914, 0x8BDEBA30,
0x4B6777E2, 0xEFBFD14C, 0x85F0DF67, 0x55DBD201, 0xCF29A01A, 0x862BD273, 0x1F43CFAF, 0x5CB128E9,
0x9C322654, 0xF8E4E47A, 0x0FCD1806, 0x0C7B6BC7, 0xF7B9748A, 0x6DE5D592, 0x0D119373, 0x5F7DC28C,
0x68F26F39, 0xAA47E18A, 0x479CDAAF, 0xE19DCBB3, 0xA72B475D, 0x2E781ED4, 0x4CBB910A, 0x5E5A5A1D,
0x2F668621, 0x86BD9FE1, 0xF1DC12E5, 0xB652E2C1, 0x2E0AC199, 0x059E43C4, 0x14F9B51E, 0x7DA378C5,
0x214E4D5F, 0x72ECE751, 0xF997A106, 0x3F362F62, 0x045DCA85, 0xAD27A58E, 0xB73B4390, 0xEB76C5D2,
0x58CE8B78, 0x73A1D1EA, 0x9705C8E4, 0x224703E8, 0xA0DE7885, 0x9CDBBEE0, 0xBAAFAE85, 0x1B5CFB8A,
0xF66B5209, 0x979335BB, 0x2AA6C7E2, 0x7E3958E7, 0xD39F8BC0, 0xD73BEA43, 0x24C74D3E, 0x9372C2D7,
0x49804670, 0xB3A983FC, 0xFA6DB662, 0x4E657550, 0xDDF757C2, 0xA7265DE7, 0x51BFA1A6, 0x63E1325D,
0x1FDBE953, 0x17348EDB, 0x6B6DC5C0, 0xE335772B, 0x32612617, 0xC13B63EB, 0x8C40891A, 0xF3566154,
0xF5345933, 0xAD9AB800, 0x4CF92B41, 0xF3B32673, 0x80577879, 0x19F8BB0E, 0xAD677483, 0x21B2EDC7,
0x3F96C8E2, 0x7B9211B7, 0x11909101, 0x6A9D9BF5, 0xC4A0E407, 0x5B013820, 0xD2102C31, 0xDC6F548A,
0xA4F6B72A, 0x79F19991, 0x6A21BE3C, 0x2E5CF7A5, 0xDCBB384B, 0xB05B5649, 0x1D61CCF1, 0x9DC122F3,
0x00CA6A82, 0xB7CECDF0, 0x605252A3, 0x0500B498, 0x61AA1618, 0x669788EF, 0xF0DB599F, 0x3ED04C4A,
0x17387F1E, 0xBDAC7B52, 0xEE7D1E0D, 0x58AF212C, 0x0CB064F8, 0x646239DB, 0x88CF9549, 0xD3C7C652,
0xB82109DB, 0x22225244, 0x305F8384, 0x9B4166D5, 0x704FA445, 0x956A0CA5, 0x89963D07, 0x5EF61FAB,
0xF71EAD06, 0xA47FB814, 0x86581A5C, 0xB81B9445, 0x4ECD608E, 0xE369E9A3, 0xFEB80EE5, 0x1399AB02,
0x30A74BEA, 0xCF08B948, 0xB0857028, 0xC81F8CD8, 0x64E13623, 0xAFF927F5, 0x592D7629, 0x7295DE02,
0xE98E2F04, 0x7407C828, 0x85EE9A47, 0xF9B6F671, 0x77F40DF0, 0x81CE7DA1, 0xB8732D5F, 0xC7AC742E,
0xF8623836, 0xE68F4E66, 0x18F29BB9, 0xF57E3350, 0x0E14399C, 0x114ACFA1, 0xFECFBDAA, 0xB0894694,
0xC8DF6D23, 0x7C71FB18, 0x91F00CF2, 0x6A45BC52, 0xFE99962D, 0x41FEBA3D, 0x5C1BB499, 0x51E50591,
0x04D9CDDE, 0x503FBB80, 0x2788B4EE, 0x82A545D8, 0x5F6DD45D, 0x7AE48BE2, 0x4C653419, 0x3C32D58C,
0x48788C71, 0x97A054A3, 0x7FC443B4, 0x805DFF9E, 0xA607D2C6, 0x02DA82C2, 0x884664C5

basegraph=
2

z_c=
72

n_cb=
3600

q_m=
2

n_filler=
64

e =
6624

rv_index =
0

code_block_mode =
1

op_flags =
RTE_BBDEV_LDPC_RATE_MATCH

expected_status =
OK
