# SPDX-License-Identifier: BSD-3-Clause
# Copyright(c) 2022 Intel Corporation
op_type =
RTE_BBDEV_OP_FFT

input0 =
0x07F7F9F1, 0xF788FA5C, 0xEFD00A45, 0xF01E0175, 0xFDEC06E6, 0x0270FBED, 0x0F91EEF8, 0xFBDDFA1C,
0x0465EFCF, 0xF597FB63, 0xE9C7029A, 0xF7B4FBB6, 0xFCE808C3, 0xFB3EF579, 0x1255F4C8, 0xF6FDFC84,
0x0EE3F2D7, 0xF5C0F227, 0xECF20039, 0xF2AB0099, 0xF4C0064D, 0x070CF1A3, 0x0F92FC3E, 0xF874F8FD,
0xE6421887, 0xF32001CB, 0x04F8ECEE, 0xF9A5F737, 0x07DC07BC, 0xFC2AFCFC, 0x0AC01797, 0xFD750933,
0xE27B1122, 0xF33C0479, 0x00BAEA5C, 0xFE51FD33, 0x04EA01FD, 0xFD95FB94, 0x0B3E16F7, 0xFD690791,
0xDF4B09A5, 0xF64FF865, 0x0C4AEF1A, 0xFB97F847, 0x0081034D, 0xFE34FE2F, 0x05A51C39, 0xFCFF073F,
0x0ED50134, 0xFBE30144, 0x0ACF02B9, 0xFA9109FD, 0xEEE00B94, 0x040618CC, 0xEC31F6E7, 0xFEB3F427,
0x0CEE09A6, 0xF2190263, 0x0EC60A12, 0xF72B0937, 0xF2FFFE41, 0xFEE31A1C, 0xF04DF3F8, 0x021DFBDD,
0xFED10D65, 0xEEF0FBB8, 0x0668071F, 0xF2F40629, 0xFA3BFEBE, 0xFD3610B2, 0xF38BEE76, 0xFE6CF72A,
0x073110AB, 0x12DC0736, 0x0130FA1B, 0xF79B0D4D, 0x06771042, 0xFADF0173, 0x08091C2C, 0x09B2F7EA,
0x049A0CE4, 0x1027104B, 0xFF38FCA9, 0xF3090A8D, 0xF7261A8C, 0xFF37FE66, 0x028E244D, 0x0AE9F82A,
0x041B0D97, 0x08A91347, 0x026BF9F1, 0xF1730B62, 0xF43A0B10, 0x013400EE, 0xFA5A1D0B, 0x084BFA86,
0x0275017E, 0x07A1EA5C, 0x00AE0651, 0xFD46EF1E, 0xFBC90402, 0x0965FB03, 0x09BE00A9, 0xFCF60E6E,
0xFE94F9F2, 0x09B2F14B, 0x00EC0AF5, 0x0085ED34, 0x015BFCCD, 0x0BF90476, 0x0A3F007F, 0xF82C1166,
0x030DF562, 0x0B01F284, 0xF6AE0526, 0x0990EA6E, 0x03D0FEDC, 0x02E2066A, 0x09240362, 0xF3D90FA7,
0x0ACDF551, 0x0133FE9E, 0x0FF01027, 0xFA56FE3B, 0xF4E408A0, 0xFDAAF68F, 0xFFBB06F4, 0xE7B303DA,
0x0584FB91, 0xFFD9F899, 0x0C4110D5, 0xFAC6FB3F, 0xFAB809AE, 0xFD6F0630, 0x03190631, 0xE366FE26,
0x056AF5BA, 0x0119F986, 0x074116E7, 0xFC09F525, 0xF6F60856, 0xF88E0574, 0x002F0ABE, 0xE30CF4FE,
0x22E70169, 0xF66AFF56, 0xF23B09F8, 0x03DBF777, 0xFE43F915, 0x035AF8D4, 0xF9DF0DC7, 0x05190B31,
0x23361014, 0xF5C5F8B1, 0xF4F90174, 0xFF82EFEE, 0xFAB7F917, 0x06F5FA3B, 0x03BC0D3B, 0xF9A80A56,
0x1CA4110D, 0x01FCFA9B, 0xF3000243, 0x0807FC82, 0xFCF7F668, 0x080CFC0B, 0xF84D13A6, 0xFC2004A5,
0xFED30132, 0xFD80EF38, 0x0A661297, 0x0CAB0996, 0x051303FD, 0xFDE3F4B1, 0xF68E0003, 0xFA840095,
0xFF840586, 0x0260ECB6, 0x06AB166E, 0x08910C99, 0x03B40831, 0xFEAEFB0C, 0xFA5AF5E0, 0x038403C4,
0xF7B50127, 0x0C83E9D9, 0x046B181F, 0x08B612A4, 0x024E0B91, 0x01000207, 0xFC97F83A, 0xF8B40DD4

output0 =
0xFFF0FE8A, 0x01B3FE2F, 0x020EFFF3, 0xFC0901D2, 0x027501DE, 0xFF660279, 0x00ADFA89, 0xFE72FF52,
0x0450FD29, 0xFB34FF92, 0x032C0136, 0xFE780346, 0x01BFFAC9, 0x00DDFF9A, 0x068FFF1F, 0xFB6DFF18,
0x02D9034F, 0xFCFF0289, 0x0101FEA7, 0xFD2D016A, 0x02E40068, 0x00000000, 0x00000000, 0x00000000,
0xFB2C034A, 0xFC8F0175, 0xFE6402D8, 0x0054FD1C, 0xFFA403AA, 0x02E1000F, 0xFBBE06CD, 0xFB8FFDEA,
0xFF910362, 0x0031FCA4, 0x00BA02DD, 0x0138FE72, 0xFBC80412, 0xFC67FD73, 0xFEDA0415, 0x00D3FD1C,
0xFDCA02DA, 0x0260FF7E, 0x0055046F, 0x0033FCD9, 0x0115029F, 0x00000000, 0x00000000, 0x00000000,
0x0157004F, 0x01B2FFA3, 0x025EFFFE, 0xFF090623, 0xFAA8024A, 0xFF63FD71, 0xFD3DFEE3, 0x025000C5,
0x010A0140, 0xFEFA0575, 0xFA6BFF7D, 0x0177FCA0, 0xFC05FF1E, 0x01D500C8, 0xFFC20139, 0xFE1A0397,
0xFD29003C, 0x020FFDE0, 0xFE3FFCB4, 0x00740264, 0xFEA4FEEA, 0x00000000, 0x00000000, 0x00000000,
0x034202FC, 0xFFFF0471, 0xFE8B012F, 0xFF440324, 0x0224032D, 0x02A6FF74, 0x04D00629, 0xFC86FEE6,
0xFE59041B, 0xFD140405, 0x00830542, 0x008401C8, 0x034707AB, 0xFCCEFCE1, 0xFE6304CE, 0xFD2A022A,
0xFF88041C, 0x0080FF16, 0x005502F2, 0x0006FB55, 0xFE420451, 0x00000000, 0x00000000, 0x00000000,
0x0143FD7B, 0x03030124, 0xFF5B02E4, 0x0064FE8F, 0xFDE7013A, 0xFEBA0406, 0x01DFFF4B, 0x023F00C1,
0x0035FF5C, 0x01D9FF2E, 0xFD26FFAA, 0xFEB902C3, 0x020FFF39, 0x02F600F7, 0x0144FE3F, 0x00DEFE9B,
0xFE25FE61, 0xFDC20265, 0xFFA00261, 0x00D00119, 0x02A9FE77, 0x00000000, 0x00000000, 0x00000000,
0x0180FE7E, 0x0186FED1, 0x0133FED6, 0xFF9B01B2, 0xFD94FF7A, 0x019B0480, 0xFD99FFDF, 0x02210198,
0x03B600C2, 0xFFE6037E, 0xFC71FF70, 0x01D90323, 0xFDA0FE73, 0x00DE01A8, 0x04810088, 0xFE5A033B,
0xFBEDFDBF, 0x01340495, 0xFC67FFF8, 0xFE9A0247, 0x03A402B8, 0x00000000, 0x00000000, 0x00000000,
0x032A0018, 0x0472FEFA, 0x05900042, 0xFEF6FE6B, 0x0155FDD4, 0xFD290258, 0x02FE0438, 0x077BFE5F,
0x0447033F, 0xFEC5FC97, 0xFF6EFFE3, 0xFD27020C, 0x03810467, 0x05140185, 0x0498032B, 0x0001FE27,
0x014EFE85, 0xFBFD0004, 0xFE8E0309, 0x018BFE07, 0xFF8601E0, 0x00000000, 0x00000000, 0x00000000,
0xFF8AFE0D, 0x01F3FFD6, 0x002A023F, 0x0341029B, 0x01F2FC54, 0x009D030A, 0xFE5FFE5B, 0x039B01AC,
0xFF260308, 0x023404C9, 0x01D3FCFC, 0x006402DF, 0x0043FC95, 0x02E90289, 0xFC41012D, 0x020E070B,
0x01D0FD78, 0xFFA001E1, 0xFEA900C2, 0x022801E2, 0x007CFD4D, 0x00000000, 0x00000000, 0x00000000


in_sequence_size =
24

in_leading_padding =
0

out_sequence_size =
3

out_leading_depadding =
0

window_index =
0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0

cs_bitmap =
255

num_antennas_log2 =
3

ifft_log2 =
5

fft_log2 =
2

cs_time_adjustment =
0

ifft_shift =
2

fft_shift =
3

ncs_reciprocal =
4096

op_flags =
RTE_BBDEV_FFT_CS_ADJUSTMENT, RTE_BBDEV_FFT_WINDOWING, RTE_BBDEV_FFT_IDFT_BYPASS

expected_status =
OK
