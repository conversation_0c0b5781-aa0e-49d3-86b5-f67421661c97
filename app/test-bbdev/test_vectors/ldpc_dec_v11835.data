# SPDX-License-Identifier: BSD-3-Clause
# Copyright(c) 2019 Intel Corporation

op_type =
RTE_BBDEV_OP_LDPC_DEC

input0 =
0x817F7F81, 0x817F817F, 0x7F818181, 0x7F818181, 0x7F817F81, 0x7F7F8181, 0x817F7F7F, 0x81817F81,
0x7F7F6B7F, 0x7F7F817F, 0x7F7F8181, 0x817F8181, 0x81817F7F, 0x7F818192, 0x7F81817F, 0x817F7F7F,
0x8181

output0 =
0x44FB08C0, 0x661CCC

basegraph=
2

z_c=
10

n_cb=
500

q_m=
6

n_filler=
44

e=
66

rv_index=
0

code_block_mode=
1

expected_iter_count=
4

op_flags=
RTE_BBDEV_LDPC_ITERATION_STOP_ENABLE

expected_status=
OK
