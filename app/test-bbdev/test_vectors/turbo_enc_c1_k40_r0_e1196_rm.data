# SPDX-License-Identifier: BSD-3-Clause
# Copyright(c) 2017 Intel Corporation

op_type =
RTE_BBDEV_OP_TURBO_ENC

input0 =
0x11D2BCAC, 0x4D

output0 =
0xBC8A2889, 0x744E649E, 0x99436EA6, 0x8B6EFD1D, 0xAB889238, 0xE744E6C9, 0x39E4664A, 0xE8D6DF91,
0x8A2889B3, 0x4E649EBC, 0x436EA674, 0x6EFD1D99, 0x8892388B, 0x44E6C9AB, 0xE4664AE7, 0xD6DF9139,
0x2889B3E8, 0x649EBC8A, 0x6EA6744E, 0xFD1D9943, 0x92388B6E, 0xE6C9AB88, 0x664AE744, 0xDF9139E4,
0x89B3E8D6, 0x9EBC8A28, 0xA6744E64, 0x1D99436E, 0x388B6EFD, 0xC9AB8892, 0x4AE744E6, 0x9139E466,
0xB3E8D6DF, 0xBC8A2889, 0x744E649E, 0x99436EA6, 0x8B6EFD1D, 0x9038

e =
1196

k =
40

ncb =
192

rv_index =
3

code_block_mode =
1

op_flags =
RTE_BBDEV_TURBO_RATE_MATCH

expected_status =
OK
