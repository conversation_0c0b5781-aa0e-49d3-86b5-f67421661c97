# SPDX-License-Identifier: BSD-3-Clause
# Copyright(c) 2019 Intel Corporation

if is_windows
    build = false
    reason = 'not supported on Windows'
    subdir_done()
endif

sources = files(
        'config.c',
        'init.c',
        'main.c',
        'pipeline_acl.c',
        'pipeline_hash.c',
        'pipeline_lpm.c',
        'pipeline_lpm_ipv6.c',
        'pipeline_stub.c',
        'runtime.c',
)
deps += ['pipeline', 'pci']
