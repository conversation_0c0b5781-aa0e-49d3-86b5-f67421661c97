
; This is an example configuration file for dma-perf, which details the meanings of each parameter
; and instructions on how to use dma-perf.

; Supported test types are DMA_MEM_COPY and CPU_MEM_COPY.

; Parameters:
; "mem_size" denotes the size of the memory footprint in megabytes (MB) for source and destination.
; "buf_size" denotes the memory size of a single operation in bytes (B).
; "dma_ring_size" denotes the dma ring buffer size. It should be must be a power of two, and between
;  64 and 4096.
; "kick_batch" denotes the dma operation batch size, and should be greater than 1 normally.

; The format for variables is variable=first,last,increment,ADD|MUL.

; src_numa_node is used to control the numa node where the source memory is allocated.
; dst_numa_node is used to control the numa node where the destination memory is allocated.

; cache_flush is used to determine whether or not the cache should be flushed, with 1 indicating to
; flush and 0 indicating to not flush.

; test_seconds controls the test time of the whole case.

; To use DMA for a test, please specify the "lcore_dma" parameter.
; If you have already set the "-l" and "-a" parameters using EAL,
; make sure that the value of "lcore_dma" falls within their range of the values.
; We have to ensure a 1:1 mapping between the core and DMA device.

; To use CPU for a test, please specify the "lcore" parameter.
; If you have already set the "-l" and "-a" parameters using EAL,
; make sure that the value of "lcore" falls within their range of values.

; "skip" To skip a test-case set skip to 1.

; "lcore_dma*" denotes below comma separated bus related parameters.
;    "dev" DMA device used for the test.
;    "dir" denotes direction of data transfer. It can take 3 values:
;        mem2mem - mem to mem transfer
;        mem2dev - mem to dev transfer
;        dev2mem - dev to mem transfer
;    "raddr" remote iova address for mem2dev and dev2mem transfer.
;    "coreid" denotes PCIe core index.
;    "pfid" denotes PF-id to be used for data transfer
;    "vfid" denotes VF-id of PF-id to be used for data transfer.
;    Example: lcore_dma0=lcore=10,dev=0000:00:04.2,dir=dev2mem,raddr=0x200000000,coreid=1,pfid=2,
;             vfid=3

; Parameters for DMA scatter-gather memory copy:
;
; "dma_src_sge" denotes number of source segments.
; "dma_dst_sge" denotes number of destination segments.
;
; For DMA scatter-gather memory copy, the parameters need to be configured
; and they are valid only when type is DMA_MEM_COPY.

; To specify a configuration file, use the "--config" flag followed by the path to the file.

; To specify a result file, use the "--result" flag followed by the path to the file.
; If you do not specify a result file, one will be generated with the same name as the configuration
; file, with the addition of "_result.csv" at the end.

[case1]
type=DMA_MEM_COPY
mem_size=10
buf_size=64,8192,2,MUL
dma_ring_size=1024
kick_batch=32
src_numa_node=0
dst_numa_node=0
cache_flush=0
test_seconds=2
lcore_dma0=lcore=10,dev=0000:00:04.1,dir=mem2mem
lcore_dma1=lcore=11,dev=0000:00:04.2,dir=mem2mem
eal_args=--in-memory --file-prefix=test

[case2]
type=DMA_MEM_COPY
mem_size=10
buf_size=64,8192,2,MUL
dma_ring_size=1024
dma_src_sge=4
dma_dst_sge=1
kick_batch=32
src_numa_node=0
dst_numa_node=0
cache_flush=0
test_seconds=2
lcore_dma0=lcore=10,dev=0000:00:04.1,dir=mem2mem
lcore_dma1=lcore=11,dev=0000:00:04.2,dir=mem2mem
eal_args=--in-memory --file-prefix=test

[case3]
skip=1
type=DMA_MEM_COPY
mem_size=10
buf_size=64,4096,2,MUL
dma_ring_size=1024
kick_batch=32
src_numa_node=0
dst_numa_node=0
cache_flush=0
test_seconds=2
lcore_dma0=lcore=10,dev=0000:00:04.1,dir=mem2mem
lcore_dma1=lcore=11,dev=0000:00:04.2,dir=dev2mem,raddr=0x200000000,coreid=1,pfid=2,vfid=3
lcore_dma2=lcore=12,dev=0000:00:04.3,dir=mem2dev,raddr=0x300000000,coreid=3,pfid=2,vfid=1
eal_args=--in-memory --file-prefix=test

[case4]
type=CPU_MEM_COPY
mem_size=10
buf_size=64,8192,2,MUL
src_numa_node=0
dst_numa_node=1
cache_flush=0
test_seconds=2
lcore = 3, 4
eal_args=--in-memory --no-pci
