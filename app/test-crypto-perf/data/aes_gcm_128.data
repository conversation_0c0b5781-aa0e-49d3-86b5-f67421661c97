# List of tests for AES-128 GCM:
# 1) [aead_buff_64]
# 2) [aead_buff_512]

##########
# GLOBAL #
##########
plaintext =
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa

ciphertext =
0x4c, 0x35, 0x11, 0xb6, 0x13, 0xcf, 0x34, 0x22, 0x1d, 0x33, 0xbd, 0x9b, 0x75, 0xad, 0x5e, 0x33,
0xa5, 0x77, 0xda, 0xfb, 0x7e, 0x57, 0x20, 0x3d, 0xc5, 0xe8, 0x19, 0x3a, 0x92, 0x59, 0xfb, 0xc5,
0xff, 0x47, 0x49, 0x8e, 0xcb, 0x4f, 0x8e, 0x6c, 0xcd, 0x9f, 0x81, 0x27, 0xa4, 0xac, 0xaa, 0xe1,
0xd0, 0x6a, 0xb0, 0x96, 0x05, 0x68, 0x8e, 0xe8, 0x44, 0x63, 0x12, 0x2a, 0xef, 0x3d, 0xc3, 0xf9,
0xcf, 0xd6, 0x31, 0x04, 0x88, 0xbb, 0xfb, 0xe0, 0x44, 0xcc, 0xef, 0x10, 0xb7, 0xaf, 0x5e, 0x90,
0x07, 0x10, 0xd8, 0x85, 0x59, 0x99, 0x29, 0x2a, 0xa8, 0x83, 0x21, 0x8d, 0x5f, 0x02, 0xed, 0xa6,
0x22, 0xa5, 0x9e, 0x09, 0xa6, 0x52, 0x84, 0x88, 0xb8, 0x1f, 0x90, 0x70, 0xab, 0x2c, 0x2c, 0x45,
0x6f, 0xdc, 0xca, 0x38, 0x3a, 0x11, 0xd0, 0x27, 0x24, 0x09, 0xf8, 0xbf, 0xa2, 0x8f, 0xd3, 0x37,
0xb4, 0x08, 0x0a, 0x61, 0xb9, 0x77, 0x92, 0xbd, 0x49, 0x36, 0x67, 0xe7, 0xef, 0x81, 0x50, 0x7f,
0xbb, 0x23, 0x46, 0x4a, 0xb9, 0x34, 0x98, 0xa2, 0xb8, 0x52, 0x86, 0x0e, 0xbd, 0x6d, 0x11, 0x0a,
0x91, 0x5c, 0x6d, 0x68, 0xea, 0x05, 0x47, 0x93, 0x33, 0x09, 0x28, 0x8a, 0xe5, 0x2f, 0x10, 0x9f,
0xd9, 0xb8, 0x4c, 0x7c, 0x23, 0x8e, 0x08, 0x03, 0xe5, 0x8b, 0x07, 0xd9, 0x29, 0x52, 0x96, 0x98,
0xe6, 0x40, 0x55, 0x62, 0xf0, 0x89, 0xbe, 0x97, 0xfe, 0x74, 0x38, 0xc6, 0xc0, 0xaa, 0xe6, 0xcf,
0x3e, 0x5e, 0x01, 0x79, 0x91, 0x26, 0x99, 0xe4, 0xe0, 0x8c, 0x17, 0xb4, 0xe7, 0x87, 0x07, 0x28,
0x1b, 0xeb, 0xa7, 0xc2, 0xa6, 0x9c, 0xe2, 0x72, 0xa4, 0x1a, 0xeb, 0x09, 0xf3, 0xdd, 0x39, 0x4c,
0x11, 0x96, 0x21, 0xc2, 0x36, 0x1e, 0x52, 0xb4, 0x85, 0x5d, 0xf1, 0x65, 0x7e, 0xc4, 0xa7, 0xaa,
0x0a, 0x75, 0x0f, 0xe8, 0x76, 0xcd, 0x97, 0x64, 0x42, 0x42, 0xb0, 0x1b, 0x09, 0x91, 0x37, 0x0c,
0xcd, 0xbb, 0x58, 0x4d, 0x96, 0x77, 0xd3, 0x4c, 0x3a, 0x3b, 0x76, 0xf8, 0xe6, 0x8e, 0xdc, 0x2b,
0x71, 0xf1, 0xdd, 0xc8, 0x1d, 0xb4, 0x0b, 0xa4, 0x0e, 0xe3, 0x6c, 0xc5, 0xef, 0x2d, 0x65, 0x49,
0x0d, 0xad, 0x12, 0xf6, 0xe8, 0x89, 0x19, 0x04, 0xe8, 0x0d, 0x8f, 0x82, 0xca, 0x84, 0x42, 0x52,
0xc3, 0xb2, 0x83, 0x4e, 0x65, 0x6b, 0x58, 0xc1, 0x83, 0xb5, 0x1a, 0xcd, 0xf1, 0x50, 0x50, 0x5d,
0xe9, 0xed, 0x17, 0x61, 0xba, 0x13, 0xff, 0x27, 0xfb, 0xaa, 0xef, 0x1e, 0x7f, 0x71, 0xc3, 0xfd,
0xc9, 0xeb, 0x94, 0x86, 0x41, 0x9d, 0x10, 0xf9, 0x3a, 0x23, 0x1a, 0x12, 0x0a, 0xc4, 0x4b, 0xec,
0x25, 0xa4, 0xdb, 0x0c, 0x2a, 0xd2, 0xa4, 0x1e, 0x57, 0x34, 0xb1, 0x62, 0xcb, 0x78, 0x3c, 0xcf,
0xdc, 0x4d, 0x7a, 0x85, 0x00, 0xf8, 0x2a, 0x5d, 0xb7, 0xdf, 0xdc, 0x47, 0xc9, 0x59, 0x8b, 0x5f,
0x67, 0x0f, 0x66, 0x50, 0x90, 0x1f, 0xd5, 0xac, 0x1a, 0x05, 0xea, 0x45, 0x76, 0xaf, 0xc3, 0x44,
0xe5, 0xc8, 0xb0, 0x22, 0x79, 0x24, 0x99, 0x5d, 0x4b, 0x02, 0x9b, 0x47, 0x26, 0xd7, 0xc3, 0x7e,
0x4c, 0x0d, 0xf1, 0xca, 0xac, 0xaa, 0x76, 0x78, 0x65, 0x8e, 0xd1, 0xac, 0x0a, 0xf3, 0x5a, 0x52,
0x9e, 0xd4, 0x6c, 0xd4, 0xd9, 0x94, 0x90, 0xc4, 0x63, 0x97, 0x39, 0x0b, 0xeb, 0xcd, 0xd5, 0x9d,
0x3a, 0x69, 0x0d, 0xf4, 0x8e, 0x30, 0xc7, 0xf7, 0x87, 0x23, 0xd5, 0x9f, 0x83, 0x87, 0xce, 0xe7,
0x07, 0xd6, 0x39, 0x67, 0xf0, 0x1f, 0xe0, 0xae, 0x78, 0x37, 0xd1, 0xea, 0x16, 0xc0, 0x4f, 0x47,
0xd2, 0x21, 0x2c, 0xc7, 0xc3, 0X94, 0xfc, 0x88, 0x8a, 0xd5, 0xd6, 0xee, 0xf6, 0xb1, 0x0f, 0x79

aead_key =
0xd9, 0x52, 0x98, 0x40, 0x20, 0x0e, 0x1c, 0x17, 0x72, 0x5a, 0xb5, 0x2c, 0x9c, 0x92, 0x76, 0x37

aead_iv =
0x6e, 0x9a, 0x63, 0x9d, 0x4a, 0xec, 0xc2, 0x55, 0x30, 0xa8, 0xad, 0x75

aad =
0x47, 0x2a, 0x6f, 0x4e, 0x77, 0x71, 0xca, 0x39, 0x1e, 0x42, 0x06, 0x50, 0x30, 0xdb, 0x3f, 0xf4

#################
# aead_buff_64 #
#################
[aead_buff_64]
digest =
0x4b, 0x84, 0x64, 0x63, 0x82, 0x0a, 0x4a, 0xb0, 0xea, 0xee, 0x50, 0x55, 0xea, 0x94, 0x9c, 0x08

#################
# aead_buff_512 #
#################
[aead_buff_512]
digest =
0x9a, 0x09, 0x0c, 0xdd, 0x42, 0x7f, 0xe1, 0x14, 0x9f, 0x09, 0x2c, 0x87, 0xbe, 0xf5, 0x93, 0x24
