/* SPDX-License-Identifier: BSD-3-Clause
 * Copyright(c) 2016-2017 Intel Corporation
 */

#include <rte_crypto.h>
#include <rte_malloc.h>

#include "cperf_test_vectors.h"

struct
cperf_modex_test_data modex_perf_data[10] = {
	{
		.base = {
			.data = {
				0xF8, 0xBA, 0x1A, 0x55, 0xD0, 0x2F, 0x85,
				0xAE, 0x96, 0x7B, 0xB6, 0x2F, 0xB6, 0xCD,
				0xA8, 0xEB, 0x7E, 0x78, 0xA0, 0x50
			},
			.len = 20
		},
		.exponent = {
			.data = {
				0x01, 0x00, 0x01
			},
			.len = 3
		},
		.modulus = {
			.data = {
				0xb3, 0xa1, 0xaf, 0xb7, 0x13, 0x08, 0x00, 0x0a,
				0x35, 0xdc, 0x2b, 0x20, 0x8d, 0xa1, 0xb5, 0xce,
				0x47, 0x8a, 0xc3, 0x80, 0xf4, 0x7d, 0x4a, 0xa2,
				0x62, 0xfd, 0x61, 0x7f, 0xb5, 0xa8, 0xde, 0x0a,
				0x17, 0x97, 0xa0, 0xbf, 0xdf, 0x56, 0x5a, 0x3d,
				0x51, 0x56, 0x4f, 0x70, 0x70, 0x3f, 0x63, 0x6a,
				0x44, 0x5b, 0xad, 0x84, 0x0d, 0x3f, 0x27, 0x6e,
				0x3b, 0x34, 0x91, 0x60, 0x14, 0xb9, 0xaa, 0x72,
				0xfd, 0xa3, 0x64, 0xd2, 0x03, 0xa7, 0x53, 0x87,
				0x9e, 0x88, 0x0b, 0xc1, 0x14, 0x93, 0x1a, 0x62,
				0xff, 0xb1, 0x5d, 0x74, 0xcd, 0x59, 0x63, 0x18,
				0x11, 0x3d, 0x4f, 0xba, 0x75, 0xd4, 0x33, 0x4e,
				0x23, 0x6b, 0x7b, 0x57, 0x44, 0xe1, 0xd3, 0x03,
				0x13, 0xa6, 0xf0, 0x8b, 0x60, 0xb0, 0x9e, 0xee,
				0x75, 0x08, 0x9d, 0x71, 0x63, 0x13, 0xcb, 0xa6,
				0x81, 0x92, 0x14, 0x03, 0x22, 0x2d, 0xde, 0x55
			},
			.len = 128
		},
		.result = {
			.len = 128
		}
	},
	{
		.base = {
			.data = {
				0x4F, 0xD8, 0x5C, 0xDB, 0x6D, 0xA2, 0xFA, 0x35,
				0x9D, 0xD7, 0x97, 0x10, 0x4B, 0x71, 0x5F, 0x53,
				0xE1, 0xC7, 0x09, 0x74, 0x88, 0xC8, 0x9D, 0x03,
				0xC0, 0x70, 0xE0, 0xBE, 0xE3, 0xF2, 0x2C, 0x01,
				0x85, 0xA6, 0x4E, 0x28, 0x6E, 0xD3, 0xB5, 0x18,
				0x58, 0x69, 0x07, 0xDA, 0x3A, 0x1B, 0x35, 0xCE,
				0xE6, 0xFA
			},
			.len = 50
		},
		.exponent = {
			.data = {
				0x30, 0xA5, 0xD5, 0xF0, 0x42, 0x03, 0xC3, 0x2D,
				0x2F, 0x58, 0xA8, 0x5C, 0x21, 0x88, 0xDE, 0x82,
				0x36, 0x44, 0xC1, 0x5A, 0x87, 0x2C, 0x33, 0x19,
				0x4E, 0xCE, 0x3F, 0x87, 0xFF, 0x98, 0x4B, 0xFC,
				0x15, 0xC0, 0xBE, 0x9E, 0x8F, 0xF0, 0x6A, 0x62
			},
			.len = 40
		},
		.modulus = {
			.data = {
				0xF8, 0x04, 0x0D, 0xD5, 0x09, 0x6C, 0x78, 0x06,
				0x7D, 0x28, 0x77, 0xA4, 0x0E, 0xA5, 0x49, 0xE7,
				0x6D, 0xC9, 0x97, 0xD3, 0xC0, 0x7F, 0x82, 0xC6,
				0x75, 0x51, 0x72, 0xAF, 0x8C, 0x77, 0x97, 0xD0,
				0xA1, 0x85, 0x54, 0xC0, 0x78, 0x86, 0xD6, 0x40,
				0x7A, 0x6B, 0xB3, 0xD7, 0x07, 0xCA, 0x27, 0xA3,
				0x66, 0xB9, 0x98, 0x22, 0xC4, 0x54, 0x18, 0x07,
				0x65, 0x76, 0x0F, 0x5A
			},
			.len = 60
		},
		.result = {
			.len = 60
		}
	},
	{
		.base = {
			.data = {
				0xD8, 0x21, 0xD2, 0x76, 0xAE, 0x01, 0x62, 0xD8,
				0x8C, 0x80, 0x01, 0x25, 0xC7, 0xE8, 0x4E, 0x0F,
				0x7F, 0x23, 0xFE, 0xBB
			},
			.len = 20
		},
		.exponent = {
			.data = {
				0xE5, 0xCE, 0x50, 0xE8, 0x97, 0x32, 0xFB, 0x5C,
				0xFC, 0x62
			},
			.len = 10
		},
		.modulus = {
			.data = {
				0x71, 0x3C, 0x6C, 0x7A, 0x19, 0x31, 0xF8, 0x94,
				0xC9, 0xAA, 0x25, 0x69, 0xA7, 0xF2, 0x28, 0x70,
				0x84, 0x5D, 0xEC, 0x40, 0xC8, 0xF9, 0xC5, 0x79,
				0xF9, 0x87, 0xD1, 0xA0, 0xC1, 0x5A, 0x06, 0xE4,
				0x65, 0xB8, 0x29, 0x0B, 0x2B, 0xFE, 0x67, 0xF0,
				0x91, 0x96, 0xE1, 0xCD, 0x5A, 0xCE, 0x44, 0xA3,
				0x4F, 0xE8, 0xBE, 0xC6, 0xA3, 0x0A, 0xCB, 0xF5,
				0x7D, 0x8B, 0x9B, 0x2F, 0x4E, 0xC9, 0x54, 0x48,
				0xA4, 0xC2, 0x09, 0xCE, 0xA5, 0x93, 0x1F, 0x43,
				0xC2, 0xCE, 0xFB, 0xBB, 0x69, 0x29, 0x03, 0x74,
				0xD6, 0x25, 0x47, 0x6B, 0xAC, 0x4E, 0x44, 0x8C,
				0x39, 0x2F, 0xB2, 0xDD, 0x15, 0x1B, 0xA3, 0x3D,
				0xA4, 0x0C, 0xFF, 0xCB, 0x05, 0xC2, 0x81, 0x97,
				0x16, 0xE2, 0xAC, 0x8A, 0xF3, 0xED, 0x80, 0xA4,
				0xC5, 0xFC, 0xF5, 0x6C, 0x4B, 0xBB, 0x05, 0x91,
				0xD4, 0x0F, 0xDA, 0x70, 0x7C, 0x9A, 0xA1, 0x63,
				0x15, 0xEE, 0xBB, 0x17, 0xE6, 0x20, 0x50, 0x74,
				0x36, 0x9C, 0xA1, 0x10, 0x29, 0x22, 0xFB, 0x7E,
				0x2A, 0x08, 0xF3, 0x07, 0xEA, 0xCD, 0x2C, 0x50,
				0x18, 0x15, 0x66, 0x87, 0x74, 0x19, 0x11, 0x2B,
				0x77, 0x85, 0xA0, 0x57, 0xA7, 0xEB, 0x6E, 0x15,
				0x15, 0x0D, 0xA4, 0x18, 0x5D, 0x54, 0x13, 0xE3,
				0x33, 0x12, 0x8D, 0xA3, 0xEF, 0x54, 0xE6, 0x1E,
				0xDB, 0x8F, 0x3D, 0x02, 0x3C, 0xCB, 0x34, 0x93,
				0x31, 0x1D, 0x4D, 0x3E, 0x9C, 0x22, 0x04, 0xD1,
				0x19, 0x53, 0x45, 0xE5, 0xBF, 0xF8, 0x70, 0x1A,
				0xEA, 0x52, 0x93, 0x2A, 0x26, 0x8A, 0x1E, 0x47,
				0xCE, 0x83, 0x5B, 0x35, 0x9A, 0xD2, 0x75, 0xC6,
				0xC6, 0x20, 0x84, 0x9F, 0x74, 0x69, 0x69, 0xB8,
				0x29, 0xD8, 0xA4, 0x70, 0x91, 0x42, 0x06, 0x25,
				0x38, 0xCB, 0x42, 0x75, 0x52, 0xEF, 0xB8, 0x64,
				0x4F, 0xC5, 0x7C, 0xC4, 0x09, 0xDB, 0x12
			},
			.len = 255
		},
		.result = {
			.len = 255
		}
	},
	{
		.base = {
			.data = {
				0x92, 0x45, 0x17, 0x7D, 0xD3, 0xF4, 0x2B, 0x93,
				0x8E, 0x1A, 0xFB, 0x1D, 0x13, 0x55, 0x53, 0x84,
				0x96, 0x3C, 0x39, 0xE0, 0xAF, 0x4A, 0xB4, 0xC9,
				0x16, 0x1F, 0xF4, 0x24, 0x65, 0xDD, 0xC3, 0x62,
				0x12, 0xAF, 0x86, 0x95, 0x0D, 0xDE, 0x28, 0x87,
				0x90, 0x11, 0xAA, 0x6E, 0x60, 0xCD, 0x54, 0xB7,
				0x48, 0x43
			},
			.len = 50
		},
		.exponent = {
			.data = {
				0x22, 0xD9, 0x4D, 0x01, 0x2F, 0x50, 0x5D, 0xE1,
				0x01, 0xAA, 0xC6, 0xC6, 0xCD, 0x5D, 0x7E, 0x61,
				0x75, 0x0A, 0xDC, 0x06, 0x07, 0x4B, 0xBD, 0x29,
				0x33, 0x09, 0x91, 0xD4, 0x29, 0xEB, 0x52, 0x24,
				0x27, 0xC6, 0x83, 0x6D, 0x70, 0xA9, 0xC9, 0x11
			},
			.len = 40
		},
		.modulus = {
			.data = {
				0xBB, 0x97, 0x8A, 0xB6, 0x26, 0xD4, 0x0E, 0x70,
				0x21, 0xA6, 0x56, 0x71, 0xE5, 0xD8, 0x18, 0x21,
				0x64, 0x9F, 0x1B, 0x6F, 0x7C, 0x27, 0x72, 0xB8,
				0x39, 0xE5, 0x2A, 0x94, 0x76, 0x22, 0xB7, 0x68,
				0x57, 0x3A, 0x01, 0x54, 0xA8, 0x50, 0x41, 0xA1,
				0xAD, 0xD0, 0xC7, 0xDB, 0xAA, 0x76, 0x7F, 0x37,
				0xA9, 0x27, 0x22, 0x8D, 0xF0, 0x5C, 0x5A, 0xAC,
				0xFB, 0x82, 0x6A, 0x8E, 0x31, 0x51, 0x54, 0x7C,
				0xDB, 0x55, 0x9C, 0xBC, 0x82, 0x27, 0xF4, 0x0B,
				0x94, 0x74, 0xC8, 0x83, 0x80, 0x1C, 0xD8, 0xFF,
				0x50, 0xA2, 0xC9, 0xED, 0x2B, 0x98, 0x77, 0xF3,
				0x31, 0x81, 0x1C, 0x41, 0x8E, 0xAF, 0x87, 0xA2,
				0x02, 0xAC, 0x8B, 0x55, 0x01, 0x5C, 0x16, 0x11,
				0x63, 0x8C, 0xE2, 0x0D, 0x51, 0xD2, 0x4C, 0xD7,
				0xD4, 0x3D, 0xE4, 0x79, 0x1A, 0xA7, 0xC4, 0xBF,
				0x4E, 0x2A, 0xC9, 0x74, 0xD6, 0xD4, 0x90, 0x03,
				0x65, 0x7F, 0x54, 0x0F, 0xAC, 0x5F, 0x98, 0x2C,
				0x46, 0xC0, 0xD7, 0xE6, 0x75, 0x95, 0xC3, 0xEA,
				0x05, 0x3A, 0x03, 0x55, 0x43, 0xC7, 0xC2, 0xD1,
				0x11, 0xCD, 0x57, 0x37, 0x0D, 0x40, 0x87, 0xDF,
				0x7D, 0xC3, 0x04, 0x54, 0xDE, 0x1D, 0xAF, 0xB8,
				0x02, 0x50, 0x42, 0xFF, 0x9D, 0xFB, 0x13, 0xF7,
				0x25, 0x5A, 0x8B, 0xE3, 0x31, 0xA2, 0x64, 0xF8,
				0x94, 0x50, 0x18, 0xFB, 0xBB, 0xA2, 0xE9, 0x13,
				0x77, 0x6E, 0xE1, 0x6F, 0x9F, 0x06, 0x03, 0xEE,
				0x0D, 0x06, 0x6E, 0xF2, 0x9B, 0x15, 0x70, 0xDD,
				0x26, 0x7C, 0xB4, 0x5D, 0xD0, 0xE7, 0x77, 0xC4,
				0xB9, 0x88, 0x75, 0xB8, 0x73, 0xFC, 0xE9, 0xB7,
				0x11, 0x26, 0xAC, 0xDB, 0x97, 0x27, 0x18, 0x21,
				0x50, 0x7E, 0x46, 0xB2, 0xF1, 0x50, 0x40, 0xD9,
				0x8B, 0x63, 0xDB, 0x1A, 0x8E, 0x29, 0xBE, 0x1F,
				0x88, 0x35, 0xFD, 0x95, 0xC3, 0xA6, 0x80, 0xEB,
				0x73, 0xF7, 0x02, 0x02, 0xB0, 0xCA, 0x97, 0x2C,
				0x32, 0x44, 0xA9, 0xCA, 0x94, 0xC0, 0xB2, 0xD9,
				0x7C, 0xD9, 0x10, 0x62, 0x31, 0xC9, 0xFA, 0x5B,
				0x5C, 0x2C, 0xB6, 0x04, 0x5B, 0x7E, 0x86, 0xBB,
				0x49, 0x02, 0x16, 0x9E, 0x1E, 0x53, 0xBD, 0xC2,
				0xA9, 0xAA, 0x94, 0x39, 0xA1, 0xB2, 0x18, 0x17,
				0xB6, 0x2C, 0xF6, 0xFF, 0xC0, 0xD0, 0x2D, 0x4D,
				0xAA, 0x6C, 0xB5, 0xC8, 0x6A, 0xBE, 0x38, 0xE4,
				0x9F, 0xDC, 0x5C, 0x56, 0x56, 0x04, 0x32, 0x49,
				0x91, 0x17, 0x44, 0x6E, 0xB3, 0xF9, 0x8F, 0xDB,
				0xEA, 0x04, 0x4C, 0x67, 0xE6, 0xDA, 0x96, 0x2F,
				0x89, 0x2D, 0x54, 0xC3, 0xAD, 0x07, 0x6B, 0xA0,
				0x87, 0xF0, 0xCF, 0x4B, 0x43, 0x46, 0xCA, 0x06,
				0x48, 0x8D, 0x34, 0xC4, 0xD2, 0xD2, 0xA9, 0x16,
				0x13, 0xF5, 0x49, 0x44, 0x8C, 0xD9, 0x0C, 0x1E,
				0x79, 0x47, 0xFC, 0x4C, 0x37, 0x8E, 0xD1, 0xFD,
				0xB9, 0xE8, 0x6E, 0x1B, 0x8D, 0x68, 0xCC, 0x49,
				0x0D, 0x98, 0xB5, 0xD5, 0x22, 0x1C, 0xFC, 0xBA,
				0x7A, 0x74, 0x3D, 0xBD, 0xD9, 0xB3, 0x80, 0x58,
				0x4A, 0x05, 0x67, 0x9D, 0x59, 0xF4, 0xF7, 0x72,
				0x11, 0x3C, 0x67, 0x96, 0xE7, 0x0D, 0x8E, 0x73,
				0xD1, 0xEE, 0x00, 0x79, 0x98, 0x7A, 0x0E, 0xE0,
				0xA8, 0xEA, 0x7D, 0xF3, 0xDB, 0x0E, 0x62, 0x3E,
				0x66, 0x95, 0xED, 0xD5, 0x8C, 0x39, 0xF5, 0xAB
			},
			.len = 448
		},
		.result = {
			.len = 448
		}
	},
	{
		.base = {
			.data = {
				0x02,
			},
			.len = 1
		},
		.exponent = {
			.data = {
				 0x6C, 0x80, 0xFF, 0x29, 0xF9, 0x27, 0x2E, 0x6D,
				 0xE1, 0xB7, 0x3F, 0x13, 0x77, 0xD2, 0x3E, 0x49,
				 0xCE, 0xAE, 0xBD, 0x73, 0x7A, 0x0F, 0xE7, 0xA4,
				 0x20, 0x49, 0x72, 0x87, 0x4E, 0x1B
			},
			.len = 30
		},
		.modulus = {
			.data = {
				0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
				0xC9, 0x0F, 0xDA, 0xA2, 0x21, 0x68, 0xC2, 0x34,
				0xC4, 0xC6, 0x62, 0x8B, 0x80, 0xDC, 0x1C, 0xD1,
				0x29, 0x02, 0x4E, 0x08, 0x8A, 0x67, 0xCC, 0x74,
				0x02, 0x0B, 0xBE, 0xA6, 0x3B, 0x13, 0x9B, 0x22,
				0x51, 0x4A, 0x08, 0x79, 0x8E, 0x34, 0x04, 0xDD,
				0xEF, 0x95, 0x19, 0xB3, 0xCD, 0x3A, 0x43, 0x1B,
				0x30, 0x2B, 0x0A, 0x6D, 0xF2, 0x5F, 0x14, 0x37,
				0x4F, 0xE1, 0x35, 0x6D, 0x6D, 0x51, 0xC2, 0x45,
				0xE4, 0x85, 0xB5, 0x76, 0x62, 0x5E, 0x7E, 0xC6,
				0xF4, 0x4C, 0x42, 0xE9, 0xA6, 0x37, 0xED, 0x6B,
				0x0B, 0xFF, 0x5C, 0xB6, 0xF4, 0x06, 0xB7, 0xED,
				0xEE, 0x38, 0x6B, 0xFB, 0x5A, 0x89, 0x9F, 0xA5,
				0xAE, 0x9F, 0x24, 0x11, 0x7C, 0x4B, 0x1F, 0xE6,
				0x49, 0x28, 0x66, 0x51, 0xEC, 0xE4, 0x5B, 0x3D,
				0xC2, 0x00, 0x7C, 0xB8, 0xA1, 0x63, 0xBF, 0x05,
				0x98, 0xDA, 0x48, 0x36, 0x1C, 0x55, 0xD3, 0x9A,
				0x69, 0x16, 0x3F, 0xA8, 0xFD, 0x24, 0xCF, 0x5F,
				0x83, 0x65, 0x5D, 0x23, 0xDC, 0xA3, 0xAD, 0x96,
				0x1C, 0x62, 0xF3, 0x56, 0x20, 0x85, 0x52, 0xBB,
				0x9E, 0xD5, 0x29, 0x07, 0x70, 0x96, 0x96, 0x6D,
				0x67, 0x0C, 0x35, 0x4E, 0x4A, 0xBC, 0x98, 0x04,
				0xF1, 0x74, 0x6C, 0x08, 0xCA, 0x23, 0x73, 0x27,
				0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
			},
			.len = 192
		},
		.result = {
			.len = 192
		}
	},
	{
		.base = {
			.data = {
				0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
				0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
				0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
				0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
				0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
				0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
				0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
				0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
			},
			.len = 64
		},
		.exponent = {
			.data = {
				0x8E, 0x4E, 0x41, 0xA2, 0xE0, 0x59, 0xA8, 0x29,
				0x71, 0xF6, 0x21, 0xC9, 0xD5, 0x0E, 0x36, 0x0F,
				0x59, 0xD6, 0x74, 0x4C, 0x3A, 0xC7, 0x13, 0x5E,
				0x7D, 0x2D, 0x43, 0x63, 0x5A, 0x3D, 0xCA, 0x5F,
				0xF7, 0xB2, 0x3D, 0x9C, 0x3F, 0xA1, 0x5D, 0x71
			},
			.len = 40
		},
		.modulus = {
			.data = {
				0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
				0xC9, 0x0F, 0xDA, 0xA2, 0x21, 0x68, 0xC2, 0x34,
				0xC4, 0xC6, 0x62, 0x8B, 0x80, 0xDC, 0x1C, 0xD1,
				0x29, 0x02, 0x4E, 0x08, 0x8A, 0x67, 0xCC, 0x74,
				0x02, 0x0B, 0xBE, 0xA6, 0x3B, 0x13, 0x9B, 0x22,
				0x51, 0x4A, 0x08, 0x79, 0x8E, 0x34, 0x04, 0xDD,
				0xEF, 0x95, 0x19, 0xB3, 0xCD, 0x3A, 0x43, 0x1B,
				0x30, 0x2B, 0x0A, 0x6D, 0xF2, 0x5F, 0x14, 0x37,
				0x4F, 0xE1, 0x35, 0x6D, 0x6D, 0x51, 0xC2, 0x45,
				0xE4, 0x85, 0xB5, 0x76, 0x62, 0x5E, 0x7E, 0xC6,
				0xF4, 0x4C, 0x42, 0xE9, 0xA6, 0x37, 0xED, 0x6B,
				0x0B, 0xFF, 0x5C, 0xB6, 0xF4, 0x06, 0xB7, 0xED,
				0xEE, 0x38, 0x6B, 0xFB, 0x5A, 0x89, 0x9F, 0xA5,
				0xAE, 0x9F, 0x24, 0x11, 0x7C, 0x4B, 0x1F, 0xE6,
				0x49, 0x28, 0x66, 0x51, 0xEC, 0xE4, 0x5B, 0x3D,
				0xC2, 0x00, 0x7C, 0xB8, 0xA1, 0x63, 0xBF, 0x05,
				0x98, 0xDA, 0x48, 0x36, 0x1C, 0x55, 0xD3, 0x9A,
				0x69, 0x16, 0x3F, 0xA8, 0xFD, 0x24, 0xCF, 0x5F,
				0x83, 0x65, 0x5D, 0x23, 0xDC, 0xA3, 0xAD, 0x96,
				0x1C, 0x62, 0xF3, 0x56, 0x20, 0x85, 0x52, 0xBB,
				0x9E, 0xD5, 0x29, 0x07, 0x70, 0x96, 0x96, 0x6D,
				0x67, 0x0C, 0x35, 0x4E, 0x4A, 0xBC, 0x98, 0x04,
				0xF1, 0x74, 0x6C, 0x08, 0xCA, 0x18, 0x21, 0x7C,
				0x32, 0x90, 0x5E, 0x46, 0x2E, 0x36, 0xCE, 0x3B,
				0xE3, 0x9E, 0x77, 0x2C, 0x18, 0x0E, 0x86, 0x03,
				0x9B, 0x27, 0x83, 0xA2, 0xEC, 0x07, 0xA2, 0x8F,
				0xB5, 0xC5, 0x5D, 0xF0, 0x6F, 0x4C, 0x52, 0xC9,
				0xDE, 0x2B, 0xCB, 0xF6, 0x95, 0x58, 0x17, 0x18,
				0x39, 0x95, 0x49, 0x7C, 0xEA, 0x95, 0x6A, 0xE5,
				0x15, 0xD2, 0x26, 0x18, 0x98, 0xFA, 0x05, 0x10,
				0x15, 0x72, 0x8E, 0x5A, 0x8A, 0xAC, 0xAA, 0x68,
				0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
			},
			.len = 256
		},
		.result = {
			.len = 256
		}
	},
	{
		.base = {
			.data = {
				0x02,
			},
			.len = 1
		},
		.exponent = {
			.data = {
				0x63, 0x4D, 0x67, 0x4E, 0x4A, 0x16, 0x0F, 0xEB,
				0x76, 0xC8, 0xAB, 0x3B, 0x4A, 0x74, 0x03, 0x02,
				0x2F, 0xAC, 0x34, 0x23, 0xC8, 0x2E, 0x1E, 0x60,
				0x63, 0x1E, 0x7D, 0x3F, 0x22, 0xB9, 0xBF, 0x2D,
				0x4F, 0xB3, 0x72, 0xAC, 0x1E, 0x62, 0xA7, 0x47,
				0x7A, 0xF3, 0x45, 0xAB, 0x5B, 0x67, 0x12, 0x80,
				0x77, 0xDA, 0xF8, 0xF4
			},
			.len = 52
		},
		.modulus = {
			.data = {
				0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
				0xC9, 0x0F, 0xDA, 0xA2, 0x21, 0x68, 0xC2, 0x34,
				0xC4, 0xC6, 0x62, 0x8B, 0x80, 0xDC, 0x1C, 0xD1,
				0x29, 0x02, 0x4E, 0x08, 0x8A, 0x67, 0xCC, 0x74,
				0x02, 0x0B, 0xBE, 0xA6, 0x3B, 0x13, 0x9B, 0x22,
				0x51, 0x4A, 0x08, 0x79, 0x8E, 0x34, 0x04, 0xDD,
				0xEF, 0x95, 0x19, 0xB3, 0xCD, 0x3A, 0x43, 0x1B,
				0x30, 0x2B, 0x0A, 0x6D, 0xF2, 0x5F, 0x14, 0x37,
				0x4F, 0xE1, 0x35, 0x6D, 0x6D, 0x51, 0xC2, 0x45,
				0xE4, 0x85, 0xB5, 0x76, 0x62, 0x5E, 0x7E, 0xC6,
				0xF4, 0x4C, 0x42, 0xE9, 0xA6, 0x37, 0xED, 0x6B,
				0x0B, 0xFF, 0x5C, 0xB6, 0xF4, 0x06, 0xB7, 0xED,
				0xEE, 0x38, 0x6B, 0xFB, 0x5A, 0x89, 0x9F, 0xA5,
				0xAE, 0x9F, 0x24, 0x11, 0x7C, 0x4B, 0x1F, 0xE6,
				0x49, 0x28, 0x66, 0x51, 0xEC, 0xE4, 0x5B, 0x3D,
				0xC2, 0x00, 0x7C, 0xB8, 0xA1, 0x63, 0xBF, 0x05,
				0x98, 0xDA, 0x48, 0x36, 0x1C, 0x55, 0xD3, 0x9A,
				0x69, 0x16, 0x3F, 0xA8, 0xFD, 0x24, 0xCF, 0x5F,
				0x83, 0x65, 0x5D, 0x23, 0xDC, 0xA3, 0xAD, 0x96,
				0x1C, 0x62, 0xF3, 0x56, 0x20, 0x85, 0x52, 0xBB,
				0x9E, 0xD5, 0x29, 0x07, 0x70, 0x96, 0x96, 0x6D,
				0x67, 0x0C, 0x35, 0x4E, 0x4A, 0xBC, 0x98, 0x04,
				0xF1, 0x74, 0x6C, 0x08, 0xCA, 0x18, 0x21, 0x7C,
				0x32, 0x90, 0x5E, 0x46, 0x2E, 0x36, 0xCE, 0x3B,
				0xE3, 0x9E, 0x77, 0x2C, 0x18, 0x0E, 0x86, 0x03,
				0x9B, 0x27, 0x83, 0xA2, 0xEC, 0x07, 0xA2, 0x8F,
				0xB5, 0xC5, 0x5D, 0xF0, 0x6F, 0x4C, 0x52, 0xC9,
				0xDE, 0x2B, 0xCB, 0xF6, 0x95, 0x58, 0x17, 0x18,
				0x39, 0x95, 0x49, 0x7C, 0xEA, 0x95, 0x6A, 0xE5,
				0x15, 0xD2, 0x26, 0x18, 0x98, 0xFA, 0x05, 0x10,
				0x15, 0x72, 0x8E, 0x5A, 0x8A, 0xAA, 0xC4, 0x2D,
				0xAD, 0x33, 0x17, 0x0D, 0x04, 0x50, 0x7A, 0x33,
				0xA8, 0x55, 0x21, 0xAB, 0xDF, 0x1C, 0xBA, 0x64,
				0xEC, 0xFB, 0x85, 0x04, 0x58, 0xDB, 0xEF, 0x0A,
				0x8A, 0xEA, 0x71, 0x57, 0x5D, 0x06, 0x0C, 0x7D,
				0xB3, 0x97, 0x0F, 0x85, 0xA6, 0xE1, 0xE4, 0xC7,
				0xAB, 0xF5, 0xAE, 0x8C, 0xDB, 0x09, 0x33, 0xD7,
				0x1E, 0x8C, 0x94, 0xE0, 0x4A, 0x25, 0x61, 0x9D,
				0xCE, 0xE3, 0xD2, 0x26, 0x1A, 0xD2, 0xEE, 0x6B,
				0xF1, 0x2F, 0xFA, 0x06, 0xD9, 0x8A, 0x08, 0x64,
				0xD8, 0x76, 0x02, 0x73, 0x3E, 0xC8, 0x6A, 0x64,
				0x52, 0x1F, 0x2B, 0x18, 0x17, 0x7B, 0x20, 0x0C,
				0xBB, 0xE1, 0x17, 0x57, 0x7A, 0x61, 0x5D, 0x6C,
				0x77, 0x09, 0x88, 0xC0, 0xBA, 0xD9, 0x46, 0xE2,
				0x08, 0xE2, 0x4F, 0xA0, 0x74, 0xE5, 0xAB, 0x31,
				0x43, 0xDB, 0x5B, 0xFC, 0xE0, 0xFD, 0x10, 0x8E,
				0x4B, 0x82, 0xD1, 0x20, 0xA9, 0x3A, 0xD2, 0xCA,
				0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
			},
			.len = 384
		},
		.result = {
			.len = 384
		}
	},
	{
		.base = {
			.data = {
				0x02,
			},
			.len = 1
		},
		.exponent = {
			.data = {
				0xF2, 0x77, 0xFF, 0x91, 0x08, 0xF6, 0x16, 0x8E,
				0xEE, 0x8C, 0xCC, 0x62, 0x07, 0xA4, 0xE3, 0x0F,
				0xB8, 0xE8, 0xFD, 0x77, 0xEA, 0x06, 0x1D, 0x9F,
				0x2A, 0x96, 0xE8, 0x0D, 0x66, 0xA4, 0x97, 0x7E,
				0xDA, 0xDB, 0xC0, 0xC0, 0x2F, 0x72, 0xCD, 0xFC,
				0xBE, 0xC3, 0xAA, 0x46, 0x31, 0x7C, 0x4B, 0x4D,
				0x0B, 0x14, 0x02, 0x5C, 0x7F, 0x29, 0xC1, 0xDE,
				0xC5, 0x06, 0x70, 0x0B
			},
			.len = 60
		},
		.modulus = {
			.data = {
				0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
				0xC9, 0x0F, 0xDA, 0xA2, 0x21, 0x68, 0xC2, 0x34,
				0xC4, 0xC6, 0x62, 0x8B, 0x80, 0xDC, 0x1C, 0xD1,
				0x29, 0x02, 0x4E, 0x08, 0x8A, 0x67, 0xCC, 0x74,
				0x02, 0x0B, 0xBE, 0xA6, 0x3B, 0x13, 0x9B, 0x22,
				0x51, 0x4A, 0x08, 0x79, 0x8E, 0x34, 0x04, 0xDD,
				0xEF, 0x95, 0x19, 0xB3, 0xCD, 0x3A, 0x43, 0x1B,
				0x30, 0x2B, 0x0A, 0x6D, 0xF2, 0x5F, 0x14, 0x37,
				0x4F, 0xE1, 0x35, 0x6D, 0x6D, 0x51, 0xC2, 0x45,
				0xE4, 0x85, 0xB5, 0x76, 0x62, 0x5E, 0x7E, 0xC6,
				0xF4, 0x4C, 0x42, 0xE9, 0xA6, 0x37, 0xED, 0x6B,
				0x0B, 0xFF, 0x5C, 0xB6, 0xF4, 0x06, 0xB7, 0xED,
				0xEE, 0x38, 0x6B, 0xFB, 0x5A, 0x89, 0x9F, 0xA5,
				0xAE, 0x9F, 0x24, 0x11, 0x7C, 0x4B, 0x1F, 0xE6,
				0x49, 0x28, 0x66, 0x51, 0xEC, 0xE4, 0x5B, 0x3D,
				0xC2, 0x00, 0x7C, 0xB8, 0xA1, 0x63, 0xBF, 0x05,
				0x98, 0xDA, 0x48, 0x36, 0x1C, 0x55, 0xD3, 0x9A,
				0x69, 0x16, 0x3F, 0xA8, 0xFD, 0x24, 0xCF, 0x5F,
				0x83, 0x65, 0x5D, 0x23, 0xDC, 0xA3, 0xAD, 0x96,
				0x1C, 0x62, 0xF3, 0x56, 0x20, 0x85, 0x52, 0xBB,
				0x9E, 0xD5, 0x29, 0x07, 0x70, 0x96, 0x96, 0x6D,
				0x67, 0x0C, 0x35, 0x4E, 0x4A, 0xBC, 0x98, 0x04,
				0xF1, 0x74, 0x6C, 0x08, 0xCA, 0x18, 0x21, 0x7C,
				0x32, 0x90, 0x5E, 0x46, 0x2E, 0x36, 0xCE, 0x3B,
				0xE3, 0x9E, 0x77, 0x2C, 0x18, 0x0E, 0x86, 0x03,
				0x9B, 0x27, 0x83, 0xA2, 0xEC, 0x07, 0xA2, 0x8F,
				0xB5, 0xC5, 0x5D, 0xF0, 0x6F, 0x4C, 0x52, 0xC9,
				0xDE, 0x2B, 0xCB, 0xF6, 0x95, 0x58, 0x17, 0x18,
				0x39, 0x95, 0x49, 0x7C, 0xEA, 0x95, 0x6A, 0xE5,
				0x15, 0xD2, 0x26, 0x18, 0x98, 0xFA, 0x05, 0x10,
				0x15, 0x72, 0x8E, 0x5A, 0x8A, 0xAA, 0xC4, 0x2D,
				0xAD, 0x33, 0x17, 0x0D, 0x04, 0x50, 0x7A, 0x33,
				0xA8, 0x55, 0x21, 0xAB, 0xDF, 0x1C, 0xBA, 0x64,
				0xEC, 0xFB, 0x85, 0x04, 0x58, 0xDB, 0xEF, 0x0A,
				0x8A, 0xEA, 0x71, 0x57, 0x5D, 0x06, 0x0C, 0x7D,
				0xB3, 0x97, 0x0F, 0x85, 0xA6, 0xE1, 0xE4, 0xC7,
				0xAB, 0xF5, 0xAE, 0x8C, 0xDB, 0x09, 0x33, 0xD7,
				0x1E, 0x8C, 0x94, 0xE0, 0x4A, 0x25, 0x61, 0x9D,
				0xCE, 0xE3, 0xD2, 0x26, 0x1A, 0xD2, 0xEE, 0x6B,
				0xF1, 0x2F, 0xFA, 0x06, 0xD9, 0x8A, 0x08, 0x64,
				0xD8, 0x76, 0x02, 0x73, 0x3E, 0xC8, 0x6A, 0x64,
				0x52, 0x1F, 0x2B, 0x18, 0x17, 0x7B, 0x20, 0x0C,
				0xBB, 0xE1, 0x17, 0x57, 0x7A, 0x61, 0x5D, 0x6C,
				0x77, 0x09, 0x88, 0xC0, 0xBA, 0xD9, 0x46, 0xE2,
				0x08, 0xE2, 0x4F, 0xA0, 0x74, 0xE5, 0xAB, 0x31,
				0x43, 0xDB, 0x5B, 0xFC, 0xE0, 0xFD, 0x10, 0x8E,
				0x4B, 0x82, 0xD1, 0x20, 0xA9, 0x21, 0x08, 0x01,
				0x1A, 0x72, 0x3C, 0x12, 0xA7, 0x87, 0xE6, 0xD7,
				0x88, 0x71, 0x9A, 0x10, 0xBD, 0xBA, 0x5B, 0x26,
				0x99, 0xC3, 0x27, 0x18, 0x6A, 0xF4, 0xE2, 0x3C,
				0x1A, 0x94, 0x68, 0x34, 0xB6, 0x15, 0x0B, 0xDA,
				0x25, 0x83, 0xE9, 0xCA, 0x2A, 0xD4, 0x4C, 0xE8,
				0xDB, 0xBB, 0xC2, 0xDB, 0x04, 0xDE, 0x8E, 0xF9,
				0x2E, 0x8E, 0xFC, 0x14, 0x1F, 0xBE, 0xCA, 0xA6,
				0x28, 0x7C, 0x59, 0x47, 0x4E, 0x6B, 0xC0, 0x5D,
				0x99, 0xB2, 0x96, 0x4F, 0xA0, 0x90, 0xC3, 0xA2,
				0x23, 0x3B, 0xA1, 0x86, 0x51, 0x5B, 0xE7, 0xED,
				0x1F, 0x61, 0x29, 0x70, 0xCE, 0xE2, 0xD7, 0xAF,
				0xB8, 0x1B, 0xDD, 0x76, 0x21, 0x70, 0x48, 0x1C,
				0xD0, 0x06, 0x91, 0x27, 0xD5, 0xB0, 0x5A, 0xA9,
				0x93, 0xB4, 0xEA, 0x98, 0x8D, 0x8F, 0xDD, 0xC1,
				0x86, 0xFF, 0xB7, 0xDC, 0x90, 0xA6, 0xC0, 0x8F,
				0x4D, 0xF4, 0x35, 0xC9, 0x34, 0x06, 0x31, 0x99,
				0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
			},
			.len = 512
		},
		.result = {
			.len = 512
		}
	},
	{
		.base = {
			.data = {
				0x02,
			},
			.len = 1
		},
		.exponent = {
			.data = {
				0x06, 0x8B, 0x74, 0x99, 0x02, 0xCE, 0x50, 0x2C,
				0xED, 0x29, 0x2F, 0xFB, 0x14, 0x74, 0x11, 0x7A,
				0x7C, 0x1D, 0xBF, 0xF8, 0xC3, 0x2D, 0xFD, 0x45,
				0x56, 0xCF, 0xCD, 0x92, 0x12, 0xF2, 0xC1, 0x96,
				0x73, 0x11, 0x4C, 0xAC, 0xFA, 0x0C, 0x4B, 0x2B,
				0xFA, 0xED, 0xA5, 0x5A, 0xDD, 0xF7, 0x5F, 0x75,
				0xB0, 0x18, 0x69, 0x63, 0xB0, 0x8E, 0x04, 0xA2,
				0x0D, 0x1F, 0x68, 0xA9, 0x1A, 0x75, 0x8A, 0x29,
				0xD4, 0xC1, 0x31, 0xAC
			},
			.len = 68
		},
		.modulus = {
			.data = {
				0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
				0xC9, 0x0F, 0xDA, 0xA2, 0x21, 0x68, 0xC2, 0x34,
				0xC4, 0xC6, 0x62, 0x8B, 0x80, 0xDC, 0x1C, 0xD1,
				0x29, 0x02, 0x4E, 0x08, 0x8A, 0x67, 0xCC, 0x74,
				0x02, 0x0B, 0xBE, 0xA6, 0x3B, 0x13, 0x9B, 0x22,
				0x51, 0x4A, 0x08, 0x79, 0x8E, 0x34, 0x04, 0xDD,
				0xEF, 0x95, 0x19, 0xB3, 0xCD, 0x3A, 0x43, 0x1B,
				0x30, 0x2B, 0x0A, 0x6D, 0xF2, 0x5F, 0x14, 0x37,
				0x4F, 0xE1, 0x35, 0x6D, 0x6D, 0x51, 0xC2, 0x45,
				0xE4, 0x85, 0xB5, 0x76, 0x62, 0x5E, 0x7E, 0xC6,
				0xF4, 0x4C, 0x42, 0xE9, 0xA6, 0x37, 0xED, 0x6B,
				0x0B, 0xFF, 0x5C, 0xB6, 0xF4, 0x06, 0xB7, 0xED,
				0xEE, 0x38, 0x6B, 0xFB, 0x5A, 0x89, 0x9F, 0xA5,
				0xAE, 0x9F, 0x24, 0x11, 0x7C, 0x4B, 0x1F, 0xE6,
				0x49, 0x28, 0x66, 0x51, 0xEC, 0xE4, 0x5B, 0x3D,
				0xC2, 0x00, 0x7C, 0xB8, 0xA1, 0x63, 0xBF, 0x05,
				0x98, 0xDA, 0x48, 0x36, 0x1C, 0x55, 0xD3, 0x9A,
				0x69, 0x16, 0x3F, 0xA8, 0xFD, 0x24, 0xCF, 0x5F,
				0x83, 0x65, 0x5D, 0x23, 0xDC, 0xA3, 0xAD, 0x96,
				0x1C, 0x62, 0xF3, 0x56, 0x20, 0x85, 0x52, 0xBB,
				0x9E, 0xD5, 0x29, 0x07, 0x70, 0x96, 0x96, 0x6D,
				0x67, 0x0C, 0x35, 0x4E, 0x4A, 0xBC, 0x98, 0x04,
				0xF1, 0x74, 0x6C, 0x08, 0xCA, 0x18, 0x21, 0x7C,
				0x32, 0x90, 0x5E, 0x46, 0x2E, 0x36, 0xCE, 0x3B,
				0xE3, 0x9E, 0x77, 0x2C, 0x18, 0x0E, 0x86, 0x03,
				0x9B, 0x27, 0x83, 0xA2, 0xEC, 0x07, 0xA2, 0x8F,
				0xB5, 0xC5, 0x5D, 0xF0, 0x6F, 0x4C, 0x52, 0xC9,
				0xDE, 0x2B, 0xCB, 0xF6, 0x95, 0x58, 0x17, 0x18,
				0x39, 0x95, 0x49, 0x7C, 0xEA, 0x95, 0x6A, 0xE5,
				0x15, 0xD2, 0x26, 0x18, 0x98, 0xFA, 0x05, 0x10,
				0x15, 0x72, 0x8E, 0x5A, 0x8A, 0xAA, 0xC4, 0x2D,
				0xAD, 0x33, 0x17, 0x0D, 0x04, 0x50, 0x7A, 0x33,
				0xA8, 0x55, 0x21, 0xAB, 0xDF, 0x1C, 0xBA, 0x64,
				0xEC, 0xFB, 0x85, 0x04, 0x58, 0xDB, 0xEF, 0x0A,
				0x8A, 0xEA, 0x71, 0x57, 0x5D, 0x06, 0x0C, 0x7D,
				0xB3, 0x97, 0x0F, 0x85, 0xA6, 0xE1, 0xE4, 0xC7,
				0xAB, 0xF5, 0xAE, 0x8C, 0xDB, 0x09, 0x33, 0xD7,
				0x1E, 0x8C, 0x94, 0xE0, 0x4A, 0x25, 0x61, 0x9D,
				0xCE, 0xE3, 0xD2, 0x26, 0x1A, 0xD2, 0xEE, 0x6B,
				0xF1, 0x2F, 0xFA, 0x06, 0xD9, 0x8A, 0x08, 0x64,
				0xD8, 0x76, 0x02, 0x73, 0x3E, 0xC8, 0x6A, 0x64,
				0x52, 0x1F, 0x2B, 0x18, 0x17, 0x7B, 0x20, 0x0C,
				0xBB, 0xE1, 0x17, 0x57, 0x7A, 0x61, 0x5D, 0x6C,
				0x77, 0x09, 0x88, 0xC0, 0xBA, 0xD9, 0x46, 0xE2,
				0x08, 0xE2, 0x4F, 0xA0, 0x74, 0xE5, 0xAB, 0x31,
				0x43, 0xDB, 0x5B, 0xFC, 0xE0, 0xFD, 0x10, 0x8E,
				0x4B, 0x82, 0xD1, 0x20, 0xA9, 0x21, 0x08, 0x01,
				0x1A, 0x72, 0x3C, 0x12, 0xA7, 0x87, 0xE6, 0xD7,
				0x88, 0x71, 0x9A, 0x10, 0xBD, 0xBA, 0x5B, 0x26,
				0x99, 0xC3, 0x27, 0x18, 0x6A, 0xF4, 0xE2, 0x3C,
				0x1A, 0x94, 0x68, 0x34, 0xB6, 0x15, 0x0B, 0xDA,
				0x25, 0x83, 0xE9, 0xCA, 0x2A, 0xD4, 0x4C, 0xE8,
				0xDB, 0xBB, 0xC2, 0xDB, 0x04, 0xDE, 0x8E, 0xF9,
				0x2E, 0x8E, 0xFC, 0x14, 0x1F, 0xBE, 0xCA, 0xA6,
				0x28, 0x7C, 0x59, 0x47, 0x4E, 0x6B, 0xC0, 0x5D,
				0x99, 0xB2, 0x96, 0x4F, 0xA0, 0x90, 0xC3, 0xA2,
				0x23, 0x3B, 0xA1, 0x86, 0x51, 0x5B, 0xE7, 0xED,
				0x1F, 0x61, 0x29, 0x70, 0xCE, 0xE2, 0xD7, 0xAF,
				0xB8, 0x1B, 0xDD, 0x76, 0x21, 0x70, 0x48, 0x1C,
				0xD0, 0x06, 0x91, 0x27, 0xD5, 0xB0, 0x5A, 0xA9,
				0x93, 0xB4, 0xEA, 0x98, 0x8D, 0x8F, 0xDD, 0xC1,
				0x86, 0xFF, 0xB7, 0xDC, 0x90, 0xA6, 0xC0, 0x8F,
				0x4D, 0xF4, 0x35, 0xC9, 0x34, 0x02, 0x84, 0x92,
				0x36, 0xC3, 0xFA, 0xB4, 0xD2, 0x7C, 0x70, 0x26,
				0xC1, 0xD4, 0xDC, 0xB2, 0x60, 0x26, 0x46, 0xDE,
				0xC9, 0x75, 0x1E, 0x76, 0x3D, 0xBA, 0x37, 0xBD,
				0xF8, 0xFF, 0x94, 0x06, 0xAD, 0x9E, 0x53, 0x0E,
				0xE5, 0xDB, 0x38, 0x2F, 0x41, 0x30, 0x01, 0xAE,
				0xB0, 0x6A, 0x53, 0xED, 0x90, 0x27, 0xD8, 0x31,
				0x17, 0x97, 0x27, 0xB0, 0x86, 0x5A, 0x89, 0x18,
				0xDA, 0x3E, 0xDB, 0xEB, 0xCF, 0x9B, 0x14, 0xED,
				0x44, 0xCE, 0x6C, 0xBA, 0xCE, 0xD4, 0xBB, 0x1B,
				0xDB, 0x7F, 0x14, 0x47, 0xE6, 0xCC, 0x25, 0x4B,
				0x33, 0x20, 0x51, 0x51, 0x2B, 0xD7, 0xAF, 0x42,
				0x6F, 0xB8, 0xF4, 0x01, 0x37, 0x8C, 0xD2, 0xBF,
				0x59, 0x83, 0xCA, 0x01, 0xC6, 0x4B, 0x92, 0xEC,
				0xF0, 0x32, 0xEA, 0x15, 0xD1, 0x72, 0x1D, 0x03,
				0xF4, 0x82, 0xD7, 0xCE, 0x6E, 0x74, 0xFE, 0xF6,
				0xD5, 0x5E, 0x70, 0x2F, 0x46, 0x98, 0x0C, 0x82,
				0xB5, 0xA8, 0x40, 0x31, 0x90, 0x0B, 0x1C, 0x9E,
				0x59, 0xE7, 0xC9, 0x7F, 0xBE, 0xC7, 0xE8, 0xF3,
				0x23, 0xA9, 0x7A, 0x7E, 0x36, 0xCC, 0x88, 0xBE,
				0x0F, 0x1D, 0x45, 0xB7, 0xFF, 0x58, 0x5A, 0xC5,
				0x4B, 0xD4, 0x07, 0xB2, 0x2B, 0x41, 0x54, 0xAA,
				0xCC, 0x8F, 0x6D, 0x7E, 0xBF, 0x48, 0xE1, 0xD8,
				0x14, 0xCC, 0x5E, 0xD2, 0x0F, 0x80, 0x37, 0xE0,
				0xA7, 0x97, 0x15, 0xEE, 0xF2, 0x9B, 0xE3, 0x28,
				0x06, 0xA1, 0xD5, 0x8B, 0xB7, 0xC5, 0xDA, 0x76,
				0xF5, 0x50, 0xAA, 0x3D, 0x8A, 0x1F, 0xBF, 0xF0,
				0xEB, 0x19, 0xCC, 0xB1, 0xA3, 0x13, 0xD5, 0x5C,
				0xDA, 0x56, 0xC9, 0xEC, 0x2E, 0xF2, 0x96, 0x32,
				0x38, 0x7F, 0xE8, 0xD7, 0x6E, 0x3C, 0x04, 0x68,
				0x04, 0x3E, 0x8F, 0x66, 0x3F, 0x48, 0x60, 0xEE,
				0x12, 0xBF, 0x2D, 0x5B, 0x0B, 0x74, 0x74, 0xD6,
				0xE6, 0x94, 0xF9, 0x1E, 0x6D, 0xCC, 0x40, 0x24,
				0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
			},
			.len = 768
		},
		.result = {
			.len = 768
		}
	},
	{
		.base = {
			.data = {
				0x02,
			},
			.len = 1
		},
		.exponent = {
			.data = {
				0x01, 0xA6, 0x8A, 0x0A, 0xDA, 0xA6, 0x14, 0x43,
				0x84, 0xD6, 0xEB, 0x11, 0x67, 0xA6, 0xD2, 0xAC,
				0x11, 0x5D, 0x15, 0x99, 0x31, 0x99, 0xAE, 0x08,
				0x3D, 0xEC, 0x19, 0x57, 0x3D, 0xDF, 0x96, 0x7C,
				0x9A, 0x1A, 0x72, 0x80, 0x1F, 0xF3, 0x50, 0x91,
				0xD0, 0x70, 0x11, 0x37, 0xA8, 0xFE, 0xE4, 0x37,
				0x3B, 0x36, 0x62, 0x89, 0xCF, 0x31, 0x1D, 0x76,
				0x28, 0xBE, 0x5F, 0x25, 0x12, 0x6E, 0x72, 0x83,
				0x8A, 0x1A, 0xC2, 0xFA, 0xD6, 0x49, 0x2C, 0x4F,
				0x2D, 0xF1, 0x77, 0x67, 0x49, 0xDA
			},
			.len = 78
		},
		.modulus = {
			.data = {
				0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
				0xC9, 0x0F, 0xDA, 0xA2, 0x21, 0x68, 0xC2, 0x34,
				0xC4, 0xC6, 0x62, 0x8B, 0x80, 0xDC, 0x1C, 0xD1,
				0x29, 0x02, 0x4E, 0x08, 0x8A, 0x67, 0xCC, 0x74,
				0x02, 0x0B, 0xBE, 0xA6, 0x3B, 0x13, 0x9B, 0x22,
				0x51, 0x4A, 0x08, 0x79, 0x8E, 0x34, 0x04, 0xDD,
				0xEF, 0x95, 0x19, 0xB3, 0xCD, 0x3A, 0x43, 0x1B,
				0x30, 0x2B, 0x0A, 0x6D, 0xF2, 0x5F, 0x14, 0x37,
				0x4F, 0xE1, 0x35, 0x6D, 0x6D, 0x51, 0xC2, 0x45,
				0xE4, 0x85, 0xB5, 0x76, 0x62, 0x5E, 0x7E, 0xC6,
				0xF4, 0x4C, 0x42, 0xE9, 0xA6, 0x37, 0xED, 0x6B,
				0x0B, 0xFF, 0x5C, 0xB6, 0xF4, 0x06, 0xB7, 0xED,
				0xEE, 0x38, 0x6B, 0xFB, 0x5A, 0x89, 0x9F, 0xA5,
				0xAE, 0x9F, 0x24, 0x11, 0x7C, 0x4B, 0x1F, 0xE6,
				0x49, 0x28, 0x66, 0x51, 0xEC, 0xE4, 0x5B, 0x3D,
				0xC2, 0x00, 0x7C, 0xB8, 0xA1, 0x63, 0xBF, 0x05,
				0x98, 0xDA, 0x48, 0x36, 0x1C, 0x55, 0xD3, 0x9A,
				0x69, 0x16, 0x3F, 0xA8, 0xFD, 0x24, 0xCF, 0x5F,
				0x83, 0x65, 0x5D, 0x23, 0xDC, 0xA3, 0xAD, 0x96,
				0x1C, 0x62, 0xF3, 0x56, 0x20, 0x85, 0x52, 0xBB,
				0x9E, 0xD5, 0x29, 0x07, 0x70, 0x96, 0x96, 0x6D,
				0x67, 0x0C, 0x35, 0x4E, 0x4A, 0xBC, 0x98, 0x04,
				0xF1, 0x74, 0x6C, 0x08, 0xCA, 0x18, 0x21, 0x7C,
				0x32, 0x90, 0x5E, 0x46, 0x2E, 0x36, 0xCE, 0x3B,
				0xE3, 0x9E, 0x77, 0x2C, 0x18, 0x0E, 0x86, 0x03,
				0x9B, 0x27, 0x83, 0xA2, 0xEC, 0x07, 0xA2, 0x8F,
				0xB5, 0xC5, 0x5D, 0xF0, 0x6F, 0x4C, 0x52, 0xC9,
				0xDE, 0x2B, 0xCB, 0xF6, 0x95, 0x58, 0x17, 0x18,
				0x39, 0x95, 0x49, 0x7C, 0xEA, 0x95, 0x6A, 0xE5,
				0x15, 0xD2, 0x26, 0x18, 0x98, 0xFA, 0x05, 0x10,
				0x15, 0x72, 0x8E, 0x5A, 0x8A, 0xAA, 0xC4, 0x2D,
				0xAD, 0x33, 0x17, 0x0D, 0x04, 0x50, 0x7A, 0x33,
				0xA8, 0x55, 0x21, 0xAB, 0xDF, 0x1C, 0xBA, 0x64,
				0xEC, 0xFB, 0x85, 0x04, 0x58, 0xDB, 0xEF, 0x0A,
				0x8A, 0xEA, 0x71, 0x57, 0x5D, 0x06, 0x0C, 0x7D,
				0xB3, 0x97, 0x0F, 0x85, 0xA6, 0xE1, 0xE4, 0xC7,
				0xAB, 0xF5, 0xAE, 0x8C, 0xDB, 0x09, 0x33, 0xD7,
				0x1E, 0x8C, 0x94, 0xE0, 0x4A, 0x25, 0x61, 0x9D,
				0xCE, 0xE3, 0xD2, 0x26, 0x1A, 0xD2, 0xEE, 0x6B,
				0xF1, 0x2F, 0xFA, 0x06, 0xD9, 0x8A, 0x08, 0x64,
				0xD8, 0x76, 0x02, 0x73, 0x3E, 0xC8, 0x6A, 0x64,
				0x52, 0x1F, 0x2B, 0x18, 0x17, 0x7B, 0x20, 0x0C,
				0xBB, 0xE1, 0x17, 0x57, 0x7A, 0x61, 0x5D, 0x6C,
				0x77, 0x09, 0x88, 0xC0, 0xBA, 0xD9, 0x46, 0xE2,
				0x08, 0xE2, 0x4F, 0xA0, 0x74, 0xE5, 0xAB, 0x31,
				0x43, 0xDB, 0x5B, 0xFC, 0xE0, 0xFD, 0x10, 0x8E,
				0x4B, 0x82, 0xD1, 0x20, 0xA9, 0x21, 0x08, 0x01,
				0x1A, 0x72, 0x3C, 0x12, 0xA7, 0x87, 0xE6, 0xD7,
				0x88, 0x71, 0x9A, 0x10, 0xBD, 0xBA, 0x5B, 0x26,
				0x99, 0xC3, 0x27, 0x18, 0x6A, 0xF4, 0xE2, 0x3C,
				0x1A, 0x94, 0x68, 0x34, 0xB6, 0x15, 0x0B, 0xDA,
				0x25, 0x83, 0xE9, 0xCA, 0x2A, 0xD4, 0x4C, 0xE8,
				0xDB, 0xBB, 0xC2, 0xDB, 0x04, 0xDE, 0x8E, 0xF9,
				0x2E, 0x8E, 0xFC, 0x14, 0x1F, 0xBE, 0xCA, 0xA6,
				0x28, 0x7C, 0x59, 0x47, 0x4E, 0x6B, 0xC0, 0x5D,
				0x99, 0xB2, 0x96, 0x4F, 0xA0, 0x90, 0xC3, 0xA2,
				0x23, 0x3B, 0xA1, 0x86, 0x51, 0x5B, 0xE7, 0xED,
				0x1F, 0x61, 0x29, 0x70, 0xCE, 0xE2, 0xD7, 0xAF,
				0xB8, 0x1B, 0xDD, 0x76, 0x21, 0x70, 0x48, 0x1C,
				0xD0, 0x06, 0x91, 0x27, 0xD5, 0xB0, 0x5A, 0xA9,
				0x93, 0xB4, 0xEA, 0x98, 0x8D, 0x8F, 0xDD, 0xC1,
				0x86, 0xFF, 0xB7, 0xDC, 0x90, 0xA6, 0xC0, 0x8F,
				0x4D, 0xF4, 0x35, 0xC9, 0x34, 0x02, 0x84, 0x92,
				0x36, 0xC3, 0xFA, 0xB4, 0xD2, 0x7C, 0x70, 0x26,
				0xC1, 0xD4, 0xDC, 0xB2, 0x60, 0x26, 0x46, 0xDE,
				0xC9, 0x75, 0x1E, 0x76, 0x3D, 0xBA, 0x37, 0xBD,
				0xF8, 0xFF, 0x94, 0x06, 0xAD, 0x9E, 0x53, 0x0E,
				0xE5, 0xDB, 0x38, 0x2F, 0x41, 0x30, 0x01, 0xAE,
				0xB0, 0x6A, 0x53, 0xED, 0x90, 0x27, 0xD8, 0x31,
				0x17, 0x97, 0x27, 0xB0, 0x86, 0x5A, 0x89, 0x18,
				0xDA, 0x3E, 0xDB, 0xEB, 0xCF, 0x9B, 0x14, 0xED,
				0x44, 0xCE, 0x6C, 0xBA, 0xCE, 0xD4, 0xBB, 0x1B,
				0xDB, 0x7F, 0x14, 0x47, 0xE6, 0xCC, 0x25, 0x4B,
				0x33, 0x20, 0x51, 0x51, 0x2B, 0xD7, 0xAF, 0x42,
				0x6F, 0xB8, 0xF4, 0x01, 0x37, 0x8C, 0xD2, 0xBF,
				0x59, 0x83, 0xCA, 0x01, 0xC6, 0x4B, 0x92, 0xEC,
				0xF0, 0x32, 0xEA, 0x15, 0xD1, 0x72, 0x1D, 0x03,
				0xF4, 0x82, 0xD7, 0xCE, 0x6E, 0x74, 0xFE, 0xF6,
				0xD5, 0x5E, 0x70, 0x2F, 0x46, 0x98, 0x0C, 0x82,
				0xB5, 0xA8, 0x40, 0x31, 0x90, 0x0B, 0x1C, 0x9E,
				0x59, 0xE7, 0xC9, 0x7F, 0xBE, 0xC7, 0xE8, 0xF3,
				0x23, 0xA9, 0x7A, 0x7E, 0x36, 0xCC, 0x88, 0xBE,
				0x0F, 0x1D, 0x45, 0xB7, 0xFF, 0x58, 0x5A, 0xC5,
				0x4B, 0xD4, 0x07, 0xB2, 0x2B, 0x41, 0x54, 0xAA,
				0xCC, 0x8F, 0x6D, 0x7E, 0xBF, 0x48, 0xE1, 0xD8,
				0x14, 0xCC, 0x5E, 0xD2, 0x0F, 0x80, 0x37, 0xE0,
				0xA7, 0x97, 0x15, 0xEE, 0xF2, 0x9B, 0xE3, 0x28,
				0x06, 0xA1, 0xD5, 0x8B, 0xB7, 0xC5, 0xDA, 0x76,
				0xF5, 0x50, 0xAA, 0x3D, 0x8A, 0x1F, 0xBF, 0xF0,
				0xEB, 0x19, 0xCC, 0xB1, 0xA3, 0x13, 0xD5, 0x5C,
				0xDA, 0x56, 0xC9, 0xEC, 0x2E, 0xF2, 0x96, 0x32,
				0x38, 0x7F, 0xE8, 0xD7, 0x6E, 0x3C, 0x04, 0x68,
				0x04, 0x3E, 0x8F, 0x66, 0x3F, 0x48, 0x60, 0xEE,
				0x12, 0xBF, 0x2D, 0x5B, 0x0B, 0x74, 0x74, 0xD6,
				0xE6, 0x94, 0xF9, 0x1E, 0x6D, 0xBE, 0x11, 0x59,
				0x74, 0xA3, 0x92, 0x6F, 0x12, 0xFE, 0xE5, 0xE4,
				0x38, 0x77, 0x7C, 0xB6, 0xA9, 0x32, 0xDF, 0x8C,
				0xD8, 0xBE, 0xC4, 0xD0, 0x73, 0xB9, 0x31, 0xBA,
				0x3B, 0xC8, 0x32, 0xB6, 0x8D, 0x9D, 0xD3, 0x00,
				0x74, 0x1F, 0xA7, 0xBF, 0x8A, 0xFC, 0x47, 0xED,
				0x25, 0x76, 0xF6, 0x93, 0x6B, 0xA4, 0x24, 0x66,
				0x3A, 0xAB, 0x63, 0x9C, 0x5A, 0xE4, 0xF5, 0x68,
				0x34, 0x23, 0xB4, 0x74, 0x2B, 0xF1, 0xC9, 0x78,
				0x23, 0x8F, 0x16, 0xCB, 0xE3, 0x9D, 0x65, 0x2D,
				0xE3, 0xFD, 0xB8, 0xBE, 0xFC, 0x84, 0x8A, 0xD9,
				0x22, 0x22, 0x2E, 0x04, 0xA4, 0x03, 0x7C, 0x07,
				0x13, 0xEB, 0x57, 0xA8, 0x1A, 0x23, 0xF0, 0xC7,
				0x34, 0x73, 0xFC, 0x64, 0x6C, 0xEA, 0x30, 0x6B,
				0x4B, 0xCB, 0xC8, 0x86, 0x2F, 0x83, 0x85, 0xDD,
				0xFA, 0x9D, 0x4B, 0x7F, 0xA2, 0xC0, 0x87, 0xE8,
				0x79, 0x68, 0x33, 0x03, 0xED, 0x5B, 0xDD, 0x3A,
				0x06, 0x2B, 0x3C, 0xF5, 0xB3, 0xA2, 0x78, 0xA6,
				0x6D, 0x2A, 0x13, 0xF8, 0x3F, 0x44, 0xF8, 0x2D,
				0xDF, 0x31, 0x0E, 0xE0, 0x74, 0xAB, 0x6A, 0x36,
				0x45, 0x97, 0xE8, 0x99, 0xA0, 0x25, 0x5D, 0xC1,
				0x64, 0xF3, 0x1C, 0xC5, 0x08, 0x46, 0x85, 0x1D,
				0xF9, 0xAB, 0x48, 0x19, 0x5D, 0xED, 0x7E, 0xA1,
				0xB1, 0xD5, 0x10, 0xBD, 0x7E, 0xE7, 0x4D, 0x73,
				0xFA, 0xF3, 0x6B, 0xC3, 0x1E, 0xCF, 0xA2, 0x68,
				0x35, 0x90, 0x46, 0xF4, 0xEB, 0x87, 0x9F, 0x92,
				0x40, 0x09, 0x43, 0x8B, 0x48, 0x1C, 0x6C, 0xD7,
				0x88, 0x9A, 0x00, 0x2E, 0xD5, 0xEE, 0x38, 0x2B,
				0xC9, 0x19, 0x0D, 0xA6, 0xFC, 0x02, 0x6E, 0x47,
				0x95, 0x58, 0xE4, 0x47, 0x56, 0x77, 0xE9, 0xAA,
				0x9E, 0x30, 0x50, 0xE2, 0x76, 0x56, 0x94, 0xDF,
				0xC8, 0x1F, 0x56, 0xE8, 0x80, 0xB9, 0x6E, 0x71,
				0x60, 0xC9, 0x80, 0xDD, 0x98, 0xED, 0xD3, 0xDF,
				0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
			},
			.len = 1024
		},
		.result = {
			.len = 1024
		}
	}
};

static uint8_t secp256r1_pkey[] = {
	0x51, 0x9b, 0x42, 0x3d, 0x71, 0x5f, 0x8b, 0x58,
	0x1f, 0x4f, 0xa8, 0xee, 0x59, 0xf4, 0x77, 0x1a,
	0x5b, 0x44, 0xc8, 0x13, 0x0b, 0x4e, 0x3e, 0xac,
	0xca, 0x54, 0xa5, 0x6d, 0xda, 0x72, 0xb4, 0x64
};

static uint8_t secp256r1_qx[] = {
	0x1c, 0xcb, 0xe9, 0x1c, 0x07, 0x5f, 0xc7, 0xf4,
	0xf0, 0x33, 0xbf, 0xa2, 0x48, 0xdb, 0x8f, 0xcc,
	0xd3, 0x56, 0x5d, 0xe9, 0x4b, 0xbf, 0xb1, 0x2f,
	0x3c, 0x59, 0xff, 0x46, 0xc2, 0x71, 0xbf, 0x83
};

static uint8_t secp256r1_qy[] = {
	0xce, 0x40, 0x14, 0xc6, 0x88, 0x11, 0xf9, 0xa2,
	0x1a, 0x1f, 0xdb, 0x2c, 0x0e, 0x61, 0x13, 0xe0,
	0x6d, 0xb7, 0xca, 0x93, 0xb7, 0x40, 0x4e, 0x78,
	0xdc, 0x7c, 0xcd, 0x5c, 0xa8, 0x9a, 0x4c, 0xa9
};

static uint8_t secp256r1_k[] = {
	0x94, 0xa1, 0xbb, 0xb1, 0x4b, 0x90, 0x6a, 0x61,
	0xa2, 0x80, 0xf2, 0x45, 0xf9, 0xe9, 0x3c, 0x7f,
	0x3b, 0x4a, 0x62, 0x47, 0x82, 0x4f, 0x5d, 0x33,
	0xb9, 0x67, 0x07, 0x87, 0x64, 0x2a, 0x68, 0xde
};

static uint8_t secp256r1_sign_r[] = {
	0xf3, 0xac, 0x80, 0x61, 0xb5, 0x14, 0x79, 0x5b,
	0x88, 0x43, 0xe3, 0xd6, 0x62, 0x95, 0x27, 0xed,
	0x2a, 0xfd, 0x6b, 0x1f, 0x6a, 0x55, 0x5a, 0x7a,
	0xca, 0xbb, 0x5e, 0x6f, 0x79, 0xc8, 0xc2, 0xac
};

static uint8_t secp256r1_sign_s[] = {
	0x8b, 0xf7, 0x78, 0x19, 0xca, 0x05, 0xa6, 0xb2,
	0x78, 0x6c, 0x76, 0x26, 0x2b, 0xf7, 0x37, 0x1c,
	0xef, 0x97, 0xb2, 0x18, 0xe9, 0x6f, 0x17, 0x5a,
	0x3c, 0xcd, 0xda, 0x2a, 0xcc, 0x05, 0x89, 0x03
};

static uint8_t secp256r1_message[] = {
	0x44, 0xac, 0xf6, 0xb7, 0xe3, 0x6c, 0x13, 0x42,
	0xc2, 0xc5, 0x89, 0x72, 0x04, 0xfe, 0x09, 0x50,
	0x4e, 0x1e, 0x2e, 0xfb, 0x1a, 0x90, 0x03, 0x77,
	0xdb, 0xc4, 0xe7, 0xa6, 0xa1, 0x33, 0xec, 0x56
};

static uint8_t ed25519_pkey[] = {
	0x4c, 0xcd, 0x08, 0x9b, 0x28, 0xff, 0x96, 0xda,
	0x9d, 0xb6, 0xc3, 0x46, 0xec, 0x11, 0x4e, 0x0f,
	0x5b, 0x8a, 0x31, 0x9f, 0x35, 0xab, 0xa6, 0x24,
	0xda, 0x8c, 0xf6, 0xed, 0x4f, 0xb8, 0xa6, 0xfb,
};

static uint8_t ed25519_pubkey[] = {
	0x3d, 0x40, 0x17, 0xc3, 0xe8, 0x43, 0x89, 0x5a,
	0x92, 0xb7, 0x0a, 0xa7, 0x4d, 0x1b, 0x7e, 0xbc,
	0x9c, 0x98, 0x2c, 0xcf, 0x2e, 0xc4, 0x96, 0x8c,
	0xc0, 0xcd, 0x55, 0xf1, 0x2a, 0xf4, 0x66, 0x0c,
};

static uint8_t ed25519_sign[] = {
	0x92, 0xa0, 0x09, 0xa9, 0xf0, 0xd4, 0xca, 0xb8,
	0x72, 0x0e, 0x82, 0x0b, 0x5f, 0x64, 0x25, 0x40,
	0xa2, 0xb2, 0x7b, 0x54, 0x16, 0x50, 0x3f, 0x8f,
	0xb3, 0x76, 0x22, 0x23, 0xeb, 0xdb, 0x69, 0xda,
	0x08, 0x5a, 0xc1, 0xe4, 0x3e, 0x15, 0x99, 0x6e,
	0x45, 0x8f, 0x36, 0x13, 0xd0, 0xf1, 0x1d, 0x8c,
	0x38, 0x7b, 0x2e, 0xae, 0xb4, 0x30, 0x2a, 0xee,
	0xb0, 0x0d, 0x29, 0x16, 0x12, 0xbb, 0x0c, 0x00,
};

static uint8_t ed25519_message[] = {
	0x72
};

static uint8_t fp256_pkey[] = {
	0x77, 0x84, 0x35, 0x65, 0x4c, 0x7a, 0x6d, 0xb1,
	0x1e, 0x63, 0x0b, 0x41, 0x97, 0x36, 0x04, 0xf4,
	0xec, 0x35, 0xee, 0x3b, 0x76, 0xc2, 0x34, 0x08,
	0xd9, 0x4a, 0x22, 0x0d, 0x7f, 0xf6, 0xc6, 0x90
};

static uint8_t fp256_qx[] = {
	0x7b, 0x24, 0xa3, 0x03, 0xcf, 0xb2, 0x22, 0xfa,
	0x4c, 0xb3, 0x88, 0x54, 0xf9, 0x30, 0xd1, 0x4d,
	0xe3, 0x50, 0xda, 0xba, 0xe6, 0xa7, 0x0b, 0x91,
	0x4c, 0x04, 0x0d, 0x5c, 0xe0, 0x8e, 0x86, 0xc5
};

static uint8_t fp256_qy[] = {
	0xbc, 0x39, 0xe3, 0x19, 0x4e, 0xd2, 0x29, 0x22,
	0x5b, 0x37, 0x2d, 0xeb, 0xcc, 0x05, 0x52, 0x8d,
	0xb9, 0x40, 0xa3, 0xab, 0x3c, 0xbe, 0x16, 0x30,
	0x1c, 0xe4, 0xe8, 0x7f, 0xba, 0x6e, 0x0b, 0xae
};

static uint8_t fp256_k[] = {
	0x01, 0x04, 0x02, 0x05, 0x04, 0x06, 0x03, 0x07
};

static uint8_t fp256_sign_r[] = {
	0x75, 0x2B, 0x8C, 0x15, 0x38, 0x10, 0xF6, 0xC0,
	0x28, 0xC9, 0x8A, 0x51, 0xD0, 0x62, 0x69, 0x4B,
	0xF6, 0x58, 0x06, 0xEB, 0xF1, 0x91, 0x1F, 0x15,
	0x8B, 0x08, 0x09, 0xF9, 0x88, 0x0A, 0x44, 0x24
};

static uint8_t fp256_sign_s[] = {
	0x5A, 0x3C, 0x96, 0x3E, 0x1C, 0xB4, 0x19, 0xF9,
	0xD7, 0x78, 0xB8, 0xCE, 0xFF, 0x9D, 0xB1, 0x31,
	0x77, 0xDB, 0xA0, 0xFE, 0x84, 0x61, 0x1A, 0xD9,
	0x4E, 0xFF, 0x82, 0x13, 0x1C, 0xCA, 0x04, 0x75,
};

static uint8_t fp256_id[] = {
	0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8
};

static uint8_t fp256_message[] = {
	0x6D, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x20,
	0x64, 0x69, 0x67, 0x65, 0x73, 0x74
};

static uint8_t fp256_digest[] = {
	0x0F, 0xB5, 0xCE, 0xF3, 0x3C, 0xB7, 0xD1, 0x35,
	0xA9, 0x3A, 0xC7, 0xA7, 0x89, 0x2A, 0x6D, 0x9A,
	0xF3, 0x1E, 0xC5, 0x38, 0xD3, 0x65, 0x1B, 0xB9,
	0xDF, 0x5F, 0x7F, 0x4A, 0xD8, 0x89, 0x57, 0xF1
};

static uint8_t fp256_cipher[] = {
	0x30, 0x78, 0x02, 0x21, 0x00, 0xAB, 0xBD, 0xE8,
	0xE8, 0x80, 0x93, 0x36, 0x77, 0xB6, 0x44, 0x47,
	0x6D, 0x00, 0xF6, 0x51, 0xC8, 0x80, 0x9C, 0x9E,
	0xD9, 0xEC, 0x36, 0x8A, 0x60, 0x8E, 0x26, 0x2D,
	0x71, 0x31, 0xB7, 0xC1, 0x38, 0x02, 0x21, 0x00,
	0xE1, 0xBF, 0x4C, 0x13, 0x7A, 0x87, 0x40, 0x32,
	0xF5, 0xA1, 0xE2, 0xA1, 0x3B, 0x83, 0xBF, 0x6B,
	0x3F, 0xFB, 0xC8, 0x13, 0x01, 0xDE, 0xCF, 0xC0,
	0xF4, 0x24, 0x66, 0x52, 0x89, 0xDA, 0x6D, 0x7A,
	0x04, 0x20, 0x8E, 0xFD, 0x52, 0x77, 0xC9, 0xE7,
	0x90, 0xD1, 0x17, 0x75, 0xDE, 0xEE, 0xF3, 0xE5,
	0x11, 0x0C, 0x5D, 0xE1, 0x3A, 0xB6, 0x2B, 0x72,
	0x60, 0xE5, 0xD5, 0xF3, 0x0F, 0xE2, 0x44, 0xDB,
	0xBC, 0x66, 0x04, 0x0E, 0x78, 0x2D, 0xC0, 0x3D,
	0x38, 0xA2, 0x42, 0xA4, 0x8E, 0x8B, 0xF5, 0x06,
	0x32, 0xFA
};

uint8_t plaintext[2048] = {
	0x71, 0x75, 0x83, 0x98, 0x75, 0x42, 0x51, 0x09, 0x94, 0x02, 0x13, 0x20,
	0x15, 0x64, 0x46, 0x32, 0x08, 0x18, 0x91, 0x82, 0x86, 0x52, 0x23, 0x93,
	0x44, 0x54, 0x28, 0x68, 0x78, 0x78, 0x70, 0x06, 0x42, 0x74, 0x41, 0x27,
	0x73, 0x38, 0x53, 0x77, 0x51, 0x96, 0x53, 0x24, 0x03, 0x88, 0x74, 0x14,
	0x70, 0x23, 0x88, 0x30, 0x85, 0x18, 0x89, 0x27, 0x41, 0x71, 0x61, 0x23,
	0x04, 0x83, 0x30, 0x57, 0x26, 0x47, 0x23, 0x75, 0x25, 0x62, 0x53, 0x80,
	0x38, 0x34, 0x21, 0x33, 0x34, 0x51, 0x46, 0x29, 0x94, 0x64, 0x22, 0x67,
	0x25, 0x45, 0x70, 0x26, 0x74, 0x39, 0x46, 0x71, 0x08, 0x85, 0x27, 0x18,
	0x93, 0x39, 0x72, 0x11, 0x57, 0x26, 0x88, 0x46, 0x47, 0x49, 0x86, 0x92,
	0x03, 0x37, 0x96, 0x40, 0x84, 0x53, 0x67, 0x47, 0x60, 0x60, 0x37, 0x67,
	0x02, 0x68, 0x76, 0x62, 0x42, 0x01, 0x59, 0x11, 0x01, 0x89, 0x40, 0x87,
	0x58, 0x20, 0x51, 0x21, 0x66, 0x26, 0x26, 0x73, 0x03, 0x06, 0x14, 0x25,
	0x98, 0x42, 0x44, 0x67, 0x24, 0x78, 0x71, 0x45, 0x32, 0x61, 0x20, 0x26,
	0x08, 0x88, 0x44, 0x26, 0x40, 0x63, 0x76, 0x23, 0x78, 0x55, 0x81, 0x97,
	0x95, 0x89, 0x39, 0x07, 0x14, 0x50, 0x50, 0x73, 0x07, 0x20, 0x86, 0x83,
	0x74, 0x57, 0x72, 0x36, 0x68, 0x61, 0x14, 0x41, 0x56, 0x49, 0x64, 0x72,
	0x75, 0x81, 0x47, 0x91, 0x08, 0x76, 0x47, 0x06, 0x55, 0x77, 0x61, 0x45,
	0x50, 0x10, 0x07, 0x46, 0x46, 0x89, 0x80, 0x07, 0x24, 0x95, 0x39, 0x43,
	0x03, 0x75, 0x24, 0x35, 0x57, 0x82, 0x09, 0x64, 0x29, 0x24, 0x26, 0x66,
	0x67, 0x29, 0x05, 0x90, 0x82, 0x02, 0x45, 0x71, 0x21, 0x34, 0x25, 0x48,
	0x68, 0x26, 0x01, 0x18, 0x73, 0x18, 0x46, 0x15, 0x14, 0x33, 0x28, 0x44,
	0x24, 0x82, 0x20, 0x12, 0x99, 0x43, 0x68, 0x43, 0x25, 0x14, 0x34, 0x33,
	0x31, 0x13, 0x77, 0x44, 0x95, 0x22, 0x99, 0x02, 0x30, 0x50, 0x74, 0x43,
	0x81, 0x78, 0x32, 0x17, 0x09, 0x85, 0x04, 0x37, 0x31, 0x98, 0x76, 0x79,
	0x64, 0x10, 0x39, 0x89, 0x59, 0x90, 0x50, 0x15, 0x77, 0x39, 0x28, 0x14,
	0x30, 0x19, 0x68, 0x77, 0x89, 0x48, 0x86, 0x16, 0x11, 0x33, 0x84, 0x56,
	0x10, 0x20, 0x94, 0x72, 0x41, 0x69, 0x13, 0x00, 0x56, 0x27, 0x01, 0x57,
	0x46, 0x65, 0x65, 0x19, 0x33, 0x07, 0x62, 0x19, 0x91, 0x60, 0x29, 0x11,
	0x41, 0x25, 0x88, 0x21, 0x93, 0x85, 0x87, 0x40, 0x91, 0x25, 0x32, 0x86,
	0x76, 0x54, 0x92, 0x52, 0x72, 0x46, 0x61, 0x84, 0x20, 0x14, 0x65, 0x83,
	0x69, 0x90, 0x80, 0x11, 0x35, 0x70, 0x42, 0x64, 0x74, 0x85, 0x15, 0x23,
	0x06, 0x55, 0x67, 0x49, 0x76, 0x47, 0x11, 0x95, 0x00, 0x85, 0x05, 0x12,
	0x58, 0x53, 0x25, 0x73, 0x62, 0x81, 0x63, 0x82, 0x32, 0x75, 0x16, 0x48,
	0x04, 0x96, 0x75, 0x16, 0x43, 0x83, 0x41, 0x85, 0x95, 0x67, 0x27, 0x83,
	0x22, 0x43, 0x02, 0x27, 0x69, 0x62, 0x78, 0x50, 0x57, 0x66, 0x99, 0x89,
	0x05, 0x06, 0x35, 0x86, 0x37, 0x27, 0x48, 0x46, 0x50, 0x80, 0x96, 0x40,
	0x42, 0x36, 0x21, 0x54, 0x49, 0x18, 0x63, 0x38, 0x45, 0x76, 0x23, 0x20,
	0x28, 0x06, 0x17, 0x32, 0x58, 0x50, 0x49, 0x54, 0x29, 0x46, 0x18, 0x12,
	0x17, 0x50, 0x02, 0x80, 0x99, 0x53, 0x15, 0x02, 0x07, 0x14, 0x19, 0x60,
	0x56, 0x43, 0x76, 0x71, 0x49, 0x99, 0x54, 0x83, 0x28, 0x94, 0x30, 0x30,
	0x57, 0x05, 0x89, 0x80, 0x11, 0x03, 0x78, 0x35, 0x73, 0x52, 0x67, 0x39,
	0x67, 0x07, 0x04, 0x49, 0x23, 0x83, 0x86, 0x89, 0x57, 0x71, 0x08, 0x41,
	0x15, 0x97, 0x19, 0x72, 0x03, 0x27, 0x72, 0x52, 0x66, 0x67, 0x99, 0x15,
	0x33, 0x64, 0x69, 0x78, 0x07, 0x83, 0x53, 0x71, 0x21, 0x50, 0x05, 0x48,
	0x59, 0x85, 0x01, 0x36, 0x65, 0x02, 0x52, 0x01, 0x09, 0x49, 0x28, 0x77,
	0x25, 0x35, 0x67, 0x77, 0x81, 0x64, 0x24, 0x29, 0x42, 0x32, 0x59, 0x22,
	0x93, 0x48, 0x59, 0x03, 0x85, 0x87, 0x15, 0x55, 0x23, 0x42, 0x58, 0x17,
	0x18, 0x37, 0x70, 0x83, 0x80, 0x12, 0x44, 0x83, 0x45, 0x70, 0x55, 0x86,
	0x03, 0x23, 0x01, 0x56, 0x94, 0x12, 0x41, 0x34, 0x82, 0x90, 0x83, 0x46,
	0x17, 0x56, 0x66, 0x96, 0x75, 0x80, 0x59, 0x07, 0x15, 0x84, 0x19, 0x52,
	0x37, 0x44, 0x44, 0x83, 0x72, 0x43, 0x25, 0x42, 0x26, 0x86, 0x87, 0x86,
	0x91, 0x62, 0x14, 0x90, 0x34, 0x26, 0x14, 0x33, 0x59, 0x70, 0x73, 0x15,
	0x49, 0x40, 0x66, 0x88, 0x42, 0x66, 0x16, 0x42, 0x55, 0x92, 0x82, 0x06,
	0x20, 0x96, 0x36, 0x96, 0x13, 0x07, 0x84, 0x94, 0x37, 0x66, 0x62, 0x78,
	0x60, 0x58, 0x80, 0x50, 0x69, 0x03, 0x97, 0x16, 0x64, 0x45, 0x21, 0x39,
	0x79, 0x28, 0x52, 0x17, 0x14, 0x77, 0x31, 0x60, 0x86, 0x70, 0x09, 0x53,
	0x39, 0x32, 0x52, 0x31, 0x35, 0x79, 0x24, 0x70, 0x25, 0x48, 0x23, 0x49,
	0x10, 0x64, 0x54, 0x30, 0x82, 0x34, 0x51, 0x20, 0x46, 0x04, 0x29, 0x25,
	0x65, 0x09, 0x55, 0x30, 0x30, 0x52, 0x85, 0x32, 0x79, 0x19, 0x59, 0x07,
	0x05, 0x12, 0x11, 0x03, 0x21, 0x90, 0x36, 0x62, 0x23, 0x67, 0x36, 0x67,
	0x47, 0x39, 0x92, 0x88, 0x45, 0x43, 0x71, 0x16, 0x48, 0x27, 0x68, 0x39,
	0x98, 0x38, 0x03, 0x31, 0x85, 0x10, 0x06, 0x95, 0x54, 0x79, 0x28, 0x79,
	0x56, 0x16, 0x65, 0x69, 0x00, 0x54, 0x09, 0x91, 0x06, 0x10, 0x10, 0x86,
	0x75, 0x01, 0x02, 0x71, 0x01, 0x09, 0x32, 0x94, 0x66, 0x43, 0x68, 0x36,
	0x19, 0x52, 0x02, 0x04, 0x45, 0x49, 0x40, 0x94, 0x07, 0x87, 0x86, 0x79,
	0x84, 0x07, 0x75, 0x30, 0x73, 0x02, 0x57, 0x81, 0x65, 0x02, 0x28, 0x96,
	0x57, 0x07, 0x70, 0x34, 0x39, 0x35, 0x75, 0x19, 0x47, 0x57, 0x08, 0x75,
	0x86, 0x57, 0x11, 0x32, 0x09, 0x47, 0x83, 0x93, 0x20, 0x94, 0x90, 0x88,
	0x39, 0x63, 0x22, 0x88, 0x54, 0x54, 0x95, 0x75, 0x67, 0x26, 0x02, 0x49,
	0x26, 0x17, 0x35, 0x16, 0x27, 0x65, 0x64, 0x26, 0x93, 0x92, 0x77, 0x85,
	0x84, 0x40, 0x59, 0x29, 0x49, 0x69, 0x94, 0x71, 0x72, 0x21, 0x55, 0x03,
	0x19, 0x74, 0x09, 0x40, 0x57, 0x68, 0x41, 0x19, 0x11, 0x21, 0x63, 0x56,
	0x29, 0x77, 0x57, 0x81, 0x44, 0x40, 0x76, 0x77, 0x02, 0x71, 0x66, 0x35,
	0x89, 0x02, 0x64, 0x51, 0x61, 0x02, 0x46, 0x91, 0x38, 0x93, 0x62, 0x57,
	0x18, 0x98, 0x12, 0x87, 0x29, 0x48, 0x65, 0x39, 0x99, 0x45, 0x54, 0x69,
	0x51, 0x16, 0x25, 0x75, 0x60, 0x70, 0x33, 0x72, 0x01, 0x60, 0x26, 0x51,
	0x44, 0x14, 0x39, 0x12, 0x95, 0x48, 0x87, 0x33, 0x90, 0x16, 0x42, 0x78,
	0x48, 0x58, 0x96, 0x93, 0x75, 0x23, 0x07, 0x13, 0x86, 0x07, 0x96, 0x30,
	0x22, 0x82, 0x91, 0x36, 0x72, 0x16, 0x48, 0x77, 0x64, 0x99, 0x07, 0x34,
	0x78, 0x60, 0x61, 0x13, 0x48, 0x93, 0x46, 0x62, 0x48, 0x38, 0x37, 0x96,
	0x58, 0x64, 0x39, 0x90, 0x69, 0x46, 0x81, 0x98, 0x61, 0x89, 0x15, 0x59,
	0x78, 0x98, 0x21, 0x34, 0x00, 0x69, 0x97, 0x80, 0x28, 0x81, 0x53, 0x49,
	0x79, 0x53, 0x92, 0x20, 0x29, 0x40, 0x70, 0x06, 0x09, 0x55, 0x99, 0x41,
	0x51, 0x35, 0x55, 0x27, 0x39, 0x06, 0x29, 0x83, 0x66, 0x03, 0x68, 0x14,
	0x11, 0x69, 0x95, 0x51, 0x71, 0x55, 0x24, 0x60, 0x52, 0x58, 0x88, 0x11,
	0x88, 0x25, 0x37, 0x86, 0x01, 0x52, 0x93, 0x52, 0x02, 0x24, 0x91, 0x58,
	0x56, 0x37, 0x50, 0x88, 0x39, 0x09, 0x61, 0x19, 0x08, 0x86, 0x29, 0x51,
	0x63, 0x38, 0x81, 0x14, 0x75, 0x75, 0x39, 0x99, 0x22, 0x04, 0x32, 0x63,
	0x14, 0x68, 0x41, 0x79, 0x09, 0x57, 0x87, 0x29, 0x26, 0x94, 0x05, 0x71,
	0x82, 0x41, 0x26, 0x98, 0x68, 0x18, 0x55, 0x42, 0x78, 0x05, 0x74, 0x17,
	0x34, 0x34, 0x07, 0x62, 0x94, 0x72, 0x21, 0x08, 0x54, 0x72, 0x21, 0x08,
	0x31, 0x53, 0x82, 0x35, 0x27, 0x40, 0x85, 0x77, 0x08, 0x52, 0x58, 0x48,
	0x03, 0x86, 0x65, 0x51, 0x96, 0x43, 0x89, 0x19, 0x15, 0x08, 0x49, 0x62,
	0x57, 0x46, 0x17, 0x68, 0x56, 0x04, 0x70, 0x63, 0x75, 0x88, 0x13, 0x27,
	0x87, 0x44, 0x46, 0x27, 0x02, 0x97, 0x71, 0x07, 0x40, 0x17, 0x24, 0x61,
	0x16, 0x94, 0x86, 0x85, 0x67, 0x58, 0x87, 0x92, 0x02, 0x84, 0x75, 0x19,
	0x43, 0x60, 0x68, 0x03, 0x54, 0x75, 0x33, 0x17, 0x97, 0x75, 0x12, 0x62,
	0x43, 0x08, 0x35, 0x75, 0x32, 0x21, 0x08, 0x82, 0x78, 0x04, 0x74, 0x09,
	0x13, 0x48, 0x63, 0x68, 0x67, 0x09, 0x08, 0x50, 0x11, 0x71, 0x64, 0x72,
	0x63, 0x76, 0x21, 0x62, 0x80, 0x57, 0x19, 0x15, 0x26, 0x88, 0x02, 0x26,
	0x83, 0x17, 0x61, 0x76, 0x28, 0x10, 0x22, 0x37, 0x56, 0x71, 0x51, 0x60,
	0x12, 0x79, 0x24, 0x83, 0x78, 0x47, 0x78, 0x20, 0x52, 0x27, 0x19, 0x88,
	0x81, 0x04, 0x70, 0x20, 0x25, 0x10, 0x04, 0x01, 0x72, 0x57, 0x30, 0x93,
	0x96, 0x23, 0x02, 0x94, 0x61, 0x44, 0x17, 0x65, 0x77, 0x60, 0x27, 0x43,
	0x24, 0x59, 0x46, 0x76, 0x00, 0x11, 0x31, 0x99, 0x41, 0x48, 0x75, 0x32,
	0x05, 0x15, 0x45, 0x31, 0x57, 0x89, 0x10, 0x47, 0x53, 0x14, 0x66, 0x54,
	0x60, 0x55, 0x36, 0x93, 0x30, 0x03, 0x63, 0x80, 0x65, 0x43, 0x17, 0x36,
	0x18, 0x64, 0x21, 0x38, 0x16, 0x19, 0x19, 0x51, 0x73, 0x80, 0x38, 0x27,
	0x30, 0x89, 0x13, 0x43, 0x54, 0x11, 0x78, 0x05, 0x24, 0x38, 0x83, 0x56,
	0x50, 0x59, 0x12, 0x47, 0x69, 0x70, 0x70, 0x91, 0x28, 0x02, 0x08, 0x91,
	0x66, 0x09, 0x31, 0x65, 0x46, 0x20, 0x04, 0x85, 0x89, 0x53, 0x91, 0x42,
	0x34, 0x09, 0x36, 0x92, 0x42, 0x06, 0x87, 0x88, 0x23, 0x54, 0x87, 0x85,
	0x52, 0x98, 0x95, 0x76, 0x13, 0x50, 0x59, 0x89, 0x18, 0x14, 0x17, 0x47,
	0x10, 0x97, 0x39, 0x14, 0x33, 0x79, 0x83, 0x62, 0x55, 0x18, 0x30, 0x83,
	0x03, 0x45, 0x38, 0x37, 0x35, 0x20, 0x94, 0x84, 0x89, 0x80, 0x89, 0x10,
	0x48, 0x77, 0x33, 0x36, 0x50, 0x07, 0x93, 0x02, 0x45, 0x42, 0x91, 0x12,
	0x98, 0x09, 0x77, 0x20, 0x31, 0x95, 0x10, 0x29, 0x89, 0x02, 0x38, 0x92,
	0x90, 0x19, 0x51, 0x10, 0x19, 0x82, 0x23, 0x68, 0x06, 0x00, 0x67, 0x50,
	0x25, 0x03, 0x41, 0x69, 0x53, 0x42, 0x23, 0x99, 0x29, 0x21, 0x63, 0x22,
	0x72, 0x54, 0x72, 0x40, 0x23, 0x39, 0x74, 0x92, 0x53, 0x28, 0x67, 0x56,
	0x46, 0x84, 0x59, 0x85, 0x10, 0x92, 0x31, 0x20, 0x39, 0x95, 0x65, 0x15,
	0x76, 0x35, 0x37, 0x21, 0x98, 0x41, 0x68, 0x74, 0x94, 0x94, 0x86, 0x90,
	0x35, 0x07, 0x06, 0x38, 0x78, 0x32, 0x00, 0x60, 0x86, 0x12, 0x34, 0x65,
	0x67, 0x35, 0x76, 0x94, 0x78, 0x22, 0x99, 0x42, 0x82, 0x40, 0x05, 0x74,
	0x18, 0x59, 0x03, 0x83, 0x89, 0x05, 0x19, 0x28, 0x88, 0x35, 0x59, 0x10,
	0x12, 0x96, 0x48, 0x67, 0x59, 0x87, 0x26, 0x85, 0x74, 0x64, 0x78, 0x56,
	0x91, 0x81, 0x45, 0x90, 0x21, 0x80, 0x32, 0x19, 0x61, 0x38, 0x61, 0x70,
	0x35, 0x08, 0x93, 0x53, 0x21, 0x95, 0x08, 0x27, 0x90, 0x28, 0x94, 0x27,
	0x35, 0x78, 0x03, 0x57, 0x74, 0x84, 0x73, 0x63, 0x27, 0x98, 0x14, 0x21,
	0x22, 0x36, 0x75, 0x31, 0x81, 0x65, 0x85, 0x51, 0x02, 0x45, 0x18, 0x06,
	0x39, 0x13, 0x29, 0x29, 0x73, 0x26, 0x99, 0x51, 0x38, 0x43, 0x35, 0x58,
	0x70, 0x92, 0x32, 0x13, 0x80, 0x16, 0x26, 0x44, 0x22, 0x28, 0x05, 0x45,
	0x86, 0x90, 0x38, 0x19, 0x40, 0x06, 0x30, 0x56, 0x94, 0x09, 0x02, 0x02,
	0x96, 0x29, 0x22, 0x44, 0x87, 0x38, 0x09, 0x95, 0x58, 0x46, 0x42, 0x78,
	0x72, 0x77, 0x86, 0x31, 0x97, 0x19, 0x86, 0x51, 0x73, 0x76, 0x63, 0x98,
	0x39, 0x40, 0x20, 0x20, 0x67, 0x42, 0x55, 0x50, 0x63, 0x76, 0x81, 0x87,
	0x13, 0x81, 0x19, 0x54, 0x11, 0x77, 0x90, 0x26, 0x47, 0x25, 0x92, 0x88,
	0x18, 0x56, 0x23, 0x73, 0x91, 0x52, 0x39, 0x08, 0x59, 0x51, 0x81, 0x57,
	0x78, 0x17, 0x13, 0x90, 0x90, 0x50, 0x65, 0x59, 0x99, 0x77, 0x42, 0x28,
	0x21, 0x59, 0x97, 0x64, 0x25, 0x17, 0x92, 0x24, 0x50, 0x00, 0x28, 0x40,
	0x85, 0x33, 0x78, 0x86, 0x79, 0x40, 0x28, 0x30, 0x14, 0x12, 0x01, 0x72,
	0x41, 0x43, 0x06, 0x87, 0x67, 0x31, 0x66, 0x77, 0x07, 0x50, 0x55, 0x50,
	0x22, 0x80, 0x42, 0x06, 0x38, 0x01, 0x63, 0x66, 0x70, 0x12, 0x52, 0x91,
	0x90, 0x97, 0x21, 0x28, 0x22, 0x65, 0x02, 0x80, 0x72, 0x31, 0x17, 0x76,
	0x35, 0x16, 0x03, 0x56, 0x59, 0x93, 0x36, 0x37, 0x67, 0x54, 0x46, 0x87,
	0x29, 0x01, 0x30, 0x80, 0x47, 0x47, 0x31, 0x98, 0x34, 0x30, 0x23, 0x86,
	0x86, 0x14, 0x05, 0x75, 0x09, 0x88, 0x77, 0x92, 0x59, 0x43, 0x98, 0x72,
	0x55, 0x54, 0x25, 0x59, 0x22, 0x27, 0x21, 0x62, 0x97, 0x10, 0x61, 0x73,
	0x86, 0x95, 0x99, 0x10, 0x62, 0x35, 0x25, 0x16, 0x62, 0x60, 0x51, 0x48,
	0x69, 0x69, 0x92, 0x27, 0x19, 0x43, 0x40, 0x52, 0x70, 0x23, 0x37, 0x28,
	0x73, 0x10, 0x32, 0x55, 0x85, 0x46, 0x97, 0x59, 0x88, 0x48, 0x54, 0x06,
	0x58, 0x04, 0x82, 0x98, 0x88, 0x34, 0x05, 0x41, 0x94, 0x44, 0x35, 0x10,
	0x96, 0x48, 0x21, 0x17, 0x24, 0x40, 0x26, 0x15, 0x49, 0x28, 0x12, 0x17,
	0x10, 0x17, 0x91, 0x42, 0x84, 0x15, 0x83, 0x36, 0x29, 0x49, 0x92, 0x77,
	0x74, 0x11, 0x72, 0x97, 0x64, 0x53, 0x23, 0x29, 0x16, 0x35, 0x22, 0x10,
	0x87, 0x07, 0x44, 0x78, 0x18, 0x19, 0x79, 0x03, 0x58, 0x24, 0x15, 0x63,
	0x55, 0x75, 0x56, 0x14, 0x63, 0x65, 0x86, 0x61, 0x92, 0x94, 0x30, 0x92,
	0x69, 0x78, 0x40, 0x95, 0x19, 0x81, 0x41, 0x66, 0x97, 0x00, 0x17, 0x37,
	0x20, 0x82, 0x14, 0x26, 0x42, 0x63, 0x84, 0x20, 0x96, 0x11, 0x68, 0x37,
	0x60, 0x28, 0x69, 0x85, 0x45, 0x04, 0x62, 0x20, 0x49, 0x39, 0x74, 0x84,
	0x60, 0x23, 0x38, 0x33, 0x42, 0x49, 0x38, 0x82, 0x30, 0x63, 0x21, 0x51,
	0x69, 0x09, 0x05, 0x55, 0x78, 0x90, 0x68, 0x69, 0x22, 0x20, 0x17, 0x26,
	0x54, 0x01, 0x10, 0x04, 0x68, 0x19, 0x88, 0x40, 0x91, 0x74, 0x81, 0x29,
	0x07, 0x45, 0x33, 0x77, 0x12, 0x47, 0x08, 0x60, 0x09, 0x42, 0x84, 0x15,
	0x63, 0x92, 0x64, 0x77, 0x07, 0x44, 0x11, 0x07, 0x79, 0x81, 0x24, 0x05,
	0x21, 0x60, 0x81, 0x70, 0x66, 0x36, 0x69, 0x68, 0x45, 0x01, 0x11, 0x95,
	0x67, 0x95, 0x55, 0x07, 0x96, 0x63, 0x84, 0x04, 0x74, 0x72, 0x61, 0x91,
	0x60, 0x09, 0x90, 0x14, 0x34, 0x94, 0x06, 0x12, 0x01, 0x94, 0x40, 0x14,
	0x12, 0x53, 0x64, 0x81, 0x75, 0x99, 0x36, 0x99, 0x11, 0x69, 0x95, 0x51,
	0x71, 0x55, 0x24, 0x60, 0x52, 0x58, 0x88, 0x11, 0x88, 0x25, 0x37, 0x86,
	0x66, 0x36, 0x69, 0x68, 0x45, 0x01, 0x11, 0x95
};

/* cipher text */
uint8_t ciphertext[2048] = {
	0xE2, 0x19, 0x24, 0x56, 0x13, 0x59, 0xA6, 0x5D, 0xDF, 0xD0, 0x72, 0xAA,
	0x23, 0xC7, 0x36, 0x3A, 0xBB, 0x3E, 0x8B, 0x64, 0xD5, 0xBF, 0xDE, 0x65,
	0xA2, 0x75, 0xD9, 0x45, 0x6C, 0x3C, 0xD2, 0x6A, 0xC7, 0xD0, 0x9A, 0xD0,
	0x87, 0xB8, 0xE4, 0x94, 0x11, 0x62, 0x5A, 0xC3, 0xC3, 0x01, 0xA3, 0x86,
	0xBC, 0xBC, 0x9C, 0xC0, 0x81, 0x9F, 0xBF, 0x5C, 0x6F, 0x3F, 0x13, 0xF1,
	0xAE, 0xCF, 0x26, 0xB3, 0xBC, 0x49, 0xD6, 0x3B, 0x7A, 0x2E, 0x99, 0x9E,
	0x1B, 0x04, 0x50, 0x6C, 0x48, 0x6B, 0x4E, 0x72, 0xFC, 0xC8, 0xA7, 0x0C,
	0x2C, 0xD9, 0xED, 0xE4, 0x82, 0xC4, 0x81, 0xA6, 0xB4, 0xCC, 0xAD, 0x10,
	0xF3, 0x1C, 0x39, 0x05, 0x41, 0x2D, 0x57, 0x32, 0xE7, 0x16, 0xF8, 0x4D,
	0xF0, 0xDE, 0x40, 0x5B, 0x5F, 0x80, 0xDC, 0xA7, 0xC3, 0x2D, 0x3D, 0x9E,
	0x27, 0xD4, 0xE8, 0x10, 0x8E, 0xEB, 0xA5, 0x68, 0x6F, 0x3D, 0xC0, 0x44,
	0xE7, 0x77, 0x73, 0xB9, 0x92, 0x8E, 0xA2, 0x26, 0x5C, 0x6F, 0x33, 0x4B,
	0x0B, 0xEF, 0x37, 0x55, 0xBE, 0xEC, 0x98, 0x83, 0x1E, 0xDF, 0xB2, 0x9E,
	0x5D, 0x1D, 0x78, 0x14, 0xD7, 0x85, 0x0E, 0xF8, 0x12, 0x30, 0x8E, 0x5D,
	0x08, 0x77, 0x0B, 0x2E, 0x9B, 0xF9, 0xA6, 0x72, 0xD2, 0x41, 0xC1, 0x8E,
	0x6B, 0x5E, 0x11, 0x85, 0x22, 0x6E, 0xE4, 0xA3, 0xEA, 0x4C, 0x91, 0xE1,
	0x7D, 0xD0, 0xEB, 0x9F, 0xD9, 0xD7, 0x05, 0x77, 0xD9, 0xA1, 0xC2, 0xFD,
	0x41, 0x63, 0x51, 0xB4, 0x7A, 0x1F, 0x21, 0xF0, 0xBF, 0x11, 0x4D, 0x9B,
	0x97, 0xAB, 0xB4, 0x94, 0x36, 0x34, 0xC9, 0x2D, 0x8B, 0xE2, 0x61, 0xCF,
	0xAF, 0x69, 0xD5, 0x5C, 0xE9, 0xED, 0xE3, 0xA0, 0x69, 0xD3, 0xE5, 0xAE,
	0x67, 0x6C, 0xC7, 0x11, 0xB1, 0x21, 0x96, 0xD6, 0xDB, 0xA8, 0x1D, 0xC9,
	0x83, 0x0B, 0xE2, 0xC6, 0x6E, 0x94, 0xE9, 0x50, 0x12, 0x9B, 0x01, 0x72,
	0xAA, 0xFD, 0x8B, 0x7C, 0xEC, 0x0D, 0x01, 0xA4, 0x5D, 0x00, 0xE9, 0x79,
	0x58, 0xF5, 0x67, 0xF9, 0x61, 0xC3, 0x11, 0xB4, 0x7E, 0x76, 0x0A, 0x4C,
	0x60, 0xD6, 0xBD, 0xC8, 0x31, 0xD3, 0x0C, 0xD0, 0x5B, 0xDF, 0x7B, 0x05,
	0x9A, 0xBB, 0xC6, 0x2E, 0x9F, 0xF8, 0x18, 0x80, 0x6D, 0x1B, 0x21, 0xE5,
	0xAC, 0x75, 0xBC, 0x0D, 0x72, 0x51, 0x61, 0xD7, 0xEA, 0xA2, 0xAC, 0x0E,
	0xC1, 0xE7, 0x49, 0x37, 0xE7, 0x7C, 0xDE, 0xBD, 0x56, 0x00, 0x44, 0x6D,
	0xAB, 0x81, 0x2B, 0x26, 0x4A, 0xAA, 0x60, 0xE6, 0x43, 0x8D, 0x88, 0x1C,
	0x48, 0x55, 0x53, 0x25, 0xE8, 0x3C, 0x46, 0xF0, 0xA6, 0x33, 0x2D, 0xA2,
	0xDC, 0x99, 0x57, 0x38, 0x59, 0xCF, 0x53, 0xFA, 0x3E, 0x78, 0x46, 0xA0,
	0xA9, 0x50, 0x12, 0x72, 0xAC, 0x15, 0xC6, 0xA7, 0x42, 0x0F, 0x59, 0x6E,
	0xEA, 0xB0, 0x3D, 0xB8, 0x94, 0x32, 0xD1, 0xB6, 0xE8, 0x90, 0x06, 0x66,
	0x0C, 0xDE, 0xA9, 0x35, 0xC7, 0xDD, 0x72, 0x42, 0x38, 0x33, 0x32, 0x2F,
	0x2C, 0x3F, 0xBD, 0x01, 0xD6, 0x47, 0xFC, 0x89, 0x31, 0x38, 0x2E, 0xB9,
	0x6B, 0xED, 0xDB, 0x85, 0x38, 0xB1, 0xA5, 0x50, 0xFA, 0xFB, 0xA7, 0x31,
	0xEC, 0xB6, 0xBB, 0x82, 0x50, 0xB4, 0x88, 0x5C, 0xED, 0xE5, 0x4B, 0x5B,
	0xBF, 0xB3, 0x18, 0xFB, 0xAD, 0x24, 0x41, 0x55, 0x80, 0xCD, 0xA3, 0xA1,
	0xD6, 0xD5, 0xB6, 0x06, 0xE9, 0x85, 0x12, 0x33, 0x52, 0x56, 0xF1, 0xB7,
	0xDC, 0x57, 0x9E, 0xB4, 0x00, 0x1E, 0xCB, 0x62, 0x13, 0x4C, 0x90, 0x9A,
	0x9D, 0x64, 0x80, 0xD1, 0x5E, 0xB3, 0xCB, 0x8A, 0x73, 0x4E, 0x7B, 0xBE,
	0x4D, 0xA7, 0xF7, 0xB7, 0x9C, 0x1C, 0x7F, 0x27, 0x1E, 0x7F, 0x58, 0xB2,
	0x74, 0xAF, 0x94, 0x0E, 0x19, 0x23, 0xE1, 0x6B, 0xD8, 0x20, 0x4F, 0x2C,
	0x13, 0xE8, 0x8C, 0x37, 0x46, 0x27, 0x55, 0x68, 0xDA, 0x3F, 0x7A, 0xC6,
	0xEF, 0x87, 0x1D, 0x3B, 0x95, 0x43, 0x5E, 0x75, 0xE0, 0x02, 0x22, 0x0E,
	0x11, 0x60, 0xAB, 0x1A, 0x91, 0x94, 0xC4, 0xFA, 0xD9, 0x92, 0x2B, 0xE5,
	0x03, 0xE0, 0x7A, 0x17, 0x5C, 0x67, 0x22, 0xB3, 0xCB, 0x77, 0x9E, 0x22,
	0x01, 0x5F, 0x5D, 0x64, 0xE4, 0x2F, 0xC4, 0x61, 0xCA, 0xC7, 0xFD, 0x20,
	0x24, 0x30, 0xAB, 0x3F, 0x1A, 0x08, 0x85, 0x08, 0x39, 0xDE, 0x19, 0x1C,
	0x1A, 0xEA, 0xB8, 0x7E, 0xE5, 0xBC, 0xD9, 0xB2, 0x59, 0xC8, 0x81, 0x02,
	0x1D, 0x5C, 0xC0, 0xDD, 0x8D, 0x56, 0xB6, 0x2E, 0x85, 0x26, 0xA8, 0x34,
	0x92, 0x36, 0x9A, 0x84, 0xBD, 0x27, 0xC1, 0x9D, 0x5E, 0x14, 0xC4, 0xB7,
	0x02, 0xA8, 0xC9, 0xC2, 0xAD, 0xDC, 0x98, 0x42, 0x51, 0xDE, 0x94, 0x28,
	0x39, 0xEF, 0xE9, 0x7F, 0x05, 0x3F, 0x1D, 0x67, 0x72, 0x04, 0xCF, 0x7D,
	0x38, 0x49, 0xC4, 0x59, 0xA5, 0xF6, 0xB6, 0x02, 0x31, 0xD0, 0x05, 0x74,
	0x4B, 0xD0, 0x89, 0xD1, 0x7F, 0xC6, 0xDB, 0x7E, 0x75, 0x62, 0xA3, 0xC2,
	0x2E, 0xB0, 0xCC, 0x9A, 0xD3, 0xA4, 0x14, 0xB6, 0xF2, 0x91, 0x44, 0x3F,
	0x84, 0xE0, 0x90, 0x4A, 0x6A, 0x34, 0x8C, 0x35, 0x3C, 0xB2, 0xA9, 0x35,
	0x88, 0xB0, 0x88, 0xF8, 0x7E, 0x5C, 0xD2, 0x08, 0x5E, 0x08, 0x15, 0x03,
	0xBC, 0xF5, 0x42, 0x6B, 0x28, 0xED, 0xDD, 0xAA, 0x4D, 0x78, 0x10, 0x31,
	0x32, 0xA2, 0xC5, 0xCA, 0xEE, 0x9A, 0x62, 0x52, 0x3E, 0x48, 0x83, 0xA4,
	0xCA, 0xD4, 0xC7, 0xA7, 0xA5, 0x3F, 0x44, 0x1C, 0x86, 0xAD, 0x52, 0x7D,
	0x80, 0x1D, 0x9E, 0x32, 0x3F, 0x2A, 0x2E, 0xD8, 0x89, 0xC1, 0xA4, 0xD6,
	0xC1, 0x90, 0x2E, 0x1A, 0x20, 0x4B, 0x87, 0x32, 0x35, 0x25, 0xD8, 0xB8,
	0x57, 0x15, 0x85, 0x1E, 0x3C, 0x8A, 0xDC, 0x1A, 0x49, 0x3D, 0x70, 0x35,
	0x99, 0xAA, 0xDE, 0x2C, 0xD4, 0xAF, 0x79, 0x72, 0xAB, 0x97, 0x84, 0x20,
	0xB6, 0x4F, 0x34, 0x3F, 0xEA, 0xAE, 0x5F, 0x8F, 0x3A, 0x42, 0xDB, 0x68,
	0xE5, 0x84, 0x63, 0x2E, 0x7A, 0x0E, 0xBD, 0x28, 0x6A, 0x24, 0xB6, 0xAB,
	0xE4, 0xAC, 0x20, 0x7C, 0x81, 0xD0, 0x69, 0x89, 0xF8, 0xDE, 0xA9, 0x02,
	0xFD, 0x1F, 0x08, 0xDA, 0x26, 0xC2, 0x24, 0xCA, 0xEB, 0x44, 0x16, 0x8D,
	0x55, 0x5F, 0xB9, 0xA9, 0x5A, 0x18, 0x50, 0xB1, 0x54, 0xF1, 0xBF, 0x06,
	0xC2, 0xB0, 0x95, 0xC2, 0xAE, 0xE5, 0xBF, 0xB3, 0xFD, 0xC9, 0xBF, 0x75,
	0x42, 0x7D, 0xA0, 0xA8, 0x95, 0xF9, 0x62, 0x3B, 0x9C, 0x0D, 0x81, 0xF3,
	0x9C, 0xFC, 0x19, 0x5B, 0xF7, 0xD1, 0x9C, 0xF0, 0xAA, 0xFE, 0xEF, 0x35,
	0x1E, 0x81, 0x9E, 0x02, 0x46, 0x52, 0x9B, 0x99, 0x0D, 0x12, 0x8B, 0x71,
	0x6C, 0x32, 0xB5, 0x23, 0x17, 0x03, 0xC5, 0xB0, 0xA1, 0xC3, 0x4B, 0x10,
	0x01, 0x4D, 0x4C, 0x4A, 0x46, 0x8F, 0xD9, 0x79, 0xBB, 0x10, 0x44, 0xB0,
	0x3C, 0x7D, 0x46, 0xFD, 0x38, 0xDF, 0xAF, 0x6E, 0x58, 0x7D, 0xE1, 0xEB,
	0xBB, 0x8C, 0xDC, 0x79, 0xDA, 0x41, 0xD1, 0x8B, 0x0B, 0x11, 0x4F, 0xE5,
	0x1C, 0xC1, 0x59, 0xA7, 0x1E, 0x5A, 0xC1, 0xEE, 0x27, 0x33, 0xC8, 0x55,
	0xA9, 0x32, 0xEA, 0xF7, 0x45, 0xB0, 0x08, 0xE9, 0x32, 0xDF, 0x70, 0x24,
	0x82, 0xD3, 0x2A, 0x3E, 0x4F, 0x42, 0xB9, 0x25, 0x10, 0xD1, 0x73, 0xFA,
	0xFD, 0xC1, 0x84, 0xF2, 0xF7, 0x0E, 0xBC, 0x9D, 0x90, 0x39, 0xD7, 0xFD,
	0x45, 0x77, 0xBA, 0x29, 0xF9, 0x87, 0x45, 0xC1, 0x32, 0x44, 0xB0, 0x27,
	0x6B, 0xFC, 0x8A, 0xFE, 0x00, 0x6F, 0x61, 0x98, 0xD0, 0x60, 0xC8, 0x10,
	0xE5, 0xBC, 0x88, 0x13, 0x45, 0x44, 0xA5, 0xEB, 0x6E, 0xCB, 0x11, 0xAF,
	0x30, 0xDC, 0x8B, 0xF8, 0x30, 0x46, 0xDA, 0x76, 0xF1, 0xE5, 0x14, 0x51,
	0x8A, 0x02, 0x5A, 0x5A, 0xAA, 0x7B, 0x2D, 0x57, 0x0A, 0x5C, 0x73, 0xD1,
	0x88, 0xCE, 0xBE, 0x3D, 0x06, 0x3F, 0x48, 0x1D, 0x44, 0x24, 0x6F, 0x4F,
	0x7F, 0x6A, 0xF2, 0x16, 0x34, 0x35, 0x38, 0x73, 0x8A, 0xE5, 0x25, 0xF4,
	0x34, 0x9E, 0x5B, 0x40, 0x90, 0x04, 0x57, 0x1B, 0x57, 0x75, 0x8F, 0xEA,
	0x1C, 0xF8, 0x7A, 0x68, 0x01, 0x1C, 0x8D, 0xBA, 0xF4, 0xE3, 0xD3, 0x8F,
	0x7F, 0xE4, 0x50, 0x35, 0x6B, 0x6B, 0xF6, 0xFC, 0x5F, 0x9B, 0x98, 0x78,
	0x16, 0x68, 0x72, 0x74, 0x71, 0x78, 0x25, 0x68, 0xE5, 0x1E, 0x66, 0xE2,
	0x4E, 0xC8, 0xDB, 0x92, 0x8E, 0x88, 0x64, 0x74, 0xDE, 0xDB, 0x85, 0x56,
	0x9F, 0xF9, 0xC4, 0x29, 0x54, 0xA8, 0xFB, 0xBA, 0xEA, 0xAB, 0xC7, 0x49,
	0x5C, 0x6C, 0xD7, 0x61, 0x8C, 0xE2, 0x2B, 0xF5, 0xA0, 0xA8, 0xD2, 0x41,
	0xC0, 0x54, 0xAB, 0xA7, 0x56, 0x5C, 0xE7, 0xA5, 0xEA, 0xBC, 0x47, 0xD1,
	0x0D, 0xD9, 0xC0, 0xA9, 0xC4, 0xA7, 0x3E, 0xD1, 0x2B, 0x1E, 0x34, 0x31,
	0x36, 0x9D, 0xB9, 0x51, 0xD3, 0xAD, 0x29, 0xE6, 0x9B, 0xD8, 0x4B, 0x93,
	0x33, 0x2F, 0x30, 0xEF, 0x18, 0x90, 0x69, 0x11, 0x09, 0xEA, 0xBA, 0xE0,
	0x10, 0x93, 0x63, 0x71, 0xA8, 0x83, 0x59, 0xDB, 0xFC, 0x12, 0x22, 0x84,
	0xC7, 0x01, 0x20, 0x99, 0xEC, 0x59, 0xA9, 0xE6, 0x9B, 0x5B, 0x8B, 0xB8,
	0x68, 0x52, 0x61, 0x8B, 0x4E, 0xF3, 0x50, 0x69, 0xF1, 0x49, 0x9B, 0xAF,
	0x53, 0xAD, 0xA0, 0x9D, 0x23, 0xE0, 0xE0, 0xC4, 0x31, 0xE4, 0x8E, 0x1C,
	0x51, 0x14, 0xFC, 0x95, 0x9C, 0xA6, 0x34, 0x85, 0xB0, 0x36, 0xFC, 0x7A,
	0x53, 0x03, 0x31, 0x0E, 0xCB, 0x34, 0x3E, 0xDF, 0xD1, 0x71, 0xBC, 0xDB,
	0xA1, 0xAF, 0x59, 0x4A, 0x03, 0x19, 0xA7, 0x8E, 0xB5, 0x82, 0x15, 0x24,
	0x69, 0x68, 0xBD, 0x9C, 0x2E, 0xFA, 0x06, 0xB5, 0x70, 0xC5, 0x70, 0xC4,
	0x14, 0x99, 0x01, 0x49, 0xBD, 0x6E, 0xAE, 0x10, 0xA1, 0xE4, 0xEF, 0xDD,
	0xE5, 0x51, 0x22, 0x9D, 0xF7, 0x93, 0xAB, 0x41, 0xBD, 0x86, 0x7A, 0xCC,
	0x51, 0x94, 0xEC, 0x22, 0xBE, 0x0D, 0x67, 0xFD, 0xA3, 0xFD, 0xCF, 0xF8,
	0x74, 0x0A, 0x5E, 0x1C, 0x71, 0xAD, 0xB6, 0xD0, 0xD7, 0xF8, 0x71, 0x34,
	0xAB, 0x62, 0xE7, 0xA8, 0x6B, 0x8F, 0x1E, 0x43, 0x46, 0xA5, 0xE4, 0xB4,
	0x52, 0x81, 0x66, 0xB3, 0xE5, 0x10, 0x23, 0x21, 0x2B, 0x31, 0x0F, 0xB8,
	0xB6, 0xC5, 0xA5, 0xC9, 0x90, 0x07, 0x83, 0xD0, 0xC3, 0x10, 0x7A, 0x04,
	0xBD, 0x8A, 0x3C, 0x7B, 0xF9, 0x0E, 0x51, 0x81, 0x96, 0xC8, 0xAE, 0xF9,
	0x27, 0xDE, 0x62, 0x7A, 0x41, 0x60, 0x35, 0x8F, 0x77, 0xBC, 0x95, 0x11,
	0x2C, 0xC4, 0x6C, 0x47, 0x7A, 0xEB, 0x29, 0xE5, 0x8E, 0xB5, 0xD6, 0xA5,
	0x54, 0x1B, 0xD0, 0xE0, 0x0F, 0x7D, 0x5C, 0x51, 0xD8, 0x6C, 0x92, 0x2F,
	0x13, 0x4E, 0x90, 0x77, 0xF8, 0x8D, 0x69, 0x78, 0x96, 0x96, 0x49, 0x9F,
	0x3C, 0x2E, 0x5C, 0xA6, 0x73, 0x27, 0x7D, 0xAD, 0x8D, 0xE3, 0x9B, 0x4A,
	0x2F, 0x50, 0x0A, 0x42, 0x7E, 0xF2, 0x3B, 0x50, 0x5C, 0x81, 0xC9, 0x49,
	0x01, 0x96, 0x83, 0x0A, 0xEC, 0x7F, 0xED, 0x1C, 0xA5, 0x7D, 0xF1, 0xE6,
	0xC4, 0xB3, 0x8F, 0xF9, 0x0F, 0xDB, 0x7B, 0xC1, 0x35, 0xF7, 0x63, 0x4A,
	0x39, 0xD4, 0x0E, 0x9E, 0x05, 0xD9, 0x42, 0xAA, 0xAB, 0x52, 0xCA, 0x4E,
	0x98, 0x3B, 0x43, 0x1A, 0x91, 0x25, 0xA9, 0x34, 0xD5, 0x66, 0xB2, 0xF4,
	0xFF, 0xDE, 0x64, 0x91, 0x90, 0xB9, 0x17, 0x70, 0xA0, 0xD6, 0xEA, 0xB6,
	0x36, 0xF4, 0x44, 0xCE, 0x86, 0x7B, 0x18, 0x74, 0x9C, 0x18, 0xAD, 0xB6,
	0xE0, 0x74, 0xC1, 0x0E, 0x29, 0x5D, 0x6A, 0x36, 0xD1, 0x3E, 0xB8, 0x2A,
	0xE4, 0x23, 0x1D, 0xB2, 0xAE, 0xF5, 0x5B, 0x8E, 0x2C, 0xD9, 0xD1, 0xE1,
	0x4F, 0x58, 0xA6, 0xE3, 0x88, 0x2E, 0xF9, 0xCF, 0x32, 0x3E, 0x8E, 0x37,
	0x95, 0xFF, 0xAD, 0x68, 0x11, 0x5E, 0x7F, 0x3D, 0x38, 0x06, 0x7C, 0x33,
	0x32, 0x78, 0x09, 0xEC, 0xCA, 0x3E, 0x08, 0xF1, 0xD0, 0x95, 0x19, 0xC9,
	0x7E, 0x62, 0xB2, 0x02, 0xA3, 0x5D, 0xF8, 0x3F, 0xA2, 0xB0, 0x8B, 0x38,
	0xB1, 0x8C, 0xEA, 0xB3, 0xE4, 0xBF, 0xD3, 0x6C, 0x6D, 0x3D, 0xD1, 0xC6,
	0xDA, 0x6B, 0x7A, 0xBA, 0x05, 0xEA, 0x9E, 0xA5, 0xE9, 0x00, 0xCC, 0x80,
	0x57, 0xAB, 0xD9, 0x0A, 0xD1, 0x00, 0x82, 0x2A, 0x51, 0x4B, 0xA2, 0x96,
	0xEB, 0x96, 0x14, 0xA8, 0x46, 0xDF, 0x1D, 0x48, 0xAE, 0xFA, 0x12, 0xA8,
	0x89, 0x8E, 0xEF, 0xBC, 0x3C, 0xA1, 0x6E, 0xDD, 0x90, 0x66, 0x2E, 0x56,
	0x6B, 0xF7, 0x1D, 0xF0, 0x46, 0x11, 0x4A, 0xA6, 0x07, 0x73, 0xC4, 0xE3,
	0x97, 0xFE, 0x7E, 0x22, 0x6F, 0x22, 0xB4, 0x6F, 0xB0, 0x32, 0x0A, 0x5E,
	0x85, 0x7E, 0x54, 0xB4, 0x24, 0xBD, 0x36, 0xA7, 0x94, 0xE7, 0x37, 0xFD,
	0x1A, 0xAF, 0xF4, 0x44, 0xB4, 0x35, 0x4F, 0xE0, 0x41, 0x0E, 0x7D, 0x73,
	0x29, 0x28, 0xDA, 0xAF, 0x69, 0xB2, 0xC5, 0xA7, 0x2A, 0x0A, 0xB5, 0x9C,
	0xC2, 0xAC, 0x5F, 0x59, 0x5C, 0xEE, 0x44, 0x49, 0x6F, 0x4F, 0x64, 0x43,
	0x6F, 0x43, 0x44, 0xAA, 0xA0, 0x4E, 0x94, 0x7C, 0x26, 0x5A, 0xF1, 0xD9,
	0xE6, 0x09, 0x80, 0x7A, 0x7D, 0x2E, 0xA2, 0xB9, 0x1A, 0x7A, 0x8F, 0x2A,
	0x97, 0x77, 0x23, 0xB4, 0x10, 0xAD, 0x20, 0x7B, 0xA3, 0x0F, 0xFD, 0x44,
	0x38, 0xAD, 0x94, 0x39, 0x88, 0x1C, 0xC4, 0xC8, 0xDF, 0xF1, 0x04, 0xA6,
	0x51, 0x5D, 0x54, 0x53, 0x60, 0xE4, 0x8A, 0x89, 0x4A, 0x9C, 0xE1, 0x68,
	0x4D, 0xFE, 0x69, 0x94, 0x0B, 0x8E, 0xED, 0x6C, 0xFE, 0x11, 0xA7, 0x77,
	0xBF, 0x08, 0x41, 0x67, 0x22, 0x59, 0x51, 0x48, 0xEE, 0x59, 0x02, 0x0E,
	0x60, 0x6D, 0xAE, 0x8C, 0xC6, 0x39, 0xB7, 0x55, 0xC5, 0x3B, 0x87, 0xA9,
	0xBD, 0xD8, 0xEA, 0x48, 0x21, 0xE4, 0x57, 0x51, 0x56, 0x03, 0xF4, 0xBE,
	0xBD, 0xBD, 0xC5, 0x26, 0x9B, 0x27, 0xE3, 0xAE, 0xD5, 0x1E, 0x30, 0xE9,
	0x7C, 0x9D, 0xDB, 0xE1, 0x09, 0x9D, 0x82, 0x49, 0x15, 0x38, 0x69, 0xFC,
	0x1D, 0x52, 0x1A, 0x75, 0xE6, 0xDD, 0x1D, 0xBE, 0x06, 0xC4, 0x9F, 0x14,
	0x4C, 0x12, 0xDE, 0xDF, 0x4A, 0xE1, 0x3B, 0xE7, 0xD1, 0xE3, 0x71, 0xD1,
	0xFA, 0xD8, 0x0E, 0x63, 0x27, 0xA9, 0xC7, 0x9D, 0xC0, 0x01, 0xC2, 0xDD,
	0xFC, 0xA6, 0x1F, 0x59, 0x87, 0xC5, 0x56, 0x99, 0x80, 0xEB, 0xF0, 0xB8,
	0xB3, 0x00, 0x9A, 0x61, 0xDB, 0x50, 0x79, 0x48, 0x37, 0x35, 0xDA, 0xD8,
	0xF2, 0x37, 0xA7, 0x43, 0xA7, 0xEB, 0x88, 0x2C, 0x68, 0xB4, 0xBB, 0x14,
	0x45, 0x31, 0x6B, 0x87, 0x65, 0xE7, 0x82, 0xB4, 0x74, 0xD2, 0xFF, 0x7F,
	0x60, 0x15, 0x94, 0x75, 0xEE, 0x30, 0x3C, 0x4E, 0xFC, 0x41, 0xD1, 0x5B,
	0xDD, 0x84, 0x6E, 0x13, 0x6C, 0xF8, 0x12, 0xE6, 0xB7, 0xA4, 0xB9, 0xC8,
	0x13, 0x89, 0x0C, 0x34, 0xA6, 0xAF, 0x09, 0xEB, 0xF2, 0xB3, 0x79, 0x77,
	0x80, 0xD8, 0x77, 0x64, 0xAD, 0x32, 0x3D, 0xD2, 0x06, 0xDF, 0x72, 0x11,
	0x4A, 0xA7, 0x70, 0xCE, 0xF9, 0xE6, 0x81, 0x35, 0xA4, 0xA7, 0x52, 0xB5,
	0x13, 0x68, 0x5C, 0x69, 0x45, 0xE2, 0x77, 0x2D, 0xBE, 0x2C, 0xE9, 0x38,
	0x25, 0x28, 0x7B, 0x63, 0x2C, 0x19, 0x8F, 0x59
};

/* aad */
uint8_t aad[] = {
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B
};

/* iv */
uint8_t iv[] = {
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B,
	0x0C, 0x0D, 0x0E, 0x0F
};

/* cipher key */
uint8_t cipher_key[] = {
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B,
	0x0C, 0x0D, 0x0E, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
	0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F
};

/* auth key */
uint8_t auth_key[] = {
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B,
	0x0C, 0x0D, 0x0E, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
	0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23,
	0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C, 0x2D, 0x2E, 0x2F,
	0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B,
	0x3C, 0x3D, 0x3E, 0x3F, 0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
	0x48, 0x49, 0x4A, 0x4B, 0x4C, 0x4D, 0x4E, 0x4F, 0x50, 0x51, 0x52, 0x53,
	0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5A, 0x5B, 0x5C, 0x5D, 0x5E, 0x5F,
	0x60, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6A, 0x6B,
	0x6C, 0x6D, 0x6E, 0x6F, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77,
	0x78, 0x79, 0x7A, 0x7B, 0x7C, 0x7D, 0x7E, 0x7F
};

/* AEAD key */
uint8_t aead_key[] = {
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B,
	0x0C, 0x0D, 0x0E, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
	0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F
};

/* Digests */
uint8_t digest[2048] = { 0x00 };

uint8_t ipsec_plaintext[2048] = {
		/* IP */
		0x45, 0x00, 0x00, 0x28, 0xa4, 0xad, 0x40, 0x00,
		0x40, 0x06, 0x78, 0x80, 0x0a, 0x01, 0x03, 0x8f,
		0x0a, 0x01, 0x06, 0x12,

		/* TCP */
		0x80, 0x23, 0x06, 0xb8, 0xcb, 0x71, 0x26, 0x02,
		0xdd, 0x6b, 0xb0, 0x3e, 0x50, 0x10, 0x16, 0xd0,
		0x75, 0x67, 0x00, 0x01
};

/** ECDSA secp256r1 elliptic curve test params */
struct
cperf_ecdsa_test_data secp256r1_perf_data = {
	.pubkey_qx = {
		.data = secp256r1_qx,
		.length = sizeof(secp256r1_qx),
	},
	.pubkey_qy = {
		.data = secp256r1_qy,
		.length = sizeof(secp256r1_qy),
	},
	.k = {
		.data = secp256r1_k,
		.length = sizeof(secp256r1_k),
	},
	.sign_r = {
		.data = secp256r1_sign_r,
		.length = sizeof(secp256r1_sign_r),
	},
	.sign_s = {
		.data = secp256r1_sign_s,
		.length = sizeof(secp256r1_sign_s),
	},
	.pkey = {
		.data = secp256r1_pkey,
		.length = sizeof(secp256r1_pkey),
	},
	.message = {
		.data = secp256r1_message,
		.length = sizeof(secp256r1_message),
	},
	.curve = RTE_CRYPTO_EC_GROUP_SECP256R1
};

/* EdDSA 25519 elliptic curve test params */
struct
cperf_eddsa_test_data ed25519_perf_data = {
	.pubkey = {
		.data = ed25519_pubkey,
		.length = sizeof(ed25519_pubkey),
	},
	.pkey = {
		.data = ed25519_pkey,
		.length = sizeof(ed25519_pkey),
	},
	.sign = {
		.data = ed25519_sign,
		.length = sizeof(ed25519_sign),
	},
	.message = {
		.data = ed25519_message,
		.length = sizeof(ed25519_message),
	},
	.curve = RTE_CRYPTO_EC_GROUP_ED25519,
	.instance = RTE_CRYPTO_EDCURVE_25519
};

/** SM2 Fp256 elliptic curve test params */
struct
cperf_sm2_test_data sm2_perf_data = {
	.pubkey_qx = {
		.data = fp256_qx,
		.length = sizeof(fp256_qx),
	},
	.pubkey_qy = {
		.data = fp256_qy,
		.length = sizeof(fp256_qy),
	},
	.k = {
		.data = fp256_k,
		.length = sizeof(fp256_k),
	},
	.sign_r = {
		.data = fp256_sign_r,
		.length = sizeof(fp256_sign_r),
	},
	.sign_s = {
		.data = fp256_sign_s,
		.length = sizeof(fp256_sign_s),
	},
	.id = {
		.data = fp256_id,
		.length = sizeof(fp256_id),
	},
	.pkey = {
		.data = fp256_pkey,
		.length = sizeof(fp256_pkey),
	},
	.message = {
		.data = fp256_message,
		.length = sizeof(fp256_message),
	},
	.digest = {
		.data = fp256_digest,
		.length = sizeof(fp256_digest),
	},
	.cipher = {
		.data = fp256_cipher,
		.length = sizeof(fp256_cipher),
	},
	.curve = RTE_CRYPTO_EC_GROUP_SM2
};

/** RSA test params */
struct cperf_rsa_plaintext rsa_plaintext = {
	.data = {
		0xf8, 0xba, 0x1a, 0x55, 0xd0, 0x2f, 0x85, 0xae,
		0x96, 0x7b, 0xb6, 0x2f, 0xb6, 0xcd, 0xa8, 0xeb,
		0x7e, 0x78, 0xa0, 0x50
	},
	.len = 20
};

uint8_t rsa_n[] = {
	0xb3, 0xa1, 0xaf, 0xb7, 0x13, 0x08, 0x00,
	0x0a, 0x35, 0xdc, 0x2b, 0x20, 0x8d, 0xa1, 0xb5,
	0xce, 0x47, 0x8a, 0xc3, 0x80, 0xf4, 0x7d, 0x4a,
	0xa2, 0x62, 0xfd, 0x61, 0x7f, 0xb5, 0xa8, 0xde,
	0x0a, 0x17, 0x97, 0xa0, 0xbf, 0xdf, 0x56, 0x5a,
	0x3d, 0x51, 0x56, 0x4f, 0x70, 0x70, 0x3f, 0x63,
	0x6a, 0x44, 0x5b, 0xad, 0x84, 0x0d, 0x3f, 0x27,
	0x6e, 0x3b, 0x34, 0x91, 0x60, 0x14, 0xb9, 0xaa,
	0x72, 0xfd, 0xa3, 0x64, 0xd2, 0x03, 0xa7, 0x53,
	0x87, 0x9e, 0x88, 0x0b, 0xc1, 0x14, 0x93, 0x1a,
	0x62, 0xff, 0xb1, 0x5d, 0x74, 0xcd, 0x59, 0x63,
	0x18, 0x11, 0x3d, 0x4f, 0xba, 0x75, 0xd4, 0x33,
	0x4e, 0x23, 0x6b, 0x7b, 0x57, 0x44, 0xe1, 0xd3,
	0x03, 0x13, 0xa6, 0xf0, 0x8b, 0x60, 0xb0, 0x9e,
	0xee, 0x75, 0x08, 0x9d, 0x71, 0x63, 0x13, 0xcb,
	0xa6, 0x81, 0x92, 0x14, 0x03, 0x22, 0x2d, 0xde,
	0x55
};

uint8_t rsa_d[] = {
	0x24, 0xd7, 0xea, 0xf4, 0x7f, 0xe0, 0xca, 0x31,
	0x4d, 0xee, 0xc4, 0xa1, 0xbe, 0xab, 0x06, 0x61,
	0x32, 0xe7, 0x51, 0x46, 0x27, 0xdf, 0x72, 0xe9,
	0x6f, 0xa8, 0x4c, 0xd1, 0x26, 0xef, 0x65, 0xeb,
	0x67, 0xff, 0x5f, 0xa7, 0x3b, 0x25, 0xb9, 0x08,
	0x8e, 0xa0, 0x47, 0x56, 0xe6, 0x8e, 0xf9, 0xd3,
	0x18, 0x06, 0x3d, 0xc6, 0xb1, 0xf8, 0xdc, 0x1b,
	0x8d, 0xe5, 0x30, 0x54, 0x26, 0xac, 0x16, 0x3b,
	0x7b, 0xad, 0x46, 0x9e, 0x21, 0x6a, 0x57, 0xe6,
	0x81, 0x56, 0x1d, 0x2a, 0xc4, 0x39, 0x63, 0x67,
	0x81, 0x2c, 0xca, 0xcc, 0xf8, 0x42, 0x04, 0xbe,
	0xcf, 0x8f, 0x6c, 0x5b, 0x81, 0x46, 0xb9, 0xc7,
	0x62, 0x90, 0x87, 0x35, 0x03, 0x9b, 0x89, 0xcb,
	0x37, 0xbd, 0xf1, 0x1b, 0x99, 0xa1, 0x9a, 0x78,
	0xd5, 0x4c, 0xdd, 0x3f, 0x41, 0x0c, 0xb7, 0x1a,
	0xd9, 0x7b, 0x87, 0x5f, 0xbe, 0xb1, 0x83, 0x41
};

uint8_t rsa_e[] = {0x01, 0x00, 0x01};

uint8_t rsa_p[] = {
	0xdc, 0xba, 0x00, 0x01, 0x57, 0x93, 0xe3, 0x05,
	0xed, 0x61, 0x9a, 0xa3, 0xaf, 0x6a, 0xd3, 0x47,
	0x8f, 0x2d, 0x1e, 0x7f, 0x4d, 0x60, 0xc8, 0x8d,
	0x34, 0xb8, 0x17, 0x84, 0xbc, 0xd4, 0xe9, 0x79,
	0x95, 0x75, 0x19, 0x37, 0xe0, 0xcc, 0xfe, 0x4c,
	0x5d, 0x49, 0x53, 0x61, 0x29, 0xf1, 0xdc, 0x82,
	0x03, 0x96, 0x7d, 0x95, 0x4f, 0xdd, 0x3c, 0x0a,
	0x64, 0x8a, 0x43, 0x2f, 0x95, 0x4a, 0xed, 0xdd
};

uint8_t rsa_q[] = {
	0xd0, 0x56, 0x7a, 0x0a, 0xd5, 0x95, 0xa4, 0x85,
	0x53, 0x35, 0xa1, 0x48, 0x07, 0x6a, 0x7c, 0x08,
	0xe0, 0xfd, 0x4b, 0x88, 0x77, 0xa6, 0x15, 0x23,
	0x0f, 0xbf, 0x14, 0x46, 0x11, 0xee, 0x95, 0xc7,
	0x5e, 0x77, 0x65, 0xa2, 0xb5, 0x50, 0xdf, 0x19,
	0x07, 0xc7, 0x72, 0xdb, 0x29, 0xf6, 0x54, 0x86,
	0xe1, 0xb3, 0x97, 0x0a, 0x28, 0x64, 0x3a, 0x38,
	0xa6, 0x7d, 0x13, 0xc3, 0x79, 0xaa, 0x56, 0xd9
};

uint8_t rsa_dp[] = {
	0xc5, 0x43, 0x0d, 0x82, 0x25, 0x8c, 0xab, 0x55,
	0xbe, 0xc2, 0x7d, 0xfb, 0x4f, 0x68, 0x3f, 0x0e,
	0x32, 0xec, 0xf5, 0xd6, 0x7b, 0x86, 0xc5, 0x75,
	0x3c, 0xea, 0x51, 0x4a, 0x75, 0xa0, 0x2a, 0x50,
	0x58, 0xbb, 0xe0, 0x1f, 0xca, 0x2e, 0x2a, 0x0e,
	0x81, 0x48, 0x68, 0xd5, 0xeb, 0x30, 0x96, 0x0b,
	0x33, 0xbd, 0xa8, 0xda, 0x6a, 0x17, 0xa3, 0xf2,
	0xfd, 0xcb, 0x7b, 0x23, 0xe9, 0x5e, 0x9f, 0x99
};
uint8_t rsa_dq[] = {
	0xbe, 0xff, 0xf9, 0x05, 0x43, 0xc8, 0xdc, 0x3b,
	0x0b, 0x0d, 0x28, 0xde, 0x73, 0x46, 0x11, 0x8e,
	0xc6, 0x4e, 0x11, 0xd8, 0x7b, 0xf0, 0xfc, 0x81,
	0xd7, 0x66, 0xd3, 0xbc, 0x65, 0xa6, 0x39, 0x14,
	0xbd, 0xab, 0x72, 0xb7, 0x57, 0xc9, 0x5b, 0xaf,
	0x83, 0xed, 0x3b, 0x84, 0x68, 0x15, 0x18, 0x6b,
	0x4c, 0x32, 0xac, 0x6f, 0x38, 0x96, 0xa2, 0xb5,
	0xdb, 0x14, 0xe2, 0x70, 0x9c, 0x73, 0x29, 0x09
};

uint8_t rsa_qinv[] = {
	0x59, 0xbd, 0xb1, 0x37, 0xeb, 0x4e, 0xcf, 0x68,
	0xe7, 0x85, 0x91, 0xbb, 0xc0, 0xdb, 0x8e, 0x41,
	0x91, 0x4a, 0xc0, 0xb1, 0xc5, 0xe8, 0x91, 0xf6,
	0xc7, 0x5a, 0x98, 0x1a, 0x8a, 0x0f, 0x45, 0xb2,
	0x5b, 0xff, 0x7a, 0x2d, 0x98, 0x89, 0x55, 0xd9,
	0xbf, 0x6e, 0xdd, 0x2d, 0xd4, 0xe8, 0x0a, 0xaa,
	0xae, 0x2a, 0xc4, 0x16, 0xb5, 0xba, 0xe1, 0x69,
	0x71, 0x94, 0xdd, 0xa0, 0xf5, 0x1e, 0x6d, 0xcc
};

uint8_t rsa_2048_n[] = {
	0xB0, 0xF9, 0xE8, 0x19, 0x43, 0xA7, 0xAE, 0x98,
	0x92, 0xAA, 0xDE, 0x17, 0xCA, 0x7C, 0x40, 0xF8,
	0x74, 0x4F, 0xED, 0x2F, 0x81, 0x48, 0xE6, 0xC8,
	0xEA, 0xA2, 0x7B, 0x7D, 0x00, 0x15, 0x48, 0xFB,
	0x51, 0x92, 0xAB, 0x28, 0xB5, 0x6C, 0x50, 0x60,
	0xB1, 0x18, 0xCC, 0xD1, 0x31, 0xE5, 0x94, 0x87,
	0x4C, 0x6C, 0xA9, 0x89, 0xB5, 0x6C, 0x27, 0x29,
	0x6F, 0x09, 0xFB, 0x93, 0xA0, 0x34, 0xDF, 0x32,
	0xE9, 0x7C, 0x6F, 0xF0, 0x99, 0x8C, 0xFD, 0x8E,
	0x6F, 0x42, 0xDD, 0xA5, 0x8A, 0xCD, 0x1F, 0xA9,
	0x79, 0x86, 0xF1, 0x44, 0xF3, 0xD1, 0x54, 0xD6,
	0x76, 0x50, 0x17, 0x5E, 0x68, 0x54, 0xB3, 0xA9,
	0x52, 0x00, 0x3B, 0xC0, 0x68, 0x87, 0xB8, 0x45,
	0x5A, 0xC2, 0xB1, 0x9F, 0x7B, 0x2F, 0x76, 0x50,
	0x4E, 0xBC, 0x98, 0xEC, 0x94, 0x55, 0x71, 0xB0,
	0x78, 0x92, 0x15, 0x0D, 0xDC, 0x6A, 0x74, 0xCA,
	0x0F, 0xBC, 0xD3, 0x54, 0x97, 0xCE, 0x81, 0x53,
	0x4D, 0xAF, 0x94, 0x18, 0x84, 0x4B, 0x13, 0xAE,
	0xA3, 0x1F, 0x9D, 0x5A, 0x6B, 0x95, 0x57, 0xBB,
	0xDF, 0x61, 0x9E, 0xFD, 0x4E, 0x88, 0x7F, 0x2D,
	0x42, 0xB8, 0xDD, 0x8B, 0xC9, 0x87, 0xEA, 0xE1,
	0xBF, 0x89, 0xCA, 0xB8, 0x5E, 0xE2, 0x1E, 0x35,
	0x63, 0x05, 0xDF, 0x6C, 0x07, 0xA8, 0x83, 0x8E,
	0x3E, 0xF4, 0x1C, 0x59, 0x5D, 0xCC, 0xE4, 0x3D,
	0xAF, 0xC4, 0x91, 0x23, 0xEF, 0x4D, 0x8A, 0xBB,
	0xA9, 0x3D, 0x39, 0x05, 0xE4, 0x02, 0x8D, 0x7B,
	0xA9, 0x14, 0x84, 0xA2, 0x75, 0x96, 0xE0, 0x7B,
	0x4B, 0x6E, 0xD9, 0x92, 0xF0, 0x77, 0xB5, 0x24,
	0xD3, 0xDC, 0xFE, 0x7D, 0xDD, 0x55, 0x49, 0xBE,
	0x7C, 0xCE, 0x8D, 0xA0, 0x35, 0xCF, 0xA0, 0xB3,
	0xFB, 0x8F, 0x9E, 0x46, 0xF7, 0x32, 0xB2, 0xA8,
	0x6B, 0x46, 0x01, 0x65, 0xC0, 0x8F, 0x53, 0x13
};

uint8_t rsa_2048_d[] = {
	0x41, 0x18, 0x8B, 0x20, 0xCF, 0xDB, 0xDB, 0xC2,
	0xCF, 0x1F, 0xFE, 0x75, 0x2D, 0xCB, 0xAA, 0x72,
	0x39, 0x06, 0x35, 0x2E, 0x26, 0x15, 0xD4, 0x9D,
	0xCE, 0x80, 0x59, 0x7F, 0xCF, 0x0A, 0x05, 0x40,
	0x3B, 0xEF, 0x00, 0xFA, 0x06, 0x51, 0x82, 0xF7,
	0x2D, 0xEC, 0xFB, 0x59, 0x6F, 0x4B, 0x0C, 0xE8,
	0xFF, 0x59, 0x70, 0xBA, 0xF0, 0x7A, 0x89, 0xA5,
	0x19, 0xEC, 0xC8, 0x16, 0xB2, 0xF4, 0xFF, 0xAC,
	0x50, 0x69, 0xAF, 0x1B, 0x06, 0xBF, 0xEF, 0x7B,
	0xF6, 0xBC, 0xD7, 0x9E, 0x4E, 0x81, 0xC8, 0xC5,
	0xA3, 0xA7, 0xD9, 0x13, 0x0D, 0xC3, 0xCF, 0xBA,
	0xDA, 0xE5, 0xF6, 0xD2, 0x88, 0xF9, 0xAE, 0xE3,
	0xF6, 0xFF, 0x92, 0xFA, 0xE0, 0xF8, 0x1A, 0xF5,
	0x97, 0xBE, 0xC9, 0x6A, 0xE9, 0xFA, 0xB9, 0x40,
	0x2C, 0xD5, 0xFE, 0x41, 0xF7, 0x05, 0xBE, 0xBD,
	0xB4, 0x7B, 0xB7, 0x36, 0xD3, 0xFE, 0x6C, 0x5A,
	0x51, 0xE0, 0xE2, 0x07, 0x32, 0xA9, 0x7B, 0x5E,
	0x46, 0xC1, 0xCB, 0xDB, 0x26, 0xD7, 0x48, 0x54,
	0xC6, 0xB6, 0x60, 0x4A, 0xED, 0x46, 0x37, 0x35,
	0xFF, 0x90, 0x76, 0x04, 0x65, 0x57, 0xCA, 0xF9,
	0x49, 0xBF, 0x44, 0x88, 0x95, 0xC2, 0x04, 0x32,
	0xC1, 0xE0, 0x9C, 0x01, 0x4E, 0xA7, 0x56, 0x60,
	0x43, 0x4F, 0x1A, 0x0F, 0x3B, 0xE2, 0x94, 0xBA,
	0xBC, 0x5D, 0x53, 0x0E, 0x6A, 0x10, 0x21, 0x3F,
	0x53, 0xB6, 0x03, 0x75, 0xFC, 0x84, 0xA7, 0x57,
	0x3F, 0x2A, 0xF1, 0x21, 0x55, 0x84, 0xF5, 0xB4,
	0xBD, 0xA6, 0xD4, 0xE8, 0xF9, 0xE1, 0x7A, 0x78,
	0xD9, 0x7E, 0x77, 0xB8, 0x6D, 0xA4, 0xA1, 0x84,
	0x64, 0x75, 0x31, 0x8A, 0x7A, 0x10, 0xA5, 0x61,
	0x01, 0x4E, 0xFF, 0xA2, 0x3A, 0x81, 0xEC, 0x56,
	0xE9, 0xE4, 0x10, 0x9D, 0xEF, 0x8C, 0xB3, 0xF7,
	0x97, 0x22, 0x3F, 0x7D, 0x8D, 0x0D, 0x43, 0x51
};

uint8_t rsa_2048_p[] = {
	0xDD, 0x10, 0x57, 0x02, 0x38, 0x2F, 0x23, 0x2B,
	0x36, 0x81, 0xF5, 0x37, 0x91, 0xE2, 0x26, 0x17,
	0xC7, 0xBF, 0x4E, 0x9A, 0xCB, 0x81, 0xED, 0x48,
	0xDA, 0xF6, 0xD6, 0x99, 0x5D, 0xA3, 0xEA, 0xB6,
	0x42, 0x83, 0x9A, 0xFF, 0x01, 0x2D, 0x2E, 0xA6,
	0x28, 0xB9, 0x0A, 0xF2, 0x79, 0xFD, 0x3E, 0x6F,
	0x7C, 0x93, 0xCD, 0x80, 0xF0, 0x72, 0xF0, 0x1F,
	0xF2, 0x44, 0x3B, 0x3E, 0xE8, 0xF2, 0x4E, 0xD4,
	0x69, 0xA7, 0x96, 0x13, 0xA4, 0x1B, 0xD2, 0x40,
	0x20, 0xF9, 0x2F, 0xD1, 0x10, 0x59, 0xBD, 0x1D,
	0x0F, 0x30, 0x1B, 0x5B, 0xA7, 0xA9, 0xD3, 0x63,
	0x7C, 0xA8, 0xD6, 0x5C, 0x1A, 0x98, 0x15, 0x41,
	0x7D, 0x8E, 0xAB, 0x73, 0x4B, 0x0B, 0x4F, 0x3A,
	0x2C, 0x66, 0x1D, 0x9A, 0x1A, 0x82, 0xF3, 0xAC,
	0x73, 0x4C, 0x40, 0x53, 0x06, 0x69, 0xAB, 0x8E,
	0x47, 0x30, 0x45, 0xA5, 0x8E, 0x65, 0x53, 0x9D
};

uint8_t rsa_2048_q[] = {
	0xCC, 0xF1, 0xE5, 0xBB, 0x90, 0xC8, 0xE9, 0x78,
	0x1E, 0xA7, 0x5B, 0xEB, 0xF1, 0x0B, 0xC2, 0x52,
	0xE1, 0x1E, 0xB0, 0x23, 0xA0, 0x26, 0x0F, 0x18,
	0x87, 0x55, 0x2A, 0x56, 0x86, 0x3F, 0x4A, 0x64,
	0x21, 0xE8, 0xC6, 0x00, 0xBF, 0x52, 0x3D, 0x6C,
	0xB1, 0xB0, 0xAD, 0xBD, 0xD6, 0x5B, 0xFE, 0xE4,
	0xA8, 0x8A, 0x03, 0x7E, 0x3D, 0x1A, 0x41, 0x5E,
	0x5B, 0xB9, 0x56, 0x48, 0xDA, 0x5A, 0x0C, 0xA2,
	0x6B, 0x54, 0xF4, 0xA6, 0x39, 0x48, 0x52, 0x2C,
	0x3D, 0x5F, 0x89, 0xB9, 0x4A, 0x72, 0xEF, 0xFF,
	0x95, 0x13, 0x4D, 0x59, 0x40, 0xCE, 0x45, 0x75,
	0x8F, 0x30, 0x89, 0x80, 0x90, 0x89, 0x56, 0x58,
	0x8E, 0xEF, 0x57, 0x5B, 0x3E, 0x4B, 0xC4, 0xC3,
	0x68, 0xCF, 0xE8, 0x13, 0xEE, 0x9C, 0x25, 0x2C,
	0x2B, 0x02, 0xE0, 0xDF, 0x91, 0xF1, 0xAA, 0x01,
	0x93, 0x8D, 0x38, 0x68, 0x5D, 0x60, 0xBA, 0x6F
};

uint8_t rsa_2048_dp[] = {
	0x09, 0xED, 0x54, 0xEA, 0xED, 0x98, 0xF8, 0x4C,
	0x55, 0x7B, 0x4A, 0x86, 0xBF, 0x4F, 0x57, 0x84,
	0x93, 0xDC, 0xBC, 0x6B, 0xE9, 0x1D, 0xA1, 0x89,
	0x37, 0x04, 0x04, 0xA9, 0x08, 0x72, 0x76, 0xF4,
	0xCE, 0x51, 0xD8, 0xA1, 0x00, 0xED, 0x85, 0x7D,
	0xC2, 0xB0, 0x64, 0x94, 0x74, 0xF3, 0xF1, 0x5C,
	0xD2, 0x4C, 0x54, 0xDB, 0x28, 0x71, 0x10, 0xE5,
	0x6E, 0x5C, 0xB0, 0x08, 0x68, 0x2F, 0x91, 0x68,
	0xAA, 0x81, 0xF3, 0x14, 0x58, 0xB7, 0x43, 0x1E,
	0xCC, 0x1C, 0x44, 0x90, 0x6F, 0xDA, 0x87, 0xCA,
	0x89, 0x47, 0x10, 0xC3, 0x71, 0xE9, 0x07, 0x6C,
	0x1D, 0x49, 0xFB, 0xAE, 0x51, 0x27, 0x69, 0x34,
	0xF2, 0xAD, 0x78, 0x77, 0x89, 0xF4, 0x2D, 0x0F,
	0xA0, 0xB4, 0xC9, 0x39, 0x85, 0x5D, 0x42, 0x12,
	0x09, 0x6F, 0x70, 0x28, 0x0A, 0x4E, 0xAE, 0x7C,
	0x8A, 0x27, 0xD9, 0xC8, 0xD0, 0x77, 0x2E, 0x65
};
uint8_t rsa_2048_dq[] = {
	0x8C, 0xB6, 0x85, 0x7A, 0x7B, 0xD5, 0x46, 0x5F,
	0x80, 0x04, 0x7E, 0x9B, 0x87, 0xBC, 0x00, 0x27,
	0x31, 0x84, 0x05, 0x81, 0xE0, 0x62, 0x61, 0x39,
	0x01, 0x2A, 0x5B, 0x50, 0x5F, 0x0A, 0x33, 0x84,
	0x7E, 0xB7, 0xB8, 0xC3, 0x28, 0x99, 0x49, 0xAD,
	0x48, 0x6F, 0x3B, 0x4B, 0x3D, 0x53, 0x9A, 0xB5,
	0xDA, 0x76, 0x30, 0x21, 0xCB, 0xC8, 0x2C, 0x1B,
	0xA2, 0x34, 0xA5, 0x66, 0x8D, 0xED, 0x08, 0x01,
	0xB8, 0x59, 0xF3, 0x43, 0xF1, 0xCE, 0x93, 0x04,
	0xE6, 0xFA, 0xA2, 0xB0, 0x02, 0xCA, 0xD9, 0xB7,
	0x8C, 0xDE, 0x5C, 0xDC, 0x2C, 0x1F, 0xB4, 0x17,
	0x1C, 0x42, 0x42, 0x16, 0x70, 0xA6, 0xAB, 0x0F,
	0x50, 0xCC, 0x4A, 0x19, 0x4E, 0xB3, 0x6D, 0x1C,
	0x91, 0xE9, 0x35, 0xBA, 0x01, 0xB9, 0x59, 0xD8,
	0x72, 0x8B, 0x9E, 0x64, 0x42, 0x6B, 0x3F, 0xC3,
	0xA7, 0x50, 0x6D, 0xEB, 0x52, 0x39, 0xA8, 0xA7
};

uint8_t rsa_2048_qinv[] = {
	0x0A, 0x81, 0xD8, 0xA6, 0x18, 0x31, 0x4A, 0x80,
	0x3A, 0xF6, 0x1C, 0x06, 0x71, 0x1F, 0x2C, 0x39,
	0xB2, 0x66, 0xFF, 0x41, 0x4D, 0x53, 0x47, 0x6D,
	0x1D, 0xA5, 0x2A, 0x43, 0x18, 0xAA, 0xFE, 0x4B,
	0x96, 0xF0, 0xDA, 0x07, 0x15, 0x5F, 0x8A, 0x51,
	0x34, 0xDA, 0xB8, 0x8E, 0xE2, 0x9E, 0x81, 0x68,
	0x07, 0x6F, 0xCD, 0x78, 0xCA, 0x79, 0x1A, 0xC6,
	0x34, 0x42, 0xA8, 0x1C, 0xD0, 0x69, 0x39, 0x27,
	0xD8, 0x08, 0xE3, 0x35, 0xE8, 0xD8, 0xCB, 0xF2,
	0x12, 0x19, 0x07, 0x50, 0x9A, 0x57, 0x75, 0x9B,
	0x4F, 0x9A, 0x18, 0xFA, 0x3A, 0x7B, 0x33, 0x37,
	0x79, 0xED, 0xDE, 0x7A, 0x45, 0x93, 0x84, 0xF8,
	0x44, 0x4A, 0xDA, 0xEC, 0xFF, 0xEC, 0x95, 0xFD,
	0x55, 0x2B, 0x0C, 0xFC, 0xB6, 0xC7, 0xF6, 0x92,
	0x62, 0x6D, 0xDE, 0x1E, 0xF2, 0x68, 0xA4, 0x0D,
	0x2F, 0x67, 0xB5, 0xC8, 0xAA, 0x38, 0x7F, 0xF7
};

uint8_t rsa_4096_n[] = {
	0xB3, 0x8B, 0x49, 0x60, 0xE6, 0x3B, 0xE6, 0xA8,
	0xDB, 0xA8, 0x9A, 0x82, 0x97, 0x8E, 0xF1, 0xF6,
	0x32, 0x44, 0xE5, 0x57, 0x7D, 0x8C, 0xF5, 0x86,
	0x16, 0xD5, 0xCA, 0x57, 0x59, 0xD4, 0x9C, 0xC8,
	0xD9, 0x36, 0xC3, 0x38, 0xAA, 0x3C, 0xB9, 0xB1,
	0x11, 0xC1, 0x49, 0x7E, 0x5B, 0x51, 0xAF, 0x69,
	0x2F, 0x26, 0x11, 0xE6, 0x89, 0xF7, 0x67, 0x54,
	0x80, 0xC0, 0xB0, 0xF4, 0xC3, 0x65, 0x4F, 0x43,
	0xAF, 0x85, 0xFE, 0x8C, 0x8A, 0xD7, 0x34, 0xE0,
	0x42, 0xA8, 0xAD, 0xA0, 0x5F, 0xD7, 0x65, 0x08,
	0xE0, 0x0B, 0xA0, 0xF7, 0x56, 0xC3, 0x44, 0x3B,
	0xBE, 0x83, 0x3E, 0xA7, 0xD1, 0x00, 0xD4, 0xFB,
	0x36, 0x7E, 0xEB, 0xD6, 0x0B, 0xDB, 0x64, 0x86,
	0x77, 0xFC, 0x7D, 0xEB, 0x94, 0x24, 0x4D, 0xAD,
	0x1A, 0xF8, 0xEE, 0xD1, 0xC6, 0x58, 0x12, 0xC0,
	0x3E, 0x7C, 0x73, 0xF7, 0xF3, 0x58, 0xE9, 0x41,
	0xBC, 0x66, 0x45, 0x8F, 0xF7, 0xBB, 0x97, 0xA4,
	0x9A, 0x98, 0xA1, 0x18, 0x07, 0xE0, 0x2C, 0x1A,
	0x3B, 0x9A, 0xD3, 0x3A, 0x57, 0x3A, 0xE1, 0x80,
	0xE1, 0xFF, 0x43, 0x2A, 0xE5, 0x58, 0x0C, 0xC9,
	0xCA, 0xBF, 0xAB, 0x60, 0x2F, 0x32, 0x5B, 0xCD,
	0xA0, 0x97, 0xE8, 0x7B, 0xC7, 0xA6, 0xD7, 0x4E,
	0x34, 0xA8, 0x7D, 0x60, 0x8A, 0x43, 0xFE, 0xB2,
	0xE4, 0xFF, 0xF1, 0xF4, 0xB8, 0xE7, 0x68, 0x6A,
	0x98, 0x47, 0x5D, 0xB5, 0x1A, 0x6E, 0xBD, 0x08,
	0x17, 0x2A, 0x57, 0x41, 0x77, 0x49, 0x24, 0x8B,
	0x21, 0x55, 0xC8, 0xB9, 0x06, 0xE0, 0xD5, 0x40,
	0xE8, 0xCB, 0x28, 0xF4, 0xC0, 0x0A, 0xDC, 0x9F,
	0xE4, 0x75, 0x8A, 0x1A, 0xC3, 0x64, 0xAB, 0x39,
	0xE4, 0xE1, 0x55, 0x28, 0x98, 0x54, 0x44, 0x15,
	0x3F, 0xEE, 0xC6, 0xAD, 0x4C, 0x53, 0x48, 0xB2,
	0xE3, 0x8F, 0xF5, 0x50, 0xF5, 0xFA, 0x58, 0x33,
	0x97, 0x93, 0x37, 0x30, 0xC8, 0x08, 0x81, 0xBF,
	0x11, 0xEE, 0xE8, 0xFE, 0x38, 0x6D, 0x5B, 0x51,
	0x28, 0x49, 0xA9, 0x83, 0x99, 0x43, 0xAB, 0xF3,
	0xD9, 0x72, 0x20, 0x76, 0x97, 0xB8, 0xEC, 0x24,
	0x11, 0xA2, 0x61, 0x9D, 0x55, 0xCA, 0x04, 0x23,
	0x3C, 0x5A, 0x2C, 0xED, 0xC6, 0xF2, 0x86, 0xD8,
	0x29, 0xD0, 0xE8, 0x37, 0x20, 0x7B, 0x76, 0x52,
	0x9A, 0xA2, 0x44, 0x87, 0x21, 0x26, 0x8D, 0xC0,
	0x15, 0x0B, 0xB7, 0xB0, 0x7E, 0x73, 0x31, 0x3A,
	0x71, 0x3E, 0x58, 0x95, 0xBA, 0xAF, 0x3A, 0xDF,
	0xFA, 0x60, 0x39, 0x58, 0xC5, 0x67, 0xF8, 0x5C,
	0xF2, 0x5B, 0x1D, 0x80, 0xA2, 0x77, 0x56, 0xA3,
	0x0D, 0x1A, 0x50, 0xA1, 0xE4, 0x69, 0x8E, 0xDA,
	0x9A, 0x12, 0x2B, 0xB0, 0xAA, 0x7A, 0x60, 0xF7,
	0xCD, 0x22, 0x6C, 0xB1, 0x16, 0x5C, 0xFC, 0xF9,
	0xCA, 0x83, 0x0A, 0x60, 0x6C, 0xC0, 0xFB, 0x14,
	0x87, 0xF2, 0x49, 0xE5, 0xE0, 0xC7, 0x1C, 0x88,
	0x62, 0x6C, 0x57, 0x12, 0x80, 0x81, 0xDE, 0x76,
	0xC1, 0x23, 0x84, 0xB6, 0xD4, 0x48, 0xB6, 0x7F,
	0x0E, 0x71, 0x23, 0xAE, 0xEF, 0x74, 0xA8, 0x85,
	0x96, 0x03, 0x74, 0x75, 0x54, 0x83, 0xF2, 0x90,
	0xA7, 0xDE, 0x66, 0x46, 0x5E, 0x22, 0x7B, 0x2B,
	0x17, 0x31, 0x8F, 0x8A, 0x49, 0x05, 0x2B, 0x01,
	0x45, 0xFB, 0xA2, 0x83, 0x77, 0x2B, 0xC2, 0x9A,
	0x5B, 0x58, 0x12, 0xAC, 0xCE, 0xE3, 0xAB, 0x62,
	0x81, 0x70, 0x19, 0xE5, 0x48, 0x07, 0xF2, 0x88,
	0x97, 0x12, 0xB7, 0xB8, 0xF3, 0x03, 0xBA, 0x5F,
	0xE1, 0x47, 0xF9, 0xC2, 0xF3, 0x43, 0x4A, 0xB7,
	0x03, 0xC1, 0xD9, 0x46, 0x73, 0x43, 0x82, 0xA0,
	0xA3, 0x53, 0xF4, 0xE0, 0xCB, 0xBE, 0xA2, 0x6A,
	0x4B, 0xBF, 0x21, 0xCE, 0x9E, 0xB5, 0xE7, 0x9D,
	0x47, 0x57, 0xD7, 0xDE, 0x02, 0x7F, 0x20, 0xE5
};

uint8_t rsa_4096_d[] = {
	0x6A, 0xD1, 0xB0, 0xBB, 0x7C, 0xDF, 0x20, 0x91,
	0x4F, 0xF6, 0x94, 0xCE, 0xA3, 0x7B, 0x01, 0x4B,
	0xD7, 0x86, 0x93, 0xE8, 0x24, 0xA3, 0x4B, 0xA4,
	0x16, 0x4B, 0xE5, 0xD1, 0x68, 0x79, 0x8D, 0x3A,
	0x15, 0xB9, 0x76, 0x16, 0x6D, 0x7A, 0x29, 0x84,
	0x46, 0xAA, 0xF7, 0x9D, 0xBC, 0x98, 0xF1, 0xC2,
	0xA3, 0xB1, 0x83, 0xAE, 0xE4, 0x60, 0x94, 0x52,
	0x7B, 0x33, 0xA9, 0x54, 0x46, 0x38, 0x2D, 0x1B,
	0x78, 0xFF, 0x40, 0x7D, 0xBF, 0x50, 0xE0, 0x7D,
	0x98, 0x4B, 0x20, 0xD9, 0xAC, 0x8B, 0xCA, 0xE9,
	0xA7, 0xDA, 0x63, 0x4F, 0x24, 0x88, 0x92, 0x3C,
	0xF5, 0x50, 0xC2, 0x63, 0x37, 0x7E, 0xC6, 0x38,
	0x1B, 0xA9, 0x11, 0x88, 0xCC, 0x8F, 0x1F, 0xD4,
	0xBC, 0xE8, 0x34, 0xC6, 0x86, 0xE1, 0xBE, 0x71,
	0x01, 0xFE, 0x1E, 0xA0, 0x21, 0xE0, 0x5E, 0x6F,
	0x8F, 0xFD, 0x9D, 0x45, 0x64, 0xBB, 0x7E, 0x33,
	0x84, 0xF2, 0x57, 0xEA, 0x9A, 0x9A, 0x3A, 0x53,
	0x4D, 0x43, 0x07, 0x7C, 0xF3, 0x9A, 0x94, 0xC2,
	0x9A, 0xB9, 0xB7, 0x78, 0x1B, 0x53, 0xC5, 0xBC,
	0x57, 0x38, 0xF6, 0x6E, 0x3B, 0xFA, 0xD1, 0xC8,
	0xF0, 0xDE, 0x6E, 0x08, 0x90, 0xAB, 0xE6, 0x60,
	0x85, 0x6E, 0x3B, 0x7C, 0x01, 0x41, 0xAB, 0x11,
	0x35, 0x55, 0x15, 0x1A, 0xED, 0xC8, 0x1C, 0x6D,
	0xB4, 0xBE, 0xED, 0xE6, 0x0A, 0x68, 0x6B, 0x00,
	0x18, 0x4F, 0x45, 0x5A, 0x2D, 0x3A, 0xBB, 0x2E,
	0x68, 0x11, 0xE1, 0xCD, 0xEA, 0x39, 0x53, 0x0B,
	0x8F, 0xAE, 0xA8, 0xF8, 0x24, 0x36, 0x79, 0xC9,
	0xDF, 0x76, 0x97, 0x8C, 0x5E, 0x01, 0x58, 0x57,
	0xAC, 0xA5, 0x9D, 0x9F, 0xE4, 0xA6, 0x2D, 0x15,
	0x09, 0xAE, 0x62, 0x6A, 0xFF, 0x8E, 0x0A, 0xDF,
	0x95, 0xA4, 0xEB, 0x01, 0x49, 0xCA, 0xB7, 0x12,
	0xEF, 0x3E, 0xC3, 0xD6, 0x02, 0x32, 0x8A, 0x6C,
	0x50, 0x21, 0xBA, 0x1B, 0x35, 0xB8, 0x58, 0x3D,
	0x9A, 0x90, 0x40, 0x55, 0x03, 0xF5, 0xFA, 0x0F,
	0x42, 0x4C, 0x72, 0x86, 0x23, 0xFC, 0x81, 0xD3,
	0xDD, 0x7B, 0xFF, 0x1B, 0xF7, 0x8C, 0x8E, 0x2E,
	0xBD, 0x03, 0xA5, 0x11, 0x2D, 0xEB, 0x65, 0x89,
	0x98, 0x6E, 0x49, 0xBD, 0x30, 0x07, 0x1A, 0xD8,
	0x41, 0xA3, 0xCC, 0xA8, 0x07, 0x6C, 0xCF, 0xC7,
	0x94, 0x63, 0x30, 0xB1, 0xFD, 0x1C, 0x21, 0x2A,
	0xD3, 0xEB, 0xD3, 0x7D, 0x9A, 0x4D, 0x0A, 0x96,
	0x95, 0xB8, 0x16, 0x8A, 0x08, 0x89, 0x32, 0x7D,
	0x52, 0x6F, 0x16, 0xD1, 0x56, 0x37, 0xFA, 0x9D,
	0xBF, 0x04, 0xB0, 0xB8, 0x3D, 0xD8, 0xB5, 0xC4,
	0x05, 0x2D, 0xC5, 0x38, 0xB6, 0xCA, 0xE9, 0xC9,
	0x2E, 0xC9, 0x70, 0x10, 0x7A, 0x2F, 0x1E, 0xE4,
	0xD4, 0x7A, 0x65, 0xCC, 0xA5, 0xB9, 0x59, 0x6E,
	0x42, 0x74, 0x91, 0x9B, 0xE7, 0xD1, 0xEC, 0x90,
	0xE4, 0x50, 0xFE, 0x58, 0x58, 0xDC, 0x2E, 0x01,
	0xE8, 0x4E, 0xBD, 0x92, 0x07, 0xD8, 0xEA, 0x20,
	0xFA, 0x37, 0x14, 0x0E, 0x89, 0xD0, 0x10, 0xD6,
	0x50, 0x1F, 0x22, 0x61, 0x97, 0x18, 0x04, 0xDE,
	0x73, 0x68, 0x0F, 0xE6, 0x1C, 0x23, 0x5C, 0x8F,
	0x4E, 0x63, 0x1F, 0xF0, 0x6D, 0xBD, 0xEE, 0x66,
	0x6D, 0xBB, 0x9A, 0xFB, 0xFD, 0xA3, 0xB9, 0x71,
	0xC3, 0x29, 0x0D, 0x7B, 0xDE, 0xF5, 0xC5, 0x78,
	0x5A, 0x07, 0x1B, 0xE9, 0x4A, 0xE1, 0x8A, 0x0B,
	0x2E, 0xD8, 0xAE, 0x67, 0x9A, 0xBA, 0xA6, 0x10,
	0x85, 0x28, 0xA8, 0x5E, 0x31, 0x7F, 0x87, 0xA8,
	0x99, 0xC5, 0x89, 0xF4, 0xA8, 0xAD, 0x42, 0x90,
	0xA6, 0xCA, 0x1E, 0xF9, 0xF1, 0x4D, 0x8D, 0x0A,
	0x7A, 0x66, 0xDC, 0x75, 0x0B, 0x69, 0xB1, 0x9C,
	0xB2, 0x58, 0x28, 0xC3, 0xEA, 0xF0, 0x42, 0x21,
	0x5C, 0x09, 0xAA, 0xD4, 0xA9, 0x54, 0xE8, 0x55
};

uint8_t rsa_4096_p[] = {
	0xE0, 0x41, 0xBD, 0xF1, 0xC9, 0xB5, 0x69, 0xAC,
	0xF5, 0xE8, 0x02, 0x2D, 0x21, 0x1B, 0x87, 0x1B,
	0x5F, 0x29, 0x21, 0x41, 0xA5, 0x89, 0xFD, 0x0F,
	0x6D, 0xCB, 0x59, 0x47, 0x6B, 0x1C, 0x1D, 0xA4,
	0x01, 0x8D, 0xD7, 0xA1, 0xE1, 0x08, 0x39, 0x32,
	0x74, 0x5E, 0xF3, 0xC6, 0x6C, 0xF7, 0xFF, 0x31,
	0x3E, 0xED, 0x4C, 0xB1, 0x68, 0x1D, 0xEF, 0x9D,
	0x29, 0xCC, 0x3F, 0xE8, 0x7A, 0xF7, 0xAD, 0x19,
	0xE9, 0xEF, 0x34, 0x56, 0x62, 0xC9, 0xC4, 0xF4,
	0xE6, 0xE7, 0x07, 0xAA, 0x4E, 0x99, 0x49, 0x63,
	0x4C, 0x08, 0x64, 0x71, 0xA5, 0x5B, 0x67, 0x46,
	0xC2, 0xAE, 0xEF, 0x87, 0x71, 0xEF, 0x21, 0xA2,
	0xEE, 0x8A, 0xB4, 0xDE, 0xC4, 0xC2, 0x04, 0x3C,
	0x70, 0xCF, 0xBA, 0x89, 0xE5, 0xEB, 0x2F, 0x62,
	0xEA, 0x07, 0xC7, 0x4B, 0xD4, 0x16, 0x67, 0x69,
	0x12, 0xA9, 0x58, 0x9F, 0xB3, 0xED, 0x70, 0x28,
	0x8F, 0x8A, 0x03, 0xD1, 0x91, 0xC5, 0x8A, 0x88,
	0x96, 0xE8, 0xB2, 0x0F, 0x1E, 0xDE, 0x91, 0x80,
	0xCE, 0xD3, 0x03, 0x59, 0xF7, 0x5F, 0x48, 0xAF,
	0xE9, 0x7C, 0x46, 0xEE, 0x59, 0xC9, 0x27, 0x1E,
	0x71, 0x37, 0x55, 0x4A, 0xD7, 0x05, 0x56, 0x17,
	0x84, 0x8F, 0xD3, 0x04, 0x1C, 0xD6, 0x30, 0x47,
	0xF6, 0x46, 0x2C, 0x0E, 0x66, 0xE1, 0x83, 0x9F,
	0x63, 0xC6, 0x12, 0xD4, 0xA3, 0x57, 0xF5, 0xE5,
	0x76, 0x35, 0x6A, 0xAA, 0xE7, 0xC6, 0x4A, 0xC0,
	0xBF, 0xD9, 0xD6, 0x5E, 0xDF, 0x4B, 0x2F, 0x34,
	0xDA, 0xDB, 0xDF, 0x69, 0x06, 0x20, 0xC8, 0x95,
	0xCA, 0x44, 0xD9, 0x61, 0xDA, 0x05, 0xB1, 0x36,
	0x2B, 0x4A, 0xD5, 0xDA, 0xAC, 0xB9, 0x0F, 0xF5,
	0x86, 0x33, 0x5E, 0xCD, 0x7E, 0x1D, 0x7A, 0x16,
	0x00, 0xCB, 0x1A, 0xB3, 0x72, 0x69, 0x5B, 0x6E,
	0xC7, 0x71, 0x76, 0x21, 0xDB, 0xBE, 0x93, 0x57
};

uint8_t rsa_4096_q[] = {
	0xCC, 0xF5, 0x51, 0x29, 0xD3, 0xB9, 0x24, 0xC8,
	0x38, 0xA7, 0x6C, 0xD3, 0x69, 0xD4, 0x6E, 0x9C,
	0xB8, 0x13, 0xFE, 0x3B, 0x39, 0xBA, 0xF1, 0xEB,
	0x10, 0x18, 0x47, 0xD3, 0x1D, 0x09, 0x13, 0x50,
	0x03, 0xAB, 0x2F, 0xC2, 0x39, 0x43, 0x1C, 0xDA,
	0x6E, 0x99, 0x08, 0x88, 0x3D, 0xE8, 0xA0, 0x54,
	0x6E, 0x35, 0x28, 0x37, 0xD4, 0xEB, 0x95, 0xCB,
	0x41, 0xD8, 0xEE, 0xC1, 0x4A, 0x66, 0xCD, 0x38,
	0xC2, 0x24, 0x7E, 0x82, 0xA3, 0x94, 0x39, 0x29,
	0x27, 0xBB, 0xF5, 0x70, 0xA8, 0x65, 0x5E, 0x0F,
	0x2A, 0xC2, 0x43, 0xE5, 0xFB, 0x87, 0x6F, 0xD2,
	0x0B, 0x48, 0x76, 0x73, 0xA2, 0x77, 0x2D, 0xA9,
	0x70, 0xC1, 0xDF, 0x47, 0xA3, 0x2D, 0xEA, 0x8A,
	0x75, 0xE7, 0x09, 0x54, 0x73, 0x22, 0x9C, 0x69,
	0x3C, 0x88, 0x6A, 0x31, 0x6D, 0x2C, 0xEC, 0xBF,
	0x03, 0x59, 0x7B, 0x04, 0xCA, 0x9E, 0xCA, 0xBD,
	0xA3, 0x36, 0x6E, 0x07, 0x64, 0x88, 0x05, 0x9B,
	0x24, 0x59, 0x6F, 0x5D, 0xF6, 0xE8, 0x56, 0x97,
	0xDB, 0xE6, 0x2A, 0xB2, 0xF8, 0xCC, 0x71, 0xAC,
	0x7F, 0x74, 0x3B, 0x64, 0x12, 0x6D, 0x01, 0xF2,
	0xB3, 0x61, 0x27, 0x16, 0xEC, 0xA7, 0x69, 0x75,
	0xE5, 0x14, 0xED, 0x4D, 0x78, 0xA3, 0x22, 0x90,
	0xBE, 0x0A, 0x82, 0xF1, 0x44, 0x14, 0x99, 0x2B,
	0xD1, 0x80, 0x3D, 0xAD, 0xC9, 0x83, 0xDD, 0xF2,
	0x76, 0xD2, 0xCA, 0xE1, 0xA0, 0xA9, 0x03, 0xF9,
	0x1E, 0x78, 0xBE, 0x3C, 0x0B, 0xCA, 0xF5, 0x8F,
	0x3C, 0xE9, 0x8D, 0x12, 0x3A, 0xA6, 0xC8, 0x5F,
	0x65, 0x51, 0xC5, 0x70, 0x07, 0xFE, 0x47, 0x7A,
	0xC8, 0x7E, 0x03, 0x8B, 0x9A, 0x94, 0xAC, 0xC6,
	0x20, 0xDE, 0x12, 0x25, 0x81, 0x05, 0x34, 0x4A,
	0x0C, 0xFB, 0x37, 0x65, 0x50, 0x5E, 0x8E, 0x7E,
	0xC8, 0x6A, 0xC0, 0x86, 0xF6, 0x55, 0x64, 0x23
};

uint8_t rsa_4096_dp[] = {
	0x2F, 0x7C, 0x1C, 0x31, 0x37, 0x69, 0xCF, 0x6F,
	0x8D, 0x3E, 0x4C, 0x3F, 0xAC, 0x13, 0xFD, 0x1E,
	0xC1, 0x9E, 0x9E, 0xE9, 0x1C, 0x99, 0x44, 0x59,
	0x61, 0x01, 0x3E, 0xED, 0x4D, 0x73, 0xCD, 0x9E,
	0xED, 0xA9, 0x50, 0x30, 0x79, 0xCA, 0xD8, 0xF9,
	0xA3, 0x04, 0x7C, 0x0F, 0xD7, 0x01, 0x08, 0x2B,
	0x30, 0x4C, 0xE5, 0x01, 0x67, 0xAF, 0x77, 0x0E,
	0x4B, 0x4C, 0x71, 0x77, 0xD3, 0x99, 0xE0, 0x30,
	0x6D, 0x85, 0x76, 0x0A, 0x98, 0xAE, 0x6A, 0xA3,
	0x04, 0xC5, 0x84, 0xAC, 0xFE, 0x29, 0x9D, 0x0D,
	0x86, 0x8A, 0xFC, 0x61, 0xC8, 0x06, 0xBB, 0xAE,
	0x93, 0x08, 0xA1, 0xB5, 0x87, 0x5D, 0x80, 0x3C,
	0xD4, 0xCF, 0xD0, 0x0E, 0x9F, 0x91, 0x09, 0x7E,
	0x96, 0xD0, 0x95, 0x8A, 0x1F, 0x82, 0x16, 0x2D,
	0x96, 0xAA, 0x80, 0xFB, 0xC0, 0x73, 0xE1, 0xFF,
	0xB0, 0xB0, 0xE5, 0x10, 0x23, 0xF4, 0x31, 0xDC,
	0x94, 0xD0, 0x3F, 0x90, 0xBF, 0x92, 0x19, 0x8C,
	0x64, 0x8F, 0xEF, 0x2C, 0x1E, 0x78, 0x38, 0x4D,
	0x12, 0xFE, 0x41, 0x66, 0x6A, 0x67, 0xE5, 0xA7,
	0x42, 0x04, 0x4B, 0xAC, 0xAA, 0x9C, 0x5A, 0x49,
	0x2A, 0xE5, 0xF1, 0x8C, 0x80, 0x4D, 0x23, 0xF6,
	0xA4, 0xDE, 0x23, 0x6B, 0x6A, 0x83, 0xBC, 0x03,
	0x70, 0xD5, 0x58, 0xFC, 0xCF, 0xB2, 0x0E, 0xC1,
	0xD0, 0x49, 0x9F, 0xB1, 0x20, 0xC9, 0x3E, 0x4B,
	0x11, 0x25, 0xAC, 0x69, 0x75, 0xDC, 0x59, 0xF5,
	0xC8, 0x69, 0xE2, 0xE7, 0x81, 0xD6, 0x94, 0xAF,
	0x57, 0x6C, 0x59, 0x39, 0x0E, 0xD0, 0x20, 0x48,
	0xFF, 0x64, 0x66, 0xB7, 0x3E, 0x88, 0x18, 0x07,
	0x05, 0x51, 0xBA, 0x48, 0xAC, 0x6C, 0x1F, 0x41,
	0xF8, 0xE1, 0xA5, 0xC0, 0x53, 0x65, 0x00, 0x75,
	0xEA, 0x43, 0x17, 0x6B, 0x49, 0xDD, 0x9F, 0x3B,
	0xAC, 0xC5, 0x8C, 0xA3, 0x0C, 0xB9, 0xA4, 0xCF
};

uint8_t rsa_4096_dq[] = {
	0x57, 0x5A, 0x87, 0x09, 0x28, 0xAF, 0xD4, 0x39,
	0x71, 0xCC, 0x09, 0xD9, 0xE1, 0x55, 0x24, 0xFF,
	0xAE, 0x84, 0xF6, 0xEA, 0x0F, 0x24, 0xDA, 0x4E,
	0xB1, 0x41, 0x67, 0xFB, 0x56, 0x78, 0xB3, 0xBE,
	0x7A, 0x91, 0xCF, 0x7D, 0x1C, 0x22, 0xBA, 0x7D,
	0x6E, 0x7D, 0xD2, 0xE1, 0x1E, 0x61, 0xB3, 0x53,
	0xC8, 0xD4, 0xE7, 0x1B, 0x44, 0xA8, 0x53, 0xE3,
	0x99, 0x60, 0xF8, 0x01, 0x71, 0xD0, 0x76, 0xCF,
	0x26, 0x0F, 0x9F, 0xCB, 0xD6, 0x24, 0x2A, 0x68,
	0x9C, 0x02, 0xC4, 0x0D, 0x0B, 0xF8, 0x88, 0x2A,
	0x36, 0xB3, 0x2D, 0x75, 0x2B, 0xCB, 0x01, 0xA1,
	0xA8, 0x25, 0x6E, 0x36, 0xC2, 0x9B, 0xC0, 0xDE,
	0x62, 0xAC, 0x7E, 0x99, 0x6D, 0xB6, 0xF8, 0x2B,
	0xA3, 0x2C, 0xA1, 0x11, 0x59, 0x30, 0xFB, 0x30,
	0xEF, 0x17, 0xC5, 0x0A, 0xE3, 0xD9, 0x2D, 0xDE,
	0x0B, 0x73, 0x6B, 0xB7, 0x13, 0x14, 0xB2, 0x9C,
	0x38, 0x9F, 0xCE, 0x2D, 0x60, 0x6F, 0x88, 0xD4,
	0x22, 0x9D, 0xEB, 0x95, 0x44, 0xD2, 0xA9, 0x75,
	0x77, 0xC7, 0x95, 0x93, 0x49, 0xEE, 0xF8, 0xD3,
	0xE8, 0x4E, 0x85, 0xB1, 0x95, 0x18, 0xD8, 0xA7,
	0xB4, 0x44, 0x48, 0x00, 0xC1, 0x44, 0x68, 0xF2,
	0x52, 0x7C, 0xA4, 0xD7, 0x4B, 0xFF, 0x5B, 0x90,
	0x0D, 0x2F, 0x35, 0xB7, 0xD6, 0xA8, 0x60, 0xD0,
	0x08, 0x2E, 0x7C, 0x1B, 0x41, 0xB3, 0xEE, 0x38,
	0x94, 0xE4, 0x2A, 0x8C, 0x17, 0x89, 0x71, 0xA4,
	0x0F, 0x94, 0xAE, 0x9F, 0xB0, 0xF7, 0x03, 0xC9,
	0xD4, 0xD0, 0x45, 0xCB, 0xEB, 0x2B, 0x82, 0x63,
	0x06, 0x2F, 0xDF, 0xD2, 0x6B, 0xD5, 0xB8, 0x69,
	0x60, 0x62, 0x34, 0xE8, 0x9F, 0x2D, 0x96, 0xA5,
	0xAB, 0x04, 0x7A, 0xFF, 0x79, 0x09, 0xDA, 0xCB,
	0x64, 0xD4, 0xFD, 0x3B, 0x35, 0x11, 0xD7, 0xF1,
	0xB9, 0x41, 0xA6, 0x64, 0xDF, 0x40, 0x6D, 0xB9
};

uint8_t rsa_4096_qinv[] = {
	0xD1, 0x0C, 0x91, 0x8D, 0xA9, 0xF2, 0x6D, 0xA9,
	0x4D, 0xFF, 0x3B, 0x09, 0x24, 0x3C, 0x8C, 0xC3,
	0xD4, 0x39, 0x02, 0x6D, 0xE6, 0x2B, 0x9E, 0x9F,
	0x37, 0xAC, 0x60, 0xBB, 0xD7, 0xA9, 0x52, 0xCB,
	0x07, 0x84, 0x94, 0xBD, 0x73, 0x7E, 0xCC, 0x3A,
	0x65, 0x0C, 0x93, 0xC4, 0x2E, 0xD7, 0xF6, 0x49,
	0x02, 0x07, 0xAE, 0x99, 0x6B, 0x3C, 0xD1, 0xFF,
	0x1F, 0x4D, 0x63, 0x9D, 0x61, 0xDD, 0xD1, 0xE7,
	0x12, 0x8D, 0x56, 0x3C, 0x1C, 0x16, 0xC8, 0xB3,
	0x9D, 0x94, 0xD5, 0xDE, 0x5E, 0x93, 0x7F, 0xE6,
	0x5A, 0x38, 0xB8, 0x19, 0xE4, 0x69, 0xF8, 0x8C,
	0x3C, 0xE0, 0x25, 0x21, 0xE2, 0xAD, 0xA9, 0xE3,
	0x46, 0xE6, 0xA1, 0xBD, 0x51, 0x27, 0xC7, 0xBD,
	0xB2, 0x1D, 0xA2, 0xC6, 0x11, 0xE3, 0x5F, 0x6C,
	0x89, 0xE7, 0xDD, 0x66, 0xA0, 0x66, 0xCB, 0x23,
	0x3E, 0xF9, 0x6B, 0xAD, 0x1A, 0xD3, 0x99, 0x94,
	0x0C, 0xAD, 0x05, 0x5A, 0xDF, 0x5C, 0x58, 0x79,
	0xF8, 0x30, 0xA8, 0x08, 0x3C, 0xA6, 0xD6, 0xC0,
	0x58, 0x58, 0xC2, 0x66, 0x03, 0x0A, 0x33, 0xBF,
	0xB4, 0xAD, 0x83, 0xB5, 0xCC, 0x92, 0x9F, 0x2F,
	0x6C, 0xA2, 0x1E, 0x50, 0x29, 0x54, 0x2B, 0x8A,
	0xEB, 0xE7, 0x6B, 0x69, 0x44, 0xE1, 0x86, 0x3E,
	0x39, 0x47, 0x3B, 0x6E, 0xD9, 0xAD, 0x92, 0x6A,
	0x7D, 0xBF, 0xE2, 0xC7, 0x28, 0xE2, 0x3C, 0x74,
	0xF6, 0x9B, 0xB0, 0xE0, 0x54, 0xF1, 0x9F, 0x14,
	0x6C, 0xE1, 0x9E, 0x1D, 0x23, 0x6B, 0x65, 0x34,
	0x30, 0xA7, 0x1D, 0xC4, 0xA7, 0x4A, 0xE2, 0x0E,
	0x0D, 0x14, 0x13, 0x31, 0x66, 0xA1, 0x8A, 0xDF,
	0x6E, 0xF7, 0xFE, 0xD9, 0x5C, 0xC4, 0x64, 0x35,
	0xFF, 0x4C, 0x96, 0x23, 0x2B, 0xD5, 0x64, 0x03,
	0xCC, 0x39, 0xFB, 0x16, 0xAD, 0xF2, 0x24, 0xB4,
	0xFD, 0xEB, 0x8A, 0xBA, 0xF4, 0x91, 0x31, 0xBF
};

uint8_t rsa_8192_n[] = {
	0xcf, 0xb0, 0x0e, 0x88, 0x72, 0x2d, 0x19, 0x63,
	0x7f, 0xe0, 0x13, 0x9a, 0xe1, 0x01, 0xec, 0x1f,
	0x37, 0xf1, 0x73, 0x76, 0x19, 0xa8, 0xc4, 0x7d,
	0x0f, 0x04, 0xe5, 0x86, 0xe3, 0x2e, 0x36, 0xb4,
	0xdb, 0x7a, 0xa8, 0x2a, 0x5d, 0x85, 0x62, 0x43,
	0x75, 0xe0, 0x0d, 0xf4, 0x0b, 0x6a, 0x1c, 0x56,
	0x20, 0xbc, 0x01, 0x0f, 0x26, 0xde, 0x12, 0xeb,
	0x74, 0x88, 0xc5, 0x5a, 0xcc, 0x14, 0x91, 0x66,
	0x13, 0x9d, 0xb5, 0x32, 0xa1, 0xa3, 0x67, 0x6f,
	0x14, 0x90, 0x83, 0xa4, 0xac, 0x4a, 0xa1, 0xf9,
	0x1e, 0xff, 0xa5, 0xe1, 0xe3, 0x24, 0xcb, 0x86,
	0x9a, 0x13, 0x66, 0x23, 0xa4, 0xb6, 0x46, 0x21,
	0xcb, 0x26, 0x76, 0xdf, 0x0b, 0x5d, 0xe6, 0xc2,
	0x0a, 0x2b, 0xb3, 0xfe, 0x01, 0x9f, 0x15, 0xfa,
	0x69, 0xea, 0x80, 0x46, 0x64, 0x10, 0xf1, 0x29,
	0xbf, 0xad, 0x60, 0xa7, 0x17, 0xe0, 0xb6, 0xdb,
	0xa1, 0x96, 0x92, 0x4a, 0x02, 0x24, 0xdd, 0x1b,
	0x95, 0x5f, 0xfa, 0x8b, 0xce, 0x9a, 0x90, 0x61,
	0xd1, 0xa7, 0x75, 0x0f, 0xb5, 0x62, 0xcb, 0xee,
	0xb8, 0x2c, 0xed, 0x82, 0xdb, 0x0b, 0x43, 0x02,
	0xa0, 0x07, 0x73, 0x50, 0x2f, 0xa1, 0x80, 0xd9,
	0xbb, 0x55, 0x62, 0x06, 0xb1, 0xc1, 0xf6, 0xdf,
	0x91, 0x50, 0xa1, 0x8a, 0x7a, 0x5e, 0x2c, 0xb9,
	0x71, 0x84, 0xe3, 0x99, 0x1b, 0xe6, 0x53, 0x0e,
	0xd2, 0x4d, 0x95, 0xf7, 0xd4, 0x28, 0x2a, 0x9f,
	0x33, 0x17, 0x3c, 0x5a, 0x92, 0xbb, 0xc3, 0x26,
	0x68, 0x84, 0xd8, 0x96, 0x55, 0x1f, 0xb2, 0x66,
	0x4f, 0x9c, 0x74, 0xb7, 0x25, 0xd1, 0xf5, 0xc2,
	0xf3, 0x41, 0x58, 0xe8, 0x3d, 0xf3, 0x56, 0xa9,
	0xbe, 0xa2, 0x94, 0xfd, 0x96, 0x17, 0x1a, 0x2d,
	0x4e, 0xdc, 0xc8, 0x23, 0xd3, 0x5c, 0x5f, 0x15,
	0xa0, 0x3e, 0x19, 0x25, 0xf8, 0x2f, 0x4c, 0x02,
	0x64, 0xf9, 0x0d, 0x13, 0x88, 0x43, 0x55, 0xb4,
	0x7d, 0x8b, 0xfb, 0xb4, 0x0f, 0x49, 0x1a, 0xc6,
	0xa2, 0xc8, 0x93, 0x52, 0x68, 0x00, 0x15, 0x74,
	0xe4, 0xd7, 0x7b, 0xe4, 0x1f, 0x32, 0xb5, 0x68,
	0x33, 0x84, 0xea, 0xd1, 0xc0, 0xb0, 0xa6, 0xf0,
	0x80, 0x2b, 0x1d, 0x1c, 0x06, 0x83, 0xdd, 0x9b,
	0xe3, 0xde, 0xb0, 0x73, 0xd5, 0x35, 0x89, 0x5c,
	0xc4, 0x64, 0x8a, 0x3e, 0xf8, 0x43, 0xc0, 0x38,
	0x6f, 0xa2, 0xe3, 0x67, 0x7d, 0xaf, 0xa2, 0xf5,
	0x5e, 0x2a, 0x11, 0xf0, 0x8d, 0xe2, 0xc0, 0x03,
	0xb7, 0x51, 0xf9, 0x21, 0xea, 0x5c, 0xaf, 0xcf,
	0x02, 0x3c, 0xa2, 0xba, 0xb6, 0x2a, 0xb1, 0x44,
	0xa2, 0xc7, 0xfc, 0x36, 0x61, 0x67, 0x67, 0x66,
	0xec, 0x42, 0xeb, 0xc9, 0xc5, 0xcb, 0x52, 0x1f,
	0xba, 0x23, 0x0b, 0x52, 0x03, 0x8f, 0x5f, 0xe0,
	0xea, 0xe4, 0xa5, 0x54, 0x04, 0xee, 0xfe, 0xd0,
	0x55, 0x1c, 0x71, 0x08, 0xb0, 0x93, 0xeb, 0xc2,
	0x42, 0x9a, 0xc3, 0x84, 0x08, 0x00, 0xfc, 0x10,
	0x43, 0x84, 0x67, 0x83, 0xde, 0x67, 0x73, 0x00,
	0x7c, 0x03, 0xe8, 0x6a, 0x2f, 0xb4, 0x2c, 0x9d,
	0x87, 0xfc, 0x72, 0x91, 0xf2, 0x1f, 0xcd, 0x1c,
	0x8a, 0xae, 0xcb, 0x94, 0x63, 0x8b, 0xd7, 0xd7,
	0x31, 0x6f, 0x97, 0xab, 0x27, 0xa5, 0x4d, 0xf7,
	0x57, 0x65, 0xcf, 0x70, 0x06, 0xca, 0x99, 0xa8,
	0x48, 0x76, 0xc3, 0x8c, 0xa6, 0xcb, 0x79, 0xe0,
	0xf3, 0x88, 0x13, 0x57, 0xde, 0xe0, 0x3f, 0xf5,
	0x10, 0xa3, 0x5e, 0x01, 0xe7, 0x39, 0x56, 0x88,
	0x17, 0xf2, 0xa1, 0x91, 0x01, 0x71, 0xeb, 0xcf,
	0xea, 0xc3, 0xa9, 0xc4, 0x70, 0x45, 0xf1, 0x20,
	0x16, 0x29, 0x85, 0x36, 0x11, 0x92, 0x71, 0x2f,
	0x9c, 0x08, 0x08, 0x90, 0x95, 0xcf, 0x0e, 0xb2,
	0x8f, 0xa2, 0xde, 0x17, 0x3e, 0x2b, 0x26, 0xaf,
	0x05, 0xa8, 0x7b, 0x5e, 0x10, 0x95, 0x7d, 0x8e,
	0xb5, 0x5d, 0xee, 0x37, 0xe5, 0x20, 0xe9, 0xfa,
	0x7b, 0x7c, 0xd5, 0x05, 0x95, 0x21, 0x29, 0x90,
	0x81, 0xed, 0x1b, 0x86, 0x5c, 0xad, 0xc7, 0x9f,
	0x8a, 0xbd, 0x8a, 0xcd, 0x94, 0x09, 0x36, 0x30,
	0x4c, 0xf7, 0xb4, 0x42, 0xab, 0x15, 0xb7, 0x4e,
	0x3b, 0x3c, 0x1a, 0xba, 0x34, 0xe0, 0x0a, 0xc8,
	0xe4, 0x66, 0xf6, 0xff, 0x2e, 0xb4, 0x83, 0x93,
	0xdb, 0xeb, 0x0c, 0x90, 0xab, 0x3a, 0xa1, 0xc0,
	0xd3, 0x7a, 0x71, 0x1c, 0xc4, 0x0c, 0xa9, 0x90,
	0x50, 0x9f, 0xa3, 0x36, 0xd8, 0x89, 0x92, 0xc7,
	0xf7, 0xf0, 0xd5, 0xbb, 0xb5, 0xd4, 0x88, 0xf5,
	0x35, 0x70, 0x58, 0x3f, 0xdd, 0xa9, 0x5f, 0xfb,
	0x45, 0x00, 0x13, 0x5f, 0x15, 0xe0, 0x8c, 0xe1,
	0xc3, 0x79, 0xed, 0x9e, 0x64, 0x99, 0xaf, 0x61,
	0x87, 0x83, 0xf8, 0x37, 0xae, 0xb1, 0x61, 0xc9,
	0xe9, 0x3c, 0xe7, 0x93, 0x35, 0xde, 0x72, 0xfc,
	0x68, 0x79, 0x2d, 0x19, 0xc3, 0xd3, 0x2d, 0x7f,
	0xc4, 0xbd, 0xb2, 0x7f, 0xe7, 0x79, 0x42, 0x93,
	0x38, 0xf2, 0xe3, 0x0e, 0x07, 0x74, 0xbc, 0x85,
	0x64, 0xbc, 0xbf, 0x88, 0x51, 0x5f, 0x51, 0xef,
	0x85, 0x74, 0x4b, 0x41, 0x52, 0x6d, 0x45, 0x30,
	0xca, 0x48, 0xb8, 0x2e, 0xfe, 0xa1, 0x65, 0x62,
	0x55, 0x0a, 0xfe, 0xcc, 0xc8, 0x24, 0xcd, 0xc9,
	0x26, 0xb3, 0x4f, 0xdf, 0xcf, 0xb3, 0xa7, 0x4a,
	0xd9, 0xdd, 0xe0, 0x03, 0x8c, 0x9f, 0xc6, 0xc7,
	0x5c, 0x03, 0x4e, 0xe9, 0x51, 0x73, 0x51, 0x08,
	0xb1, 0x39, 0xf6, 0x6b, 0xfc, 0xac, 0xf1, 0xbe,
	0x07, 0x48, 0x11, 0xa9, 0x69, 0xa1, 0x36, 0x2b,
	0xac, 0x01, 0x95, 0x18, 0x67, 0x77, 0x82, 0xfc,
	0x2b, 0xbb, 0xba, 0x05, 0x3f, 0xc9, 0x97, 0x5b,
	0x32, 0x6a, 0x7a, 0xc0, 0x23, 0x11, 0x9d, 0xe1,
	0xda, 0xc7, 0x63, 0x80, 0x07, 0x82, 0xf3, 0xf0,
	0x85, 0xa6, 0x93, 0xdd, 0x77, 0xdf, 0x63, 0x10,
	0x5a, 0xa2, 0xd4, 0xd0, 0xc6, 0xab, 0x0f, 0x0c,
	0xaf, 0x52, 0xb7, 0x52, 0xb3, 0x7a, 0x7c, 0xc6,
	0x41, 0x79, 0x98, 0x93, 0x7a, 0x6e, 0xec, 0xe8,
	0xe7, 0x3e, 0x9a, 0xbc, 0x9b, 0x35, 0xc7, 0xc3,
	0x52, 0x99, 0xbb, 0x02, 0x6a, 0x1a, 0xd5, 0x98,
	0x66, 0x0a, 0x76, 0x63, 0x08, 0xdd, 0x0b, 0xe9,
	0x11, 0x11, 0xb3, 0x2b, 0x86, 0x93, 0x92, 0xec,
	0xdb, 0xae, 0xf8, 0x3b, 0x1c, 0xac, 0xcf, 0x7f,
	0x36, 0x6d, 0x2f, 0x9b, 0x4f, 0x79, 0x85, 0x2b,
	0xc7, 0xc9, 0xa1, 0x93, 0x78, 0x81, 0xa0, 0x1a,
	0xb0, 0x3e, 0x91, 0x17, 0xb7, 0xcd, 0x4e, 0xd4,
	0x5a, 0x15, 0x3d, 0x5d, 0xbc, 0xfc, 0x02, 0x22,
	0xb3, 0xc2, 0xb5, 0xa6, 0xe4, 0x73, 0xf4, 0x66,
	0xc5, 0xfc, 0x71, 0xc8, 0x54, 0xfe, 0xfd, 0x9e,
	0x1d, 0x04, 0xe9, 0xc3, 0x39, 0x10, 0x57, 0x87,
	0x38, 0xe1, 0xb4, 0x8e, 0x3a, 0x61, 0x78, 0x9f,
	0x59, 0xee, 0xa1, 0x2f, 0x67, 0x65, 0x78, 0x66,
	0x07, 0x7f, 0xd5, 0x60, 0x56, 0x5e, 0x68, 0xe4,
	0xd1, 0x34, 0xf7, 0xd7, 0xf0, 0xc9, 0x8b, 0x5b,
	0x20, 0x6a, 0x3f, 0x4a, 0xa9, 0x12, 0xa0, 0xba,
	0xa1, 0x6c, 0xe6, 0xcc, 0xed, 0xfd, 0xc1, 0x25,
	0x7d, 0xa5, 0x83, 0x65, 0x6e, 0x54, 0x64, 0x12,
	0xef, 0xf8, 0x91, 0x93, 0xe1, 0x88, 0xf9, 0xf6,
	0xf3, 0xb7, 0xe8, 0x4b, 0xbf, 0xf0, 0x93, 0xc1,
	0x85, 0xdd, 0x31, 0x28, 0xe9, 0xbf, 0x81, 0xa6,
	0xa5, 0xdd, 0xf2, 0x35, 0x17, 0x7c, 0x8a, 0x3a,
	0x38, 0x15, 0xd8, 0x16, 0x11, 0xd3, 0x12, 0x27,
	0xe6, 0xd9, 0xca, 0x6d, 0x66, 0x55, 0xa3, 0xc9,
	0xcc, 0x6b, 0x4f, 0xfe, 0x7f, 0x84, 0xa7, 0xcf,
	0xee, 0x01, 0x8f, 0x0d, 0x5d, 0x93, 0x63, 0x2b,
};

uint8_t rsa_8192_d[] = {
	0x3d, 0x09, 0xd4, 0x2b, 0x9b, 0xca, 0xcf, 0x64,
	0x63, 0xc7, 0xba, 0x5b, 0x51, 0x77, 0xdc, 0x2a,
	0x8c, 0xa9, 0x84, 0xb2, 0x97, 0xf5, 0xf6, 0xd1,
	0x7e, 0x61, 0x85, 0x07, 0xb0, 0xaa, 0xfb, 0x5f,
	0x0a, 0x06, 0x31, 0x78, 0x4a, 0x06, 0x12, 0x8c,
	0x1b, 0x0f, 0x9f, 0x6a, 0x33, 0x14, 0x9b, 0x81,
	0x60, 0x9b, 0x80, 0x80, 0x30, 0x7f, 0x21, 0xf6,
	0x5b, 0x48, 0x7b, 0x20, 0x25, 0x4c, 0x82, 0x33,
	0x0a, 0xde, 0xd5, 0xcb, 0x2c, 0xa0, 0x36, 0x9c,
	0xf9, 0xe1, 0x43, 0x1f, 0x59, 0x97, 0x91, 0xa5,
	0x02, 0xcc, 0x45, 0xb7, 0x6e, 0xa7, 0x78, 0xbd,
	0x75, 0x78, 0xf0, 0x12, 0xd7, 0xac, 0xce, 0x4d,
	0xce, 0x43, 0xde, 0xb9, 0xcc, 0xc0, 0x6b, 0x3a,
	0x69, 0xfc, 0xd0, 0x28, 0x7d, 0xd7, 0x80, 0x81,
	0x19, 0x31, 0xd7, 0x43, 0x58, 0xd3, 0x75, 0xdd,
	0x3c, 0x3d, 0x3b, 0x63, 0xc0, 0x6b, 0xc5, 0xb2,
	0x24, 0xfa, 0x78, 0xc2, 0x11, 0x81, 0x3e, 0x0e,
	0x86, 0x47, 0x2f, 0xfe, 0x78, 0xd9, 0x0a, 0x48,
	0x05, 0x72, 0xfe, 0xbf, 0x80, 0x22, 0xaa, 0x26,
	0xdd, 0xb0, 0x2e, 0x99, 0xbf, 0xc0, 0xbe, 0x8f,
	0xfd, 0xf2, 0x52, 0x9c, 0xa5, 0x73, 0x04, 0xc8,
	0x78, 0xfb, 0x2e, 0x15, 0x23, 0x97, 0x0a, 0xf4,
	0x47, 0xe3, 0xf7, 0xa6, 0x18, 0x32, 0xa7, 0x58,
	0x55, 0x19, 0xa6, 0x3e, 0x70, 0x69, 0xaf, 0xcd,
	0x72, 0x57, 0x65, 0x14, 0xf2, 0x20, 0x5f, 0xee,
	0x6b, 0xf8, 0x8f, 0x3f, 0x5b, 0x52, 0x9d, 0x97,
	0x23, 0x2e, 0xaf, 0xec, 0xf6, 0xd5, 0x9a, 0xfb,
	0xe0, 0xb7, 0xed, 0xc1, 0x89, 0xf6, 0xfc, 0x10,
	0xb0, 0xa7, 0x6a, 0xf6, 0xce, 0x86, 0x3e, 0xd9,
	0x22, 0x72, 0xeb, 0xf0, 0xdf, 0x74, 0xa8, 0xb7,
	0xda, 0x2e, 0x9f, 0x0f, 0x90, 0x65, 0xdc, 0x78,
	0x21, 0xa1, 0x3e, 0x88, 0xdf, 0x39, 0x1f, 0x50,
	0xb0, 0xd6, 0x55, 0x56, 0x81, 0x3c, 0xb0, 0xea,
	0x9b, 0xdf, 0x51, 0x39, 0x69, 0x28, 0xa6, 0x1a,
	0xec, 0xea, 0xa0, 0x09, 0x9f, 0x11, 0x2f, 0x3d,
	0x2c, 0xa8, 0x7b, 0xfb, 0xde, 0x83, 0x6b, 0xb7,
	0x55, 0x34, 0xe2, 0x42, 0x85, 0x2c, 0x25, 0xd4,
	0x17, 0x07, 0x14, 0xc6, 0x10, 0xd0, 0x1e, 0x88,
	0x5b, 0x5f, 0x2e, 0x62, 0xcc, 0xad, 0xb0, 0x52,
	0x38, 0x52, 0x08, 0x23, 0x5b, 0x99, 0xa5, 0xdb,
	0xb0, 0x4b, 0xa2, 0x23, 0xdf, 0x57, 0x88, 0x40,
	0x2e, 0xcd, 0x94, 0x7a, 0x2b, 0x69, 0x11, 0xdf,
	0xc5, 0xb7, 0xec, 0xcd, 0x5f, 0xf0, 0x9f, 0xa6,
	0xea, 0x54, 0x61, 0xcb, 0x18, 0x10, 0xd2, 0x0a,
	0x87, 0x00, 0xd9, 0x8a, 0x2d, 0xf4, 0x52, 0xfa,
	0x77, 0xc3, 0x6f, 0x42, 0xdc, 0xc3, 0xd5, 0xbe,
	0x07, 0x59, 0x97, 0x51, 0x22, 0x44, 0xb7, 0xfc,
	0x05, 0x6c, 0xef, 0x7e, 0x12, 0xf9, 0x1e, 0xa0,
	0xa1, 0xf2, 0xfa, 0xf1, 0x7f, 0xdb, 0xe9, 0x75,
	0x2d, 0x58, 0x08, 0xd0, 0x3b, 0xfd, 0xf6, 0x16,
	0xdd, 0x91, 0x03, 0x4f, 0x49, 0x45, 0x04, 0x12,
	0x87, 0x60, 0x2a, 0xa5, 0x52, 0xa1, 0xd4, 0x5e,
	0xf4, 0x01, 0x68, 0xb6, 0xa5, 0xff, 0x5c, 0xab,
	0xdf, 0xc2, 0x61, 0x88, 0x99, 0xfd, 0x16, 0x91,
	0x63, 0x31, 0x15, 0xd5, 0x32, 0x90, 0xad, 0xaa,
	0x5c, 0xe0, 0x4a, 0x27, 0xf5, 0x87, 0x03, 0xc3,
	0xe6, 0x90, 0x29, 0x06, 0xa4, 0x5a, 0xb6, 0xd6,
	0x3b, 0x1c, 0xa9, 0xfa, 0xb0, 0x41, 0x1d, 0xea,
	0xeb, 0x59, 0xe8, 0x1e, 0x0d, 0x13, 0xd7, 0xef,
	0xa5, 0x82, 0x24, 0xa3, 0xd6, 0x55, 0xe1, 0xe5,
	0x38, 0x6d, 0x0a, 0xee, 0xf5, 0xc4, 0x48, 0x8a,
	0x92, 0x98, 0xdf, 0x52, 0xb5, 0xb5, 0x61, 0x58,
	0x89, 0xb5, 0xfe, 0xb6, 0x67, 0xa0, 0xf4, 0x03,
	0xdc, 0x9d, 0x14, 0x42, 0xa6, 0x7d, 0xce, 0x1f,
	0x28, 0xae, 0x56, 0xab, 0x21, 0xf8, 0xfa, 0x01,
	0xbe, 0xc8, 0x31, 0x40, 0x40, 0x20, 0xc1, 0xb5,
	0x19, 0xfd, 0x32, 0x59, 0x15, 0x5c, 0xc4, 0xd1,
	0xe4, 0xef, 0x1f, 0x1f, 0x0b, 0x24, 0x6c, 0x6e,
	0x6e, 0xa4, 0xc4, 0x81, 0x47, 0x23, 0x54, 0xf5,
	0x60, 0x95, 0xb9, 0xa9, 0x42, 0x95, 0xe4, 0x04,
	0x67, 0x36, 0x04, 0xdd, 0xdd, 0x51, 0x83, 0x47,
	0xd0, 0x41, 0x54, 0x35, 0x45, 0xaf, 0x81, 0x3d,
	0xb6, 0x33, 0xa2, 0xfc, 0xf5, 0xe5, 0x8e, 0x06,
	0xf4, 0xc0, 0x09, 0x9a, 0xd7, 0x44, 0xe6, 0x07,
	0xdd, 0xa1, 0x8d, 0x99, 0xf8, 0x5f, 0x32, 0x5c,
	0xec, 0x95, 0xa4, 0x73, 0xfa, 0x8b, 0x0f, 0x8d,
	0x42, 0xe2, 0x5d, 0x2b, 0x03, 0xf1, 0x83, 0x02,
	0x02, 0x1c, 0x75, 0x30, 0x11, 0xa0, 0xda, 0xe3,
	0xeb, 0x18, 0x5e, 0x23, 0xab, 0xbe, 0x34, 0x7a,
	0xd4, 0x6e, 0xb8, 0x60, 0x97, 0xeb, 0xeb, 0x19,
	0x22, 0xaf, 0x97, 0x96, 0x0f, 0xd8, 0xa6, 0xca,
	0xf8, 0x92, 0x95, 0x54, 0x88, 0x04, 0x6c, 0xac,
	0xbf, 0xe9, 0x45, 0xa3, 0x2f, 0xde, 0x67, 0xba,
	0xe9, 0xaf, 0x2a, 0x1f, 0x2b, 0x9b, 0xa5, 0x82,
	0xb9, 0x91, 0x9d, 0xaa, 0x1a, 0x2c, 0x02, 0xfb,
	0xf0, 0x9a, 0x15, 0x8a, 0x83, 0x6f, 0x51, 0xfc,
	0x5a, 0x51, 0x4b, 0x76, 0x1d, 0xc7, 0x12, 0x5f,
	0xf0, 0x46, 0xeb, 0xfd, 0xf1, 0x2b, 0x75, 0x71,
	0xad, 0xe4, 0x58, 0x6d, 0x99, 0x41, 0x57, 0x99,
	0x46, 0xae, 0x89, 0xb4, 0x78, 0x68, 0x53, 0x1a,
	0xb5, 0x36, 0xaf, 0x26, 0xeb, 0x49, 0xbc, 0x69,
	0x4a, 0x77, 0xde, 0x41, 0x95, 0x74, 0xf4, 0x0c,
	0xb6, 0x53, 0xae, 0x6f, 0x48, 0x84, 0xb0, 0xf6,
	0x19, 0xa6, 0x87, 0x7c, 0x32, 0xba, 0x4d, 0x1d,
	0xc4, 0x6c, 0x2d, 0xf2, 0xfb, 0x59, 0xb4, 0x2f,
	0x03, 0xe0, 0x04, 0x10, 0x83, 0x8e, 0x77, 0x95,
	0xd6, 0x43, 0xd7, 0x01, 0x0a, 0x92, 0xf3, 0xc8,
	0x5b, 0xcb, 0x32, 0xa5, 0x9b, 0x8b, 0xb9, 0x3c,
	0x2e, 0x8c, 0x6c, 0xb7, 0x4e, 0xe7, 0x0f, 0xf9,
	0x6a, 0x8f, 0x31, 0xe9, 0xd7, 0x7c, 0x51, 0x6d,
	0x90, 0xa6, 0x98, 0x78, 0x07, 0x7b, 0xd2, 0xbc,
	0x5c, 0x68, 0x7c, 0x96, 0xc2, 0xaf, 0x62, 0xe6,
	0xd4, 0x1e, 0x8b, 0xb2, 0x32, 0x66, 0x69, 0xc9,
	0xe8, 0x18, 0x65, 0x2f, 0x75, 0x95, 0xb4, 0x78,
	0x62, 0x48, 0xb5, 0xfe, 0xb9, 0xef, 0x9e, 0x9a,
	0x99, 0xbe, 0x49, 0x05, 0xa2, 0x6f, 0xac, 0x2b,
	0x10, 0x58, 0x43, 0x36, 0x39, 0x17, 0x36, 0x8a,
	0x36, 0x81, 0xde, 0x74, 0xb6, 0x81, 0xc5, 0x2d,
	0x9c, 0x1d, 0x86, 0x04, 0x30, 0x89, 0x9b, 0x93,
	0x3f, 0xbb, 0x42, 0x1f, 0x87, 0x46, 0x0f, 0x3d,
	0xb8, 0x60, 0x6a, 0x1f, 0x9b, 0x86, 0xd3, 0x6f,
	0x9f, 0x58, 0xaf, 0x35, 0x29, 0xa1, 0x0b, 0xfd,
	0x35, 0xf3, 0xe5, 0xf0, 0x78, 0xd9, 0x9f, 0x75,
	0x02, 0xab, 0x14, 0xed, 0x21, 0xda, 0x86, 0x7e,
	0x2b, 0x6c, 0x66, 0x1f, 0xd9, 0xc1, 0xac, 0xfd,
	0x49, 0x0d, 0xad, 0x84, 0xf4, 0x97, 0x53, 0x4f,
	0x43, 0x6b, 0x96, 0x82, 0x30, 0x0a, 0x9b, 0x25,
	0x0a, 0xd5, 0xd4, 0x06, 0x59, 0x00, 0x06, 0x5f,
	0x0d, 0xe0, 0xc9, 0x3e, 0x13, 0x5d, 0x7d, 0x4d,
	0xc6, 0xb6, 0x1d, 0x6f, 0xa1, 0x3c, 0x92, 0xe3,
	0x85, 0x7e, 0x81, 0x5f, 0x7b, 0x0b, 0x76, 0x14,
	0x60, 0xf8, 0x61, 0x31, 0xc3, 0xc5, 0x84, 0xb0,
	0x33, 0x99, 0x8b, 0x5f, 0x01, 0xb7, 0x10, 0x2d,
	0x2c, 0xe1, 0x22, 0xc8, 0xad, 0x9e, 0xd5, 0x14,
	0x95, 0x65, 0x91, 0xda, 0x43, 0x5c, 0x0f, 0x1f,
	0xe5, 0x84, 0xec, 0x42, 0x52, 0x18, 0xaf, 0x8f,
	0x1a, 0xc8, 0x5a, 0x75, 0xa3, 0x3c, 0xce, 0xdd,
	0xbb, 0xa9, 0xcb, 0x09, 0xb7, 0x3b, 0x66, 0xd1,
};

uint8_t rsa_8192_p[] = {
	0xea, 0xeb, 0x4c, 0xdd, 0x3e, 0x0c, 0x75, 0xd7,
	0x99, 0x62, 0xd2, 0x4b, 0x71, 0x3d, 0x02, 0xe7,
	0x9a, 0xc6, 0x74, 0x9c, 0x4e, 0x40, 0x5d, 0xbb,
	0xb8, 0x64, 0x4a, 0xd3, 0x34, 0x2b, 0x68, 0x65,
	0xf7, 0x00, 0x3c, 0xca, 0x96, 0x6d, 0xbe, 0x02,
	0x15, 0x28, 0x75, 0x6a, 0xed, 0x84, 0x1f, 0xd1,
	0x63, 0x4c, 0xf3, 0xab, 0xd4, 0xd9, 0x0d, 0xba,
	0xe4, 0xac, 0x3e, 0xa4, 0x46, 0xf3, 0xa6, 0xdf,
	0x39, 0x6e, 0x95, 0x40, 0xcc, 0x33, 0x4b, 0x72,
	0x78, 0xa1, 0x12, 0x73, 0x7e, 0x14, 0xf0, 0x44,
	0xd6, 0x00, 0xea, 0x94, 0xa2, 0x9a, 0xdf, 0x0a,
	0xac, 0x3f, 0xd5, 0xbf, 0xe8, 0x61, 0x09, 0xf8,
	0x80, 0xc1, 0x63, 0xdc, 0xb8, 0x5f, 0x6e, 0x14,
	0xb5, 0x81, 0xfe, 0x43, 0xc2, 0x72, 0x66, 0x6e,
	0x17, 0x7d, 0x40, 0xc2, 0x43, 0x1b, 0xdd, 0x81,
	0x9f, 0xf1, 0x57, 0x58, 0x25, 0x0a, 0xca, 0x45,
	0xc5, 0x34, 0x44, 0x2a, 0x6b, 0xda, 0xdc, 0x30,
	0x1f, 0x2f, 0xa8, 0xca, 0x8f, 0xd4, 0xf0, 0x1d,
	0x14, 0xc2, 0xa3, 0xb4, 0x44, 0x62, 0x32, 0x50,
	0x66, 0xcc, 0x3e, 0x99, 0x64, 0x9b, 0xde, 0xff,
	0xeb, 0xa3, 0xd3, 0xa8, 0xcc, 0xf6, 0x93, 0xff,
	0x51, 0x46, 0x40, 0x5d, 0xd7, 0x1c, 0x15, 0xb1,
	0xd5, 0x4e, 0x83, 0x58, 0x6c, 0x90, 0x89, 0xd8,
	0xb1, 0xa4, 0xb9, 0x1a, 0x5b, 0x0b, 0x4d, 0x82,
	0x54, 0xd7, 0x9f, 0x78, 0xd6, 0xfa, 0x84, 0x3e,
	0x05, 0x4b, 0xb1, 0x7b, 0x71, 0x0d, 0x55, 0xf3,
	0x61, 0xdf, 0x16, 0xe3, 0xc2, 0x9d, 0x61, 0x5e,
	0x10, 0x7e, 0xf9, 0x0f, 0x8a, 0x59, 0x51, 0x47,
	0x5f, 0x1b, 0x3c, 0xbb, 0xda, 0x16, 0x45, 0x63,
	0x94, 0x9b, 0x3b, 0x4f, 0x2d, 0x54, 0xa9, 0x8e,
	0xe8, 0x11, 0x02, 0x75, 0x9b, 0xee, 0xf6, 0xc7,
	0x5b, 0xbf, 0xa1, 0x3e, 0x75, 0x53, 0x21, 0xba,
	0x64, 0xef, 0x06, 0x29, 0x3f, 0x81, 0x4a, 0xcb,
	0x9e, 0xdc, 0x49, 0x9a, 0x69, 0x6d, 0xeb, 0x99,
	0x44, 0x67, 0xeb, 0xec, 0x1b, 0x77, 0x9e, 0xea,
	0x17, 0xba, 0xcb, 0x8d, 0x6d, 0x19, 0x42, 0xc7,
	0xc2, 0x76, 0xb6, 0x12, 0x20, 0xcc, 0xff, 0x6c,
	0x8e, 0xfe, 0x72, 0x5c, 0x3a, 0xa6, 0x51, 0x81,
	0x5d, 0x5f, 0xec, 0xd1, 0xa1, 0x67, 0x0b, 0xe3,
	0x8e, 0x57, 0x3f, 0x99, 0x1b, 0x4d, 0x63, 0xb1,
	0x21, 0xdf, 0x71, 0x79, 0x62, 0x92, 0x07, 0xf8,
	0x95, 0x42, 0x08, 0x89, 0xf2, 0xdf, 0xf1, 0xcf,
	0x19, 0x75, 0x69, 0x77, 0xdb, 0x39, 0xb5, 0x6b,
	0x8b, 0xbc, 0x0f, 0x55, 0x0b, 0xcf, 0x16, 0x6a,
	0xa5, 0xe2, 0x28, 0x3f, 0x34, 0x82, 0xd4, 0x09,
	0x95, 0x20, 0xa2, 0x6a, 0x2e, 0xf9, 0x3e, 0xf1,
	0x29, 0xe2, 0xff, 0x7a, 0xa0, 0xb9, 0xaf, 0xdf,
	0xf9, 0x46, 0x52, 0x7a, 0x8b, 0xe8, 0x7f, 0xae,
	0x6c, 0x8c, 0x7d, 0x0b, 0x52, 0xfb, 0xc4, 0x49,
	0x7f, 0x9a, 0xb9, 0xa2, 0xd3, 0x0b, 0x6c, 0x8e,
	0x21, 0xde, 0x45, 0xa2, 0x08, 0xcd, 0xba, 0x5f,
	0xb2, 0x72, 0x4f, 0xf8, 0x4d, 0xdc, 0x4e, 0x06,
	0x27, 0x6c, 0x2a, 0xc4, 0x58, 0x2d, 0xbd, 0xd2,
	0x1b, 0x13, 0x10, 0x06, 0x80, 0x3a, 0xb6, 0x88,
	0x05, 0x51, 0x49, 0x3d, 0x97, 0x3f, 0x6f, 0x8c,
	0xfa, 0x99, 0x9b, 0x70, 0xa1, 0x97, 0x2f, 0x1d,
	0x82, 0x56, 0xdc, 0x80, 0x41, 0x81, 0x10, 0xe6,
	0x7c, 0xe7, 0xe5, 0x8a, 0x13, 0xac, 0x8f, 0x10,
	0x83, 0xb3, 0xbd, 0x2c, 0x4a, 0xe2, 0x6e, 0x21,
	0x4e, 0x96, 0xa8, 0xd5, 0x07, 0x24, 0xf3, 0x4a,
	0x79, 0xe3, 0x50, 0xe3, 0xbe, 0x2c, 0xaa, 0x52,
	0x95, 0xda, 0xf3, 0x93, 0xd2, 0xf0, 0x6a, 0x22,
	0xe9, 0x95, 0x37, 0xe6, 0x94, 0xec, 0x80, 0x8f,
	0x32, 0xb1, 0xd8, 0x2c, 0xd7, 0x0c, 0x36, 0x49,
};

uint8_t rsa_8192_q[] = {
	0xe2, 0x53, 0x2e, 0x30, 0xf5, 0xfe, 0x97, 0x1a,
	0x79, 0xf6, 0x90, 0x26, 0x02, 0xed, 0xd5, 0x51,
	0x96, 0xd9, 0x8a, 0x57, 0x48, 0xd8, 0xdf, 0x4e,
	0xa1, 0x4d, 0x52, 0xd1, 0x4c, 0x89, 0x05, 0xef,
	0xea, 0xb3, 0x55, 0x9d, 0x14, 0x46, 0x19, 0x72,
	0x05, 0xa2, 0x5f, 0x16, 0x4f, 0xcf, 0x0a, 0xb4,
	0x51, 0x4a, 0x27, 0xde, 0x53, 0x46, 0xaf, 0x4f,
	0x87, 0xbf, 0xa8, 0x84, 0x74, 0x67, 0x1f, 0x1e,
	0x4e, 0x71, 0x20, 0x50, 0x53, 0x1b, 0x38, 0xbd,
	0x67, 0xb0, 0x3a, 0xa4, 0xd9, 0x5b, 0x30, 0xc2,
	0x28, 0xe6, 0x95, 0x49, 0x1e, 0xe8, 0xf6, 0xc0,
	0x68, 0x0f, 0x75, 0x96, 0x9e, 0x0d, 0xf6, 0xc9,
	0xf4, 0x5d, 0xb7, 0xfc, 0xca, 0x14, 0x09, 0x6e,
	0xa8, 0xec, 0xed, 0x22, 0x25, 0x8c, 0xe6, 0x20,
	0x3a, 0xfe, 0xda, 0x13, 0x62, 0x2e, 0x52, 0xa0,
	0x07, 0x58, 0x30, 0xa0, 0x29, 0x67, 0x2e, 0x02,
	0xba, 0x66, 0x4f, 0x77, 0xa1, 0x29, 0x35, 0x2a,
	0xa2, 0x8e, 0x61, 0x1c, 0xee, 0x77, 0xca, 0x82,
	0x16, 0x26, 0x2e, 0x1d, 0xd2, 0x92, 0x43, 0x9d,
	0x16, 0xad, 0xc9, 0xf8, 0xd1, 0xdc, 0x36, 0x8d,
	0xb6, 0x64, 0x32, 0xf8, 0x92, 0xb8, 0xdf, 0x38,
	0x49, 0x78, 0xcd, 0x98, 0x69, 0xf2, 0xa7, 0x51,
	0xd0, 0xf9, 0xf8, 0x58, 0x49, 0xe1, 0x1d, 0x27,
	0xdc, 0x38, 0x88, 0xb3, 0x02, 0x42, 0xa7, 0xcd,
	0x74, 0x46, 0x70, 0x61, 0x35, 0x1b, 0x00, 0x53,
	0x56, 0x00, 0x95, 0xf9, 0xea, 0xfe, 0xea, 0x7b,
	0x29, 0x66, 0x41, 0xea, 0x62, 0x94, 0xb5, 0x1b,
	0x04, 0xe0, 0x76, 0x03, 0x03, 0xb9, 0x67, 0x43,
	0x55, 0xc0, 0x72, 0x29, 0x87, 0xe3, 0x2c, 0x80,
	0x56, 0x7f, 0x63, 0xad, 0xa1, 0xc6, 0x06, 0xbc,
	0x3b, 0xa0, 0x5f, 0x22, 0x1a, 0xa4, 0x0b, 0xff,
	0xfc, 0xcc, 0x95, 0x50, 0xee, 0x94, 0x0e, 0xd1,
	0x44, 0xfd, 0xc9, 0xed, 0xcc, 0xe9, 0x36, 0xb0,
	0xb7, 0x5f, 0x98, 0xb5, 0x69, 0x18, 0xf3, 0x67,
	0xf0, 0x17, 0x01, 0xc7, 0x19, 0xe2, 0x63, 0xf7,
	0xa7, 0xa0, 0xaf, 0xd3, 0xc5, 0x2c, 0xc1, 0x20,
	0x58, 0xf8, 0x51, 0x17, 0x9e, 0xb8, 0xbe, 0xdf,
	0x11, 0xb4, 0x15, 0x31, 0xb1, 0xf7, 0x6a, 0xcc,
	0x40, 0x68, 0x9b, 0x1a, 0x5c, 0x51, 0x89, 0x9c,
	0xc9, 0x87, 0x18, 0xdb, 0xf0, 0xc3, 0x1b, 0x32,
	0xdc, 0x44, 0xbd, 0xa7, 0xfc, 0xb6, 0x81, 0xdf,
	0x9a, 0x52, 0x0d, 0x2f, 0xa2, 0xa6, 0xf7, 0x09,
	0x9f, 0x37, 0x64, 0x1f, 0x98, 0x7d, 0x02, 0x39,
	0x91, 0x86, 0xeb, 0x67, 0xf9, 0x1c, 0x8d, 0x48,
	0xd4, 0xc4, 0xfb, 0x73, 0x51, 0xff, 0x35, 0xeb,
	0x61, 0xb4, 0x3d, 0x06, 0xa6, 0x16, 0x22, 0x52,
	0x25, 0x90, 0x69, 0x04, 0x8a, 0xc6, 0xae, 0x9c,
	0x36, 0xea, 0xa4, 0x30, 0xbb, 0xba, 0x3e, 0x52,
	0x56, 0x32, 0x1d, 0xbc, 0xd8, 0x89, 0x14, 0xb6,
	0xf3, 0x43, 0xe0, 0x1b, 0xc9, 0x45, 0x00, 0xda,
	0x86, 0xf7, 0x34, 0x1d, 0x3b, 0xe8, 0xc4, 0xab,
	0xb9, 0xe7, 0xfe, 0x22, 0x66, 0x50, 0x28, 0x86,
	0x74, 0x28, 0x20, 0x37, 0x24, 0x7c, 0xa8, 0xca,
	0x3d, 0x97, 0x1a, 0x7a, 0x03, 0xc8, 0x69, 0x97,
	0x86, 0x9e, 0x47, 0x5b, 0x0b, 0x26, 0x3f, 0x0d,
	0x16, 0x86, 0xac, 0x8f, 0xca, 0xad, 0x1f, 0x7a,
	0xa0, 0x6a, 0xa8, 0x47, 0x0a, 0xf8, 0xcf, 0x5f,
	0xee, 0x54, 0x52, 0x50, 0xaf, 0x88, 0x13, 0x72,
	0xc3, 0x2a, 0x5b, 0x75, 0x17, 0x0a, 0x74, 0x1a,
	0xaa, 0x6f, 0xd0, 0xd0, 0x68, 0x7e, 0xc9, 0xfe,
	0xd2, 0x70, 0x0c, 0xa0, 0xd3, 0x81, 0x95, 0x32,
	0x47, 0x74, 0x71, 0x21, 0x3c, 0x97, 0x52, 0xef,
	0x32, 0xcd, 0x84, 0xa4, 0x5a, 0xaf, 0x84, 0x89,
	0x33, 0x78, 0x22, 0xf7, 0x83, 0xf9, 0x7d, 0xd3,
};

uint8_t rsa_8192_dp[] = {
	0x30, 0x78, 0x50, 0x29, 0xad, 0xed, 0xc0, 0x3a,
	0xb5, 0xc9, 0x2f, 0xfe, 0xa4, 0xe4, 0xa3, 0xbe,
	0xc9, 0xae, 0x7c, 0xcc, 0xeb, 0x50, 0x58, 0x86,
	0xac, 0xea, 0xf4, 0x8d, 0x53, 0x4c, 0x49, 0x84,
	0x10, 0x4c, 0x9a, 0x3e, 0x6a, 0x7f, 0x46, 0x1f,
	0x03, 0xe2, 0x8f, 0x27, 0x7a, 0xce, 0x32, 0x0a,
	0x6f, 0xe4, 0xa6, 0xba, 0x23, 0x76, 0x8d, 0x60,
	0xac, 0x61, 0xe5, 0xa2, 0xdb, 0x88, 0xd6, 0x3b,
	0x64, 0x8c, 0x2e, 0x7b, 0x6a, 0x26, 0x83, 0x40,
	0xa1, 0x27, 0x0a, 0xb9, 0x8d, 0xa0, 0x20, 0x5a,
	0xf3, 0x94, 0xc8, 0xd1, 0xd6, 0x23, 0x02, 0x36,
	0x1f, 0x88, 0x24, 0x40, 0x2e, 0x60, 0x27, 0x40,
	0x80, 0xc0, 0x66, 0x2c, 0x5a, 0x56, 0x46, 0x4e,
	0x3b, 0x8d, 0x10, 0xbb, 0x46, 0x59, 0x78, 0x4c,
	0xea, 0xc7, 0xcb, 0x1e, 0xc9, 0xdd, 0xeb, 0xfc,
	0x1c, 0xd6, 0x1c, 0xe1, 0x70, 0xf3, 0x22, 0xfc,
	0xf6, 0x4d, 0x6c, 0x6b, 0x1c, 0x49, 0xd1, 0xf8,
	0x93, 0xd6, 0xaf, 0x8a, 0xeb, 0xd6, 0x51, 0x9c,
	0x8e, 0x76, 0x02, 0x74, 0xe5, 0x4f, 0x25, 0x76,
	0x75, 0x5f, 0x5a, 0xe3, 0x27, 0x61, 0x09, 0xc7,
	0xe0, 0x04, 0xb3, 0x5d, 0x36, 0xbd, 0x01, 0x98,
	0x42, 0x5a, 0xae, 0x59, 0x2e, 0x52, 0xfb, 0x1e,
	0xf0, 0x15, 0xfc, 0xff, 0xb7, 0x96, 0xb2, 0xfa,
	0x64, 0xdc, 0xc9, 0xdc, 0x68, 0x40, 0x92, 0xc3,
	0x7b, 0x61, 0xe9, 0xe3, 0x72, 0xe7, 0xc9, 0x75,
	0x4a, 0x7e, 0x6d, 0xa6, 0x3c, 0x93, 0x35, 0xd2,
	0xbb, 0xb4, 0xdd, 0xf2, 0x2c, 0x6b, 0x70, 0x75,
	0x8a, 0x0d, 0xc8, 0xb1, 0xae, 0x27, 0xf3, 0x9f,
	0x38, 0x3e, 0x54, 0x6f, 0xa5, 0xf5, 0xc6, 0x67,
	0x5f, 0x78, 0x0f, 0xea, 0xac, 0x3d, 0xb5, 0xa6,
	0x68, 0xb7, 0x69, 0xbc, 0x7e, 0x69, 0xf5, 0xbc,
	0xf8, 0x6f, 0x37, 0x83, 0xbd, 0x8d, 0x63, 0x7e,
	0x23, 0xe9, 0x35, 0x6a, 0x39, 0x52, 0x21, 0xee,
	0x95, 0x5e, 0x7a, 0x49, 0x43, 0xcb, 0x8c, 0x3a,
	0xd9, 0x64, 0x5f, 0xc7, 0x1d, 0x7b, 0xb6, 0xec,
	0x31, 0xed, 0x36, 0xa6, 0x7d, 0x86, 0x22, 0xc3,
	0x38, 0x9a, 0x69, 0xc2, 0x9f, 0xc5, 0xec, 0x45,
	0xa7, 0xd3, 0x54, 0x21, 0x37, 0x70, 0xad, 0x8c,
	0x75, 0x89, 0x9f, 0x41, 0x39, 0x7e, 0x1b, 0x73,
	0xde, 0x20, 0x9e, 0x5c, 0xa6, 0xc7, 0x76, 0xf4,
	0x4b, 0x3a, 0xe1, 0x1c, 0xdc, 0x7b, 0xa8, 0xed,
	0x5f, 0x4e, 0x7a, 0x98, 0x8b, 0xe6, 0x2c, 0xec,
	0x40, 0x22, 0x7b, 0x9a, 0x61, 0x3f, 0x2c, 0xf1,
	0x7d, 0xa9, 0x55, 0xb8, 0x84, 0x71, 0x3b, 0xfc,
	0xd1, 0x31, 0x32, 0x80, 0xa8, 0x02, 0xfe, 0x60,
	0xe3, 0xc6, 0x11, 0xfc, 0xba, 0xa8, 0x6e, 0x96,
	0x2f, 0xc0, 0x48, 0x5a, 0x32, 0xe3, 0x5d, 0x28,
	0x67, 0x34, 0x5d, 0x3c, 0x2f, 0xb5, 0xce, 0xf2,
	0x5c, 0x2f, 0x38, 0x75, 0xa7, 0xaf, 0x8e, 0xb6,
	0x63, 0x7f, 0xb7, 0x21, 0xa3, 0x4d, 0xce, 0x1e,
	0x1d, 0x5a, 0xd1, 0x1f, 0x37, 0x95, 0xcb, 0x08,
	0x87, 0xb7, 0x12, 0x4c, 0xf7, 0x33, 0xa7, 0x5d,
	0x99, 0xb0, 0xb5, 0xba, 0x41, 0x3f, 0x5e, 0xd5,
	0x05, 0x05, 0x0f, 0xdf, 0x67, 0x81, 0xb5, 0xbb,
	0xa6, 0xed, 0x38, 0xa1, 0x5e, 0x9d, 0x96, 0x14,
	0x8e, 0x78, 0x37, 0x1c, 0x2c, 0x1b, 0x17, 0x7b,
	0x8d, 0x33, 0x02, 0xd7, 0xeb, 0xac, 0x07, 0xa2,
	0xa6, 0xce, 0xea, 0x0b, 0x6d, 0x37, 0xc3, 0x64,
	0xf2, 0xe7, 0x98, 0xf6, 0x94, 0x94, 0xd7, 0x88,
	0x43, 0xc0, 0xce, 0x20, 0xf6, 0x12, 0x67, 0xa8,
	0x4c, 0x31, 0xdc, 0xb4, 0xc0, 0xbe, 0x52, 0x7e,
	0x2b, 0xb1, 0x3b, 0x64, 0x9a, 0xb7, 0x44, 0x0e,
	0xe4, 0x07, 0x07, 0xbb, 0xa9, 0xaa, 0x8a, 0x74,
	0x01, 0x4f, 0xb4, 0x7c, 0xbb, 0xfa, 0x27, 0x11,
};

uint8_t rsa_8192_dq[] = {
	0x0a, 0xc1, 0xdf, 0x61, 0x2e, 0x60, 0x12, 0x78,
	0x8c, 0xb1, 0x7e, 0x7a, 0x80, 0x0b, 0x23, 0xe7,
	0x83, 0xdf, 0x05, 0x27, 0x4e, 0xec, 0x17, 0xf9,
	0x19, 0xcb, 0x9e, 0x13, 0x1a, 0xab, 0x2c, 0xa0,
	0x11, 0xac, 0xc2, 0x75, 0xfb, 0xdf, 0xb5, 0x17,
	0xe9, 0x52, 0xa5, 0x68, 0x5f, 0x14, 0xbd, 0x32,
	0x82, 0xe6, 0x9e, 0x6c, 0x7f, 0xd2, 0xcd, 0x44,
	0x5f, 0x74, 0x69, 0xa3, 0x1a, 0x11, 0x48, 0xb6,
	0x5d, 0x0e, 0x1d, 0x76, 0x13, 0xed, 0x28, 0x4e,
	0x40, 0x5e, 0x6c, 0xfb, 0x26, 0xe5, 0xa1, 0xb4,
	0xa8, 0xd3, 0x04, 0xe7, 0x7b, 0xd3, 0xa0, 0xde,
	0xd6, 0x53, 0x5d, 0xc8, 0x71, 0xec, 0xc1, 0xfd,
	0x29, 0x63, 0x46, 0xfa, 0x79, 0xf4, 0x67, 0xda,
	0x5d, 0xa7, 0x1d, 0x42, 0x51, 0x3c, 0x98, 0x37,
	0x91, 0xf6, 0x49, 0x11, 0x40, 0xca, 0x68, 0xdc,
	0xf8, 0x5c, 0x65, 0xbb, 0x47, 0x1c, 0x83, 0x60,
	0x57, 0xd1, 0xc3, 0x17, 0x31, 0x8e, 0xce, 0xdb,
	0x1e, 0xec, 0xdb, 0x8a, 0xcf, 0x7e, 0xdc, 0x1e,
	0x40, 0xc5, 0x41, 0xcd, 0xe3, 0x13, 0x56, 0xa9,
	0x61, 0xc9, 0x92, 0xdf, 0x03, 0xc2, 0x8e, 0xc9,
	0x3f, 0x91, 0x15, 0xa7, 0x20, 0xfa, 0x2e, 0x89,
	0xf3, 0x5c, 0x0d, 0x91, 0x9f, 0xff, 0x8b, 0x9a,
	0x64, 0xef, 0x6c, 0xb1, 0x6d, 0x99, 0x88, 0x94,
	0x3b, 0x70, 0x68, 0x76, 0xf1, 0xb5, 0x8f, 0x5a,
	0x41, 0x9b, 0x93, 0x7d, 0xdb, 0x53, 0x54, 0x39,
	0xa1, 0xcd, 0xd7, 0x53, 0x10, 0xab, 0x5b, 0x23,
	0xcd, 0x7c, 0xf1, 0xc7, 0x1d, 0x36, 0x6a, 0x41,
	0x45, 0x83, 0x11, 0xb8, 0xe4, 0xf5, 0x6c, 0x06,
	0x14, 0x4e, 0x58, 0x5e, 0xc6, 0xf9, 0xb2, 0x44,
	0xcb, 0x57, 0xb4, 0xba, 0x2d, 0x05, 0x47, 0x8e,
	0xac, 0xda, 0xdb, 0x8a, 0xb8, 0x6f, 0x71, 0xae,
	0x92, 0x2a, 0x7b, 0x32, 0x64, 0x94, 0x9e, 0xfb,
	0xad, 0x66, 0xce, 0xb1, 0x41, 0xdb, 0x17, 0xe1,
	0x60, 0x65, 0xe7, 0xd1, 0x3f, 0x94, 0x93, 0x05,
	0x2e, 0xb1, 0x39, 0x41, 0x68, 0x66, 0x8d, 0x4f,
	0xd9, 0x24, 0xd7, 0x16, 0x9e, 0xb9, 0x98, 0xd8,
	0x5b, 0xc2, 0x39, 0xc0, 0x80, 0xa7, 0xd1, 0x52,
	0xb2, 0xf9, 0xb7, 0x44, 0x2c, 0x6c, 0x51, 0x24,
	0x32, 0x2f, 0x86, 0xa7, 0xc3, 0x19, 0x91, 0x0b,
	0xe5, 0xbc, 0x49, 0x1f, 0x33, 0x1b, 0x62, 0xf0,
	0x90, 0xed, 0xa7, 0x82, 0x13, 0x8d, 0xed, 0x30,
	0x5e, 0x9a, 0x65, 0xcc, 0x0c, 0xd4, 0x18, 0xcb,
	0xd9, 0x48, 0x7d, 0xb6, 0xce, 0xbd, 0xaa, 0x5c,
	0x68, 0x21, 0x5c, 0x91, 0x0d, 0x93, 0x20, 0xd8,
	0x04, 0x5c, 0xf4, 0x82, 0xaf, 0xb4, 0x0c, 0xb1,
	0x0a, 0x6c, 0xd1, 0xb5, 0xbe, 0x31, 0x31, 0x22,
	0x6c, 0x6c, 0xa7, 0xc1, 0x32, 0x83, 0x6d, 0x0b,
	0x39, 0xc4, 0xff, 0x56, 0xdd, 0xa5, 0x65, 0x8e,
	0xee, 0x4f, 0xe5, 0x7f, 0xf3, 0x5e, 0xd8, 0xa0,
	0x35, 0x81, 0xd5, 0x66, 0x1c, 0xb3, 0xed, 0xed,
	0x75, 0x1a, 0xcc, 0xdc, 0x52, 0xdb, 0xa6, 0x75,
	0x6f, 0x19, 0xaa, 0xc7, 0xa1, 0xcb, 0x4f, 0x91,
	0xcc, 0x75, 0x97, 0xca, 0x67, 0x2e, 0x91, 0xab,
	0x43, 0x30, 0x2a, 0x6f, 0x00, 0x3d, 0x07, 0x5f,
	0xcf, 0x63, 0x38, 0x8b, 0x08, 0x45, 0xa2, 0x1b,
	0x92, 0x25, 0x86, 0x90, 0x33, 0x16, 0xf5, 0x77,
	0x13, 0xa6, 0xde, 0x26, 0xba, 0x4b, 0xc7, 0x37,
	0x8d, 0x11, 0x6e, 0xf4, 0x2c, 0xd3, 0xaf, 0xf0,
	0xf5, 0x7b, 0xce, 0xeb, 0x0a, 0xd4, 0x51, 0x5e,
	0x6f, 0x19, 0x6f, 0x78, 0x68, 0x6f, 0x16, 0x54,
	0xc9, 0x55, 0xd0, 0x13, 0xd8, 0xd9, 0xc0, 0xe8,
	0xa9, 0xe2, 0xcb, 0xf9, 0xc3, 0x72, 0x81, 0xdb,
	0xa2, 0xb3, 0x88, 0x09, 0x73, 0xd2, 0x01, 0xdf,
	0xaf, 0x95, 0x97, 0x3b, 0xe2, 0x86, 0xfa, 0x47,
};

uint8_t rsa_8192_qinv[] = {
	0x7b, 0xc0, 0x95, 0xaa, 0x37, 0xad, 0x98, 0xff,
	0xaa, 0xd5, 0xc5, 0xe8, 0x25, 0xd6, 0xe9, 0x62,
	0x3b, 0x1d, 0x42, 0xdd, 0x39, 0x3f, 0x09, 0x76,
	0x62, 0x2c, 0x91, 0x76, 0x5f, 0xe2, 0x7a, 0x64,
	0x2d, 0xc8, 0xa6, 0xa2, 0xc9, 0x76, 0x86, 0xfa,
	0x0d, 0xaf, 0x64, 0x88, 0x3e, 0x22, 0x73, 0x75,
	0x50, 0x4c, 0xac, 0x94, 0x65, 0xd6, 0x81, 0x6f,
	0xf2, 0xe7, 0x74, 0x40, 0x8e, 0x6e, 0x96, 0xb0,
	0xfb, 0x9c, 0x59, 0xd2, 0xb3, 0xd8, 0x52, 0xa4,
	0x22, 0xbf, 0xdc, 0x46, 0x31, 0xdd, 0x43, 0x26,
	0xf5, 0xaf, 0xd3, 0xca, 0x6d, 0x47, 0x80, 0x0e,
	0x48, 0x08, 0xdc, 0xce, 0xe8, 0x3a, 0x27, 0xea,
	0x57, 0x67, 0x0c, 0xe0, 0x6a, 0xa6, 0x35, 0x0a,
	0x23, 0xd5, 0x47, 0x90, 0x19, 0xd0, 0xc5, 0x96,
	0x12, 0x39, 0x6b, 0x9f, 0x58, 0xde, 0x34, 0x24,
	0x4e, 0x09, 0xc0, 0x5f, 0x28, 0x7c, 0x9d, 0x49,
	0xe6, 0xf5, 0x04, 0xf5, 0x1d, 0x47, 0x2d, 0xa9,
	0xf0, 0x56, 0xac, 0x23, 0x41, 0xb8, 0xdd, 0x1d,
	0x15, 0x37, 0x22, 0x77, 0x00, 0x7e, 0x99, 0x34,
	0x8c, 0x78, 0xab, 0xe9, 0x79, 0x2c, 0x41, 0x35,
	0xd0, 0x23, 0xc7, 0x3a, 0x6a, 0x19, 0x29, 0x02,
	0x72, 0x62, 0x45, 0xd1, 0x8d, 0x78, 0x20, 0xec,
	0x0e, 0xc6, 0xef, 0x75, 0x8c, 0xce, 0xb1, 0xcb,
	0x51, 0x42, 0x2c, 0xe5, 0x51, 0x54, 0x26, 0x21,
	0x6b, 0x7c, 0xee, 0x17, 0xeb, 0xcc, 0xe4, 0xff,
	0xf6, 0xbb, 0xc9, 0xdf, 0x53, 0x7f, 0xd3, 0x20,
	0x50, 0x87, 0x03, 0xd1, 0xc1, 0xe8, 0x56, 0x88,
	0x52, 0xfb, 0xf4, 0xe3, 0x89, 0x55, 0x37, 0x32,
	0x29, 0xcc, 0xd9, 0x4f, 0xd2, 0x5a, 0x5a, 0x94,
	0xc5, 0x21, 0xe8, 0x01, 0x88, 0x3b, 0xec, 0x14,
	0xf9, 0xc0, 0xd0, 0x44, 0xc1, 0xe1, 0xda, 0xdf,
	0x53, 0x4a, 0x58, 0xb6, 0x10, 0x4d, 0x24, 0x45,
	0x0a, 0x60, 0x6e, 0x62, 0x6f, 0x85, 0x6e, 0xc0,
	0x4c, 0xf2, 0x6b, 0x7f, 0x73, 0x07, 0xd9, 0xbf,
	0xd2, 0x14, 0x44, 0xd2, 0xc2, 0x9b, 0xf7, 0xc3,
	0x04, 0xdd, 0xce, 0x2c, 0x25, 0xde, 0x47, 0x94,
	0x36, 0x7d, 0xef, 0x86, 0xef, 0x4a, 0x27, 0x68,
	0xd3, 0x0c, 0xc9, 0x9e, 0x28, 0xcf, 0xf6, 0x03,
	0x49, 0xed, 0xe6, 0xf4, 0x9f, 0x64, 0x87, 0x1d,
	0x1f, 0xff, 0x1d, 0xa0, 0xa1, 0xca, 0x4f, 0x1f,
	0xc0, 0x36, 0xe9, 0xb8, 0x54, 0x7a, 0xaf, 0xd1,
	0x3f, 0x56, 0x9e, 0x73, 0xc1, 0x98, 0xbc, 0x9d,
	0xff, 0xb9, 0xf2, 0xf2, 0x12, 0xfa, 0xdc, 0x9c,
	0xc4, 0x1f, 0xb0, 0xac, 0x85, 0x3f, 0x2b, 0xed,
	0xfb, 0x1d, 0x4b, 0x9f, 0x45, 0xf3, 0xc4, 0x54,
	0x56, 0xd6, 0x8f, 0x57, 0xfe, 0x02, 0xb2, 0x54,
	0xd3, 0x04, 0x88, 0x53, 0x6d, 0xd1, 0x5e, 0x2d,
	0xa2, 0x06, 0x2b, 0x0d, 0xa7, 0x7c, 0xae, 0x33,
	0x8b, 0x46, 0x83, 0x17, 0xe7, 0xab, 0xc3, 0xbf,
	0xa2, 0x06, 0x83, 0xe9, 0x94, 0x4b, 0x44, 0x78,
	0x5c, 0x19, 0xee, 0x1a, 0x4c, 0x68, 0x0f, 0x28,
	0x7f, 0x7f, 0x6f, 0x7e, 0x85, 0x2c, 0x20, 0x06,
	0x0b, 0xa5, 0x46, 0xff, 0xd5, 0xa4, 0xea, 0x29,
	0xa1, 0x9a, 0x18, 0xb6, 0x00, 0x8f, 0xec, 0x22,
	0x96, 0xa2, 0x7c, 0x0f, 0x13, 0x6c, 0x1b, 0x66,
	0x5c, 0x3a, 0xea, 0x6c, 0x70, 0x7d, 0x55, 0x55,
	0xcf, 0xe9, 0x31, 0x6b, 0x1f, 0x78, 0x63, 0xc2,
	0x08, 0x41, 0x18, 0x39, 0xf9, 0x1d, 0xac, 0xa7,
	0x2d, 0xd4, 0xd1, 0x26, 0xe6, 0x01, 0x4d, 0x62,
	0xb4, 0x0b, 0x13, 0xf1, 0x8c, 0xbd, 0x1c, 0xdb,
	0x4a, 0xc4, 0xd4, 0xb1, 0x11, 0x63, 0xb8, 0x7a,
	0x90, 0x76, 0xfe, 0x4e, 0x4b, 0x81, 0x44, 0x70,
	0x1c, 0xd9, 0x08, 0xfa, 0x01, 0xf0, 0x48, 0x73,
	0x25, 0x33, 0xf1, 0x4a, 0x8b, 0x68, 0x07, 0x7b,
};

struct
cperf_rsa_test_data rsa_qt_perf_data[4] = {
	{
		.name = "rsa_1024_qt",
		.n = {
			.data = rsa_n,
			.length = sizeof(rsa_n),
		},
		.e = {
			.data = rsa_e,
			.length = sizeof(rsa_e),
		},
		.d = {
			.data = NULL,
			.length = 0,
		},
		.p = {
			.data = rsa_p,
			.length = sizeof(rsa_p),
		},
		.q = {
			.data = rsa_q,
			.length = sizeof(rsa_q),
		},
		.dp = {
			.data = rsa_dp,
			.length = sizeof(rsa_dp),
		},
		.dq = {
			.data = rsa_dq,
			.length = sizeof(rsa_dq),
		},
		.qinv = {
			.data = rsa_qinv,
			.length = sizeof(rsa_qinv),
		},
		.key_type = RTE_RSA_KEY_TYPE_QT,
		.padding = RTE_CRYPTO_RSA_PADDING_PKCS1_5,
	},
	{
		.name = "rsa_2048_qt",
		.n = {
			.data = rsa_2048_n,
			.length = sizeof(rsa_2048_n),
		},
		.e = {
			.data = rsa_e,
			.length = sizeof(rsa_e),
		},
		.d = {
			.data = NULL,
			.length = 0,
		},
		.p = {
			.data = rsa_2048_p,
			.length = sizeof(rsa_2048_p),
		},
		.q = {
			.data = rsa_2048_q,
			.length = sizeof(rsa_2048_q),
		},
		.dp = {
			.data = rsa_2048_dp,
			.length = sizeof(rsa_2048_dp),
		},
		.dq = {
			.data = rsa_2048_dq,
			.length = sizeof(rsa_2048_dq),
		},
		.qinv = {
			.data = rsa_2048_qinv,
			.length = sizeof(rsa_2048_qinv),
		},
		.key_type = RTE_RSA_KEY_TYPE_QT,
		.padding = RTE_CRYPTO_RSA_PADDING_PKCS1_5,
	},
	{
		.name = "rsa_4096_qt",
		.n = {
			.data = rsa_4096_n,
			.length = sizeof(rsa_4096_n),
		},
		.e = {
			.data = rsa_e,
			.length = sizeof(rsa_e),
		},
		.d = {
			.data = NULL,
			.length = 0,
		},
		.p = {
			.data = rsa_4096_p,
			.length = sizeof(rsa_4096_p),
		},
		.q = {
			.data = rsa_4096_q,
			.length = sizeof(rsa_4096_q),
		},
		.dp = {
			.data = rsa_4096_dp,
			.length = sizeof(rsa_4096_dp),
		},
		.dq = {
			.data = rsa_4096_dq,
			.length = sizeof(rsa_4096_dq),
		},
		.qinv = {
			.data = rsa_4096_qinv,
			.length = sizeof(rsa_4096_qinv),
		},
		.key_type = RTE_RSA_KEY_TYPE_QT,
		.padding = RTE_CRYPTO_RSA_PADDING_PKCS1_5,
	},
	{
		.name = "rsa_8192_qt",
		.n = {
			.data = rsa_8192_n,
			.length = sizeof(rsa_8192_n),
		},
		.e = {
			.data = rsa_e,
			.length = sizeof(rsa_e),
		},
		.d = {
			.data = NULL,
			.length = 0,
		},
		.p = {
			.data = rsa_8192_p,
			.length = sizeof(rsa_8192_p),
		},
		.q = {
			.data = rsa_8192_q,
			.length = sizeof(rsa_8192_q),
		},
		.dp = {
			.data = rsa_8192_dp,
			.length = sizeof(rsa_8192_dp),
		},
		.dq = {
			.data = rsa_8192_dq,
			.length = sizeof(rsa_8192_dq),
		},
		.qinv = {
			.data = rsa_8192_qinv,
			.length = sizeof(rsa_8192_qinv),
		},
		.key_type = RTE_RSA_KEY_TYPE_QT,
		.padding = RTE_CRYPTO_RSA_PADDING_PKCS1_5,
	}
};

struct
cperf_rsa_test_data rsa_exp_perf_data[4] = {
	{
		.name = "rsa_1024_exp",
		.n = {
			.data = rsa_n,
			.length = sizeof(rsa_n),
		},
		.e = {
			.data = rsa_e,
			.length = sizeof(rsa_e),
		},
		.d = {
			.data = rsa_d,
			.length = sizeof(rsa_d),
		},
		.key_type = RTE_RSA_KEY_TYPE_EXP,
		.padding = RTE_CRYPTO_RSA_PADDING_PKCS1_5,
	},
	{
		.name = "rsa_2048_exp",
		.n = {
			.data = rsa_2048_n,
			.length = sizeof(rsa_2048_n),
		},
		.e = {
			.data = rsa_e,
			.length = sizeof(rsa_e),
		},
		.d = {
			.data = rsa_2048_d,
			.length = sizeof(rsa_2048_d),
		},
		.key_type = RTE_RSA_KEY_TYPE_EXP,
		.padding = RTE_CRYPTO_RSA_PADDING_PKCS1_5,
	},
	{
		.name = "rsa_4096_exp",
		.n = {
			.data = rsa_4096_n,
			.length = sizeof(rsa_4096_n),
		},
		.e = {
			.data = rsa_e,
			.length = sizeof(rsa_e),
		},
		.d = {
			.data = rsa_4096_d,
			.length = sizeof(rsa_4096_d),
		},
		.key_type = RTE_RSA_KEY_TYPE_EXP,
		.padding = RTE_CRYPTO_RSA_PADDING_PKCS1_5,
	},
	{
		.name = "rsa_8192_exp",
		.n = {
			.data = rsa_8192_n,
			.length = sizeof(rsa_8192_n),
		},
		.e = {
			.data = rsa_e,
			.length = sizeof(rsa_e),
		},
		.d = {
			.data = rsa_8192_d,
			.length = sizeof(rsa_8192_d),
		},
		.key_type = RTE_RSA_KEY_TYPE_EXP,
		.padding = RTE_CRYPTO_RSA_PADDING_PKCS1_5,
	}
};

struct
cperf_rsa_test_data rsa_pub_perf_data[4] = {
	{
		.name = "rsa_1024_pub",
		.n = {
			.data = rsa_n,
			.length = sizeof(rsa_n),
		},
		.e = {
			.data = rsa_e,
			.length = sizeof(rsa_e),
		},
		.key_type = RTE_RSA_KEY_TYPE_EXP,
		.padding = RTE_CRYPTO_RSA_PADDING_PKCS1_5,
	},
	{
		.name = "rsa_2048_pub",
		.n = {
			.data = rsa_2048_n,
			.length = sizeof(rsa_2048_n),
		},
		.e = {
			.data = rsa_e,
			.length = sizeof(rsa_e),
		},
		.key_type = RTE_RSA_KEY_TYPE_EXP,
		.padding = RTE_CRYPTO_RSA_PADDING_PKCS1_5,
	},
	{
		.name = "rsa_4096_pub",
		.n = {
			.data = rsa_4096_n,
			.length = sizeof(rsa_4096_n),
		},
		.e = {
			.data = rsa_e,
			.length = sizeof(rsa_e),
		},
		.key_type = RTE_RSA_KEY_TYPE_EXP,
		.padding = RTE_CRYPTO_RSA_PADDING_PKCS1_5,
	},
	{
		.name = "rsa_8192_pub",
		.n = {
			.data = rsa_8192_n,
			.length = sizeof(rsa_8192_n),
		},
		.e = {
			.data = rsa_e,
			.length = sizeof(rsa_e),
		},
		.key_type = RTE_RSA_KEY_TYPE_EXP,
		.padding = RTE_CRYPTO_RSA_PADDING_PKCS1_5,
	}
};

struct cperf_test_vector*
cperf_test_vector_get_dummy(struct cperf_options *options)
{
	struct cperf_test_vector *t_vec;

	t_vec = (struct cperf_test_vector *)rte_malloc(NULL,
			sizeof(struct cperf_test_vector), 0);
	if (t_vec == NULL)
		return t_vec;

	t_vec->plaintext.data = plaintext;
	t_vec->plaintext.length = options->max_buffer_size;

	if (options->op_type == CPERF_ASYM_MODEX) {
		t_vec->modex.mod = options->modex_data->modulus.data;
		t_vec->modex.exp = options->modex_data->exponent.data;
		t_vec->modex.mlen = options->modex_data->modulus.len;
		t_vec->modex.elen = options->modex_data->exponent.len;
	}

	if (options->op_type ==	CPERF_PDCP || options->op_type == CPERF_TLS ||
			options->op_type == CPERF_IPSEC) {
		if (options->cipher_algo == RTE_CRYPTO_CIPHER_NULL) {
			t_vec->cipher_key.length = 0;
			t_vec->ciphertext.data = plaintext;
			t_vec->cipher_key.data = NULL;
		} else {
			t_vec->cipher_key.length = options->cipher_key_sz;
			t_vec->ciphertext.data = ciphertext;
			t_vec->cipher_key.data = cipher_key;
		}

		if (options->op_type == CPERF_IPSEC)
			t_vec->plaintext.data = ipsec_plaintext;

		/* Init IV data ptr */
		t_vec->cipher_iv.data = NULL;

		if (options->cipher_iv_sz != 0) {
			/* Set IV parameters */
			t_vec->cipher_iv.data = rte_malloc(NULL,
					options->cipher_iv_sz, 16);
			if (t_vec->cipher_iv.data == NULL) {
				rte_free(t_vec);
				return NULL;
			}
			memcpy(t_vec->cipher_iv.data, iv, options->cipher_iv_sz);
		}
		t_vec->ciphertext.length = options->max_buffer_size;
		t_vec->cipher_iv.length = options->cipher_iv_sz;
		t_vec->data.cipher_offset = 0;
		t_vec->data.cipher_length = options->max_buffer_size;
		if (options->auth_algo == RTE_CRYPTO_AUTH_NULL) {
			t_vec->auth_key.length = 0;
			t_vec->auth_key.data = NULL;
			t_vec->digest.data = NULL;
			t_vec->digest.length = 0;
		} else {
			t_vec->auth_key.length = options->auth_key_sz;
			t_vec->auth_key.data = auth_key;

			t_vec->digest.data = rte_malloc(NULL,
					options->digest_sz,
					16);
			if (t_vec->digest.data == NULL) {
				rte_free(t_vec->cipher_iv.data);
				rte_free(t_vec);
				return NULL;
			}
			t_vec->digest.phys_addr =
				rte_malloc_virt2iova(t_vec->digest.data);
			t_vec->digest.length = options->digest_sz;
			memcpy(t_vec->digest.data, digest,
					options->digest_sz);
		}
		t_vec->data.auth_offset = 0;
		t_vec->data.auth_length = options->max_buffer_size;
	}

	if (options->op_type ==	CPERF_CIPHER_ONLY ||
			options->op_type == CPERF_CIPHER_THEN_AUTH ||
			options->op_type == CPERF_AUTH_THEN_CIPHER ||
			options->op_type == CPERF_DOCSIS) {
		if (options->cipher_algo == RTE_CRYPTO_CIPHER_NULL) {
			t_vec->cipher_key.length = 0;
			t_vec->ciphertext.data = plaintext;
			t_vec->cipher_key.data = NULL;
		} else {
			t_vec->cipher_key.length = options->cipher_key_sz;
			t_vec->ciphertext.data = ciphertext;
			t_vec->cipher_key.data = cipher_key;
		}

		/* Init IV data ptr */
		t_vec->cipher_iv.data = NULL;

		if (options->cipher_iv_sz != 0) {
			/* Set IV parameters */
			t_vec->cipher_iv.data = rte_malloc(NULL,
					options->cipher_iv_sz, 16);
			if (t_vec->cipher_iv.data == NULL) {
				rte_free(t_vec);
				return NULL;
			}
			memcpy(t_vec->cipher_iv.data, iv, options->cipher_iv_sz);
		}
		t_vec->ciphertext.length = options->max_buffer_size;
		t_vec->cipher_iv.length = options->cipher_iv_sz;
		t_vec->data.cipher_offset = 0;
		t_vec->data.cipher_length = options->max_buffer_size;

	}

	if (options->op_type ==	CPERF_AUTH_ONLY ||
			options->op_type == CPERF_CIPHER_THEN_AUTH ||
			options->op_type == CPERF_AUTH_THEN_CIPHER) {
		if (options->auth_algo == RTE_CRYPTO_AUTH_NULL) {
			t_vec->auth_key.length = 0;
			t_vec->auth_key.data = NULL;
			t_vec->digest.data = NULL;
			t_vec->digest.length = 0;
		} else {
			t_vec->auth_key.length = options->auth_key_sz;
			t_vec->auth_key.data = auth_key;

			t_vec->digest.data = rte_malloc(NULL,
					options->digest_sz,
					16);
			if (t_vec->digest.data == NULL) {
				rte_free(t_vec->cipher_iv.data);
				rte_free(t_vec);
				return NULL;
			}
			t_vec->digest.phys_addr =
				rte_malloc_virt2iova(t_vec->digest.data);
			t_vec->digest.length = options->digest_sz;
			memcpy(t_vec->digest.data, digest,
					options->digest_sz);
		}
		t_vec->data.auth_offset = 0;
		t_vec->data.auth_length = options->max_buffer_size;

		/* Set IV parameters */
		t_vec->auth_iv.data = rte_malloc(NULL, options->auth_iv_sz,
				16);
		if (options->auth_iv_sz && t_vec->auth_iv.data == NULL) {
			if (options->op_type != CPERF_AUTH_ONLY)
				rte_free(t_vec->cipher_iv.data);
			rte_free(t_vec);
			return NULL;
		}
		memcpy(t_vec->auth_iv.data, iv, options->auth_iv_sz);
		t_vec->auth_iv.length = options->auth_iv_sz;
	}

	if (options->op_type == CPERF_AEAD || options->op_type == CPERF_TLS ||
			options->op_type == CPERF_IPSEC) {
		t_vec->aead_key.length = options->aead_key_sz;
		t_vec->aead_key.data = aead_key;

		if (options->aead_aad_sz) {
			t_vec->aad.data = rte_malloc(NULL,
					options->aead_aad_sz, 16);
			if (t_vec->aad.data == NULL) {
				rte_free(t_vec);
				return NULL;
			}

			if (options->aead_aad_sz > sizeof(aad))
				options->aead_aad_sz = sizeof(aad);

			memcpy(t_vec->aad.data, aad, options->aead_aad_sz);
			t_vec->aad.phys_addr = rte_malloc_virt2iova(t_vec->aad.data);
			t_vec->aad.length = options->aead_aad_sz;
		} else {
			t_vec->aad.data = NULL;
			t_vec->aad.length = 0;
		}

		t_vec->digest.data = rte_malloc(NULL, options->digest_sz,
						16);
		if (t_vec->digest.data == NULL) {
			rte_free(t_vec->aad.data);
			rte_free(t_vec);
			return NULL;
		}
		t_vec->digest.phys_addr =
				rte_malloc_virt2iova(t_vec->digest.data);
		t_vec->digest.length = options->digest_sz;
		memcpy(t_vec->digest.data, digest, options->digest_sz);
		t_vec->data.aead_offset = 0;
		t_vec->data.aead_length = options->max_buffer_size;

		/* Set IV parameters */
		t_vec->aead_iv.data = rte_malloc(NULL, options->aead_iv_sz,
				16);
		if (options->aead_iv_sz && t_vec->aead_iv.data == NULL) {
			rte_free(t_vec->aad.data);
			rte_free(t_vec->digest.data);
			rte_free(t_vec);
			return NULL;
		}
		memcpy(t_vec->aead_iv.data, iv, options->aead_iv_sz);
		t_vec->aead_iv.length = options->aead_iv_sz;
	}
	return t_vec;
}
