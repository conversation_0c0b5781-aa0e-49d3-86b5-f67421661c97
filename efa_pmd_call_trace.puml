@startuml EFA_PMD_Call_Trace
!theme plain
title AWS EFA PMD Call Tracing Diagram\nDPDK Application → EFA NIC Hardware

participant "DPDK Application" as App
participant "DPDK EthDev Framework" as EthDev
participant "EFA PMD" as EFA_PMD
participant "libefa (User Library)" as LibEFA
participant "rdma-core (ibverbs)" as RDMACore
participant "EFA Kernel Driver" as EFA_Kernel
participant "EFA NIC Hardware" as EFA_HW

== Device Discovery & Initialization ==

App -> EthDev: rte_eal_init()
activate EthDev
EthDev -> EFA_PMD: efa_pci_probe()
activate EFA_PMD
EFA_PMD -> LibEFA: efa_device_open()
activate LibEFA
LibEFA -> RDMACore: ibv_open_device()
activate RDMACore
RDMACore -> EFA_Kernel: ioctl(OPEN_DEVICE)
activate EFA_Kernel
EFA_Kernel -> EFA_HW: PCIe Configuration
activate EFA_HW
EFA_HW --> EFA_Kernel: Device Ready
EFA_Kernel --> RDMACore: <PERSON>ce <PERSON>le
deactivate EFA_Kernel
RDMACore --> LibEFA: ibv_context
deactivate RDMACore
LibEFA --> EFA_PMD: efa_device
deactivate LibEFA
EFA_PMD -> EFA_PMD: efa_eth_dev_init()
EFA_PMD --> EthDev: eth_dev registered
deactivate EFA_PMD
EthDev --> App: EAL initialized
deactivate EthDev

== Device Configuration ==

App -> EthDev: rte_eth_dev_configure(port_id, nb_rx_q, nb_tx_q, &conf)
activate EthDev
EthDev -> EFA_PMD: efa_dev_configure()
activate EFA_PMD
EFA_PMD -> LibEFA: efa_alloc_pd()
activate LibEFA
LibEFA -> RDMACore: ibv_alloc_pd()
activate RDMACore
RDMACore -> EFA_Kernel: ioctl(ALLOC_PD)
activate EFA_Kernel
EFA_Kernel -> EFA_HW: Allocate Protection Domain
EFA_HW --> EFA_Kernel: PD Handle
EFA_Kernel --> RDMACore: PD ID
deactivate EFA_Kernel
RDMACore --> LibEFA: ibv_pd
deactivate RDMACore
LibEFA --> EFA_PMD: efa_pd
deactivate LibEFA
EFA_PMD --> EthDev: Configuration complete
deactivate EFA_PMD
EthDev --> App: Device configured
deactivate EthDev

== Queue Setup ==

App -> EthDev: rte_eth_rx_queue_setup(port_id, queue_id, nb_desc, ...)
activate EthDev
EthDev -> EFA_PMD: efa_rx_queue_setup()
activate EFA_PMD
EFA_PMD -> LibEFA: efa_create_cq()
activate LibEFA
LibEFA -> RDMACore: ibv_create_cq()
activate RDMACore
RDMACore -> EFA_Kernel: ioctl(CREATE_CQ)
activate EFA_Kernel
EFA_Kernel -> EFA_HW: Create Completion Queue
EFA_HW --> EFA_Kernel: CQ Handle
EFA_Kernel --> RDMACore: CQ ID
deactivate EFA_Kernel
RDMACore --> LibEFA: ibv_cq
deactivate RDMACore
LibEFA --> EFA_PMD: efa_cq
deactivate LibEFA

EFA_PMD -> LibEFA: efa_create_qp()
activate LibEFA
LibEFA -> RDMACore: ibv_create_qp()
activate RDMACore
RDMACore -> EFA_Kernel: ioctl(CREATE_QP)
activate EFA_Kernel
EFA_Kernel -> EFA_HW: Create Queue Pair
EFA_HW --> EFA_Kernel: QP Handle
EFA_Kernel --> RDMACore: QP ID
deactivate EFA_Kernel
RDMACore --> LibEFA: ibv_qp
deactivate RDMACore
LibEFA --> EFA_PMD: efa_qp
deactivate LibEFA

EFA_PMD -> EFA_PMD: efa_setup_rx_resources()
EFA_PMD --> EthDev: RX Queue ready
deactivate EFA_PMD
EthDev --> App: RX Queue setup complete
deactivate EthDev

App -> EthDev: rte_eth_tx_queue_setup(port_id, queue_id, nb_desc, ...)
activate EthDev
EthDev -> EFA_PMD: efa_tx_queue_setup()
activate EFA_PMD
EFA_PMD -> EFA_PMD: efa_setup_tx_resources()
EFA_PMD --> EthDev: TX Queue ready
deactivate EFA_PMD
EthDev --> App: TX Queue setup complete
deactivate EthDev

== Device Start ==

App -> EthDev: rte_eth_dev_start(port_id)
activate EthDev
EthDev -> EFA_PMD: efa_dev_start()
activate EFA_PMD
EFA_PMD -> LibEFA: efa_modify_qp_to_rtr()
activate LibEFA
LibEFA -> RDMACore: ibv_modify_qp(IBV_QPS_RTR)
activate RDMACore
RDMACore -> EFA_Kernel: ioctl(MODIFY_QP)
activate EFA_Kernel
EFA_Kernel -> EFA_HW: Set QP to Ready-to-Receive
EFA_HW --> EFA_Kernel: QP State Updated
EFA_Kernel --> RDMACore: Success
deactivate EFA_Kernel
RDMACore --> LibEFA: QP Ready
deactivate RDMACore
LibEFA --> EFA_PMD: QP in RTR state
deactivate LibEFA

EFA_PMD -> LibEFA: efa_modify_qp_to_rts()
activate LibEFA
LibEFA -> RDMACore: ibv_modify_qp(IBV_QPS_RTS)
activate RDMACore
RDMACore -> EFA_Kernel: ioctl(MODIFY_QP)
activate EFA_Kernel
EFA_Kernel -> EFA_HW: Set QP to Ready-to-Send
EFA_HW --> EFA_Kernel: QP State Updated
EFA_Kernel --> RDMACore: Success
deactivate EFA_Kernel
RDMACore --> LibEFA: QP Ready
deactivate RDMACore
LibEFA --> EFA_PMD: QP in RTS state
deactivate LibEFA

EFA_PMD --> EthDev: Device started
deactivate EFA_PMD
EthDev --> App: Device ready for traffic
deactivate EthDev

== Packet Transmission ==

App -> EthDev: rte_eth_tx_burst(port_id, queue_id, tx_pkts, nb_pkts)
activate EthDev
EthDev -> EFA_PMD: efa_xmit_pkts()
activate EFA_PMD

loop for each packet
    EFA_PMD -> EFA_PMD: efa_prepare_tx_desc()
    EFA_PMD -> LibEFA: efa_post_send()
    activate LibEFA
    LibEFA -> RDMACore: ibv_post_send()
    activate RDMACore
    RDMACore -> EFA_Kernel: write to SQ doorbell
    activate EFA_Kernel
    EFA_Kernel -> EFA_HW: Ring SQ Doorbell
    activate EFA_HW
    EFA_HW -> EFA_HW: Process Send Request
    EFA_HW -> EFA_HW: DMA packet data
    EFA_HW --> EFA_Kernel: Send completion
    deactivate EFA_HW
    EFA_Kernel --> RDMACore: Completion event
    deactivate EFA_Kernel
    RDMACore --> LibEFA: Send posted
    deactivate RDMACore
    LibEFA --> EFA_PMD: Packet queued
    deactivate LibEFA
end

EFA_PMD --> EthDev: nb_tx (packets sent)
deactivate EFA_PMD
EthDev --> App: Transmission count
deactivate EthDev

== Packet Reception ==

App -> EthDev: rte_eth_rx_burst(port_id, queue_id, rx_pkts, nb_pkts)
activate EthDev
EthDev -> EFA_PMD: efa_recv_pkts()
activate EFA_PMD

EFA_PMD -> LibEFA: efa_poll_cq()
activate LibEFA
LibEFA -> RDMACore: ibv_poll_cq()
activate RDMACore
RDMACore -> EFA_Kernel: read CQ entries
activate EFA_Kernel
EFA_Kernel -> EFA_HW: Poll Completion Queue
activate EFA_HW
EFA_HW --> EFA_Kernel: Completion entries
deactivate EFA_HW
EFA_Kernel --> RDMACore: CQ entries
deactivate EFA_Kernel
RDMACore --> LibEFA: Completions
deactivate RDMACore
LibEFA --> EFA_PMD: Received packets
deactivate LibEFA

loop for each completion
    EFA_PMD -> EFA_PMD: efa_process_rx_completion()
    EFA_PMD -> EFA_PMD: efa_alloc_mbuf()
    EFA_PMD -> EFA_PMD: efa_post_recv() [refill]
end

EFA_PMD --> EthDev: nb_rx (packets received)
deactivate EFA_PMD
EthDev --> App: Received packets
deactivate EthDev

== Completion Processing ==

EFA_HW -> EFA_Kernel: Interrupt (if enabled)
activate EFA_Kernel
EFA_Kernel -> RDMACore: Event notification
activate RDMACore
RDMACore -> LibEFA: Completion event
activate LibEFA
LibEFA -> EFA_PMD: efa_completion_handler()
activate EFA_PMD
EFA_PMD -> EFA_PMD: Process completions
EFA_PMD --> LibEFA: Completions processed
deactivate EFA_PMD
LibEFA --> RDMACore: Event handled
deactivate LibEFA
RDMACore --> EFA_Kernel: Interrupt handled
deactivate RDMACore
EFA_Kernel --> EFA_HW: ACK interrupt
deactivate EFA_Kernel

== Device Stop & Cleanup ==

App -> EthDev: rte_eth_dev_stop(port_id)
activate EthDev
EthDev -> EFA_PMD: efa_dev_stop()
activate EFA_PMD
EFA_PMD -> LibEFA: efa_destroy_qp()
activate LibEFA
LibEFA -> RDMACore: ibv_destroy_qp()
activate RDMACore
RDMACore -> EFA_Kernel: ioctl(DESTROY_QP)
activate EFA_Kernel
EFA_Kernel -> EFA_HW: Destroy Queue Pair
EFA_HW --> EFA_Kernel: QP destroyed
EFA_Kernel --> RDMACore: Success
deactivate EFA_Kernel
RDMACore --> LibEFA: QP destroyed
deactivate RDMACore
LibEFA --> EFA_PMD: Resources freed
deactivate LibEFA
EFA_PMD --> EthDev: Device stopped
deactivate EFA_PMD
EthDev --> App: Stop complete
deactivate EthDev

@enduml
