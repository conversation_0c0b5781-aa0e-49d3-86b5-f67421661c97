name: build

on:
  push:
  schedule:
    - cron: '0 0 * * 1'

defaults:
  run:
    shell: bash --noprofile --norc -exo pipefail {0}

env:
  REF_GIT_BRANCH: main
  REF_GIT_REPO: https://github.com/DPDK/dpdk
  REF_GIT_TAG: v25.03

jobs:
  checkpatch:
    if: github.repository != 'DPDK/dpdk'
    name: Check patches
    runs-on: ubuntu-24.04
    steps:
    - name: Checkout sources
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Check patches
      run: |
        git remote add upstream ${{ env.REF_GIT_REPO }}
        git fetch upstream ${{ env.REF_GIT_BRANCH }}
        failed=
        devtools/check-doc-vs-code.sh upstream/${{ env.REF_GIT_BRANCH }} || failed=true
        devtools/check-meson.py || failed=true
        devtools/check-spdx-tag.sh || failed=true
        [ -z "$failed" ]
  ubuntu-vm-builds:
    name: ${{ join(matrix.config.*, '-') }}
    runs-on: ${{ matrix.config.os }}
    env:
      AARCH64: ${{ matrix.config.cross == 'aarch64' }}
      ABI_CHECKS: ${{ contains(matrix.config.checks, 'abi') }}
      ASAN: ${{ contains(matrix.config.checks, 'asan') }}
      BUILD_32BIT: ${{ matrix.config.cross == 'i386' }}
      BUILD_DEBUG: ${{ contains(matrix.config.checks, 'debug') }}
      BUILD_DOCS: ${{ contains(matrix.config.checks, 'doc') }}
      BUILD_EXAMPLES: ${{ contains(matrix.config.checks, 'examples') }}
      CC: ccache ${{ matrix.config.compiler }}
      CCACHE_DIR: $HOME/.ccache
      DEF_LIB: ${{ matrix.config.library }}
      LIBABIGAIL_VERSION: libabigail-2.6
      MINGW: ${{ matrix.config.cross == 'mingw' }}
      MINI: ${{ matrix.config.mini != '' }}
      PPC64LE: ${{ matrix.config.cross == 'ppc64le' }}
      RISCV64: ${{ matrix.config.cross == 'riscv64' }}
      RUN_TESTS: ${{ contains(matrix.config.checks, 'tests') }}
      STDATOMIC: ${{ contains(matrix.config.checks, 'stdatomic') }}

    strategy:
      fail-fast: false
      matrix:
        config:
          - os: ubuntu-22.04
            compiler: gcc
            mini: mini
          - os: ubuntu-22.04
            compiler: gcc
            checks: stdatomic
          - os: ubuntu-22.04
            compiler: clang
            checks: stdatomic
          - os: ubuntu-22.04
            compiler: gcc
            checks: abi+debug+doc+examples+tests
          - os: ubuntu-22.04
            compiler: clang
            checks: asan+doc+tests
          - os: ubuntu-22.04
            compiler: gcc
            library: static
            cross: i386
          - os: ubuntu-22.04
            compiler: gcc
            library: static
            cross: mingw
          - os: ubuntu-22.04
            compiler: gcc
            library: shared
            cross: aarch64
          - os: ubuntu-22.04
            compiler: gcc
            cross: ppc64le
          - os: ubuntu-22.04
            compiler: gcc
            cross: riscv64

    steps:
    - name: Checkout sources
      uses: actions/checkout@v4
    - name: Generate cache keys
      id: get_ref_keys
      run: |
        echo 'ccache=ccache-${{ matrix.config.os }}-${{ matrix.config.compiler }}-${{ matrix.config.cross }}-'$(date -u +%Y-w%W) >> $GITHUB_OUTPUT
        echo 'libabigail=libabigail-${{ env.LIBABIGAIL_VERSION }}-${{ matrix.config.os }}' >> $GITHUB_OUTPUT
        echo 'abi=abi-${{ matrix.config.os }}-${{ matrix.config.compiler }}-${{ matrix.config.cross }}-${{ env.REF_GIT_TAG }}' >> $GITHUB_OUTPUT
    - name: Retrieve ccache cache
      uses: actions/cache@v4
      with:
        path: ~/.ccache
        key: ${{ steps.get_ref_keys.outputs.ccache }}-${{ github.ref }}
        restore-keys: |
          ${{ steps.get_ref_keys.outputs.ccache }}-refs/heads/main
    - name: Retrieve libabigail cache
      id: libabigail-cache
      uses: actions/cache@v4
      if: env.ABI_CHECKS == 'true'
      with:
        path: libabigail
        key: ${{ steps.get_ref_keys.outputs.libabigail }}
    - name: Retrieve ABI reference cache
      uses: actions/cache@v4
      if: env.ABI_CHECKS == 'true'
      with:
        path: reference
        key: ${{ steps.get_ref_keys.outputs.abi }}
    - name: Configure i386 architecture
      if: env.BUILD_32BIT == 'true'
      run: sudo dpkg --add-architecture i386
    - name: Update APT cache
      run: sudo apt update || true
    - name: Install packages
      run: sudo apt install -y ccache libarchive-dev libbsd-dev libbpf-dev
        libfdt-dev libibverbs-dev libipsec-mb-dev libisal-dev libjansson-dev
        libnuma-dev libpcap-dev libssl-dev libvirt-dev ninja-build pkg-config python3-pip
        python3-pyelftools python3-setuptools python3-wheel zlib1g-dev
    - name: Install libabigail build dependencies if no cache is available
      if: env.ABI_CHECKS == 'true' && steps.libabigail-cache.outputs.cache-hit != 'true'
      run: sudo apt install -y autoconf automake libdw-dev libtool libxml2-dev libxxhash-dev
    - name: Install i386 cross compiling packages
      if: env.BUILD_32BIT == 'true'
      run: sudo apt install -y gcc-multilib g++-multilib libnuma-dev:i386
    - name: Install aarch64 cross compiling packages
      if: env.AARCH64 == 'true'
      run: sudo apt install -y crossbuild-essential-arm64
    - name: Install mingw cross compiling packages
      if: env.MINGW == 'true'
      run: sudo apt install -y mingw-w64 mingw-w64-tools
    - name: Install ppc64le cross compiling packages
      if: env.PPC64LE == 'true'
      run: sudo apt install -y crossbuild-essential-ppc64el
    - name: Install riscv64 cross compiling packages
      if: env.RISCV64 == 'true'
      run: sudo apt install -y crossbuild-essential-riscv64
    - name: Install test tools packages
      if: env.AARCH64 != 'true' || env.PPC64LE != 'true' || env.RISCV64 != 'true' || env.RUN_TESTS == 'true'
      run: sudo apt install -y babeltrace gdb jq
    - name: Install doc generation packages
      if: env.BUILD_DOCS == 'true'
      run: sudo apt install -y doxygen graphviz man-db python3-sphinx
        python3-sphinx-rtd-theme
    - name: Run setup
      run: |
        .ci/linux-setup.sh
        # Workaround on $HOME permissions as EAL checks them for plugin loading
        chmod o-w $HOME
    - name: Build and test
      run: .ci/linux-build.sh
    - name: Upload logs on failure
      if: failure()
      uses: actions/upload-artifact@v4
      with:
        name: meson-logs-${{ join(matrix.config.*, '-') }}
        path: |
          build/.ninja_log
          build/gdb.log
          build/meson-logs/meson-log.txt
          build/meson-logs/testlog.txt

  windows-vm-builds:
    name: ${{ join(matrix.config.*, '-') }}
    runs-on: ${{ matrix.config.os }}
    strategy:
      fail-fast: false
      matrix:
        config:
          - os: windows-2022
            compiler: msvc

    steps:
    - name: Checkout sources
      uses: actions/checkout@v4
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.x'
    - name: Install dependencies
      run: python -m pip install meson==1.6.1 ninja
    - name: Configure
      shell: cmd
      run: |
        call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\Tools\VsDevCmd.bat" -host_arch=amd64 -arch=amd64
        meson setup -Denable_stdatomic=true build
    - name: Build
      shell: cmd
      run: |
        call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\Tools\VsDevCmd.bat" -host_arch=amd64 -arch=amd64
        meson compile -C build
    - name: Upload logs on failure
      if: failure()
      uses: actions/upload-artifact@v4
      with:
        name: meson-logs-${{ join(matrix.config.*, '-') }}
        path: |
          build/.ninja_log
          build/meson-logs/meson-log.txt

  prepare-container-images:
    name: ${{ join(matrix.config.*, '-') }}
    runs-on: ubuntu-latest
    outputs:
      image: ${{ steps.get_keys.outputs.image }}

    strategy:
      fail-fast: false
      matrix:
        config:
          - image: fedora:39

    steps:
    - name: Generate various keys
      id: get_keys
      run: |
        echo 'image=image-${{ matrix.config.image }}-'$(date -u +%Y-%m-%d) >> $GITHUB_OUTPUT
    - name: Retrieve image cache
      id: image_cache
      uses: actions/cache@v4
      with:
        path: ~/.image
        key: ${{ steps.get_keys.outputs.image }}
    - name: Pull and prepare a fresh image
      if: steps.image_cache.outputs.cache-hit != 'true'
      run: |
        docker pull registry.fedoraproject.org/${{ matrix.config.image }}
        docker run -d -i --rm --name dpdk \
          registry.fedoraproject.org/${{ matrix.config.image }} \
          bash -li
    - name: Update
      if: steps.image_cache.outputs.cache-hit != 'true'
      run: docker exec -i dpdk dnf update -y
    - name: Install packages
      if: steps.image_cache.outputs.cache-hit != 'true'
      run: docker exec -i dpdk dnf install -y ccache intel-ipsec-mb-devel
        isa-l-devel jansson-devel libarchive-devel libatomic libbsd-devel
        libbpf-devel libfdt-devel libpcap-devel libvirt-devel libxdp-devel
        ninja-build numactl-devel openssl-devel python3-pip python3-pyelftools
        python3-setuptools python3-wheel rdma-core-devel zlib-devel
    - name: Save image in cache
      if: steps.image_cache.outputs.cache-hit != 'true'
      run: |
        docker commit dpdk dpdk-local
        mkdir -p ~/.image
        docker save -o ~/.image/${{ matrix.config.image }}.tar dpdk-local
    - name: Stop image
      if: steps.image_cache.outputs.cache-hit != 'true'
      run: docker kill dpdk

  rpm-container-builds:
    needs: prepare-container-images
    name: ${{ join(matrix.config.*, '-') }}
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        config:
          - image: fedora:39
            compiler: gcc
          - image: fedora:39
            compiler: clang

    steps:
    - name: Checkout sources
      uses: actions/checkout@v4
    - name: Generate various keys
      id: get_keys
      run: |
        echo 'ccache=ccache-${{ matrix.config.image }}-${{ matrix.config.compiler }}-'$(date -u +%Y-w%W) >> $GITHUB_OUTPUT
        echo 'logs=meson-logs-${{ join(matrix.config.*, '-') }}' | tr -d ':' >> $GITHUB_OUTPUT
    - name: Retrieve image cache
      id: image_cache
      uses: actions/cache@v4
      with:
        path: ~/.image
        key: ${{ needs.prepare-container-images.outputs.image }}
    - name: Fail if no image (not supposed to happen)
      if: steps.image_cache.outputs.cache-hit != 'true'
      run: |
        echo 'Image ${{ matrix.config.image }} is not cached.'
        false
    - name: Retrieve ccache cache
      uses: actions/cache@v4
      with:
        path: ~/.ccache
        key: ${{ steps.get_keys.outputs.ccache }}-${{ github.ref }}
        restore-keys: |
          ${{ steps.get_keys.outputs.ccache }}-refs/heads/main
    - name: Prepare working directory
      run: |
        mkdir -p ~/.ccache
        > ~/env
        echo CC=ccache ${{ matrix.config.compiler }} >> ~/env
        echo CCACHE_DIR=/root/.ccache >> ~/env
        echo DEF_LIB=${{ matrix.config.library }} >> ~/env
        echo STDATOMIC=false >> ~/env
    - name: Load the cached image
      run: |
        docker load -i ~/.image/${{ matrix.config.image }}.tar
        docker run -d -i --rm --name dpdk \
          --mount type=bind,src=$HOME/.ccache,dst=/root/.ccache \
          --mount type=bind,src=$(pwd),dst=/root/dpdk \
          --env-file ~/env \
          -w /root/dpdk \
          dpdk-local
          bash -li
    - name: Update
      run: docker exec -i dpdk dnf update -y || true
    - name: Install packages
      run: docker exec -i dpdk dnf install -y ccache intel-ipsec-mb-devel
        isa-l-devel jansson-devel libarchive-devel libatomic libbsd-devel
        libbpf-devel libfdt-devel libpcap-devel libvirt-devel libxdp-devel
        ninja-build numactl-devel openssl-devel python3-pip python3-pyelftools
        python3-setuptools python3-wheel rdma-core-devel zlib-devel
        ${{ matrix.config.compiler }}
    - name: Run setup
      run: docker exec -i dpdk .ci/linux-setup.sh
    - name: Build
      run: docker exec -i dpdk .ci/linux-build.sh
    - name: Stop image
      run: docker kill dpdk
    - name: Upload logs on failure
      if: failure()
      uses: actions/upload-artifact@v4
      with:
        name: ${{ steps.get_keys.outputs.logs }}
        path: |
          build/.ninja_log
          build/meson-logs/meson-log.txt
