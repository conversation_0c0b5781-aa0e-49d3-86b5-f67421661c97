[binaries]
c = ['ccache', 'riscv64-unknown-linux-gnu-gcc']
cpp = ['ccache', 'riscv64-unknown-linux-gnu-g++']
ar = 'riscv64-unknown-linux-gnu-ar'
strip = 'riscv64-unknown-linux-gnu-strip'
pcap-config = ''

[host_machine]
system = 'linux'
cpu_family = 'riscv64'
cpu = 'rv64gc'
endian = 'little'

[properties]
vendor_id = '0x489'
arch_id = '0x8000000000000007'
max_lcores = 4
max_numa_nodes = 1
pkg_config_libdir = '/usr/lib/riscv64-linux-gnu/pkgconfig'
sys_root = '/opt/riscv/sysroot'
