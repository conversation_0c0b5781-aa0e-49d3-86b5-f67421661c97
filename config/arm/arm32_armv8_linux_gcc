[binaries]
c = ['ccache', 'arm-linux-gnueabihf-gcc']
cpp = ['ccache', 'arm-linux-gnueabihf-g++']
ar = 'arm-linux-gnueabihf-gcc-ar'
strip = 'arm-linux-gnueabihf-strip'
pkgconfig = 'arm-linux-gnueabihf-pkg-config'
pkg-config = 'arm-linux-gnueabihf-pkg-config'
pcap-config = ''

[host_machine]
system = 'linux'
cpu_family = 'aarch32'
cpu = 'armv8-a'
endian = 'little'

[properties]
platform = 'generic_aarch32'
