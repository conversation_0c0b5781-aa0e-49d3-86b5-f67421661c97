[binaries]
c = ['ccache', 'aarch64-linux-gnu-gcc']
cpp = ['ccache', 'aarch64-linux-gnu-g++']
ar = 'aarch64-linux-gnu-ar'
as = 'aarch64-linux-gnu-as'
strip = 'aarch64-linux-gnu-strip'
pkgconfig = 'aarch64-linux-gnu-pkg-config'
pkg-config = 'aarch64-linux-gnu-pkg-config'
pcap-config = ''

[host_machine]
system = 'linux'
cpu_family = 'aarch64'
cpu = 'armv8-a'
endian = 'little'

[properties]
platform = 'armada'
