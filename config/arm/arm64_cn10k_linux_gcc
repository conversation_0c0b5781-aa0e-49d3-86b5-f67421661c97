[binaries]
c = ['ccache', 'aarch64-linux-gnu-gcc']
cpp = ['ccache', 'aarch64-linux-gnu-g++']
ar = 'aarch64-linux-gnu-gcc-ar'
strip = 'aarch64-linux-gnu-strip'
pkgconfig = 'aarch64-linux-gnu-pkg-config'
pkg-config = 'aarch64-linux-gnu-pkg-config'
pcap-config = ''
cmake = 'cmake'

[host_machine]
system = 'linux'
cpu_family = 'aarch64'
cpu = 'armv8.6-a'
endian = 'little'

[properties]
platform = 'cn10k'
