# Please keep these options sorted alphabetically.

option('cxi_headers_path', type: 'string', value: '', description:
       'Path to CXI hardware headers directory')
option('check_includes', type: 'boolean', value: false, description:
       'build "chkincs" to verify each header file can compile alone')
option('cpu_instruction_set', type: 'string', value: 'auto',
	description: 'Set the target machine ISA (instruction set architecture). Will be set according to the platform option by default.')
option('developer_mode', type: 'feature', description:
       'turn on additional build checks relevant for DPDK developers')
option('disable_apps', type: 'string', value: '', description:
       'Comma-separated list of apps to explicitly disable.')
option('disable_drivers', type: 'string', value: '', description:
       'Comma-separated list of drivers to explicitly disable.')
option('disable_libs', type: 'string', value: '', description:
       'Comma-separated list of optional libraries to explicitly disable. [NOTE: mandatory libs cannot be disabled]')
option('drivers_install_subdir', type: 'string', value: 'dpdk/pmds-<VERSION>', description:
       'Subdirectory of libdir where to install PMDs. Defaults to using a versioned subdirectory.')
option('enable_docs', type: 'boolean', value: false, description:
       'build documentation')
option('enable_apps', type: 'string', value: '', description:
       'Comma-separated list of apps to build. If unspecified, build all apps.')
option('enable_deprecated_libs', type: 'string', value: '', description:
       'Comma-separated list of deprecated libraries to explicitly enable.')
option('enable_drivers', type: 'string', value: '', description:
       'Comma-separated list of drivers to build. If unspecified, build all drivers.')
option('enable_driver_sdk', type: 'boolean', value: false, description:
       'Install headers to build drivers.')
option('enable_kmods', type: 'boolean', value: true, description:
       '[Deprecated - will be removed in future release] build kernel modules')
option('enable_libs', type: 'string', value: '', description:
       'Comma-separated list of optional libraries to explicitly enable. [NOTE: mandatory libs are always enabled]')
option('examples', type: 'string', value: '', description:
       'Comma-separated list of examples to build by default')
option('ibverbs_link', type: 'combo', choices : ['static', 'shared', 'dlopen'], value: 'shared', description:
       'Linkage method (static/shared/dlopen) for NVIDIA PMDs with ibverbs dependencies.')
option('include_subdir_arch', type: 'string', value: '', description:
       'subdirectory where to install arch-dependent headers')
option('kernel_dir', type: 'string', value: '', description:
       'Path to the kernel for building kernel modules. Headers must be in $kernel_dir or $kernel_dir/build. Modules will be installed in /lib/modules.')
option('machine', type: 'string', value: 'auto', description:
       'Alias of cpu_instruction_set.')
option('max_ethports', type: 'integer', value: 32, description:
       'maximum number of Ethernet devices')
option('max_lcores', type: 'string', value: 'default', description:
       'Set maximum number of cores/threads supported by EAL; "default" is different per-arch, "detect" detects the number of cores on the build machine.')
option('max_numa_nodes', type: 'string', value: 'default', description:
       'Set the highest NUMA node supported by EAL; "default" is different per-arch, "detect" detects the highest NUMA node on the build machine.')
option('enable_iova_as_pa', type: 'boolean', value: true, description:
       'Support the use of physical addresses for IO addresses, such as used by UIO or VFIO in no-IOMMU mode. When disabled, DPDK can only run with IOMMU support for address mappings, but will have more space available in the mbuf structure.')
option('mbuf_refcnt_atomic', type: 'boolean', value: true, description:
       'Atomically access the mbuf refcnt.')
option('platform', type: 'string', value: 'native', description:
       'Platform to build, either "native", "generic" or a SoC. Please refer to the Linux build guide for more information.')
option('pkt_mbuf_headroom', type: 'integer', value: 128, description:
       'Default data offset (in bytes) in a packet buffer to leave room for additional headers.')
option('enable_stdatomic', type: 'boolean', value: false, description:
       'enable use of C11 stdatomic')
option('enable_trace_fp', type: 'boolean', value: false, description:
       'enable fast path trace points.')
option('tests', type: 'boolean', value: true, description:
       'build unit tests')
option('use_hpet', type: 'boolean', value: false, description:
       'use HPET timer in EAL')
