;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
; Core suppression rules: DO NOT TOUCH ;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

[suppress_function]
        symbol_version = EXPERIMENTAL
[suppress_variable]
        symbol_version = EXPERIMENTAL

[suppress_function]
        symbol_version = INTERNAL
[suppress_variable]
        symbol_version = INTERNAL

; Ignore generated PMD information strings
[suppress_variable]
        name_regexp = _pmd_info$

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
; Special rules to skip libraries ;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;
; This is not a libabigail rule (see check-abi.sh).
; This is used for driver removal and other special cases like mlx glue libs.
;
; SKIP_LIBRARY=librte_common_iavf
; SKIP_LIBRARY=librte_common_idpf
; SKIP_LIBRARY=librte_common_mlx5_glue
; SKIP_LIBRARY=librte_net_mlx4_glue
; SKIP_LIBRARY=librte_net_igc

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
; Experimental APIs exceptions ;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
; Temporary exceptions till next major ABI version ;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
[suppress_type]
        name = rte_dma_port_param
        type_kind = struct
        has_size_change = no
        has_data_member = {pcie}
