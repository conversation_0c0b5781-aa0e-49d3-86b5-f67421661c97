# SPDX-License-Identifier: BSD-3-Clause
# Copyright(c) 2017 Intel Corporation


drivers = [
        'af_packet',
        'af_xdp',
        'ark',
        'atlantic',
        'avp',
        'axgbe',
        'bnx2x',
        'bnxt',
        'bonding',
        'cxi',
        'cnxk',
        'cxgbe',
        'dpaa',
        'dpaa2',
        'ena',
        'enetc',
        'enetfec',
        'enic',
        'failsafe',
        'gve',
        'hinic',
        'hns3',
        'intel/e1000',
        'intel/fm10k',
        'intel/i40e',
        'intel/iavf',
        'intel/ice',
        'intel/idpf',
        'intel/ipn3ke',
        'intel/ixgbe',
        'intel/cpfl',  # depends on idpf, so must come after it
        'ionic',
        'mana',
        'memif',
        'mlx4',
        'mlx5',
        'mvneta',
        'mvpp2',
        'netvsc',
        'nfb',
        'nfp',
        'ngbe',
        'ntnic',
        'null',
        'octeontx',
        'octeon_ep',
        'pcap',
        'pfe',
        'qede',
        'r8169',
        'ring',
        'rnp',
        'sfc',
        'softnic',
        'tap',
        'thunderx',
        'txgbe',
        'vdev_netvsc',
        'vhost',
        'virtio',
        'vmxnet3',
        'xsc',
        'zxdh',
]
std_deps = ['ethdev', 'kvargs'] # 'ethdev' also pulls in mbuf, net, eal etc
std_deps += ['bus_pci']         # very many PMDs depend on PCI, so make std
std_deps += ['bus_vdev']        # same with vdev bus
