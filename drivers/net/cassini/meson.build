# SPDX-License-Identifier: BSD-3-Clause
# Copyright(c) 2024 Hewlett Packard Enterprise Development LP

if not is_linux
    build = false
    reason = 'only supported on Linux'
    subdir_done()
endif

sources = files(
    'cassini_ethdev.c',
    'cassini_hw.c',
    'cassini_rxtx.c',
)

headers = files(
    'cassini_ethdev.h',
    'cassini_hw.h',
)

# Include Cassini hardware headers from the system
cassini_headers_dep = dependency('cassini-headers', required: false)
if not cassini_headers_dep.found()
    # Fallback to local headers if available
    cassini_inc_dir = get_option('cassini_headers_path')
    if cassini_inc_dir != ''
        cassini_headers_dep = declare_dependency(include_directories: include_directories(cassini_inc_dir))
    else
        build = false
        reason = 'missing cassini headers dependency'
        subdir_done()
    endif
endif

deps += [cassini_headers_dep]

# Additional dependencies for Cassini hardware interface
deps += ['pci', 'mempool']

# Add meson option for cassini headers path
if get_option('cassini_headers_path') == ''
    # Try to find headers in standard locations
    cassini_inc_dirs = [
        '/usr/include/cassini',
        '/usr/local/include/cassini',
        '../../../include',  # Relative to this directory
    ]

    foreach inc_dir : cassini_inc_dirs
        if fs.is_dir(inc_dir)
            cassini_headers_dep = declare_dependency(include_directories: include_directories(inc_dir))
            break
        endif
    endforeach
endif
