/* SPDX-License-Identifier: BSD-3-Clause
 * Copyright(c) 2024 Hewlett Packard Enterprise Development LP
 */

#ifndef _CASSINI_HW_H_
#define _CASSINI_HW_H_

#include <stdint.h>
#include <stdbool.h>
#include <rte_mbuf.h>

#include "cassini_ethdev.h"

/* Cassini PCI Device IDs */
#define CASSINI_VENDOR_ID           0x17DB  /* HPE Vendor ID */
#define CASSINI_DEVICE_ID_C1        0x0501  /* Cassini 1 */
#define CASSINI_DEVICE_ID_C2        0x0502  /* Cassini 2 */

/* Hardware register offsets and definitions */
#define CASSINI_REG_DEVICE_ID       0x0000
#define CASSINI_REG_DEVICE_STATUS   0x0008
#define CASSINI_REG_DEVICE_CONTROL  0x0010

/* Device status bits */
#define CASSINI_STATUS_READY        (1 << 0)
#define CASSINI_STATUS_ERROR        (1 << 1)
#define CASSINI_STATUS_RESET        (1 << 2)

/* Command queue configuration */
#define CASSINI_CQ_SIZE_MIN         64
#define CASSINI_CQ_SIZE_MAX         4096
#define CASSINI_CQ_SIZE_DEFAULT     1024

/* Event queue configuration */
#define CASSINI_EQ_SIZE_MIN         64
#define CASSINI_EQ_SIZE_MAX         4096
#define CASSINI_EQ_SIZE_DEFAULT     1024

/* Memory descriptor limits */
#define CASSINI_MD_MAX_SIZE         (1ULL << 32)  /* 4GB max */
#define CASSINI_MD_ALIGN            4096          /* Page alignment */

/* Packet format definitions */
#define CASSINI_PKT_FORMAT_STD      C_PKT_FORMAT_STD
#define CASSINI_PKT_FORMAT_SMALL    0  /* For small packets */

/* Checksum control definitions */
#define CASSINI_CSUM_NONE           C_CHECKSUM_CTRL_NONE
#define CASSINI_CSUM_TCP            C_CHECKSUM_CTRL_TCP
#define CASSINI_CSUM_UDP            C_CHECKSUM_CTRL_UDP

/* Hardware capabilities */
struct cassini_hw_caps {
    uint32_t max_cqs;           /* Maximum command queues */
    uint32_t max_eqs;           /* Maximum event queues */
    uint32_t max_mds;           /* Maximum memory descriptors */
    uint32_t max_mtu;           /* Maximum MTU */
    uint32_t min_mtu;           /* Minimum MTU */
    bool supports_checksum;     /* Hardware checksum support */
    bool supports_tso;          /* TCP segmentation offload */
    bool supports_rss;          /* Receive side scaling */
    bool supports_vlan;         /* VLAN support */
};

/* Hardware statistics */
struct cassini_hw_stats {
    uint64_t rx_packets;
    uint64_t rx_bytes;
    uint64_t rx_errors;
    uint64_t rx_dropped;
    uint64_t rx_crc_errors;
    uint64_t rx_length_errors;
    uint64_t rx_fifo_errors;
    
    uint64_t tx_packets;
    uint64_t tx_bytes;
    uint64_t tx_errors;
    uint64_t tx_dropped;
    uint64_t tx_fifo_errors;
    uint64_t tx_carrier_errors;
};

/* Function prototypes */

/* Hardware initialization and cleanup */
int cassini_hw_probe(struct rte_pci_device *pci_dev);
int cassini_hw_init_device(struct cassini_adapter *adapter);
void cassini_hw_cleanup_device(struct cassini_adapter *adapter);
int cassini_hw_reset_device(struct cassini_adapter *adapter);

/* Hardware capabilities */
int cassini_hw_get_capabilities(struct cassini_adapter *adapter,
                               struct cassini_hw_caps *caps);

/* Command queue management */
int cassini_hw_cq_alloc(struct cassini_adapter *adapter,
                       struct cassini_cq *cq, uint32_t size, bool is_tx);
void cassini_hw_cq_free(struct cassini_adapter *adapter,
                       struct cassini_cq *cq);
int cassini_hw_cq_start(struct cassini_adapter *adapter,
                       struct cassini_cq *cq);
void cassini_hw_cq_stop(struct cassini_adapter *adapter,
                       struct cassini_cq *cq);

/* Event queue management */
int cassini_hw_eq_alloc(struct cassini_adapter *adapter,
                       struct cassini_eq *eq, uint32_t size);
void cassini_hw_eq_free(struct cassini_adapter *adapter,
                       struct cassini_eq *eq);
int cassini_hw_eq_start(struct cassini_adapter *adapter,
                       struct cassini_eq *eq);
void cassini_hw_eq_stop(struct cassini_adapter *adapter,
                       struct cassini_eq *eq);

/* Memory descriptor management */
int cassini_hw_md_alloc(struct cassini_adapter *adapter,
                       struct cassini_md *md, void *va, size_t len);
void cassini_hw_md_free(struct cassini_adapter *adapter,
                       struct cassini_md *md);

/* Packet transmission */
int cassini_hw_tx_idc(struct cassini_adapter *adapter,
                     struct cassini_tx_queue *txq,
                     struct rte_mbuf *mbuf);
int cassini_hw_tx_dma(struct cassini_adapter *adapter,
                     struct cassini_tx_queue *txq,
                     struct rte_mbuf *mbuf);

/* Packet reception */
int cassini_hw_rx_setup_buffers(struct cassini_adapter *adapter,
                               struct cassini_rx_queue *rxq);
uint16_t cassini_hw_rx_process_events(struct cassini_adapter *adapter,
                                     struct cassini_rx_queue *rxq,
                                     struct rte_mbuf **rx_pkts,
                                     uint16_t nb_pkts);

/* Statistics */
int cassini_hw_get_stats(struct cassini_adapter *adapter,
                        struct cassini_hw_stats *stats);
void cassini_hw_clear_stats(struct cassini_adapter *adapter);

/* MAC address management */
int cassini_hw_get_mac_addr(struct cassini_adapter *adapter,
                           struct rte_ether_addr *mac_addr);
int cassini_hw_set_mac_addr(struct cassini_adapter *adapter,
                           const struct rte_ether_addr *mac_addr);

/* Link management */
int cassini_hw_get_link_info(struct cassini_adapter *adapter,
                            struct rte_eth_link *link);
int cassini_hw_set_link_up(struct cassini_adapter *adapter);
int cassini_hw_set_link_down(struct cassini_adapter *adapter);

/* Promiscuous mode */
int cassini_hw_set_promiscuous(struct cassini_adapter *adapter, bool enable);
int cassini_hw_set_allmulticast(struct cassini_adapter *adapter, bool enable);

/* MTU management */
int cassini_hw_set_mtu(struct cassini_adapter *adapter, uint16_t mtu);

/* Interrupt management */
int cassini_hw_enable_interrupts(struct cassini_adapter *adapter);
void cassini_hw_disable_interrupts(struct cassini_adapter *adapter);

/* Utility functions */
static inline bool cassini_is_cassini_2(struct cassini_adapter *adapter)
{
    return adapter->hw_info.is_cassini_2;
}

static inline uint32_t cassini_get_platform_type(struct cassini_adapter *adapter)
{
    return adapter->hw_info.platform_type;
}

/* Hardware register access helpers */
static inline uint32_t cassini_read_reg(struct cassini_adapter *adapter,
                                       uint32_t offset)
{
    return rte_read32((volatile void *)((char *)adapter->hw_addr + offset));
}

static inline void cassini_write_reg(struct cassini_adapter *adapter,
                                    uint32_t offset, uint32_t value)
{
    rte_write32(value, (volatile void *)((char *)adapter->hw_addr + offset));
}

static inline uint64_t cassini_read_reg64(struct cassini_adapter *adapter,
                                         uint32_t offset)
{
    return rte_read64((volatile void *)((char *)adapter->hw_addr + offset));
}

static inline void cassini_write_reg64(struct cassini_adapter *adapter,
                                      uint32_t offset, uint64_t value)
{
    rte_write64(value, (volatile void *)((char *)adapter->hw_addr + offset));
}

/* Command submission helpers */
static inline void cassini_cq_ring_doorbell(struct cassini_cq *cq)
{
    if (cq->cq) {
        cxi_cq_ring(cq->cq);
    }
}

/* Event processing helpers */
static inline bool cassini_eq_has_events(struct cassini_eq *eq)
{
    if (eq->eq) {
        return cxi_eq_get_event(eq->eq) != NULL;
    }
    return false;
}

#endif /* _CASSINI_HW_H_ */
